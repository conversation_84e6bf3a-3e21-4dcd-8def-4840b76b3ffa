# arm-toolchain.cmake
message(STATUS "Loading ARM toolchain from arm-toolchain.cmake")

# 设置目标系统名称，例如 Linux
SET(CMAKE_SYSTEM_NAME Linux)

# 设置目标处理器架构，例如 aarch64
# SET(CMAKE_SYSTEM_PROCESSOR aarch64)
SET(CMAKE_SYSTEM_PROCESSOR arm)

# 指定交叉编译器的路径
# SET(CMAKE_C_COMPILER /home/<USER>/ti-processor-sdk-linux-am62xx-evm-***********/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc)
# SET(CMAKE_CXX_COMPILER /home/<USER>/ti-processor-sdk-linux-am62xx-evm-***********/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++)
SET(CMAKE_C_COMPILER /home/<USER>/ti-processor-sdk-linux-am62xx-evm-***********/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-linux-gnueabihf/bin/arm-none-linux-gnueabihf-gcc)
SET(CMAKE_CXX_COMPILER /home/<USER>/ti-processor-sdk-linux-am62xx-evm-***********/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-linux-gnueabihf/bin/arm-none-linux-gnueabihf-g++)


# 指定 strip 工具路径
SET(CMAKE_STRIP /home/<USER>/ti-processor-sdk-linux-am62xx-evm-***********/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-strip)
