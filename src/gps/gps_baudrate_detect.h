#ifndef GPS_BAUDRATE_DETECT_H
#define GPS_BAUDRATE_DETECT_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************
                                Includes
 ******************************************************************************/
#include <stdint.h>
#include <stdbool.h>
#include <pthread.h>
#include <time.h>

/******************************************************************************
                                Defines
 ******************************************************************************/
// GPS波特率定义
#define AUTOSET_GPS_BAUDRATE0           9600
#define AUTOSET_GPS_BAUDRATE1           38400
#define AUTOSET_GPS_BAUDRATE2           115200
#define SET_GPS_DEFAULT_BAUDRATE        AUTOSET_GPS_BAUDRATE2
#define SET_GPS_DEFAULT_BAUDRATE_IDX    2

// GPS自动检测状态标志
#define AUTOSET_GPS_IS_ALIVE            0x01
#define AUTOSET_GPS_IS_DETECTED         0x02
#define AUTOSET_GPS_IS_CONFIGURED       0x04
#define AUTOSET_GPS_IS_FINISHED         0x08

// GPS模块类型
#define GPS_MODULE_UNKNOWN              0
#define GPS_MODULE_UM220                1  // Unicore
#define GPS_MODULE_CASIC                2  // 中科微
#define GPS_MODULE_TECHTOTOP            3  // 泰斗
#define GPS_MODULE_GMTK                 4  // 联发科

// GPS检测参数
#define MAX_GPS_ARGS                    20
#define GPS_BUFFER_SIZE                 512
#define GPS_DETECT_TIMEOUT_MS           3000
#define GPS_SENTENCE_MIN_COUNT          5

/******************************************************************************
                                Enums
 ******************************************************************************/
typedef enum {
    GPS_DETECT_STATE_IDLE = 0,
    GPS_DETECT_STATE_TESTING,
    GPS_DETECT_STATE_CONFIGURING,
    GPS_DETECT_STATE_FINISHED,
    GPS_DETECT_STATE_FAILED
} gps_detect_state_t;

/******************************************************************************
                                Struct
 ******************************************************************************/
typedef struct {
    int fd;                             // GPS串口文件描述符
    char device_path[64];               // GPS设备路径 (如 /dev/ttyS3)
    uint32_t current_baudrate;          // 当前波特率
    uint8_t current_baudrate_idx;       // 当前波特率索引
    uint8_t detect_stage;               // 检测阶段标志
    uint8_t module_type;                // GPS模块类型
    gps_detect_state_t state;           // 检测状态
    
    // 统计信息
    uint16_t sentence_count;            // 接收到的GPS语句数量
    uint16_t valid_sentence_count;      // 有效GPS语句数量
    
    // 缓冲区
    char rx_buffer[GPS_BUFFER_SIZE];    // 接收缓冲区
    uint16_t rx_index;                  // 接收索引
    
    // 定时器
    struct timespec start_time;         // 检测开始时间
    uint32_t timeout_ms;                // 超时时间(毫秒)
    
    // 线程同步
    pthread_mutex_t mutex;              // 互斥锁
    bool is_running;                    // 运行状态
} gps_baudrate_detect_t;

/******************************************************************************
                                Variables
 ******************************************************************************/
extern gps_baudrate_detect_t g_gps_detect;

/******************************************************************************
                                Public Functions
 ******************************************************************************/

/**
 * @brief 初始化GPS自动波特率检测
 * 
 * @param device_path GPS设备路径 (如 "/dev/ttyS3")
 * @return 0:成功 -1:失败
 */
int gps_baudrate_detect_init(const char *device_path);

/**
 * @brief 反初始化GPS自动波特率检测
 * 
 * @return 0:成功 -1:失败
 */
int gps_baudrate_detect_deinit(void);

/**
 * @brief 开始GPS自动波特率检测
 * 
 * @param default_baudrate_idx 默认波特率索引 (0:9600, 1:38400, 2:115200)
 * @return 0:成功 -1:失败
 */
int autoset_gps_module_baudrate(uint8_t default_baudrate_idx);

/**
 * @brief GPS自动波特率检测处理函数
 * 需要在主循环中定期调用
 * 
 * @return 0:成功 -1:失败
 */
int autoset_gps_module_baudrate_handle(void);

/**
 * @brief 切换GPS串口波特率
 * 
 * @return 0:成功 -1:失败
 */
int switch_gps_uart_baudrate(void);

/**
 * @brief 检查GPS模块是否存活
 * 
 * @return 0:成功 -1:失败
 */
int check_gps_is_alive(void);

/**
 * @brief 处理接收到的GPS数据
 * 
 * @param data GPS数据缓冲区
 * @param length 数据长度
 * @return 0:成功 -1:失败
 */
int process_gps_detect_data(const char *data, uint16_t length);

/**
 * @brief 解析GPS语句并检测模块类型
 * 
 * @param sentence GPS语句
 * @param length 语句长度
 * @return 检测到的模块类型
 */
uint8_t detect_gps_module_type(const char *sentence, uint16_t length);

/**
 * @brief 获取当前检测状态
 * 
 * @return 检测状态
 */
gps_detect_state_t get_gps_detect_state(void);

/**
 * @brief 获取检测到的GPS模块类型
 * 
 * @return GPS模块类型
 */
uint8_t get_detected_gps_module_type(void);

/**
 * @brief 获取检测到的波特率
 * 
 * @return 波特率值
 */
uint32_t get_detected_gps_baudrate(void);

/**
 * @brief 重置GPS检测状态
 * 
 * @return 0:成功 -1:失败
 */
int reset_gps_detect_state(void);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* GPS_BAUDRATE_DETECT_H */
