/******************************************************************************
                                Includes
 ******************************************************************************/
#include "gps_baudrate_detect.h"
#include "gps.h"
#include "serial.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h>
#include <errno.h>
#include <sys/time.h>

/******************************************************************************
                                Defines
 ******************************************************************************/
#define GPS_DETECT_DEBUG    1

#if GPS_DETECT_DEBUG
#define GPS_DEBUG(fmt, ...) printf("[GPS_DETECT] " fmt "\n", ##__VA_ARGS__)
#else
#define GPS_DEBUG(fmt, ...)
#endif

/******************************************************************************
                                Variables
 ******************************************************************************/
// 全局GPS检测实例
gps_baudrate_detect_t g_gps_detect = {0};

// 支持的波特率表
static const uint32_t gps_baudrate_table[] = {
    AUTOSET_GPS_BAUDRATE0,  // 9600
    AUTOSET_GPS_BAUDRATE1,  // 38400
    AUTOSET_GPS_BAUDRATE2   // 115200
};

// GPS模块识别字符串
static const char gps_pattern_string[4][12] = {
    "Unicore",      // UM220
    "CASIC",        // 中科微
    "Techtotop",    // 泰斗
    "MTKGPS"        // GMTK
};

// GPS模块配置命令
static const char *gps_config_commands[] = {
    // UM220配置命令
    "$CFGPRT,0,1,0x23,0x23*16\r\n",
    // CASIC配置命令
    "$PCAS01,1*1D\r\n$PCAS02,1000*2E\r\n",
    // 泰斗配置命令
    "$CFGMSG,0,1,1,1,1,1*56\r\n",
    // GMTK配置命令
    "$PMTK314,0,1,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0*28\r\n"
};

/******************************************************************************
                                Static Functions
 ******************************************************************************/

/**
 * @brief 获取当前时间戳(毫秒)
 */
static uint64_t get_timestamp_ms(void)
{
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (uint64_t)ts.tv_sec * 1000 + ts.tv_nsec / 1000000;
}

/**
 * @brief 计算时间差(毫秒)
 */
static uint32_t get_time_diff_ms(struct timespec *start)
{
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);

    uint32_t diff_ms = (now.tv_sec - start->tv_sec) * 1000 +
                       (now.tv_nsec - start->tv_nsec) / 1000000;
    return diff_ms;
}

/**
 * @brief 设置串口波特率
 */
static int set_serial_baudrate(int fd, uint32_t baudrate)
{
    struct termios tty;
    speed_t speed;

    if (tcgetattr(fd, &tty) != 0) {
        GPS_DEBUG("Error getting serial attributes: %s", strerror(errno));
        return -1;
    }

    // 根据波特率设置speed
    switch (baudrate) {
        case 9600:   speed = B9600;   break;
        case 38400:  speed = B38400;  break;
        case 115200: speed = B115200; break;
        default:
            GPS_DEBUG("Unsupported baudrate: %u", baudrate);
            return -1;
    }

    cfsetispeed(&tty, speed);
    cfsetospeed(&tty, speed);

    // 设置串口参数：8N1，无流控
    tty.c_cflag &= ~PARENB;        // 无奇偶校验
    tty.c_cflag &= ~CSTOPB;        // 1个停止位
    tty.c_cflag &= ~CSIZE;         // 清除数据位设置
    tty.c_cflag |= CS8;            // 8个数据位
    tty.c_cflag &= ~CRTSCTS;       // 无硬件流控
    tty.c_cflag |= CREAD | CLOCAL; // 启用接收，忽略调制解调器控制线

    // 设置为原始模式
    tty.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    tty.c_iflag &= ~(IXON | IXOFF | IXANY);
    tty.c_iflag &= ~(IGNBRK | BRKINT | PARMRK | ISTRIP | INLCR | IGNCR | ICRNL);
    tty.c_oflag &= ~OPOST;

    // 设置读取超时
    tty.c_cc[VTIME] = 1;  // 100ms超时
    tty.c_cc[VMIN] = 0;   // 非阻塞读取

    if (tcsetattr(fd, TCSANOW, &tty) != 0) {
        GPS_DEBUG("Error setting serial attributes: %s", strerror(errno));
        return -1;
    }

    // 清空缓冲区
    tcflush(fd, TCIOFLUSH);

    GPS_DEBUG("Serial baudrate set to %u", baudrate);
    return 0;
}

/**
 * @brief 从字符串中提取参数
 */
static uint16_t get_paras_from_string(const char *str, char **paras, uint16_t max_paras, char delimiter)
{
    uint16_t para_count = 0;
    char *start = (char *)str;
    char *end;

    while (para_count < max_paras && (end = strchr(start, delimiter)) != NULL) {
        paras[para_count] = start;
        *end = '\0';  // 临时终止字符串
        para_count++;
        start = end + 1;
    }

    // 处理最后一个参数
    if (para_count < max_paras && *start != '\0') {
        paras[para_count] = start;
        para_count++;
    }

    return para_count;
}

/******************************************************************************
                                Public Functions
 ******************************************************************************/

int gps_baudrate_detect_init(const char *device_path)
{
    if (!device_path) {
        GPS_DEBUG("Invalid device path");
        return -1;
    }

    // 初始化检测结构体
    memset(&g_gps_detect, 0, sizeof(g_gps_detect));
    strncpy(g_gps_detect.device_path, device_path, sizeof(g_gps_detect.device_path) - 1);

    // 初始化互斥锁
    if (pthread_mutex_init(&g_gps_detect.mutex, NULL) != 0) {
        GPS_DEBUG("Failed to initialize mutex");
        return -1;
    }

    g_gps_detect.fd = -1;
    g_gps_detect.state = GPS_DETECT_STATE_IDLE;
    g_gps_detect.timeout_ms = GPS_DETECT_TIMEOUT_MS;

    GPS_DEBUG("GPS baudrate detect initialized for device: %s", device_path);
    return 0;
}

int gps_baudrate_detect_deinit(void)
{
    pthread_mutex_lock(&g_gps_detect.mutex);

    if (g_gps_detect.fd >= 0) {
        close(g_gps_detect.fd);
        g_gps_detect.fd = -1;
    }

    g_gps_detect.is_running = false;
    g_gps_detect.state = GPS_DETECT_STATE_IDLE;

    pthread_mutex_unlock(&g_gps_detect.mutex);
    pthread_mutex_destroy(&g_gps_detect.mutex);

    GPS_DEBUG("GPS baudrate detect deinitialized");
    return 0;
}

int autoset_gps_module_baudrate(uint8_t default_baudrate_idx)
{
    pthread_mutex_lock(&g_gps_detect.mutex);

    // 检查参数有效性
    if (default_baudrate_idx >= sizeof(gps_baudrate_table) / sizeof(gps_baudrate_table[0])) {
        GPS_DEBUG("Invalid baudrate index: %u", default_baudrate_idx);
        pthread_mutex_unlock(&g_gps_detect.mutex);
        return -1;
    }

    // 打开GPS设备
    g_gps_detect.fd = open(g_gps_detect.device_path, O_RDWR | O_NOCTTY | O_NONBLOCK);
    if (g_gps_detect.fd < 0) {
        GPS_DEBUG("Failed to open GPS device %s: %s", g_gps_detect.device_path, strerror(errno));
        pthread_mutex_unlock(&g_gps_detect.mutex);
        return -1;
    }

    // 初始化检测参数
    g_gps_detect.current_baudrate_idx = default_baudrate_idx;
    g_gps_detect.current_baudrate = gps_baudrate_table[default_baudrate_idx];
    g_gps_detect.detect_stage = 0;
    g_gps_detect.module_type = GPS_MODULE_UNKNOWN;
    g_gps_detect.sentence_count = 0;
    g_gps_detect.valid_sentence_count = 0;
    g_gps_detect.rx_index = 0;
    g_gps_detect.is_running = true;
    g_gps_detect.state = GPS_DETECT_STATE_TESTING;

    // 设置初始波特率
    if (set_serial_baudrate(g_gps_detect.fd, g_gps_detect.current_baudrate) != 0) {
        GPS_DEBUG("Failed to set initial baudrate");
        close(g_gps_detect.fd);
        g_gps_detect.fd = -1;
        pthread_mutex_unlock(&g_gps_detect.mutex);
        return -1;
    }

    // 记录开始时间
    clock_gettime(CLOCK_MONOTONIC, &g_gps_detect.start_time);

    GPS_DEBUG("GPS baudrate detection started with baudrate %u", g_gps_detect.current_baudrate);

    pthread_mutex_unlock(&g_gps_detect.mutex);
    return 0;
}

int autoset_gps_module_baudrate_handle(void)
{
    char buffer[256];
    ssize_t bytes_read;

    if (!g_gps_detect.is_running || g_gps_detect.fd < 0) {
        return 0;
    }

    pthread_mutex_lock(&g_gps_detect.mutex);

    // 检查超时
    uint32_t elapsed_ms = get_time_diff_ms(&g_gps_detect.start_time);
    if (elapsed_ms > g_gps_detect.timeout_ms) {
        GPS_DEBUG("Detection timeout, switching baudrate");
        if (switch_gps_uart_baudrate() != 0) {
            g_gps_detect.state = GPS_DETECT_STATE_FAILED;
            GPS_DEBUG("All baudrates tested, detection failed");
        }
        pthread_mutex_unlock(&g_gps_detect.mutex);
        return 0;
    }

    // 读取GPS数据
    bytes_read = read(g_gps_detect.fd, buffer, sizeof(buffer) - 1);
    if (bytes_read > 0) {
        buffer[bytes_read] = '\0';
        process_gps_detect_data(buffer, bytes_read);
    }

    // 检查是否检测完成
    if (g_gps_detect.state == GPS_DETECT_STATE_TESTING) {
        check_gps_is_alive();
    }

    pthread_mutex_unlock(&g_gps_detect.mutex);
    return 0;
}

int switch_gps_uart_baudrate(void)
{
    // 切换到下一个波特率
    g_gps_detect.current_baudrate_idx++;

    if (g_gps_detect.current_baudrate_idx >= sizeof(gps_baudrate_table) / sizeof(gps_baudrate_table[0])) {
        // 所有波特率都测试完毕
        g_gps_detect.current_baudrate_idx = 0;
        g_gps_detect.state = GPS_DETECT_STATE_FAILED;
        GPS_DEBUG("All baudrates tested, detection failed");
        return -1;
    }

    g_gps_detect.current_baudrate = gps_baudrate_table[g_gps_detect.current_baudrate_idx];

    // 设置新的波特率
    if (set_serial_baudrate(g_gps_detect.fd, g_gps_detect.current_baudrate) != 0) {
        GPS_DEBUG("Failed to set baudrate %u", g_gps_detect.current_baudrate);
        return -1;
    }

    // 重置检测参数
    g_gps_detect.sentence_count = 0;
    g_gps_detect.valid_sentence_count = 0;
    g_gps_detect.rx_index = 0;
    clock_gettime(CLOCK_MONOTONIC, &g_gps_detect.start_time);

    GPS_DEBUG("Switched to baudrate %u", g_gps_detect.current_baudrate);
    return 0;
}

int check_gps_is_alive(void)
{
    static uint8_t check_counter = 0;

    check_counter++;
    if (check_counter >= 10) {  // 每10次调用检查一次
        check_counter = 0;

        if (g_gps_detect.valid_sentence_count >= GPS_SENTENCE_MIN_COUNT) {
            // GPS模块检测成功
            g_gps_detect.detect_stage |= AUTOSET_GPS_IS_ALIVE;
            g_gps_detect.state = GPS_DETECT_STATE_CONFIGURING;

            GPS_DEBUG("GPS module detected at baudrate %u, type: %u",
                     g_gps_detect.current_baudrate, g_gps_detect.module_type);

            // 发送配置命令
            if (g_gps_detect.module_type > 0 && g_gps_detect.module_type <= 4) {
                const char *config_cmd = gps_config_commands[g_gps_detect.module_type - 1];
                write_gps_data(g_gps_detect.fd, (void *)config_cmd, strlen(config_cmd));
                GPS_DEBUG("Sent configuration command for module type %u", g_gps_detect.module_type);
            }

            g_gps_detect.state = GPS_DETECT_STATE_FINISHED;
            g_gps_detect.detect_stage |= AUTOSET_GPS_IS_FINISHED;

            return 0;
        } else {
            // 当前波特率下未检测到有效GPS数据，切换波特率
            GPS_DEBUG("No valid GPS data at baudrate %u, switching...", g_gps_detect.current_baudrate);
            return switch_gps_uart_baudrate();
        }
    }

    return 0;
}

int process_gps_detect_data(const char *data, uint16_t length)
{
    for (uint16_t i = 0; i < length; i++) {
        char ch = data[i];

        // 将字符添加到接收缓冲区
        if (g_gps_detect.rx_index < GPS_BUFFER_SIZE - 1) {
            g_gps_detect.rx_buffer[g_gps_detect.rx_index] = ch;
            g_gps_detect.rx_index++;
        }

        // 检查是否为完整的GPS语句（以\n结尾）
        if (ch == '\n') {
            g_gps_detect.rx_buffer[g_gps_detect.rx_index - 1] = '\0';  // 移除\n

            // 移除可能的\r
            if (g_gps_detect.rx_index > 1 && g_gps_detect.rx_buffer[g_gps_detect.rx_index - 2] == '\r') {
                g_gps_detect.rx_buffer[g_gps_detect.rx_index - 2] = '\0';
            }

            // 检查是否为有效的GPS语句
            if (g_gps_detect.rx_index > 6 && g_gps_detect.rx_buffer[0] == '$') {
                g_gps_detect.sentence_count++;

                // 检查是否为NMEA标准语句
                if (strstr(g_gps_detect.rx_buffer, "RMC") ||
                    strstr(g_gps_detect.rx_buffer, "GGA") ||
                    strstr(g_gps_detect.rx_buffer, "GSV")) {
                    g_gps_detect.valid_sentence_count++;

                    // 检测GPS模块类型
                    if (g_gps_detect.module_type == GPS_MODULE_UNKNOWN) {
                        g_gps_detect.module_type = detect_gps_module_type(g_gps_detect.rx_buffer, g_gps_detect.rx_index);
                    }
                }

                GPS_DEBUG("GPS sentence: %s", g_gps_detect.rx_buffer);
            }

            // 重置接收索引
            g_gps_detect.rx_index = 0;
        }
    }

    return 0;
}

uint8_t detect_gps_module_type(const char *sentence, uint16_t length)
{
    // 检查GPS模块识别字符串
    for (uint8_t i = 0; i < 4; i++) {
        if (strstr(sentence, gps_pattern_string[i]) != NULL) {
            GPS_DEBUG("Detected GPS module type: %s", gps_pattern_string[i]);
            return i + 1;  // 返回模块类型（1-4）
        }
    }

    // 根据语句格式推断模块类型
    if (strstr(sentence, "BD") != NULL) {
        return GPS_MODULE_CASIC;  // 中科微通常使用BD前缀
    } else if (strstr(sentence, "GP") != NULL) {
        return GPS_MODULE_UM220;  // 默认为UM220
    }

    return GPS_MODULE_UNKNOWN;
}

gps_detect_state_t get_gps_detect_state(void)
{
    return g_gps_detect.state;
}

uint8_t get_detected_gps_module_type(void)
{
    return g_gps_detect.module_type;
}

uint32_t get_detected_gps_baudrate(void)
{
    return g_gps_detect.current_baudrate;
}

int reset_gps_detect_state(void)
{
    pthread_mutex_lock(&g_gps_detect.mutex);

    g_gps_detect.detect_stage = 0;
    g_gps_detect.module_type = GPS_MODULE_UNKNOWN;
    g_gps_detect.sentence_count = 0;
    g_gps_detect.valid_sentence_count = 0;
    g_gps_detect.rx_index = 0;
    g_gps_detect.state = GPS_DETECT_STATE_IDLE;

    pthread_mutex_unlock(&g_gps_detect.mutex);

    GPS_DEBUG("GPS detect state reset");
    return 0;
}
