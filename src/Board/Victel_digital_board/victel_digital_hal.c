/**
  ******************************************************************************
  *                Copyright (c) 2011, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    22-December-2012
  * @brief   This file provides
  *            - All system call for user
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include "victel_digital_board.h"
#include "victel_digital_keyboard.h"
#include "victel_digital_usart.h"
#include "victel_digital_lcd.h"
#include "victel_digital_flash.h"
#include "victel_digital_spi_flash.h"
#include "victel_digital_keyboard.h"
#include "screen_message.h"
#include "global_define.h"
#include "stack_config.h"
#include "regDefine.h"
#include "drv.h"
#include "vlog.h"

/** @addtogroup STM32F2xx_StdPeriph_Examples
  * @{
  */
/** @addtogroup HW_Calendar
  * @{
  */
extern uint8_t mcu_running_mode;
extern uint8_t interphone_mode, loggin_the_base;
extern uint16_t device_desync_counter;
extern FACTORY_PARAS_TYPEDEF *g_factory_inst;
extern P_FUN_VOID_VOID p_dbgdata_process;
extern P_FUN_VOID_VOID p_config_bt_process;
extern RUNTIME_PARAMETERS_XVBASE_TYPEDEF *g_runtime_inst_xvbase;
extern RUNTIME_PARAMETERS_TYPEDEF g_runtime_inst;
extern int16_t g_rssi_regulator_ex;
extern STATIC_PARAMETERS_TYPEDEF *g_static_ptr;
extern uint32_t timestamp_10ms;
extern GPS_STRUCT_TYPEDEF rmc_data;


static uint8_t lcd_print_zzw_slot = 0, local_second = 60;
static uint8_t module_init_done = 0;
uint8_t g_print_adc = 0;		// bit7:1-pps present; bit6:1-bkio present; bit4:1-enable slot detail; bit3:1-enable can; bit2:1-enable FPGA; bit1:1-enable dqi; bit0:1-enable adc
uint8_t g_misc_control = 0;		// bit3:1-solar control; bit2:1-led warning; bit1:1-autocall; bit0:1-print bar
GUI_INTERACTIVE_TYPEDEF *g_gui_interactive;
uint32_t set_gui_interactive_addr(void);


void null_void_void(void)
{
}

void null_void_u8(uint8_t para8)
{
}

void null_void_u8_2(uint8_t para8)
{
}

void null_void_u16(uint16_t para16)
{
}

void __enable_irq(void)
{
}

void __disable_irq(void)
{
}

#ifdef TEST_CUSTOM_DELAY
extern uint32_t timestamp_1s, timestamp_2s;
void test_custom_delay(void)
{
	uint16_t i, delay[6] = {1000, 2000, 3000, 4000, 5000, 6000};
	uint32_t timestamp_1s_bak, timestamp_2s_bak, timestamp_hard40us_bak, timestamp_hard1ms_bak, hard40us, hard1ms;

	vlog_i("hal","test delay start at 2s later:");
	sys_delay(2000);
	for (i = 0; i < 6; i++)
	{
		timestamp_1s_bak = timestamp_1s;
		timestamp_2s_bak = timestamp_2s;
		timestamp_hard1ms_bak = get_timestamp_measure();
		timestamp_hard40us_bak = get_timestamp_40us_measure();
		sys_delay(delay[i]);
		hard40us = get_timestamp_40us_measure();
		hard1ms = get_timestamp_measure();
		vlog_i("hal","\tdelay %dms,check:1s=%d,2s=%d,40us_hard(max=2.6s)=%d,1ms_hard=%d", delay[i],
			(timestamp_1s - timestamp_1s_bak) * TIMESTAMP_POLL_PERIOD,
			(timestamp_2s - timestamp_2s_bak) * TIMESTAMP_POLL_PERIOD,
			get_40us_measure_timer_difference(hard40us, timestamp_hard40us_bak) * TIMESTAMP_MEASURE_PERIOD / 1000,	// unit of 40us timer is us
			get_measure_timer_difference(hard1ms, timestamp_hard1ms_bak) * TIMESTAMP_MEASURE_PERIOD);
	}
}
#endif

void set_module_init_done_flag(uint8_t flag)
{
	module_init_done |= flag;
}

uint8_t get_module_init_done_flag(uint8_t flag)
{
	return module_init_done & flag;
}

void toggle_print_adv_state(void)
{
	if (g_print_adc & 0x01)
		g_print_adc &= ~0x01;
	else
		g_print_adc |= 0x01;
}

void toggle_print_dqi_state(void)
{
	if (g_print_adc & 0x02)
		g_print_adc &= ~0x02;
	else
		g_print_adc |= 0x02;
}

void toggle_print_fpga_state(void)
{
	if (g_print_adc & 0x04)
		g_print_adc &= ~0x04;
	else
		g_print_adc |= 0x04;
}

void toggle_print_can_state(void)
{
	if (g_print_adc & 0x08)
		g_print_adc &= ~0x08;
	else
		g_print_adc |= 0x08;
}

uint8_t toggle_print_slot_detail(uint8_t on_off)	// 0-disable, 1-enable, 2-toggle, 3-get the setup
{
	if (on_off <= 2)
	{
		if (on_off == 0)
		{
			g_print_adc &= ~0x10;
		}
		else if (on_off == 1)
		{
			g_print_adc |= 0x10;
		}
		else
		{
			if (g_print_adc & 0x10)
				g_print_adc &= ~0x10;
			else
				g_print_adc |= 0x10;
		}
	}
	return (g_print_adc & 0x10) ? 1 : 0;
}


//#define PRINT_TEMP_DETAIL		1
extern uint8_t dqi_prev_slot;
extern uint8_t dqi_prev_slot_ex;
void print_adc(void)
{
	uint8_t n, v, text[200];
	uint16_t *ptr16;
#ifdef PRINT_TEMP_DETAIL
	float temp_mv, temp_res, temp;
#endif
	uint16_t rp_watt, fp_watt;
	uint32_t mv;

	n = sprintf((char *)text, "RSSI=%d(%d)/%d(%d|%d),DQI=%d/%d", get_rssi_value_real(), g_factory_inst->rssi_regulator,
			get_rssi_value_real_ex(), g_rssi_regulator_ex, g_factory_inst->rssi_regulator_ex, dqi_prev_slot, dqi_prev_slot_ex);

	mv = get_battery_voltage();
	v = (uint8_t)(mv >> 8);
	n += sprintf((char *)(text + n), " BAT=%d.%02dV", v, (uint8_t)mv);	// %02d: 12 9->12.09, 12 10->12.10

  #ifdef PRINT_TEMP_DETAIL
	cal_temp(&temp_mv, &temp_res, &temp);
  #endif
	ptr16 = get_temp_rf_power_voltage();
	rp_watt = convert_rp_watt(cal_801_rp(ptr16[1]));
	fp_watt = cal_801_fp(ptr16[2]);

  #ifdef PRINT_TEMP_DETAIL
	n += sprintf((char *)(text + n), " T=%d(adc=%d,mv=%d,res=%3.2f),Rp=%d(%d.%dW),Fp=%d(%dW)",
		GET_MODULE_TEMP_ADJUST(temp), ptr16[0], (uint16_t)(temp_mv + 0.5f), temp_res,
		ptr16[1], rp_watt >> 4, rp_watt & 0x0f, ptr16[2], fp_watt);
  #else
	n += sprintf((char *)(text + n), " T=%d(adc=%d),Rp=%d(%d.%dW),Fp=%d(%dW)",
		get_module_temp(), ptr16[0],
		ptr16[1], rp_watt >> 4, rp_watt & 0x0f, ptr16[2], fp_watt);
  #endif

	vlog_i("hal","%s", text);
}

extern uint32_t timestamp_kb_scan;
uint16_t TRUNK_AUTO_CALLING_GAP = 50, TRUNK_AUTO_CALLING_VOICE_TIME = 25;

void toggle_auto_calling(uint16_t gap, uint16_t voice_time, uint8_t ctrl)
{
	TRUNK_AUTO_CALLING_GAP = gap;
	TRUNK_AUTO_CALLING_VOICE_TIME = voice_time;
	set_voice_source(3);
	if (ctrl == 0)
		g_misc_control &= ~MISC_CTRL_AUTOCALL;
	else if (ctrl == 1)
		g_misc_control |= MISC_CTRL_AUTOCALL;
	else
		g_misc_control ^= MISC_CTRL_AUTOCALL;
}

uint8_t is_auto_call_enable(void)
{
	return g_misc_control & MISC_CTRL_AUTOCALL;
}

void auto_calling_test(void)
{
	static uint8_t auto_calling_fsm = 0;
	static uint32_t auto_calling_count = 0;

	if (auto_calling_count != timestamp_kb_scan)
	{
		if (g_misc_control & MISC_CTRL_AUTOCALL)
		{
trunk_auto_calling_precedure:
			if ((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) || (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING))
			{
				switch (auto_calling_fsm)
				{
					case 0:	// idle
						if (auto_calling_count + TRUNK_AUTO_CALLING_GAP < timestamp_kb_scan)
						{
							set_sync_call_stack_type(get_setup_ptt_event_with_ptt_key());
							auto_calling_count = timestamp_kb_scan;
							auto_calling_fsm = 1;
						}
						break;
					case 1:	// ptt pressed
						if (auto_calling_count + TRUNK_AUTO_CALLING_VOICE_TIME < timestamp_kb_scan)
						{
							set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
							auto_calling_count = timestamp_kb_scan;
							auto_calling_fsm = 2;
						}
						break;
					case 2:	// ptt released
						if (auto_calling_count + TRUNK_AUTO_CALLING_GAP < timestamp_kb_scan)
						{
							set_sync_call_stack_type(SYNC_CALL_STACK_CANCEL_PRESSED);
							auto_calling_count = timestamp_kb_scan;
							auto_calling_fsm = 3;
						}
						break;
					case 3:	// cancel pressed
						if (auto_calling_count + 2 < timestamp_kb_scan)
						{
							set_sync_call_stack_type(SYNC_CALL_STACK_CANCEL_RELEASED);
							auto_calling_count = timestamp_kb_scan;
							auto_calling_fsm = 0;
						}
						break;
					default:
						auto_calling_count = timestamp_kb_scan;
						auto_calling_fsm = 2;
						break;
				}
			}
			else
			{
				switch (auto_calling_fsm)
				{
					case 0: // idle
						if (auto_calling_count + TRUNK_AUTO_CALLING_GAP < timestamp_kb_scan)
						{
							set_sync_call_stack_type(get_setup_ptt_event_with_ptt_key());
							auto_calling_count = timestamp_kb_scan;
							auto_calling_fsm = 1;
						}
						break;
					case 1:	// ptt pressed
						if (auto_calling_count + TRUNK_AUTO_CALLING_VOICE_TIME < timestamp_kb_scan)
						{
							set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
							auto_calling_count = timestamp_kb_scan;
							auto_calling_fsm = 0;
						}
						break;
					default:
						auto_calling_count = timestamp_kb_scan;
						auto_calling_fsm = 1;
						break;
				}
			}
		}
		else
		{
			if (auto_calling_fsm)
				goto trunk_auto_calling_precedure;
		}
	}
}

extern uint32_t g_38400000hz;
extern uint16_t reg_mod_gain_dc[4];
uint8_t fpga_read_all_regs(uint32_t buf_addr)
{
	uint32_t *ptr32 = (uint32_t *)buf_addr, satellite = get_satellite_number();

	memset((void *)buf_addr, 0, FPGA_REG_TOTAL_REGS * sizeof(uint32_t));

//	ptr32[1] = FPGA_REG_GCR_60MS_SEL_MASK | FPGA_REG_GCR_REF_SEL_MASK | FPGA_REG_GCR_SYNC39_SEL_MASK;			// GCR: ext60ms(DSP)-ref38.4-9s
	ptr32[1] = g_runtime_inst_xvbase->fpga_write_gcr;
	ptr32[1] |= FPGA_REG_GCR_REF_SEL_MASK | FPGA_REG_GCR_SYNC39_SEL_MASK;	// set source from DSP should be successful

	ptr32[3] = is_gps_pps_ok() ? ((uint32_t)1 << 31) : 0;					// GPSR: PPS lock-sate lock-n_satellite(5bit,max=31)
	ptr32[3] |= (uint32_t)is_gps_gga_locked() << 20;
	ptr32[3] |= ((satellite < 31) ? satellite : 31) << 24;

	ptr32[4] = g_runtime_inst_xvbase->fpga_write_tcr;
	ptr32[5] = dsp_read_19m2_crystal() * 10;	// FPGA_REG_CLK192SR

	ptr32[6] = g_38400000hz * 10;				// FPGA_REG_CLK384SR
	ptr32[7] = reg_mod_gain_dc[2] * 10;

	return 0;
}

/**
  * @brief  Inserts a delay time.
  * @param  ms: specifies the delay time length.
  * @retval None
  */
uint16_t binary_search(uint16_t *table, uint16_t max_item, uint16_t val)	// return the index of the table; bit15==1 means that not search
{
    int32_t low = 0, high = max_item - 1, mid, cnt = 0;

	while (low <= high)
	{
		cnt++;
		mid = (low + high) / 2;
//		vlog_i("hal","\t[%d]%d(%d):%d", cnt, table[mid], mid, val);
		if (val == table[mid])
			return (uint16_t)mid;
		else if (val < table[mid])
			high = mid - 1;
		else
			low = mid + 1;
	}
	return 0x8000 | mid;
}

void bubble_sort (uint8_t *talbe, uint8_t len)
{
	uint8_t i, j, temp;

	for (i = 0; i < len - 1; i++)				/* ��ѭ��Ϊ����������len��������len-1�� */
	{
		for (j = 0; j < len - 1  - i; j++)		/* ��ѭ��Ϊÿ�˱ȽϵĴ�������i�˱Ƚ�len-i�� */
		{
			if (talbe[j] > talbe[j + 1])		/* ����Ԫ�رȽϣ��������򽻻�������Ϊ������ң�����֮�� */
			{
				temp = talbe[j];
				talbe[j] = talbe[j + 1];
				talbe[j + 1] = temp;
			}
		}
	}
}

void bubble_u16_sort(uint16_t *talbe, uint16_t len)
{
	uint16_t i, j, temp;

	for (i = 0; i < len - 1; i++)				/* ��ѭ��Ϊ����������len��������len-1�� */
	{
		for (j = 0; j < len - 1  - i; j++)		/* ��ѭ��Ϊÿ�˱ȽϵĴ�������i�˱Ƚ�len-i�� */
		{
			if (talbe[j] > talbe[j + 1])		/* ����Ԫ�رȽϣ��������򽻻�������Ϊ������ң�����֮�� */
			{
				temp = talbe[j];
				talbe[j] = talbe[j + 1];
				talbe[j + 1] = temp;
			}
		}
	}
}

#define GET_CHAR_TYPE(c)		(((c)>='0')&&((c)<='9'))?'0':((((c)>='A')&&((c)<='F'))?0x37:((((c)>='a')&&((c)<='f'))?0x57:0))
uint32_t string2hexadecimal(uint8_t *str, uint8_t *hex, uint32_t number)	// one byte of str -> half of one hex byte, first byte at highest 4bit
{
	uint32_t i, n;
	uint8_t c, type;

	for (i = 0, n = 0; i < number; i++)
	{
		c = str[number - i - 1];
		type = GET_CHAR_TYPE(c);
		if (type)
		{
			if (i & 0x01)
				hex[n++] |= (c - type) << 4;
			else
				hex[n] = (c - type) & 0x0f;
		}
		else
		{
			if (i & 0x01)
				hex[n] &= 0x0f;
			break;
		}
	}
	return i;
}

uint32_t hexadecimal2string(uint8_t *str, uint8_t *hex, uint32_t number, uint8_t lowercase)	// 4bit hex->byte, highest 4bit at first byte
{
	uint32_t i = 0, n;
	uint8_t c, proc_low4bit;

	if (number)
	{
		proc_low4bit = number & 0x01;		// odd, so the highest 4bit at low
		n = number / 2 + proc_low4bit - 1;	// byte position of the highest hex 4bit
		for (; i < number; i++)
		{
			if (proc_low4bit)
				c = hex[n] & 0x0f;
			else
				c = hex[n] >> 4;

			if (c <= 9)
				str[i] = c + '0';
			else if (c <= 15)
				str[i] = c + 0x37 + (lowercase ? 0x20 : 0);
			else
				break;

			if (proc_low4bit)
			{
				n--;
				proc_low4bit = 0;
			}
			else
			{
				proc_low4bit = 1;
			}
		}
		str[i] = 0;
	}
	return i;
}

int32_t get_integer_from_string(uint8_t *str)
{
	uint8_t *ch = str;
	int32_t symbol = 1, val = 0;

	if (*ch == '-')
	{
		ch++;
		symbol = -1;
	}
	while ((*ch >= '0') && (*ch <= '9'))
	{
		val = val * 10 + *ch - '0';
		ch++;
	}

	return symbol * val;
}

void test_hex_decimal(void)
{
	uint8_t test[30] = "d2f01cc98f2";
	uint32_t hexa[4];

	string2hexadecimal(test, (uint8_t *)hexa, p_strlen(test));

	hexa[0] = 0x23f6cc88;
	hexa[1] = 0x5cb017a2;
	hexa[2] = 0xea097;

	hexadecimal2string(test, (uint8_t *)hexa, 21, 0);
	vlog_i("hal","hex->str1:%s", (char *)test);

	hexa[0] = 0x83BB2Cd;

	hexadecimal2string(test, (uint8_t *)hexa, 7, 1);
	vlog_i("hal","hex->str2:%s", (char *)test);
}

uint32_t BIG_to_U32(uint8_t *data)
{
	uint32_t ret;
	uint8_t *ptr;

	ptr = (uint8_t *)(&ret);
	*ptr++ = data[3];
	*ptr++ = data[2];
	*ptr++ = data[1];
	*ptr++ = data[0];

	return ret;
}

uint32_t LITTLE_to_U32(uint8_t *data)
{
	uint32_t ret;
	uint8_t *ptr;

	ptr = (uint8_t *)(&ret);
	*ptr++ = data[0];
	*ptr++ = data[1];
	*ptr++ = data[2];
	*ptr++ = data[3];

	return ret;
}

void print_heximal(uint8_t *pre, uint8_t *dat, uint16_t len)
{
	uint16_t i;

	if (pre)
		vlog_i("hal","%s", pre);
	for (i = 0; i < len; i++)
		vlog_i("hal","%02x ", dat[i]);
	vlog_i("hal","");
}

uint32_t string_to_bcd(uint8_t *bcd, uint8_t *str, uint32_t max_num)
{
	uint32_t n_bcd = 0, n_str = 0;

	while ((str[n_str] >= '0') && (str[n_str] <= '9') && (n_str < max_num))
	{
		if (n_str & 0x01)						// copy to low 4bit at second
			bcd[n_bcd++] |= (str[n_str++] - '0') & 0x0f;
		else									// copy to high 4bit at first
			bcd[n_bcd] = ((str[n_str++] - '0') & 0x0f) << 4;
	}

	if (n_str & 0x01)
		bcd[n_bcd] |= 0x0f;

	return n_bcd;
}

uint32_t bcd_to_string(uint8_t *bcd, char *str, uint32_t bytes, uint32_t max_num)
{
	uint8_t  cont;
	uint32_t n_bcd = 0, n_str = 0;

	while ((n_bcd < bytes) && (n_str < max_num))
	{
		if (n_str & 0x01)
			cont = bcd[n_bcd++] & 0x0f;
		else
			cont = (bcd[n_bcd] >> 4) & 0x0f;

		if (cont <= 9)
			str[n_str++] = '0' + cont;
		else
			break;
	}
	str[n_str] = 0;
	return n_str;
}

uint32_t string2decimal(uint8_t *str)
{
	int16_t i;
	uint16_t len;
	uint32_t dec, mul;

	len = p_strlen(str);
	if (len && (len <= 10))
	{
		dec = 0;
		mul = 1;
		for (i = len - 1; i >= 0; i--)
		{
			if ((str[i] >= '0') && (str[i] <= '9'))
			{
				dec += (str[i] - '0') * mul;
			}
			else
			{
				break;
			}
			mul *= 10;
		}
		return dec;
	}
	else
		return 0;

}

uint32_t num_string2decimal(uint8_t *str, uint8_t num)
{
	uint8_t tmp[11];

	if (num <= 10)
	{
		p_strncpy(tmp, str, num);
		tmp[num] = 0;
		return string2decimal(tmp);
	}
	else
	{
		return 0;
	}
}

uint8_t get_decimal_bits(uint32_t decimal)
{
	uint8_t nbit = 0;
	uint32_t dec = decimal;

	if (dec == 0)
	{
		nbit = 1;
	}
	else
	{
		while (dec)
		{
			nbit++;
			dec /= 10;
		}
	}
	return nbit;
}

uint8_t decimal2string(uint32_t decimal, uint8_t nbits, uint8_t *string)
{
#if 0
	char temp[22], *ptr;
	uint8_t i, nbit;

	nbit = (nbits > 10) ? 10 : nbits;
	ptr = temp + 10;
	sprintf(ptr, "%d", decimal);
	i = strlen(ptr);
	if (nbit)
	{
		if (i > nbit)
		{
			ptr += i - nbit;
		}
		else
		{
			for (; i < nbit; i++)
			{
				*(--ptr) = '0';
			}
		}
	}
	else
	{
		nbit = i;
	}
	p_strcpy(string, ptr);
	return nbit;
#else
	uint8_t i, nbit, bit, j, temp[11];
	uint32_t dec;

	if (nbits == 0)
	{
		nbit = get_decimal_bits(decimal);
	}
	else
	{
		nbit = nbits;
	}

	if (nbit > 10)
		nbit = 10;

	if (nbit)
	{
		j = nbit - 1;
		temp[nbit] = 0;
		dec = decimal;
		for (i = 0; i < nbit; i++, j--)
		{
			bit = dec % 10;
			temp[j] = bit + '0';
			dec /= 10;
			if (dec == 0)
			{
				break;
			}
		}
		for (i += 1; i < nbits; i++)
		{
			temp[--j] = '0';
		}
		p_strcpy(string, temp);
	}
	return nbit;
#endif
}

uint32_t input_one_decimal_with_assert(uint8_t ch, uint32_t original, uint32_t lim_l, uint32_t lim_h)
{
	uint8_t nbit;
	uint32_t power, ret;

	if (ch <= 9)
	{
		nbit = get_decimal_bits(lim_h);
		if (nbit)
		{
			power = pow(10, nbit - 1);
			ret = (original % power) * 10 + ch;
		}
		else
			ret = ch;

		if (ret > lim_h)
			ret %= 10;
	}
	else if (ch == KEY_CODE_STAR - KEY_CODE_0)		// *
		ret = original / 10;
	else
		ret = original;

	if (ret < lim_l)
		ret = original;

	return ret;
}

void move_one_u32_item_to_top(uint32_t *list, uint32_t n)
{
	uint32_t i, tmp;

/*	for (i = 0; i < n; i++)
	{
		tmp = list[i];
		list[i] = list[n];
		list[n] = tmp;
	}*/

	tmp = list[n];
	for (i = n; i > 0; i--)
		list[i] = list[i - 1];
	list[i] = tmp;
}

void delete_one_aligned_struct_block(uint32_t *addr, uint32_t block_size, uint32_t rem_block_num)	// rem_block_num: ��ɾ���Ŀ�֮��Ŀ����������ܹ�5���飬ɾ����1��(��0��ʼ)������ֵΪ3
{
	uint32_t i, n, *ptr;

	for (i = 0, n = rem_block_num * block_size / sizeof(uint32_t), ptr = addr + block_size / sizeof(uint32_t); i < n; i++)
		addr[i] = ptr[i];

	memset(&addr[i], 0, block_size);
}

void set_gps_power_onoff(uint8_t onoff)					// 1-on, 0-off
{
	if (onoff)
		set_gps_power(1);
		//set_gpio_low(GPS_POWER_CTRL_LINE);				// pin = 0: power on
	else
		set_gps_power(0);
		//set_gpio_high(GPS_POWER_CTRL_LINE);
}

void gps_power_onoff(uint8_t onoff)
{

	if (onoff == 0)
	{
		set_gps_power_onoff(0);
		set_gps_link_state(0);
		set_gps_pps_state(0);
		set_base_dsp_sync();
//		set_base_bkio();
		set_sync_call_stack_type(SYNC_CALL_STACK_GPS_UPDATE);
	}
	else
	{
		set_gps_power_onoff(1);
		update_gps_work_mode();
	}
}

void wifi_power_onoff(uint8_t onoff)				// 1-on, 0-off
{
}

void bt_power_onoff(uint8_t onoff)
{
	set_real_bt_config(onoff);
	if (onoff == 0)
	{
		set_bt_state(false);
		if (uart2_use_as_bt())
		{
			usart_deinit(UART_BT_INDEX);
			usart_configuration(UART_BT_INDEX, 460800, 0);
			set_uart2_use_as_dataport(0);
		}
	}
	else
	{
		set_bt_state(true);					// pin = 0: power on
		usart_configuration(UART_BT_INDEX, 460800, 0);
	}
}

void rf_power_switch(uint8_t level)
{
	packet_ctrl_to_mcu_inf_frame(BASEBAND_TYPE_SET_RF_PWR, level, 0);
}

void set_dsp_work_mode(uint32_t mode);
void dsp_work_mode_switch(uint32_t mode)	// 0:����ֱͨ; 1:���ֳ���; 2:PDT ��Ⱥ; 3: MPT ��Ⱥ; 4: MPT ���� 5:������; 6:������PRO_X; 7:������PRO_V; 20-��վ
{
	set_dsp_work_mode(mode);
}

#ifndef P1_DEL_UNNECESSARY_KEY_PROC

static uint8_t lcd_print_ber = 0;	// lcd_print_ber: 0-stop, 1-to lcd, 2-to uart, 3-toggle
void set_lcd_print_ber(uint8_t flag)
{
	if (flag <= 3)
	{
		if (flag == 3)
			lcd_print_ber = (lcd_print_ber + 1) & 0x01;
		else
			lcd_print_ber = flag;

		if (lcd_print_ber == 0)
		{
			set_dsp_ber_test_mode(0);
		}
		else
		{
			set_dsp_ber_test_mode(1);
			admittance_testing_clear_berr(1);
		}
	}
}

#endif

uint8_t set_lcd_print_zzw_slot(uint8_t mask, uint8_t flag)
{
	if (flag <= 1)
	{
		if (flag)
			lcd_print_zzw_slot |= mask;
		else
			lcd_print_zzw_slot &= ~mask;
	}
	else
	{
		if (lcd_print_zzw_slot & mask)
			lcd_print_zzw_slot &= ~mask;
		else
			lcd_print_zzw_slot |= mask;
	}

	return lcd_print_zzw_slot & mask;
}

#define PRE_POWER_ENABLE_TRUNKING		0x80
#define PRE_POWER_ENABLE_ZZWPRO			0x40
#define PRE_POWER_LEVEL_MASK			0x38
  #define PRE_POWER_LEVEL_LOW			0x00
  #define PRE_POWER_LEVEL_HIGH			0x08
  #define PRE_POWER_LEVEL_10W			0x10/*reserved*/
  #define PRE_POWER_LEVEL_15W			0x18/*reserved*/
  #define PRE_POWER_LEVEL_20W			0x20/*reserved*/
  #define PRE_POWER_LEVEL_25W			0x28/*reserved*/
  #define PRE_POWER_LEVEL_TINY			0x30/*reserved*/
static uint8_t pre_power_level = PRE_POWER_LEVEL_MASK | 2;	// 2:reserved(2 levels power, ctrl_by_db is 6)
void reset_auto_switch_rf_power(void)
{
	pre_power_level |= PRE_POWER_LEVEL_MASK;
}

void set_auto_switch_rf_power_enable(void)
{
	pre_power_level &= ~(PRE_POWER_ENABLE_TRUNKING | PRE_POWER_ENABLE_ZZWPRO);
	if ((dev_is_base() == 0) && (dev_have_base_feature() == 0) && (dev_power_ctrl_by_db() == 0) && (maintain_setup_parameter(PARA_OPERATE_READ, RF_POWER_PARAS_POS, 0) > 1))
	{
		if ((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) || (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING))
			pre_power_level |= PRE_POWER_ENABLE_TRUNKING;
		else if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
			pre_power_level |= PRE_POWER_ENABLE_ZZWPRO;
	}
}

#define get_auto_switch_rf_power_enable_trunking()	pre_power_level & PRE_POWER_ENABLE_TRUNKING
uint8_t get_auto_switch_rf_power_enable_zzwpro(void)
{
	return pre_power_level & PRE_POWER_ENABLE_ZZWPRO;
}

void set_auto_switch_rf_power(uint8_t dat)
{
	pre_power_level = PRE_POWER_LEVEL_MASK | 2;
	if (dat == 1)
		pre_power_level |= PRE_POWER_ENABLE_TRUNKING;
	else if (dat == 2)
		pre_power_level |= PRE_POWER_ENABLE_ZZWPRO;
	else if (dat == 3)
		pre_power_level |= PRE_POWER_ENABLE_TRUNKING | PRE_POWER_ENABLE_ZZWPRO;
	vlog_i("hal","auto rf setup=%02x", pre_power_level);
}

void auto_switch_rf_power(int16_t rssi)
{

//	if (rssi >= -50)		// tiny, 200mW
//	{
//		if ((pre_power_level & PRE_POWER_LEVEL_MASK) != PRE_POWER_LEVEL_TINY)
//		{
//			vlog_i("hal","\tauto->T(%d)", rssi);
//			rf_power_switch(2);
//			pre_power_level &= ~PRE_POWER_LEVEL_MASK;
//			pre_power_level |= PRE_POWER_LEVEL_TINY;
//		}
//	}
//	else if (rssi >= -70)	// small, 1W
	if (rssi > -75)
	{
		if ((pre_power_level & PRE_POWER_LEVEL_MASK) != PRE_POWER_LEVEL_LOW)
		{
			vlog_i("hal","\tauto->L(%d)", rssi);
			rf_power_switch(0);
			pre_power_level &= ~PRE_POWER_LEVEL_MASK;
		}
	}
//	else								// large, 4W
	else if (rssi < -85)
	{
		if ((pre_power_level & PRE_POWER_LEVEL_MASK) != PRE_POWER_LEVEL_HIGH)
		{
			vlog_i("hal","\tauto->H(%d)", rssi);
			rf_power_switch(1);
			pre_power_level &= ~PRE_POWER_LEVEL_MASK;
			pre_power_level |= PRE_POWER_LEVEL_HIGH;
		}
	}
}
void update_time_force(void)
{
	local_second = 60;
}

void update_time(void)
{

	if (local_second < 58)
	{
		local_second++;
		g_gui_interactive->rtc_time[2] = local_second;
	}
	else
	{
		if (get_module_init_done_flag(MODULE_INIT_DONE_DSP))	// P1��bar_enable��Ч�������DSP�Ƿ���������ȷ��rtc�Ƿ��ѱ���ʼ��
		{
			RTC_GetTD(0, 0, 0, 0, g_gui_interactive->rtc_time, g_gui_interactive->rtc_time + 1, g_gui_interactive->rtc_time + 2);
			local_second = g_gui_interactive->rtc_time[2];
		}
	}
}

static uint8_t led_play_timer = 0;
void led_warning_player(void)
{
	if (g_misc_control & MISC_CTRL_LED_WARNING)
	{
		switch (led_play_timer)
		{
			case 1:
				led_play(BAR_TYPE_TR_ARROW_UP);
				break;
			case 3:
				led_play(BAR_TYPE_TR_ARROW_RESET);
				break;
			case 20:
				led_play(BAR_TYPE_TR_ARROW_DOWN);
				break;
			case 22:
				led_play(BAR_TYPE_TR_ARROW_RESET);
				break;
		}
	}
	else if (phone_is_busy_now() == 0)
	{
		switch (led_play_timer)
		{
			case 1:
				led_play(BAR_TYPE_TR_ARROW_DOWN);
				break;
			case 3:
				led_play(BAR_TYPE_TR_ARROW_RESET);
				break;
		}
	}

	led_play_timer++;
}

#define PERIOD_PRINT_SOMETHING()	if (g_print_adc & 0x01)\
										print_adc();\
									print_gps_parse_data(0)
void peroidic_refresh(void)
{
	if (peroidic_refresh_check())
	{
		(*p_config_bt_process)();
		check_gps_is_alive();

		battery_login_sound_warning();
		PERIOD_PRINT_SOMETHING();
		act_report_check_vmode(TIMESTAMP_POLL_PERIOD);

		if (phone_is_busy_now())
			set_delay_to_enter_sleep_external(0xff);		// ����������ʡ����ʱ������

		high_rp_protect_process();
		if (dev_is_base())
		{
			send_module_status_to_can();
		}
		else
		{
			if (dev_enable_can())
			{
				send_module_status_to_can();
				send_channel_status_to_can_not_base();
			}
			if (interphone_mode > INTERPHONE_MODE_ZZWPRO_Q)
			{
				if (device_desync_counter < 0x7fff)
					device_desync_counter++;
			}
		}
	}
}

void peroidic_refresh_admittest(void)
{
	battery_login_sound_warning();

	if (g_print_adc & 0x01)
		print_adc();

	print_gps_parse_data(0);

	high_rp_protect_process();
}

void is_update_mode(void)
{
/*	if (upgrade_key_state() == 0)
	{
		set_prompt(0);
		set_printf_redirection(UART_DEBUG_INDEX);
		vlog_i("hal","Command of maintain mode: ");
		vlog_i("hal","\texit:  return to running mode");

		while (maintain_line_poll() == 0)
			;
	}
*/
	set_prompt(1);
}

const char normal_poweron_flag[] = "Device Power On";
uint8_t is_soft_reset(void)
{
//	return p_strcmp(STARTED_FLAG_BASE, normal_poweron_flag) ? 0 : 1;
	return 1;
}
void set_normal_power_on_flag(uint8_t flag)	// 0-invalid the flag; else-set to normal power on flag
{
}

void powerdown_to_switch_mode(void)
{
	system_power_down(1);
}

const uint8_t powerdown_to_switch_mode_tips[2][2][20] = {
		{" ����ģʽ���л�", " ���ڹػ�������"},
		{"Will power down", "to switch mode"}
};

void power_down_device_now(uint8_t flag)	// 0: normal; 1: power on is invalid
{
	set_normal_power_on_flag(0);
	if (flag == 0)
	{
		save_user_data_to_flash(0xf0);
		send_tip_sound(TIP_TYPE_POWER_DOWN, 1);
		sys_delay(600);
		timer9to14_stop(TIMER_40MS_INDEX);

		sys_delay(100);
	}

	sys_delay(200);
	vlog_i("hal","\r\n==== Device power down now ====\r\n");
	system("poweroff");
	/*
	at32_led_init(DEV_POWER_CTRL_LINE);
	set_gpio_low(DEV_POWER_CTRL_LINE);
	while (1)
		;
										*/
}

void waiting_for_reset_or_off(uint8_t no_delay, uint8_t fail_type)	// fail_type: 6-re-generate tune table
{
	uint32_t waiting_for_off = 0;

	if (dev_is_base())
	{
	}
	else // dev_is_base
	{
		timer9to14_stop(TIMER_40MS_INDEX);
		power_down_device_now(1);
		while (1)
			;
	} // dev_is_base
}

const uint8_t hardware_not_match_tip[2][9][20] = {
		{"SPI��ʼ������", "ESN���ʹ���", "���뱡��������", "���в�������", "ESN���ݴ���", "�����汾��ƥ��", "��г����ʽ����", "��̬��������", "������������"},
		{"SPI init FAIL", "ESN invalid", "User book error", "Runtime error", "ESN error", "Paras NOT match", "Tune table ERR", "STATIC error", "FACTORY error"}
};
void parameters_valid_checking(uint8_t force)
{
	uint8_t lang_type;

//	lang_type = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0;
	lang_type = 1;
	library_lost_process((uint8_t *)"Parameters check", (uint8_t *)hardware_not_match_tip[lang_type][force], 0, 0, 0);
}

#ifndef RF_DEUBG_ENABLE
uint8_t now_can_power_on(void)		// return: 1-cold start
{
	return 1;
}
#endif	// RF_DEUBG_ENABLE

void print_powerdown_time(uint8_t flag, uint8_t wait_flag)
{
	static uint32_t powerdown_timestamps = 0;
	uint32_t times_stamp;

	times_stamp = get_timestamp_measure();

	if (flag == 0)
		vlog_i("hal","powerdown flag=%02x:%d %d", wait_flag, get_measure_timer_difference(times_stamp, powerdown_timestamps), timestamp_kb_scan);
	else
		vlog_i("hal","powerdown ctrl=%02x(%02x):%d %d", flag, wait_flag, get_measure_timer_difference(times_stamp, powerdown_timestamps), timestamp_kb_scan);

	powerdown_timestamps = times_stamp;
}

uint8_t system_power_down(uint8_t flag)	// 0-check for power down; 1-want to powerdown; 2-want to reboot; 3-powerdown screen; 4-reset to normal; else-now can power down
{
	static uint8_t power_down_waiting = 0;	// 1-wait to powerdown; 2-wait to reboot; 3-powerdown screen; 4-reset to normal; 0xfe-power down now; 0xfd-reboot now
	static uint32_t power_down_downcount = 0;

	if (flag == 0)
	{
		if (power_down_waiting)
		{
			print_powerdown_time(flag, power_down_waiting);
			if ((power_down_waiting == 1) || (power_down_waiting == 2))
			{
				if ((timestamp_kb_scan > power_down_downcount + 1200 / KEYBOARD_POLL_PERIOD) ||
					(((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) || (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING)) && (loggin_the_base < 0xf0)) ||
					(((interphone_mode <= INTERPHONE_MODE_PDT_CONV) || (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)) && (get_log_in_out_paras(1) == 0)))
				{
					// normal: 160-200ms can return; else: almost 3600ms
//					vlog_i("hal","KB=%d DC=%d(%d)", timestamp_kb_scan, power_down_downcount, timestamp_kb_scan - power_down_downcount);
					power_down_waiting = 0xff - power_down_waiting;
				}
			}
			else if (power_down_waiting == 0xfe)
			{
				power_down_device_now(0);
			}
			else if (power_down_waiting == 0xfd)
			{
				set_delay_to_enter_sleep_external(30);
				save_user_data_to_flash(0xf0);
				sys_delay(100);
				system("reboot");
//				nvic_system_reset();	// NVIC_SystemReset();
			}
			else if (power_down_waiting != 3)
			{
				power_down_waiting = 0;
			}
		}
	}
	else
	{
		print_powerdown_time(flag, power_down_waiting);
		if ((flag == 1) || (flag == 2))			// want to power down or reboot
		{
			if ((power_down_waiting != 1) && (power_down_waiting != 2))
			{
#ifdef SET_WATCHDOG_ENABLE
				iwatchdog_init(10000);
#endif
//				if (get_module_init_done_flag(MODULE_INIT_DONE_STACK) &&
//					((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) || (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING) || get_log_in_out_paras(1)))
				if (get_module_init_done_flag(MODULE_INIT_DONE_STACK))
				{
					power_down_downcount = timestamp_kb_scan;
					set_sync_call_stack_type(SYNC_CALL_STACK_POWER_DOWN);
					power_down_waiting = flag;
					if (get_log_in_out_paras(1))
						timer_initial(1, 80, send_offline_status);
				}
				else
				{
					power_down_waiting = 0xff - flag;
				}
			}
		}
		else if (flag == 3)						// switch to power off page
		{
			if (power_down_waiting != flag)
				power_down_waiting = flag;
			else
				return 0x80 | power_down_waiting;
		}
		else if (flag == 4)						// reset to normal
		{
			power_down_waiting = 0;
		}
		else									// stack return only, you can power down now
		{
			if (get_log_in_out_paras(1) == 0)	// stack should return powerdown_now immediately at conv or trunking not login
			{
				if ((power_down_waiting == 1) || (power_down_waiting == 2))
					power_down_waiting = 0xff - power_down_waiting;
//				else
//					power_down_waiting = 0xfe;
			}
		}
	}

	return power_down_waiting;
}

uint8_t bypass_power_on_check(void)
{
	vlog_i("hal","######## Bypass power on check ########");
	return is_soft_reset() ? 0 : 1;
}

extern const char compile_date[];
extern const char compile_time[];
void set_stack_version_same_as_main(void)
{
	set_software_version(1, g_runtime_inst.device_information.mcu_soft_ver.ver_major,
			g_runtime_inst.device_information.mcu_soft_ver.ver_minor,
			(uint8_t *)compile_date, (uint8_t *)compile_time, (uint8_t *)0);
}

#ifdef TEST_VOCODER_PERFORMANCE
void test_ambe_performance(uint8_t *voc_data);
void test_nvoc_performance(uint8_t *voc_data);
#endif
#define SYSTEM_TICK_TIME_MS		10
void system_initial(void)
{
	uint8_t cold_start = 0;	// bit0:cold start; bit1:iwatchdog set; bit2:app upgrade

  /*!< At this stage the microcontroller clock setting is already configured,
       this is done through SystemInit() function which is called from startup
       file (startup_stm32f2xx.s) before to branch to application main.
       To reconfigure the default setting of SystemInit() function, refer to
       system_stm32f2xx.c file
     */

  /* Setup SysTick Timer for 10 msec interrupts.
     ------------------------------------------
    1. The SysTick_Config() function is a CMSIS function which configure:
       - The SysTick Reload register with value passed as function parameter.
       - Configure the SysTick IRQ priority to the lowest value (0x0F).
       - Reset the SysTick Counter register.
       - Configure the SysTick Counter clock source to be Core Clock Source (HCLK).
       - Enable the SysTick Interrupt.
       - Start the SysTick Counter.

    2. You can change the SysTick Clock source to be HCLK_Div8 by calling the
       SysTick_CLKSourceConfig(SysTick_CLKSource_HCLK_Div8) just after the
       SysTick_Config() function call. The SysTick_CLKSourceConfig() is defined
       inside the misc.c file.

    3. You can change the SysTick IRQ priority by calling the
       NVIC_SetPriority(SysTick_IRQn,...) just after the SysTick_Config() function
       call. The NVIC_SetPriority() is defined inside the core_cm3.h file.

    4. To adjust the SysTick time base, use the following formula:

         Reload Value = SysTick Counter Clock (Hz) x  Desired Time base (s)

       - Reload Value is the parameter to be passed for SysTick_Config() function
       - Reload Value should not exceed 0xFFFFFF
   */

	set_software_version(0, 0x23, 0x15, (uint8_t *)compile_date, (uint8_t *)compile_time, (uint8_t *)0);	// soft ver.minor==1: 350M
	print_version();

/*
	vlog_i("hal","sizeof(char) = %d", sizeof(char));
	vlog_i("hal","sizeof(uint8_t) = %d", sizeof(uint8_t));

	vlog_i("hal","sizeof(short) = %d", sizeof(short));
	vlog_i("hal","sizeof(uint16_t) = %d", sizeof(uint16_t));

	vlog_i("hal","sizeof(int) = %d", sizeof(int));
	vlog_i("hal","sizeof(uint32_t) = %d", sizeof(uint32_t));

	vlog_i("hal","sizeof(long) = %d", sizeof(long));
	vlog_i("hal","sizeof(float) = %d", sizeof(float));
	vlog_i("hal","sizeof(double) = %d", sizeof(double));
	vlog_i("hal","sizeof(double long) = %d", sizeof(double long));
	vlog_i("hal","sizeof(long long) = %d", sizeof(long long));
	vlog_i("hal","sizeof(uint64_t) = %d", sizeof(uint64_t));
	sizeof(char) = 1
	sizeof(uint8_t) = 1
	sizeof(short) = 2
	sizeof(uint16_t) = 2
	sizeof(int) = 4
	sizeof(uint32_t) = 4
	sizeof(long) = 4
	sizeof(float) = 4
	sizeof(double) = 8
	sizeof(double long) = 8
	sizeof(long long) = 8
	sizeof(uint64_t) = 8
*/

//	gui_struct_length_show();
//	set_output_stack_dump_data(1);
//	parse_gps_data("$BDGGA,�wabbbj?M,,*6F", p_strlen("$BDGGA,�wabbbj?M,,*6F"));

	copy_system_parameters();

#ifdef RF_DEUBG_ENABLE

	cold_start |= bypass_power_on_check();

#else	// RF_DEUBG_ENABLE

	if (dev_is_auto_power())
		cold_start |= bypass_power_on_check();
	else
		cold_start |= now_can_power_on();

#endif	// RF_DEUBG_ENABLE

	init_system_parameters();

	if (dev_is_base() || dev_enable_can())
		CAN_Config(0);					//CAN�����ʼ��;�����ж�ֱ����ʼ��DSP���ٿ���

	kb_timer_init();
	is_update_mode();

	vocoder_initial();
	init_mcu_inf_interface();

#ifdef TEST_VOCODER_PERFORMANCE
	test_ambe_performance(0);
	test_nvoc_performance(0);
#endif

//	test_gb_unicode();
//	test_cal_distance_mutual();

	if (dev_is_base())
	{
		init_lcd_power_ctrl_line();
		set_normal_power_on_flag(1);
		set_xvbase_address();
	}
	else // dev_is_base
	{
		set_gui_interactive_addr();
		call_ui_to_do_something(PFUNC_GUI_MAINTAIN_TYPE_LOGO);
		GL_LCD_Init();
	}

	set_normal_power_on_flag(1);

#ifdef TEST_CUSTOM_DELAY
	test_custom_delay();
#endif
#ifdef SET_WATCHDOG_ENABLE
	iwatchdog_init(8000);
#endif
	check_dsp_configure(cold_start);	// P1����������rtc_init֮�󣨿����DSP�Ƿ���������ȷ��rtc�Ƿ��ѱ���ʼ����
	encrypt_initial(0xff);	// ENCRYPT_TYPE_FENGHUO 0xff
#ifdef TEST_SD_CARD_SPEED
	sd_single_block_test();
	sd_multiple_blocks_test();
#endif
	stack_initial();
	dsp_power_on();

	rf_power_switch(maintain_setup_parameter(PARA_OPERATE_READ, RF_POWER_PARAS_POS, 0));
	sys_delay(10);
	gps_power_onoff(maintain_setup_parameter(PARA_OPERATE_READ, GPS_POWER_PARAS_POS, 0));
	wifi_power_onoff(maintain_setup_parameter(PARA_OPERATE_READ, WIFI_POWER_PARAS_POS, 0));

#ifndef USE_INT_TO_RECEIVE_GPS
	autoset_gps_module_baudrate(SET_GPS_DEFAULT_BAUDRATE_IDX);
#endif

	stack_set_watch();
	if (check_tune_table_is_valid((uint8_t *)get_tune_table_address()))
		parameters_valid_checking(6);

	dsp_interrupt_config(0xf1);

	if (dev_is_base() || dev_enable_can())
		CAN_Config(1);					//CAN�����ʼ��;�����ж�ֱ����ʼ��DSP���ٿ���
	timer9to14_start(TIMER_1S_INDEX);
	if (maintain_setup_parameter(PARA_OPERATE_READ, SPEAKER_MUTE_PARAS_POS, 0))
		mute_speaker();

	send_tip_sound(TIP_TYPE_POWER_ON, 1);

#ifdef SET_WATCHDOG_ENABLE
	iwatchdog_init(2000);
#endif

	print_prompt();
	if (get_log_in_out_paras(0))
		timer_initial(1, 960, send_online_status);
}



extern uint16_t watch_index;
extern USER_BOOK *inst_conv;
extern uint8_t g_dial_number_index, g_dial_number[];
extern const uint8_t speed_lv_define[];

uint16_t watch_index_bak_for_gui = 0xffff;

void ui_enable_rf_power_setup(void)
{
	reset_auto_switch_rf_power();
	rf_power_switch((uint8_t)maintain_setup_parameter(PARA_OPERATE_READ, RF_POWER_PARAS_POS, 0));
}

void run_command(uint8_t gui_call_flag, uint8_t *info);
void get_admit_paras_to_ui(uint32_t *para);
void gui_maintain_function(void)
{
	uint8_t  type, temp[31];
	uint16_t next;
	uint32_t gui_interactive_paras_bak[4];
	USER_BOOK *book;
#ifdef DEBUG_UI_TIMESTAMPS
	uint32_t timer_1ms = get_timestamp_measure();
#endif

	__disable_irq();
	memcpy(gui_interactive_paras_bak, g_gui_interactive->para, sizeof(uint32_t) * 4);
	__enable_irq();
	type = (uint8_t)gui_interactive_paras_bak[0];

	switch (type)
	{
		case PFUNC_MAINTAIN_TYPE_GET_WATCH:
			if (is_enter_dynamic_reconf())
			{
				book = &g_runtime_inst.air_config.air_dynamic_reconf;
			}
			else if (is_enter_instruction_to_conv())
			{
				book = inst_conv;
			}
			else
			{
				next = search_next_watching_group(watch_index_bak_for_gui, (gui_interactive_paras_bak[1] == 0) ? KEY_CODE_KNOB_CLOCK : KEY_CODE_KNOB_ANTICLOCK);
				if (next == 0xffff)
				{
					book = (USER_BOOK *)0;
				}
				else
				{
					watch_index_bak_for_gui = next;
gui_get_watch_group_book:
					book = get_watch_at_user_book(1, watch_index_bak_for_gui, 0);
				}
			}

			if (book)
			{
				memcpy(&g_gui_interactive->watch_grp, book, sizeof(USER_BOOK));
			}
			else
			{
				p_strcpy(g_gui_interactive->watch_grp.name, "Get WATCH fail");
				g_gui_interactive->watch_grp.id = 0;
				g_gui_interactive->watch_grp.bind_ch = 0;
			}
			break;

		case PFUNC_MAINTAIN_TYPE_SWITCH_WATCH:
			if (gui_interactive_paras_bak[1] == 0)			// reset
			{
				watch_index_bak_for_gui = watch_index;
				goto gui_get_watch_group_book;
			}
			else if (gui_interactive_paras_bak[1] == 0xff)	// set watch
			{
				set_sync_call_stack_type(SYNC_CALL_STACK_SWITCH_WATCH);
			}
			else
			{
				if (watch_index != watch_index_bak_for_gui)
				{
					watch_index = watch_index_bak_for_gui;
					maintain_stack_parameter(PARA_OPERATE_WRITE, STACK_PARA_WATCH_ID, &watch_index);
					set_sync_call_stack_type(SYNC_CALL_STACK_SWITCH_WATCH);
				}
			}
			break;

		case PFUNC_MAINTAIN_TYPE_ADJUST_VOL:
			if (gui_interactive_paras_bak[1] < VOICE_GAIN_LEVEL)
			{
				if (type == PFUNC_MAINTAIN_TYPE_ADJUST_VOL)	// speaker
					Set_Speak_Volume((uint8_t )gui_interactive_paras_bak[1]);
				else
					Set_Tip_Volume((uint8_t )gui_interactive_paras_bak[1]);
			}
			else if ((gui_interactive_paras_bak[1] == 0xf0) || (gui_interactive_paras_bak[1] == 0xf1))
			{
				screen_volume_adjust((type == PFUNC_MAINTAIN_TYPE_ADJUST_VOL) ? 1 : 2, (gui_interactive_paras_bak[1] == 0xf0) ? KEY_CODE_RIGHT : KEY_CODE_LEFT, 0);
			}
			save_user_data_to_flash(1);
			break;

		case PFUNC_MAINTAIN_TYPE_ADJUST_TIP_VOL:
			if (gui_interactive_paras_bak[1] < VOICE_GAIN_LEVEL)
			{
				if (type == PFUNC_MAINTAIN_TYPE_ADJUST_VOL)	// speaker
					Set_Speak_Volume((uint8_t )gui_interactive_paras_bak[1]);
				else
					Set_Tip_Volume((uint8_t )gui_interactive_paras_bak[1]);
			}
			else if ((gui_interactive_paras_bak[1] == 0xf0) || (gui_interactive_paras_bak[1] == 0xf1))
			{
				screen_volume_adjust((type == PFUNC_MAINTAIN_TYPE_ADJUST_VOL) ? 1 : 2, (gui_interactive_paras_bak[1] == 0xf0) ? KEY_CODE_RIGHT : KEY_CODE_LEFT, 0);
			}
			save_user_data_to_flash(1);
			break;

		case PFUNC_MAINTAIN_TYPE_MUTE_VOL:
			if (gui_interactive_paras_bak[1] == 0)
			{
				maintain_setup_parameter(PARA_OPERATE_WRITE, SPEAKER_MUTE_PARAS_POS, 0);
				Set_Speak_Volume(0xff);
				Set_Tip_Volume(0xff);
			}
			else
			{
				maintain_setup_parameter(PARA_OPERATE_WRITE, SPEAKER_MUTE_PARAS_POS, 1);
				mute_speaker();
			}
			save_user_data_to_flash(1);
			break;

		case PFUNC_MAINTAIN_TYPE_SET_GPS:
			if (gui_interactive_paras_bak[1] & 0x80)							// set GPS work mode
			{
				assessories_gps_set_mode((uint8_t)(gui_interactive_paras_bak[1] & 0x03));
			}
			else if (gui_interactive_paras_bak[1] <= 1)							// set GPS power
			{
				maintain_setup_parameter(PARA_OPERATE_WRITE, GPS_POWER_PARAS_POS, (uint8_t)gui_interactive_paras_bak[1]);
				gps_power_onoff((uint8_t)gui_interactive_paras_bak[1]);			// set absolutely
				save_user_data_to_flash(1);
			}
			break;

		case PFUNC_MAINTAIN_TYPE_SET_BT:
			maintain_setup_parameter(PARA_OPERATE_WRITE, BT_POWER_PARAS_POS, (uint8_t)gui_interactive_paras_bak[1]);
			bt_power_onoff((uint8_t)gui_interactive_paras_bak[1]);
			save_user_data_to_flash(1);
			break;

		case PFUNC_MAINTAIN_TYPE_READ_SPI:
			flash_spi_read_data((uint8_t *)gui_interactive_paras_bak[3], gui_interactive_paras_bak[1], gui_interactive_paras_bak[2]);
			break;

		case PFUNC_MAINTAIN_TYPE_PUT_TO_DEBUG:
			put_data(UART_DEBUG_INDEX, (uint8_t *)gui_interactive_paras_bak[1], (uint16_t)gui_interactive_paras_bak[2]);
			break;

		case PFUNC_MAINTAIN_TYPE_SET_VOCODER:
			vocoder_setup_to_dsp();
			break;

		case PFUNC_MAINTAIN_TYPE_SEND_MSG:
			send_message_asynchronous(gui_interactive_paras_bak[1], (uint8_t)gui_interactive_paras_bak[2],
				(uint16_t)(gui_interactive_paras_bak[2] >> 16), (uint8_t *)gui_interactive_paras_bak[3]);
			break;

		case PFUNC_MAINTAIN_TYPE_KEY_EVENT:
			g_dial_number_index = 0;
			if (gui_interactive_paras_bak[2])
			{
				next = p_strlen(gui_interactive_paras_bak[2]);
				if (next && (next < DIAL_NUMBER_MAX_LENGTH))
				{
					g_dial_number_index = next;
					memcpy(g_dial_number, (void *)gui_interactive_paras_bak[2], next);
				}
			}
			g_dial_number[g_dial_number_index] = 0;

			next = (uint8_t)gui_interactive_paras_bak[1];
			if (next == REPLACE_WELL_INPUT_CHAR)
			{
				if (g_dial_number_index > 1)		// g_dial_number_index=�ܳ���-1����Ϊ����Ǹ�#���ᱻ����
				{
					g_dial_number_index--;
					standby_screen_process_well();
				}
				else
				{
					g_dial_number_index = 0;
				}
			}
			else if ((next == KEY_CODE_PTT) || (next == KEY_CODE_USER1) || (next == KEY_CODE_ALARM) || (next == KEY_CODE_VIRT_RETURN) || (next == KEY_CODE_3PTT))
			{
				if ((uint8_t)(gui_interactive_paras_bak[1] >> 8) == 0)	// press
					key_press_process(next, phone_is_busy_now());
				else
					key_release_process(next);
			}
			break;

		case PFUNC_MAINTAIN_TYPE_REBOOT:
		case PFUNC_MAINTAIN_TYPE_SHUTDOWN:
			vlog_i("hal","\r\n\t\tUI:%s", (type == PFUNC_MAINTAIN_TYPE_SHUTDOWN) ? "shutdown" : "reboot");
			system_power_down((type == PFUNC_MAINTAIN_TYPE_SHUTDOWN) ? 1 : 2);
			break;

		case PFUNC_MAINTAIN_TYPE_RESET_STACK:
			reset_stack_with_disable_int();
			break;

		case PFUNC_MAINTAIN_TYPE_SWITCH_RF_POWER:
			ui_enable_rf_power_setup();
			break;

		case PFUNC_MAINTAIN_TYPE_SET_BRIGHTNESS:
			if (gui_interactive_paras_bak[1] <= LCD_BRIGHTNESS_MAX_LEVEL)
				set_base_fan_speed(0, speed_lv_define[gui_interactive_paras_bak[1]]);
			else if ((gui_interactive_paras_bak[1] >= TIM4_PWM_PERIOD) &&  (gui_interactive_paras_bak[1] <= TIM4_PWM_PERIOD * 2))
				set_base_fan_speed(0, (uint8_t)gui_interactive_paras_bak[1] - TIM4_PWM_PERIOD);
			else if (gui_interactive_paras_bak[1] == 0xff)
				pwm_output_init();
			break;

		case PFUNC_MAINTAIN_TYPE_ID_TO_NUMBER:
			id_to_number(interphone_mode, gui_interactive_paras_bak[2] ? 0 : 1, gui_interactive_paras_bak[1], 0, temp);
			p_strncpy(&gui_interactive_paras_bak[0], temp, 15);
			((uint8_t *)(&gui_interactive_paras_bak[0]))[15] = 0;
			break;

		case PFUNC_MAINTAIN_TYPE_NUMBER_TO_ID:
//			gui_interactive_paras_bak[0] = number_to_id(interphone_mode, 0, (uint8_t *)(&gui_interactive_paras_bak[1]));
			gui_interactive_paras_bak[0] = dial_number_to_id((uint8_t *)(&gui_interactive_paras_bak[1]));
			break;

		case PFUNC_MAINTAIN_TYPE_KEY_TONE:
			send_tip_sound(TIP_TYPE_KEYBOARD + ((gui_interactive_paras_bak[1] < TIP_TOTAL_TYPE) ? gui_interactive_paras_bak[1] : 0),
					1 + ((gui_interactive_paras_bak[2] < 60) ? gui_interactive_paras_bak[2] : 0));
			break;

		case PFUNC_MAINTAIN_TYPE_GET_NAME:
			get_be_called_name(gui_interactive_paras_bak[1], temp);
			memcpy(&gui_interactive_paras_bak[0], temp, MAX_USER_NAME_LEN);
			break;

		case PFUNC_MAINTAIN_TYPE_PUT_TO_DPROT:
			send_bt_frame_to_host((uint8_t *)gui_interactive_paras_bak[1], BT_DMA_DATA_DLEN, TSC_CTASK_SEND_DATA_IAP, (uint16_t)gui_interactive_paras_bak[2], gui_interactive_paras_bak[3]);
			break;
		case PFUNC_MAINTAIN_TYPE_ADMIT_CMD:
			run_command(1, (uint8_t *)gui_interactive_paras_bak[1]);
			break;

		case PFUNC_MAINTAIN_TYPE_GET_ADMIT:
			get_admit_paras_to_ui(gui_interactive_paras_bak);
			break;

		case PFUNC_MAINTAIN_TYPE_SHORTCUT_FUNC:
			if ((uint8_t)gui_interactive_paras_bak[1] <= 9)
				shortcut_input_process((uint8_t)gui_interactive_paras_bak[1]);
			break;

		case PFUNC_MAINTAIN_TYPE_GET_USER_GROUP:
		case PFUNC_MAINTAIN_TYPE_GET_POC_GROUP:
			gui_interactive_paras_bak[3] = get_group_trunk_items((type == PFUNC_MAINTAIN_TYPE_GET_USER_GROUP) ? 0 : 0xff,
				(uint8_t *)gui_interactive_paras_bak[1], 0xffff, (uint16_t *)gui_interactive_paras_bak[2]);
			break;

		case PFUNC_MAINTAIN_TYPE_GET_CTRL_GROUP:
			gui_interactive_paras_bak[3] = get_ctrl_table_items((uint8_t *)gui_interactive_paras_bak[1], 0xffff, (uint32_t *)gui_interactive_paras_bak[2]);
			break;

		case PFUNC_MAINTAIN_TYPE_CLR_LAST_CALL:
			clr_last_call_id(0, 0);
			break;

		case PFUNC_MAINTAIN_TYPE_POC_VOICE:
			if (gui_interactive_paras_bak[1] == 0xff)
			{
			}
			else if (gui_interactive_paras_bak[1] <= 1)
			{
				dsp_speech_out_pa_ctrl((uint8_t)gui_interactive_paras_bak[1], SPEECH_PA_CTRL_OUT_VOICE | SPEECH_PA_CTRL_OUT_POC);
			}
			else if (gui_interactive_paras_bak[1] <= 3)
			{
				dsp_speech_in_pa_ctrl((uint8_t)(gui_interactive_paras_bak[1] - 2), SPEECH_PA_CTRL_IN_POC);
			}
//			vlog_i("hal","[POCvoc] ctrl:%d", gui_interactive_paras_bak[1]);
			break;

		case PFUNC_MAINTAIN_TYPE_CAL_DISTANCE:
			gui_interactive_paras_bak[1] = cal_2points_distance(&rmc_data, (GPS_STRUCT_TYPEDEF *)gui_interactive_paras_bak[1], (uint8_t *)gui_interactive_paras_bak[2]);
			break;

		case PFUNC_MAINTAIN_TYPE_POC_NOTIFY:
			set_poc_mode();
			break;

		case PFUNC_MAINTAIN_TYPE_GET_BT_INFO:
			get_real_new_bt_info(&gui_interactive_paras_bak[1], &temp[0], &temp[1]);
			gui_interactive_paras_bak[2] = temp[0] | ((uint16_t)temp[1] << 8);
			break;

		case PFUNC_MAINTAIN_TYPE_SET_BT_TODO:
			set_real_new_bt_todo_something((uint8_t)gui_interactive_paras_bak[1], (uint8_t)(gui_interactive_paras_bak[1] >> 8));
			break;

		case PFUNC_MAINTAIN_TYPE_SET_BT_UPGRADE:
			send_upgrade_cmd_to_real_new_bt();
			break;

		default:
			break;
	}

	__disable_irq();
	memcpy(g_gui_interactive->para, gui_interactive_paras_bak, sizeof(uint32_t) * 4);
	__enable_irq();

#ifdef DEBUG_UI_TIMESTAMPS
	vlog_i("hal","\tuic=%d(%d)", get_measure_timer_difference(get_timestamp_measure(), timer_1ms), type);
#endif
}

#define GUI_INITIAL_ENTRY		0x08130001
extern P_FUN_VOID_VOID gui_execute;
extern uint8_t zzw_distance_string[];
extern int32_t knob_turn_times;
extern const uint16_t lut_gb2unicode[];
extern const uint16_t lut_unicode2gb[];
extern uint32_t zzwpro_xcs;
#ifdef SHOW_SOLT_DETAIL_WITH_VOICE
extern uint8_t receive_voice_counter[ZZWPRO_TR_STATUS_MAX_SLOT];
#endif

void gui_initial(void);

P_FUN_VOID_VOID gui_maintain = null_void_void;
GUI_INTERACTIVE_TYPEDEF gui_ia;


void set_gui_interactive_pointer(void)				// �����ڳ�ʼ��DSPǰ��ʼ��������DSP��sin/sout�жϻ�ʹ��g_gui_interactive->para�����쳣
{
	g_gui_interactive = (GUI_INTERACTIVE_TYPEDEF *)&gui_ia;
	memset(g_gui_interactive, 0, sizeof(GUI_INTERACTIVE_TYPEDEF));
//	*((uint32_t *)GUI_INT_ADDR_SAVE) = (uint32_t)g_gui_interactive;
//	vlog_i("hal","GUI inter struct=%d,used=%d,addr_sav=0x%08x", GUI_INST_ENTITY_MAX_SIZE, sizeof(GUI_INTERACTIVE_TYPEDEF), GUI_INT_ADDR_SAVE);
}

uint32_t set_gui_interactive_addr(void)
{
//	uint32_t gui_proj_stack, gui_init_handle;
	uint32_t stack_limit_gui, reset_handler_gui;

	g_gui_interactive->g_runtime_inst_addr = (uint32_t)&g_runtime_inst;
	g_gui_interactive->g_runtime_inst_xvbase_addr = (uint32_t)g_runtime_inst_xvbase;
	g_gui_interactive->g_static_addr = (uint32_t)g_static_ptr;
	g_gui_interactive->g_factory_inst_addr = (uint32_t)g_factory_inst;
	g_gui_interactive->zzw_sta = (ZZWPRO_STATUS_STRUCT *)get_channel_status_fr_can_frame();
	g_gui_interactive->pfunc_maintain = (uint32_t)gui_maintain_function;
	g_gui_interactive->knob_val = &knob_turn_times;
	g_gui_interactive->timer_10ms = &timestamp_10ms;
	g_gui_interactive->relative_distance = zzw_distance_string;
	g_gui_interactive->gb2unicode = (uint16_t *)lut_gb2unicode;
	g_gui_interactive->unicode2gb = (uint16_t *)lut_unicode2gb;
	g_gui_interactive->zzwpro_stack_xcs = &zzwpro_xcs;
#ifdef SHOW_SOLT_DETAIL_WITH_VOICE
	g_gui_interactive->zzwpro_rcv_voice = receive_voice_counter;
#else
	g_gui_interactive->zzwpro_rcv_voice = 0;
#endif

	watch_index_bak_for_gui = watch_index;

//	gui_init_handle = get_gui_address_flash();				// GUI program addr: 0x08120000
//	gui_proj_stack = *((uint32_t *)gui_init_handle);		// stack addr
//	gui_init_handle = *((uint32_t *)(gui_init_handle + 4));	// reset addr
//	vlog_i("hal","GUI stack=0x%08x,prog=0x%08x", gui_proj_stack, gui_init_handle);
//	((void (*)(void))gui_init_handle)();

	gui_initial(); 	// GUI_INITIAL_ENTRY
	vlog_i("hal","\tGUI callback=0x%08x,exec=0x%08x,maintain=0x%08x",
		g_gui_interactive->gui_dma_callback, g_gui_interactive->gui_exec, g_gui_interactive->pfunc_gui_maintain);
	set_module_init_done_flag(MODULE_INIT_DONE_GUI);

	gui_execute  = g_gui_interactive->gui_exec ? (P_FUN_VOID_VOID)g_gui_interactive->gui_exec : null_void_void;
	gui_maintain = g_gui_interactive->pfunc_gui_maintain ? (P_FUN_VOID_VOID)g_gui_interactive->pfunc_gui_maintain : null_void_void;

	return 0;
}

void send_tip_to_ui(uint8_t *tip1, uint8_t *tip2)
{
	uint32_t gui_interactive_paras_bak[4];
#ifdef DEBUG_UI_TIMESTAMPS
	uint32_t timer_1ms = get_timestamp_measure();
#endif

	__disable_irq();
	memcpy(gui_interactive_paras_bak, g_gui_interactive->para, sizeof(uint32_t) * 4);
	__enable_irq();

	g_gui_interactive->para[0] = PFUNC_GUI_MAINTAIN_TYPE_TIP;
	g_gui_interactive->para[1] = (uint32_t)tip1;
	g_gui_interactive->para[2] = (uint32_t)tip2;
	((void (*)(void))gui_maintain)();

	__disable_irq();
	memcpy(g_gui_interactive->para, gui_interactive_paras_bak, sizeof(uint32_t) * 4);
	__enable_irq();

#ifdef DEBUG_UI_TIMESTAMPS
	vlog_i("hal","\ttip=%d", get_measure_timer_difference(get_timestamp_measure(), timer_1ms));
#endif
}

void call_ui_to_do_something(uint8_t reason)
{
	uint32_t gui_interactive_paras_bak[4];
#ifdef DEBUG_UI_TIMESTAMPS
	uint32_t timer_1ms = get_timestamp_measure();
#endif

	__disable_irq();
	memcpy(gui_interactive_paras_bak, g_gui_interactive->para, sizeof(uint32_t) * 4);
	__enable_irq();

	g_gui_interactive->para[0] = reason;
	g_gui_interactive->para[1] = (uint32_t)get_long_message_display_buffer();
	((void (*)(void))gui_maintain)();

	__disable_irq();
	memcpy(g_gui_interactive->para, gui_interactive_paras_bak, sizeof(uint32_t) * 4);
	__enable_irq();

#ifdef DEBUG_UI_TIMESTAMPS
	vlog_i("hal","\tcui=%d(%d)", get_measure_timer_difference(get_timestamp_measure(), timer_1ms), reason);
#endif
}

void put_info_to_ui(uint8_t reason, uint32_t info)	// 0-external call number, 1-real name, 2-rssi regulate freq, 3-rssi regulate result
{
	uint32_t gui_interactive_paras_bak[4];
#ifdef DEBUG_UI_TIMESTAMPS
	uint32_t timer_1ms = get_timestamp_measure();
#endif

	__disable_irq();
	memcpy(gui_interactive_paras_bak, g_gui_interactive->para, sizeof(uint32_t) * 4);
	__enable_irq();

	g_gui_interactive->para[0] = PFUNC_GUI_MAINTAIN_TYPE_PUT_INFO;
	g_gui_interactive->para[1] = reason;
	g_gui_interactive->para[2] = info;
	((void (*)(void))gui_maintain)();

	__disable_irq();
	memcpy(g_gui_interactive->para, gui_interactive_paras_bak, sizeof(uint32_t) * 4);
	__enable_irq();

#ifdef DEBUG_UI_TIMESTAMPS
	vlog_i("hal","\tput=%d(%d)", get_measure_timer_difference(get_timestamp_measure(), timer_1ms), reason);
#endif
}

void send_dport_to_ui(uint8_t *data, uint16_t bytes, uint32_t address)
{
	uint32_t gui_interactive_paras_bak[4];
#ifdef DEBUG_UI_TIMESTAMPS
	uint32_t timer_1ms = get_timestamp_measure();
#endif

	__disable_irq();
	memcpy(gui_interactive_paras_bak, g_gui_interactive->para, sizeof(uint32_t) * 4);
	__enable_irq();

	g_gui_interactive->para[1] = (uint32_t)data;
	g_gui_interactive->para[2] = bytes;
	g_gui_interactive->para[3] = address;
	g_gui_interactive->para[0] = PFUNC_GUI_MAINTAIN_TYPE_DPORT;
	((void (*)(void))gui_maintain)();

	__disable_irq();
	memcpy(g_gui_interactive->para, gui_interactive_paras_bak, sizeof(uint32_t) * 4);
	__enable_irq();

#ifdef DEBUG_UI_TIMESTAMPS
	vlog_i("hal","\tdat=%d(%d)", get_measure_timer_difference(get_timestamp_measure(), timer_1ms), bytes);
#endif
}

void play_voice_with_tip_interface(uint8_t *alaw_voice);
uint32_t poc_get_alaw_to_play(void)
{
	uint32_t gui_interactive_paras_bak[4], ret;
#ifdef DEBUG_UI_TIMESTAMPS
	uint32_t timer_1ms = get_timestamp_measure();
#endif

	if ((g_gui_interactive->dev_misc_notify.poc_mode == 0) || (is_poc_open_voice_pa() == 0))
		return 0;

	__disable_irq();
	memcpy(gui_interactive_paras_bak, g_gui_interactive->para, sizeof(uint32_t) * 4);
	__enable_irq();

	g_gui_interactive->para[0] = PFUNC_GUI_MAINTAIN_TYPE_PLAY_VOICE;
#ifdef DEBUG_SPI_TX_STRESS_TEST
	g_gui_interactive->para[1] = 0x20004000;
#else
	g_gui_interactive->para[1] = 0;
#endif
	((void (*)(void))gui_maintain)();
//	vlog_i("hal","POC-voc:%08x", g_gui_interactive->para[1]);
	ret = g_gui_interactive->para[1];
	play_voice_with_tip_interface((uint8_t *)g_gui_interactive->para[1]);
    vlog_v("digital hal","%s %d",__FUNCTION__,__LINE__);
	__disable_irq();
	memcpy(g_gui_interactive->para, gui_interactive_paras_bak, sizeof(uint32_t) * 4);
	__enable_irq();

#ifdef DEBUG_UI_TIMESTAMPS
	vlog_i("hal","\tply=%d(%08x)", get_measure_timer_difference(get_timestamp_measure(), timer_1ms), ret);
#endif

	return ret;
}

void get_mic_voice_from_dsp(uint8_t *alaw_mic);
void poc_send_mic_voice(void)
{
	uint8_t apcm[REG_AD_PCM_LENGTH];
	uint32_t gui_interactive_paras_bak[4];
#ifdef DEBUG_UI_TIMESTAMPS
	uint32_t timer_1ms = get_timestamp_measure();
#endif

	if ((g_gui_interactive->dev_misc_notify.poc_mode == 0) || (is_poc_open_mic_pa() == 0))
		return;

	__disable_irq();
	memcpy(gui_interactive_paras_bak, g_gui_interactive->para, sizeof(uint32_t) * 4);
	__enable_irq();

	g_gui_interactive->para[0] = PFUNC_GUI_MAINTAIN_TYPE_MIC_VOICE;
	g_gui_interactive->para[1] = (uint32_t)apcm;
	get_mic_voice_from_dsp(apcm);
	((void (*)(void))gui_maintain)();
//	vlog_i("hal","POC-mic:%08x", g_gui_interactive->para[1]);

	__disable_irq();
	memcpy(g_gui_interactive->para, gui_interactive_paras_bak, sizeof(uint32_t) * 4);
	__enable_irq();

#ifdef DEBUG_UI_TIMESTAMPS
	vlog_i("hal","\tmic=%d", get_measure_timer_difference(get_timestamp_measure(), timer_1ms));
#endif
}
