/**
  ******************************************************************************
  *                Copyright (c) 2011, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    16-November-2011
  * @brief   This file provides
  *            - scan keyboard gpio initial
  *            - scan the keyboard and return the state
  *            - encode switch gpio initial
  *            - read the code of switch
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __VICTEL_DIGITAL_KEYBOARD_H__
#define __VICTEL_DIGITAL_KEYBOARD_H__

#ifdef __cplusplus
 extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>


/* keyboard code defined----------------------------------------*/
#define	KB_CODE_KEY0					(1 << 0)
#define	KB_CODE_KEY1					(1 << 1)
#define	KB_CODE_KEY2					(1 << 2)
#define	KB_CODE_KEY3					(1 << 3)
#define	KB_CODE_KEY4					(1 << 4)
#define	KB_CODE_KEY5					(1 << 5)
#define KB_CODE_KEY6					(1 << 6)
#define	KB_CODE_KEY7					(1 << 7)
#define	KB_CODE_KEY8					(1 << 8)
#define	KB_CODE_KEY9					(1 << 9)
#define	KB_CODE_KEY_STAR				(1 << 10)
#define	KB_CODE_KEY_WELL				(1 << 11)
#define D_KB_CODE_KEY_LEFT				(1 << 12)
#define D_KB_CODE_KEY_RIGHT				(1 << 13)
#define D_KB_CODE_KNOB_ENTER			(1 << 14)
#define D_KB_CODE_KEY_MIDDLE			(1 << 15)

#define D_KB_CODE_KEY_PTT				(1 << 16)
#define D_KB_CODE_KEY_USER1				(1 << 17)
#define D_KB_CODE_KEY_USER2				(1 << 18)
#define D_KB_CODE_KEY_USER3				(1 << 19)
#define D_KB_CODE_KEY_ALARM				(1 << 20)
#define D_KB_CODE_KEY_EDIT_MSG			(1 << 25)
#define D_KB_CODE_KEY_SW_MODE			(1 << 26)
#define D_KB_CODE_KEY_SCANNING			(1 << 27)
#define D_KB_CODE_KEY_3PTT				(1 << 28)
#define D_KB_CODE_KNOB_DISENTER			(1 << 30)

#define KB_CODE_KNOB_MASK				0xFF000000
#define KB_CODE_KNOB_CLOCK				(KB_CODE_KNOB_MASK | 30)
#define KB_CODE_KNOB_ANTICLOCK			(KB_CODE_KNOB_MASK | 31)


/* keyboard Finite State Machine defined---------------------------------------*/
#define COMMON_TIMER_CLK				100000
#define SECOND_TIMER_CLK				(COMMON_TIMER_CLK / 4)
#define MSECOND_TIMER_CLK				(COMMON_TIMER_CLK / 50)

#define CAL_TMR_ARR(t_ms, t_clk)		((t_ms) * (t_clk) / 1000 - 1)

#define TIMER_1S_INDEX					0
#define TIMER_1MS_INDEX					1
#define TIMER_40MS_INDEX				2
#define TIMER_MISC_INDEX				3
#define TIMER_10MS_INDEX				4

#define TIMESTAMP_POLL_PERIOD			1000
#define TIMESTAMP_MEASURE_PERIOD		1
#define KEYBOARD_POLL_PERIOD			40
#define MPT_TRUNK_STACK_POLL_PERIOD		10
#define MPT_CONV_BASE_POLL_VOICE_PERIOD	5
#define MPT_CONV_STACK_POLL_PERIOD		23
#define ZZW_INQUIRY_POLL_PERIOD			19
#define PDT_INQUIRY_POLL_PERIOD			18
#define TIMESTAMP_10MS_PERIOD			10

#define TIM4_PWM_PERIOD					99

#define FAN_SPEED_TOTAL_LEVEL			6
#define LCD_BRIGHTNESS_MAX_LEVEL		(FAN_SPEED_TOTAL_LEVEL - 1)


// attention: the FSM must increasing
#define	KB_FSM_IDLE						0
#define	KB_FSM_DETECTED					1
#define	KB_FSM_DELAY					2
#define	KB_FSM_DELAY2					3
#define	KB_FSM_AFFIRM					4
#define	KB_FSM_TIMEOUT					5
#define KB_FSM_IS_FREE					6
#define	KB_FSM_DELAY3					7
#define	KB_FSM_DELAY4					8
#define	KB_FSM_FREED					9
#define	KB_FSM_INVALID					10

#define KEY_LONGPRESS_TIMEOUT_TIMES		2

#define	KB_CONJOINED_CHECK_TIMEOUT		(1000 / 5 / KEYBOARD_POLL_PERIOD)
#define	KB_TIMEOUT_TIME					(2 * 1000 / KEYBOARD_POLL_PERIOD)
#define	KB_TIMEOUT_RPT_TIME				(1000 / 5 / KEYBOARD_POLL_PERIOD)

#define TIMESTAMP_POLL_RELOAD			9999
#define PERIODIC_POLL_RELOAD			10577

#define PTT_IS_RELEASED()				at32_button_state(PTT_BUTTON)
#define EXTPTT_IS_RELEASED()			1
#define EXTPTT2_IS_RELEASED()			1
#define ALARM_IS_RELEASED()				at32_button_state(ALARM_BUTTON)
#define HOOK_IS_RELEASED()				1
#define POWER_ONOFF_IS_RELEASED()		at32_button_state(POWER_ONOFF_BUTTON)
#define KNOB_SURE_IS_RELEASED()			at32_button_state(KNOB_SURE)

#define INT_BUTTON_NUMBER				4
#define INT_BUTTON_PTT					0
#define INT_BUTTON_2PTT					1
#define INT_BUTTON_ALARM				2
#define INT_BUTTON_HOOK					3

typedef struct
{
	uint8_t		kb_fsm;
	uint8_t		kb_attr;			// function button, numeric, up, down, and so on
	uint8_t		kb_press_flg;		// 0: run the process not yet
	uint8_t		kb_release_flg;		// 0: run the process not yet
	uint32_t	kb_value;			// the keyboard code
	uint16_t	kb_timeout;			// record the times of the kb-timer repeat
	uint16_t	kb_timeout_times;	// unit: overflow times of kb-timer
	uint16_t	kb_timeout_flg;		// 0: run the process not yet
}KB_DETECT;

typedef struct
{
	uint8_t		id;					// 0: empty timer
	uint8_t		attr;				// 0: periodic infinite; others: repeat 'attr' times and auto destroy, e.g. attr==1: use as delay
	uint16_t	interval;
	uint32_t	compval;
	void		(*timer_handler)(void);
}TIMER_TYPEDEF;


#define LR_KEY_REMAP_ENABLE			0x01
#define LR_KEY_REMAP_NOW			0x02
#define BUTTON_STATUS_MASK			0x04
#define MULTI_KEY_DETECTED			0x08
#define MULTI_KEY_CONFIRMED			0x10
#define LR_KEY_KNOB_REVERSE_FUNC	0x80

#define EAR_PHONE_CONFIRM			0x01
#define EAR_PHONE_DETECTED			0x02
#define KEYBOARD_MANUAL_CTRL_PA		0x40
#define KEYBOARD_ONLINE				0x80

#define IS_TERMINAL_GATE			0x00
#define IS_BASE_GATE				0x80

uint16_t code_to_numeric(uint32_t val);
uint8_t keyboard_check(void);
uint8_t sleep_and_keyboard_idle(void);
uint8_t is_keyboard_pressed(uint32_t *code);
void set_dev_gate(uint8_t is_base, uint8_t set_to_low);
uint8_t is_keyboard_released(uint32_t *code);
uint16_t kb_timeout_process(uint32_t *code);
uint16_t is_kb_timeout_present(void);
uint8_t keyboard_locked_pattern_check(void);
uint32_t keyboard_810c_reverse(uint32_t val);
uint8_t is_knob_turn(uint32_t *code);
void mcu_sleep_prepare(void);
void mcu_stop_prepare(void);
void mcu_sleep_resume(void);
void mcu_stop_resume(void);



/**
  * @}
  */
#ifdef __cplusplus
}
#endif

#endif /* __VICTEL_DIGITAL_KEYBOARD_H__ */

