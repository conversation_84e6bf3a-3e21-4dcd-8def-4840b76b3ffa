/**
  ******************************************************************************
  *                Copyright (c) 2013, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    18-September-2013
  * @brief   This file provides
  *            - flash layout operation
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __VICTEL_DIGITAL_FLASH_H__
#define __VICTEL_DIGITAL_FLASH_H__

#ifdef __cplusplus
 extern "C" {
#endif

#include "cheji_mobile_setup_data_location_defined.h"


#define ADDR_FLASH_SECTOR_END	((uint32_t)0x083F0000)
#define IS_WR_FLASH_PARAS_INVALID(addr, len, align, max_len)	((addr & (align - 1)) || (addr + len > max_len))

#define FLASH_SECTOR_SIZE						4096

#define FLASH_BOOTLOADER						0
#define FLASH_BOOTLOADER_MAX_LENGTH				0x10000

#define DATA_LENGTH_AT_ONE_MAINTAIN_FRAME		512
#define MAINTAIN_FRAME_TOTAL_LENGTH				(12 + DATA_LENGTH_AT_ONE_MAINTAIN_FRAME + 2)
#define MAINTAIN_RETURN_SUCCESSFUL				0x1234
#define MAINTAIN_RETURN_ERASE_FLASH_FAIL		0x0110
#define MAINTAIN_RETURN_LOST_FRAME				0x0220
#define MAINTAIN_RETURN_WRITE_FLASH_FAIL		0x0330
#define MAINTAIN_RETURN_VERIFY_DATA_FAIL		0x0440
#define MAINTAIN_RETURN_FILE_FORMAT_NOT_MATCH	0x0550
#define MAINTAIN_RETURN_UNDEFINE_COMMAND		0x0660
#define MAINTAIN_RETURN_COMMAND_NOT_MATCH		0x0770

// command defined
#define TSC_CTASK_READ_RSSI_GPS					0xff00
#define TSC_CTASK_WRITE_PARAS					0xff57
#define TSC_CTASK_READ_PARAS					0xff52
#define TSC_CTASK_READ_FLASH_ABSOLUTELY			0xff53
#define TSC_CTASK_WRITE_FLASH					0xff50
#define TSC_CTASK_READ_FLASH					0xff46
#define TSC_CTASK_REBOOT						0xff42

#define TSC_CTASK_ENTER_MAINTAIN_MODE			0xff70

#define TSC_CTASK_UPGRADE_MCU					0xff6D
#define TSC_CTASK_VERIFY_MCU					0xff6E

#define TSC_CTASK_UPGRADE_FONT_LIB				0xff66
#define TSC_CTASK_VERIFY_FONT_LIB				0xff67

#define TSC_CTASK_UPGRADE_STACK					0xff73
#define TSC_CTASK_VERIFY_STACK					0xff74

#define TSC_CTASK_UPGRADE_TIP_DSP				0xff64
#define TSC_CTASK_VERIFY_TIP_DSP				0xff65

#define TSC_CTASK_WRITE_FLASH_ABSOLUTELY		0xff61
#define TSC_CTASK_VERIFY_FLASH_ABSOLUTELY		0xff62
#define TSC_CTASK_UPGRADE_PROGRAM_ABSOLUTELY	0xff71

//////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////// menu config define //////////////////////////
#define CONF_DATA_FULL							1
#define CONF_DATA_NOT_FOUND						2
#define CONF_DATA_NOT_ENOUGH_SPACE				3

//////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////// menu config operation macro //////////////////////////
#define MENU_CONF_HEAD_SIZE						sizeof(MENU_CFG_STRUCT_HEAD)
#define MENU_CONF_DATA_RECALC(len)				((((len) + 31) & 0xffe0) / 8)
#define MENU_CONF_STRUCT_SIZE(p_struct)			(MENU_CONF_HEAD_SIZE + MENU_CONF_DATA_RECALC((p_struct)->menu_config_num))
#define MENU_CONF_STRUCT_NEXT(p_struct)			(MENU_CFG_STRUCT_HEAD *)((uint32_t)(p_struct) + MENU_CONF_STRUCT_SIZE(p_struct))
#define MENU_CONF_DATA_END_ADDR(start)			((uint32_t)(start) + MENU_CONFIG_DATA_MAX_LENGTH - MENU_CONF_HEAD_SIZE)

uint32_t get_fonts_address_flash(void);

/**
  * @}
  */
#ifdef __cplusplus
}
#endif

#endif /* __VICTEL_DIGITAL_FLASH_H__ */

