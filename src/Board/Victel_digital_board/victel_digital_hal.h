/**
  ******************************************************************************
  *                Copyright (c) 2011, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    16-November-2011
  * @brief   This file provides
  *            - scan keyboard gpio initial
  *            - scan the keyboard and return the state
  *            - encode switch gpio initial
  *            - read the code of switch
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __VICTEL_DIGITAL_HAL_H__
#define __VICTEL_DIGITAL_HAL_H__

#ifdef __cplusplus
 extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include "user_define.h"



#define MISC_CTRL_PRINT_BAR			0x01
#define MISC_CTRL_AUTOCALL			0x02
#define MISC_CTRL_LED_WARNING		0x04
#define MISC_CTRL_SOLAR_CONTROL		0x08

#define MISC_STATUS_FAN_RUNNING		0x80
#define MISC_STATUS_M1_KEY_OPERATE	0x40

#define REPLACE_WELL_INPUT_CHAR		'#'
#define THE_STAR_INPUT_CHAR			'*'


uint8_t get_decimal_bits(uint32_t decimal);
uint16_t binary_search(uint16_t *table, uint16_t max_item, uint16_t val);
void bubble_sort (uint8_t *talbe, uint8_t len);
void bubble_u16_sort(uint16_t *talbe, uint16_t len);
uint32_t string2hexadecimal(uint8_t *str, uint8_t *hex, uint32_t number);
uint32_t hexadecimal2string(uint8_t *str, uint8_t *hex, uint32_t number, uint8_t lowercase);
int32_t get_integer_from_string(uint8_t *str);
uint32_t BIG_to_U32(uint8_t *data);
uint32_t LITTLE_to_U32(uint8_t *data);

uint32_t get_8bit_difference(uint32_t new_8bit, uint32_t old_8bit);
void print_heximal(uint8_t *pre, uint8_t *dat, uint16_t len);
uint32_t string_to_bcd(uint8_t *bcd, uint8_t *str, uint32_t max_num);
uint32_t bcd_to_string(uint8_t *bcd, char *str, uint32_t bytes, uint32_t max_num);
uint32_t string2decimal(uint8_t *str);
uint32_t num_string2decimal(uint8_t *str, uint8_t num);
uint8_t decimal2string(uint32_t num, uint8_t nbits, uint8_t *string);
uint32_t input_one_decimal_with_assert(uint8_t ch, uint32_t original, uint32_t lim_l, uint32_t lim_h);
void move_one_u32_item_to_top(uint32_t *list, uint32_t n);
void delete_one_aligned_struct_block(uint32_t *addr, uint32_t block_size, uint32_t rem_block_num);
void peroidic_refresh(void);
void peroidic_refresh_admittest(void);

void set_gps_power_onoff(uint8_t onoff);
void gps_power_onoff(uint8_t onoff);
void wifi_power_onoff(uint8_t onoff);
void bt_power_onoff(uint8_t onoff);
void update_gps_bt_icon(void);
void rf_power_switch(uint8_t level);
void dsp_work_mode_switch(uint32_t mode);
void set_lcd_print_ber(uint8_t flag);
uint8_t set_lcd_print_zzw_slot(uint8_t mask, uint8_t flag);
void reset_auto_switch_rf_power(void);
void set_auto_switch_rf_power_enable(void);
uint8_t get_auto_switch_rf_power_enable_zzwpro(void);
void auto_switch_rf_power(int16_t rssi);
void update_time_force(void);
void update_time(void);
void led_warning_player(void);
void led_warning_player_mpt_conv(void);
uint8_t is_soft_reset(void);
void waiting_for_reset_or_off(uint8_t no_delay, uint8_t fail_type);
void parameters_valid_checking(uint8_t force);
void powerdown_to_switch_mode(void);
uint8_t system_power_down(uint8_t flag);
void set_stack_version_same_as_main(void);
void system_initial(void);
void send_tip_to_ui(uint8_t *tip1, uint8_t *tip2);
void call_ui_to_do_something(uint8_t reason);
void put_info_to_ui(uint8_t reason, uint32_t info);
void send_dport_to_ui(uint8_t *data, uint16_t bytes, uint32_t address);
uint32_t poc_get_alaw_to_play(void);
void poc_send_mic_voice(void);


/////////////// define at victel_digital_flash.c ///////////////////////
uint32_t flash_address_remap_misc(uint32_t addr, uint32_t len);
uint32_t flash_address_remap(uint32_t addr, uint32_t len);
uint16_t ntohs(uint16_t s);
uint32_t ntohl(uint32_t l);
void ntohs_mul(uint16_t *s, uint16_t num);
uint32_t convert_bit_stream(uint8_t bits, uint32_t dat);
uint32_t combine_short_to_long(uint16_t s0, uint16_t s1);
uint16_t combine_char_to_short(uint8_t c0, uint8_t c1);
uint32_t get_user_name_address(void);
void get_device_type_address(uint8_t *str);
void get_device_band_range(uint8_t *str);
void get_local_calling_name(uint8_t *info);
uint32_t get_factory_data_address(void);
uint32_t get_tune_table_address(void);
uint32_t get_esn_from_flash(uint32_t obj_address);
uint8_t get_work_mode_setup_from_flash(void);
uint32_t get_address_books_address(void);
uint32_t get_pdt_stack_address(void);
uint32_t get_zzwx_stack_address(void);
uint8_t check_tune_table_is_valid(uint8_t *table);
void copy_system_parameters(void);
uint32_t memcpy_compare(uint32_t *ram, uint32_t *flash, uint32_t len);
void init_system_parameters(void);
uint32_t erase_some_sectors(uint32_t sector_start, uint32_t sector_num);
uint32_t program_data_to_nor_flash(uint32_t obj_addr, uint32_t *dat_addr, uint32_t length, uint8_t erase_first, uint8_t locked);
uint32_t program_one_sector_to_spi_flash(uint32_t addr, uint32_t *data, uint32_t length);
uint8_t set_username_id(uint32_t *save_id, uint8_t *save_name, uint8_t *username_id);
uint32_t save_static_data_to_flash(void *data, void *data2, uint8_t flag);
uint32_t save_user_data_to_flash(uint8_t flag);
uint32_t get_menu_config_data_length(void);
uint32_t get_menu_config(uint32_t menu_flag, uint32_t menu_max_num, uint16_t *num);
uint16_t menu_config_data_set(uint32_t menu_flag, uint32_t *val, uint16_t num);


/////////////// define at victel_digital_spi_flash.c ///////////////////////
uint8_t get_spi_flash_status(void);
uint8_t flash_spi_init(void);
void fpga_spi_init(void);
uint32_t fpga_spi_tr_word(uint8_t cmd, uint32_t data);
uint32_t get_dsp_2mode_adhoc_image_address(void);
uint32_t get_dsp_64xx_image_address(void);
uint32_t get_dsp_image_address(void);
uint32_t get_dsp_image_nvoc_address(void);
uint32_t get_power_on_logo_address(void);
uint32_t get_qrcode_address(void);
uint32_t flash_spi_erase_data(uint32_t addr, uint32_t len);
uint32_t flash_spi_read_data(uint8_t *buf, uint32_t addr, uint32_t len);
uint32_t flash_spi_write_data(uint8_t *buf, uint32_t addr, uint32_t len);
uint32_t flash_spi_write_one_byte(uint8_t *buf, uint32_t addr);

/////////////// define at victel_digital_usart.c ///////////////////////
void config_real_bt_process(void);
void get_real_new_bt_info(uint32_t *dev_list, uint8_t *dev_num, uint8_t *dev_fsm);
void send_upgrade_cmd_to_real_new_bt(void);
void set_real_new_bt_todo_something(uint8_t cmd, uint8_t para);
void config_real_new_bt_process(void);
void set_real_new_bt_to_own_calling(uint8_t flag);
void power_real_bt(uint8_t on_off);
void usart_deinit(uint8_t uart_index);
void debug_txdma_start(uint8_t *data, uint32_t len);
void process_gps_dma_data(uint8_t *gps_data, uint8_t num);
void bt_txdma_start(uint8_t *data, uint32_t len);
void send_xv_wireless_frame_to_host(uint8_t *data, uint16_t len, uint32_t id_caller, uint32_t id_be_called);
void send_bt_frame_to_host(uint8_t *data, uint16_t len, uint16_t ctask, uint16_t bytes, uint32_t addr);
void send_bt_frame_to_host_auto_sel(uint8_t *data, uint16_t len, uint16_t ctask, uint16_t bytes, uint32_t addr);
void send_remote_gps_info_to_host(uint8_t *air_data);
void set_bt_voice_receive_time(void);
uint8_t is_rcv_bt_voice_frame(void);
uint8_t process_bt_frame_now(uint8_t *frame);
void process_bt_dma_data(uint8_t *bt_data, uint8_t num);
uint8_t get_uart_redirection(uint8_t uart_io);
void set_printf_redirection(uint8_t uart_index);					// index: see the define at global_define.h, ITM or USART3 only now
int put_data(uint8_t uart_index, uint8_t *dat, uint16_t len);		// index: see the define at global_define.h
int getch_noblock(uint8_t uart_index, uint8_t *ch);
int putch_noblock(uint8_t uart_index, uint8_t ch);
void debug_printf(char *str, ...);
void set_prompt(uint8_t flag);
void print_prompt(void);
uint8_t maintain_line_poll(void);
void set_gps_link_state(uint8_t new_state);
void gps_float_to_deg_int_dec(GPS_STRUCT_TYPEDEF *obj, double lon, double lat);
uint32_t cal_2points_distance(GPS_STRUCT_TYPEDEF *a, GPS_STRUCT_TYPEDEF *b, uint8_t *str);
void init_unify_utc_regulator(void);
uint8_t get_local_hour_with_utc(uint8_t utc_hour);
uint8_t get_weekday_with_date(uint8_t year, uint8_t month, uint8_t day);
uint8_t get_satellite_number(void);
void set_gps_data_output(uint8_t flag);
void print_gps_parse_data(uint8_t *data);
void update_gps_work_mode(void);
uint8_t is_gps_rmc_locked(void);
uint8_t is_gps_gga_locked(void);
void process_gps_data(void);
void set_real_bt_config(uint8_t flag);
void get_bt_mac_string(uint8_t *bt_mac_addr);
void process_bt_data(void);
void check_gps_is_alive(void);
uint8_t is_gps_alive(void);
void null_void_void(void);
void null_void_u8(uint8_t para8);
void null_void_u8_2(uint8_t para8);
void null_void_u16(uint16_t para16);
void __enable_irq(void);
void __disable_irq(void);
void autoset_gps_module_baudrate(uint8_t flag);
void set_module_init_done_flag(uint8_t flag);
uint8_t get_module_init_done_flag(uint8_t flag);
void toggle_print_adv_state(void);
void toggle_print_dqi_state(void);
void toggle_print_fpga_state(void);
void toggle_print_can_state(void);
uint8_t toggle_print_slot_detail(uint8_t on_off);
void fan_self_test(void);
uint8_t fpga_read_all_regs(uint32_t buf_addr);
void print_fpga_info(void);
void toggle_auto_calling(uint16_t gap, uint16_t voice_time, uint8_t ctrl);
uint8_t is_auto_call_enable(void);
uint8_t is_solar_ctrl_enable(void);
void switch_ctrl_init(uint8_t init);
int init_bt_serial_thread(void);


/////////////// define at victel_digital_lcd.c ////////////////////
uint8_t *get_main_screen_icon_start(void);


/////////////// define at victel_digital_lcd_spi_dma.c ////////////////////
void set_lcd_refresh_continuous(uint8_t en);

/////////////// define at victel_digital_keyboard.c ////////////////////
void init_lcd_power_ctrl_line(void);
void led_play(uint8_t flag);
uint8_t is_ptt_pressed(void);
uint8_t is_reverse_knob_clock_lr_key(void);
void lr_key_remap_ctrl(uint8_t enable);
uint8_t is_bt_insert(void);
uint8_t is_kb_knob_enter_disable(void);
uint8_t keyboard_val2code(uint32_t key);
uint8_t keyboard_get_one_input(void);
uint8_t is_power_off_key_pressed(void);
uint8_t is_ptt_key_pressed(void);
uint8_t is_alarm_key_pressed(void);
void dsp_interrupt_config(uint8_t flag);
uint16_t get_setup_ptt_event_with_ptt_key(void);
uint8_t zzwprobase_send_canframe_to_stack_immediately(void);
void set_timer12_handler(uint32_t func);
uint8_t mpt_base_is_loopback(void);
uint8_t audio_pa_manual_config(uint8_t flag);
void audio_pa_ctrl_process(uint8_t flag);
void kb_timer_init(void);
uint32_t get_timestamp_measure(void);
uint32_t get_timestamp_measure_format(void);
uint32_t get_measure_timer_difference(uint32_t new_16bit, uint32_t old_16bit);
uint32_t get_timestamp_40us_measure(void);
uint32_t get_40us_measure_timer_difference(uint32_t new_16bit, uint32_t old_16bit);
void timer9to14_start(uint8_t tim);
void timer9to14_stop(uint8_t tim);
uint8_t is_timer12_running(void);
void timer9to14_set_timeout_immediately(uint8_t tim);
uint8_t is_dev_control_fans(void);
void test_custom_delay(void);
void start_measure_timer(void);
void stop_measure_timer(void);
void crystal_measure_init(void);
uint32_t set_base_fan_speed(uint8_t ch, uint8_t speed);
void pwm_output_init(void);
uint8_t peroidic_refresh_check(void);

void matrix_keyboard_init(void);
uint32_t get_matrix_keyboard_scan(void);
void print_matrix_keyboard_state(void);
uint32_t matrix_keyboard_scanning(void);



/////////////// define at victel_digital_rtc.c ///////////////////////
void mcu_enter_sleep_mode(void);
void mcu_enter_stop_mode(uint8_t flag);
void rtc_init(void);
void printf_time(void);
void rtc_date_regular(uint8_t year, uint8_t month, uint8_t day, uint8_t weekday);
void rtc_time_regular(uint8_t hour, uint8_t min, uint8_t second);
void RTC_GetTD(uint8_t *year, uint8_t *month, uint8_t *day, uint8_t *weekday, uint8_t *hour, uint8_t *minute, uint8_t *second);
uint32_t compress_rtc_to_word (void);


/////////////// define at victel_digital_adc_dma.c ///////////////////////
void adc_init(void);
uint32_t get_battery_voltage(void);
uint16_t get_real_battery_voltage(void);
#define GET_MODULE_TEMP_ADJUST(temp)	(int8_t)(temp + 0.5f)
uint16_t *get_temp_rf_power_voltage(void);
void cal_temp(float *temp_mv, float *temp_res, float *temp);
int8_t get_module_temp(void);
uint16_t cal_801_fp_manual_init(uint8_t watt);
void average_power_check_adc(uint8_t slot);
uint8_t is_high_rp_protect(void);
void high_rp_protect_process(void);
void average_power_adc(uint8_t total_slot);
uint8_t cal_801_fp(uint16_t fp_adc);
uint8_t cal_801_rp(uint16_t rp_adc);
uint16_t convert_rp_watt(uint8_t rp_watt);
uint32_t get_vbat_voltage(void);
uint8_t get_battery_power_level(void);
uint8_t is_battery_no_power(void);
void battery_login_sound_warning(void);


/////////////// define at victel_digital_local_can.c ///////////////////////
void CAN_Config(uint8_t flag);


/////////////// define at victel_digital_dsp.c ///////////////////////
void check_dsp_configure(uint8_t must_reset_dsp);
void dsp_slot_middle_interrupt(void);
void mpt_base_set_loopback_mode(uint8_t flag);
void init_mpt_conv_pointer(void);
void dsp_slot_middle_xv_check_ccm_data(void);
void print_crystal(void);


/////////////// define at selp_interface.c ///////////////////////
void vocoder_initial(void);
void vocoder_working_process(void);


/////////////// define at victel_digital_spi_arm.c ///////////////////////
uint8_t voice_is_mute(void);
uint8_t tip_is_mute(void);
void mcu_inf_rx_dma_reinit(void);
void packet_data_to_mcu_inf_frame(uint8_t *dat, uint16_t len);
void packet_ctrl_to_mcu_inf_frame(e_baseband_control_type_t cmd, uint32_t para1, uint32_t para2);
void packet_dsp_tune_to_mcu_inf_frame(uint8_t cmd, uint8_t op_type, uint32_t *paras);
void save_mic_voice(void *mic_voice, uint8_t len);
void mcu_inf_rcv_frame(void);
int16_t *get_mic_voice_buffer(void);
void save_mic_encode_data(uint8_t *enc_data);
void get_mic_encode_data(uint8_t *mic_encode);
void save_spk_codeword(uint8_t *spk_codeword);
uint8_t *get_spk_codeword_buffer(void);
uint8_t get_air_data_buffer(uint8_t *air_data);
int8_t set_speaker_volume(uint8_t flag, int8_t vol);
int8_t set_mic_voice_gain(int8_t vol);
void packet_spk_voice_to_mcu_inf_frame(uint8_t flag, int16_t *spk_voice);
void packet_signaling_to_mcu_inf_frame(uint8_t *signal);
void reset_mic_voice_paras(void);
void clear_ptt_voice_flag(void);
void init_mcu_inf_interface(void);
uint8_t let_dsp_todo_something(uint8_t gui_call_flag, uint8_t cmd);
uint8_t dsp_tune_process(uint8_t gui_call_flag, uint8_t cmd, uint32_t *paras);
uint8_t dsp_op_paras_process(uint8_t gui_call_flag, uint8_t cmd, uint32_t save_addr);


/////////////// define at xv_base_hal.c ///////////////////////
uint32_t set_xvbase_network_address(uint32_t addr);
uint32_t get_xvbase_network_address(void);
uint8_t set_xvbase_canid(uint8_t canid);
uint8_t set_xvbase_dispatchid(uint32_t id);
void send_exchange_info(uint8_t *info_start);
#ifndef MAIN_DONOT_PROCESS_VO_MODE
void get_exchange_info(uint8_t *info_start);
#endif
uint8_t get_xvbase_canid(void);
uint32_t write_to_fpga_tath(uint32_t tath);
void write_to_fpga_gcr_60ms_source(void);
uint32_t xvbase_force_check_and_60ms_source(void);
void xvbase_set_fpga(void);
void write_to_fpga_gcr_sync_sel(uint8_t work_mode, uint8_t vocoder_q);
void set_xvbase_address(void);
uint16_t get_xvbase_rx_freq(void);
uint16_t get_xvbase_tx_freq(void);
uint16_t get_xvbase_tx2_freq(void);
void set_xvbase_tr_freq(uint16_t tx, uint16_t rx);
void update_xvbase_freq(void);
void update_xvbase_freq(void);
void update_xvbase_freq_imme(uint8_t which);
void request_panel_do_something(uint8_t reset);
void set_module_save_power_status_to_can(uint8_t flag);
void send_module_status_to_can(void);
void send_channel_status_to_can(uint32_t dat, uint32_t dat_ex);
void send_channel_status_to_can_not_base(void);
uint8_t copy_channel_status_to_can_frame(uint32_t dat, uint32_t dat_ex);
uint8_t *get_channel_status_fr_can_frame(void);
void send_mpt_voice_to_can(uint8_t *mpt_voice, uint8_t n);
void send_signaling_to_can(uint32_t *aux1, uint32_t *aux2, uint32_t buff);
uint8_t get_signaling_fr_can(uint32_t buff);
void set_base_freq_common(uint8_t force, uint16_t rx, uint16_t tx);
void LBUS_ModuleReplyTM_Probe(uint32_t id, uint8_t *data, uint8_t op_code);
void LBUS_ModuleReplyTM_Frame(uint32_t id, uint8_t *data, uint8_t len);
void AIR_TMtoModule_Handle(uint32_t id, uint8_t *data, uint8_t len);
void save_bt_data_to_can_buffer(uint8_t *data);
void LBusRxFrame_Handle(void);
void debug_info_redirect_can(uint8_t *dat, uint16_t len, uint8_t mode);
void send_transcode_buffer_to_can(uint8_t *data, uint8_t style, uint16_t len);
void set_can_control_valid(void);
uint8_t send_to_can_is_valid(void);
uint8_t *get_can_vocoder_address(void);
void send_bt_frame_to_host_from_can(uint8_t *data, uint16_t len, uint16_t ctask, uint16_t bytes, uint32_t addr);
void send_bt_data_to_host_from_can(uint8_t *data, uint16_t len, uint16_t ctask, uint16_t bytes, uint32_t addr);
void can_stress_test_setup(uint8_t can_frame_num, uint8_t can_frame_gap, uint8_t can_frame_times);
void can_stress_test_process(void);


/////////////// define at dsp_config.c ///////////////////////
void modify_dsp_rx_int_ex(uint16_t rx_int);
uint16_t get_xvbase_ex_rx_freq(void);
uint16_t config_pll_paras_base_ex_rx(void);
uint16_t config_pll_paras_base(uint8_t work_mode, uint8_t flag, uint16_t rx_int, uint16_t tx_int);

void set_dsp_30ms_counter(uint8_t flag);
//uint8_t is_dsp_save_power_enable(void);
void set_dsp_save_power_mode_absolutely(uint8_t flag);
void dsp_save_power_init(uint8_t flag);
uint8_t set_dsp_save_power_paras(uint16_t wake, uint16_t sleep, uint16_t cont);
void stack_force_set_savepower(uint8_t no_savepower);
uint8_t temp_to_set_dsp_save_power(uint16_t wake, uint16_t sleep, uint16_t cont);
void set_dsp_temporary_to_normal(uint8_t flag);
void set_dsp_exit_to_normal_abs(uint16_t time_slot);
void print_dsp_save_power_paras(void);

/////////////// define at victel_digital_sdio_sd.c ///////////////////////
void printf_512B_buffer(uint8_t *buf);
uint8_t test_erase_some_blocks(void);
void test_read_one_block(uint8_t *buf, uint32_t addr);
void test_write_one_block(uint8_t *buf_wr);
int SmartCard_Init(char drive);
int SmartCard_SendAPDU(uint8_t *APDU, int Size);
int SmartCard_GetAPDU(uint8_t *APDU, int *Size);
void SmartCard_GetAPDU_ABS(uint8_t *data, int n);
uint8_t smartcard_is_online(void);
uint8_t sd_can_be_crypto(void);
uint8_t is_enable_aes256_crypto(void);
uint8_t crypto_is_aes_mode(void);
uint8_t crypto_is_enable(void);
void sd_encrypt_setup_by_user(void);
uint8_t sd_encrypt_if_apdu_c1(uint8_t *p_auth, uint8_t test);
uint8_t sd_calling_release(uint8_t is_own_calling);
uint8_t sd_calling_setup(uint32_t obj_id, uint32_t self_id, uint8_t call_type, uint8_t is_own_calling);
void sd_voice_encrypt(uint8_t *voice);
uint8_t sd_voice_decrypt(uint8_t *voice, uint8_t aes_key_index);
uint8_t sd_data_encrypt(uint32_t obj_id, uint32_t self_id, uint8_t call_type, uint16_t len, uint8_t *data);
uint8_t sd_data_decrypt(uint32_t obj_id, uint32_t self_id, uint8_t call_type, uint16_t len, uint8_t *data);
void encrypt_initial(uint8_t flag);						// cipher_card_interface.c
void get_aes_key_number(void);							// aes_key256.c.c
uint8_t get_aes_key_total(void);						// aes_key256.c.c
void aes256_switch_key_index(uint8_t index);			// aes_key256.c.c



/////////////// define at pcm_interconvert_alaw.c ///////////////////////
void linear_to_alaw(short *sample, unsigned char *alaw, int len);
void alaw_to_linear(unsigned char *alaw, short *sample, int len);



/////////////// define at at32f435_437_clock.c ///////////////////////
void system_clock_config(uint8_t fast);



/////////////// define at aipl_demo.c ///////////////////////
int aipl_pa_ctrl(uint8_t type, uint8_t flag);

/**
  * @}
  */
#ifdef __cplusplus
}
#endif

#endif /* __VICTEL_DIGITAL_HAL_H__ */
