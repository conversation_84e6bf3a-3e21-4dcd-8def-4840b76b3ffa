/**
  **************************************************************************
  * @file     at32f435_437_board.c
  * @brief    set of firmware functions to manage leds and push-button.
  *           initialize delay function.
  **************************************************************************
  *                       Copyright notice & Disclaimer
  *
  * The software Board Support Package (BSP) that is made available to
  * download from Artery official website is the copyrighted work of Artery.
  * Artery authorizes customers to use, copy, and distribute the BSP
  * software and its related documentation for the purpose of design and
  * development in conjunction with Artery microcontrollers. Use of the
  * software is governed by this copyright notice and the following disclaimer.
  *
  * THIS SOFTWARE IS PROVIDED ON "AS IS" BASIS WITHOUT WARRANTIES,
  * <PERSON><PERSON><PERSON><PERSON><PERSON>ES OR REPRESENTATIONS OF ANY KIND. ARTERY EXPRESSLY DISCLAIMS,
  * TO THE FULLEST EXTENT PERMITTED BY LAW, ALL EXPRESS, IMPLIED OR
  * STATUTORY OR OTHER WARRANTIES, GUARANTEES OR REPRESENTATIONS,
  * INCLUDING BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY,
  * FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT.
  *
  **************************************************************************
  */

#include "victel_digital_board.h"
#include "victel_digital_usart.h"
#include "global_define.h"
#include "drv.h"

// 全局变量定义
int g_bt_serial_fd = -1;
int g_gps_serial_fd = -1;
int g_debug_serial_fd = -1;

/** @addtogroup AT32F435_437_board
  * @{
  */

/** @defgroup BOARD
  * @brief onboard periph driver
  * @{
  */

/* at-start led resouce array */

void delay_init(void)
{
}

void sys_udelay(uint16_t us)
{
}

void sys_delay(uint16_t ms)
{
}

void sys_sdelay(uint16_t sec)
{
	uint16_t index;

	for(index = 0; index < sec; index++)
	{
		sys_delay(500);
		sys_delay(500);
	}
}

#if 0

/* support printf function, usemicrolib is unnecessary */
#if (__ARMCC_VERSION > 6000000)
  __asm (".global __use_no_semihosting\n\t");
  void _sys_exit(int x)
  {
    x = x;
  }
  /* __use_no_semihosting was requested, but _ttywrch was */
  void _ttywrch(int ch)
  {
    ch = ch;
  }
  FILE __stdout;
#else
 #ifdef __CC_ARM
  #pragma import(__use_no_semihosting)
  struct __FILE
  {
    int handle;
  };
  FILE __stdout;
  void _sys_exit(int x)
  {
    x = x;
  }
  /* __use_no_semihosting was requested, but _ttywrch was */
  void _ttywrch(int ch)
  {
    ch = ch;
  }
 #endif
#endif

#if defined (__GNUC__) && !defined (__clang__)
  #define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
  #define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

/**
  * @brief  retargets the c library printf function to the usart.
  * @param  none
  * @retval none
  */
PUTCHAR_PROTOTYPE
{
  while(usart_flag_get(PRINT_UART, USART_TDBE_FLAG) == RESET);
  usart_data_transmit(PRINT_UART, ch);
  return ch;
}

#if defined (__GNUC__) && !defined (__clang__)
int _write(int fd, char *pbuffer, int size)
{
  for(int i = 0; i < size; i ++)
  {
    __io_putchar(*pbuffer++);
  }

  return size;
}
#endif

#endif // #if 0

/**
  * @brief  config usart
  * @param  none
  * @retval none
  */
void usart_deinit(uint8_t com)
{
    int fd = -1;

    if (com == UART_BT_INDEX) {
        fd = g_bt_serial_fd;
        g_bt_serial_fd = -1;
    } else if (com == UART_GPS_INDEX) {
        fd = g_gps_serial_fd;
        g_gps_serial_fd = -1;
    } else if (com == UART_DEBUG_INDEX) {
        fd = g_debug_serial_fd;
        g_debug_serial_fd = -1;
    }

    if (fd >= 0) {
        close(fd);
    }
}

//void usart_configuration(uint8_t com, uint32_t baud_rate, usart_data_bit_num_type data_bit, usart_stop_bit_num_type stop_bit, uint8_t dma_mode)
void usart_configuration(uint8_t com, uint32_t baud_rate, uint8_t dma_mode)	// dma_mode: bit0-tx, bit1-rx
{
    char com_path[20];
    int fd;

    switch(com) {
        case UART_BT_INDEX:
            strcpy(com_path, BLUETOOTH_PATH);
            break;
        case UART_GPS_INDEX:
            strcpy(com_path, GPS_PATH);
            break;
        case UART_DEBUG_INDEX:
            strcpy(com_path, SERIAL_PATH(S0));
            break;
        default:
            return;
    }

    fd = open_port(com_path);
    if (fd < 0) {
        return;
    }
    vlog_i("usart", "open %s fd=%d", com_path, fd);

    if (set_opt(fd, baud_rate, 8, 1, 'n') < 0) {
        close(fd);
        return;
    }
    vlog_i("usart", "set_opt %s fd=%d, baud_rate=%d", com_path, fd, baud_rate);

    if (com == UART_BT_INDEX) {
        g_bt_serial_fd = fd;
    } else if (com == UART_GPS_INDEX) {
        g_gps_serial_fd = fd;
    } else if (com == UART_DEBUG_INDEX) {
        g_debug_serial_fd = fd;
    }
}

void usart_set_baudrate(uint8_t com, uint32_t baud_rate)
{
  int fd;

  if (com == UART_BT_INDEX) {
    fd = g_bt_serial_fd;
  } else if (com == UART_GPS_INDEX) {
    fd = g_gps_serial_fd;
  } else if (com == UART_DEBUG_INDEX) {
    fd = g_debug_serial_fd;
  }else
    return;

  if(set_baud(fd, baud_rate) < 0){
    return;
  }
  vlog_i("usart", "set_baud %s fd=%d, baud_rate=%d", com == UART_BT_INDEX ? "BT" : (com == UART_GPS_INDEX ? "GPS" : "DEBUG"), fd, baud_rate);
}

void set_button_interrupt(button_type Button, uint8_t enable)
{
}

/**
  * @brief  configure button gpio
  * @param  button: specifies the button to be configured.
  * @retval none
  */
void at32_button_init(button_type button)
{
}

/**
  * @brief  returns the selected button state
  * @param  none
  * @retval the button gpio pin value
  */
uint8_t at32_button_state(button_type button)
{
	return 1;
}

/**
  * @brief  returns which button have press down
  * @param  none
  * @retval the button have press down
  */

/**
  * @brief  configure led gpio
  * @param  led: specifies the led to be configured.
  * @retval none
  */
void at32_led_init(led_type led)
{
}

void at32_pins_init(uint32_t pins)
{
}

/**
  * @brief  turns selected led on.
  * @param  led: specifies the led to be set on.
  *   this parameter can be one of following parameters:
  *     @arg LED2
  *     @arg LED3
  *     @arg LED4
  * @retval none
  */
void set_gpio_high(led_type led)
{
}

void set_gpio_low(led_type led)
{
}

void set_gpio_toggle(led_type led)
{
}

/**
  * @}
  */

/**
  * @}
  */
