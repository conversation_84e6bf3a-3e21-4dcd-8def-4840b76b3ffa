/**
  ******************************************************************************
  *                Copyright (c) 2011, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    07-December-2011
  * @brief   This file provides
  *            - FSMC(NE2) initial
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __VICTEL_DIGITAL_USART_H__
#define __VICTEL_DIGITAL_USART_H__

#ifdef __cplusplus
 extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/

#include <stdio.h>
#include <stdint.h>
#include "global_define.h"
#include "victel_digital_local_can.h"

/** @addtogroup Utilities
  * @{
  */

/** @addtogroup STM32_EVAL
  * @{
  */

/** @addtogroup STM3210E_EVAL
  * @{
  */

#define GPS_STATE_LINKED_CONFIRM			3


#define TSC_CTASK_SET_RF_POWER				0xfc70
#define TSC_CTASK_GET_RF_POWER				0xfa70

#define TSC_CTASK_SWITCH_WORK_MODE			0xfc6d
#define TSC_CTASK_GET_WORK_MODE				0xfa6d

#define TSC_CTASK_SELECT_USER_AREA			0xfc61
#define TSC_CTASK_GET_USER_AREA				0xfa61

#define TSC_CTASK_SELECT_CTRL_AREA			0xfc41
#define TSC_CTASK_GET_CTRL_AREA				0xfa41

#define TSC_CTASK_GET_USER_AREA_NAME		0xfa6e
#define TSC_CTASK_GET_CTRL_AREA_NAME		0xfa4e

#define TSC_CTASK_SET_VOLUME				0xfc76
#define TSC_CTASK_GET_VOLUME				0xfa76

#define TSC_CTASK_SCAN_CHANNEL				0xfc73
#define TSC_CTASK_GET_SCAN_CHANNEL			0xfa73

#define TSC_CTASK_SET_GPS					0xfc67
#define TSC_CTASK_GET_GPS					0xfa67

#define TSC_CTASK_SET_VOCODER				0xfc63
#define TSC_CTASK_GET_VOCODER				0xfa63

#define TSC_CTASK_KEY_OPERATE				0xfc50

#define TSC_CTASK_MSG_SEND					0xfc4d

#define TSC_CTASK_MSG_READ					0xfa4d
  #define BT_MSG_DELETE_OPCODE				0x1234

#define TSC_CTASK_DEV_STATUS				0xfa53
#define TSC_CTASK_DEV_REQUEST_RESET_GPS		0xfa54
#define TSC_CTASK_DEV_REQUEST_ALARM			0xfa55

#define TSC_CTASK_STACK_STATUS				0xfb53
#define TSC_CTASK_GET_REL_DISTANCE			0xfb54

#define TSC_CTASK_SEND_VOICE				0xfc56
#define TSC_CTASK_SEND_FAKE_VOICE			0xfd56
#define TSC_CTASK_SEND_VOCODER				0xfc86

#define TSC_CTASK_READ_DATA					0xfa64

#define TSC_CTASK_GET_USER_NAME				0xfa75

#define TSC_CTASK_SWITCH_WATCH				0xfc53

#define TSC_CTASK_SEND_SIGNALING			0xfc55
#define TSC_CTASK_SEND_PLAY_VOICE			0xfc90

#define TSC_CTASK_SEND_XV_WIRELESS			0xfc57

#define TSC_CTASK_SETUP_MEETING				0xfc66

#define TSC_CTASK_REMOTE_GPS_INFO			0xfc47

#define TSC_CTASK_SET_BT_DISABLE			0xfc62

#define TSC_CTASK_HOST_INQUIRE_DATA			0xfd67
#define TSC_CTASK_SEND_DATA_TO_HOST			0xfd68

#define TSC_CTASK_SEND_DATA_IAP				0xfc49
#define TSC_CTASK_SEND_DATA_OTA				0xfc4f

// POC defined
#define TSC_CTASK_PC_VER					0x5701
#define TSC_CTASK_PC_SET					0x5702
#define TSC_CTASK_PC_GET					0x5703
#define TSC_CTASK_PC_OTA					0x5710


#define SET_BT_TOTAL_TYPE					5
#define SET_BT_STOP_MIC						0
#define SET_BT_START_MIC					1
#define SET_BT_STOP_PLAY					2
#define SET_BT_START_PLAY					3
#define SET_BT_FAKE_STOP					4

#define TSC_CTASK_NEW_BT_UNI_CMD			0xfb00
  #define TSC_CTASK_NEW_BT_ADDR_GET_MAC		0
  #define TSC_CTASK_NEW_BT_ADDR_SET_TO_CALL	1
  #define TSC_CTASK_NEW_BT_ADDR_READ_SCAN	2
  #define TSC_CTASK_NEW_BT_ADDR_CONNECT		3
  #define TSC_CTASK_NEW_BT_ADDR_LINK_BACK	4
  #define TSC_CTASK_NEW_BT_ADDR_DISCONNECT	5
  #define TSC_CTASK_NEW_BT_ADDR_VOICE		6
  #define TSC_CTASK_NEW_BT_ADDR_PTT			7
  #define TSC_CTASK_NEW_BT_ADDR_UPGRADE		8


#define BT_DMA_DATA_PREFIX1					0x17
#define BT_DMA_DATA_PREFIX2					0xEA
#define BT_DMA_DATA_PREFIX3					0x18
#define BT_DMA_DATA_PREFIX4					0xEB
#define BT_DMA_DATA_PREFIX					(((uint32_t)BT_DMA_DATA_PREFIX4<<24)|((uint32_t)BT_DMA_DATA_PREFIX3<<16)|((uint32_t)BT_DMA_DATA_PREFIX2<<8)|(uint32_t)BT_DMA_DATA_PREFIX1)
#define BT_DMA_DATA_EPILOG					0xEC19
#define BT_DMA_DATA_CAN_PREFIX				0xEC20EC20
#define BT_DMA_DATA_DLEN					80
#define BT_DMA_DATA_CAN_DLEN				40
typedef struct
{
	uint32_t prefix;					// BT_DMA_DATA_PREFIX
	uint16_t ctask;
	uint16_t bytes;
	uint32_t addr;
	uint8_t  data[BT_DMA_DATA_DLEN + 2];// 80B data, 2B checksum
	uint16_t epilog;					// BT_DMA_DATA_EPILOG
} BT_DMA_DATA_TYPEDEF;

#define GET_INDEX_CAN_READ(bt_voice)			(bt_voice.which_buf_to_write ? 0 : 1)
#define GET_INDEX_WRITE_NOW(bt_voice)			bt_voice.which_buf_to_write
#define SWITCH_INDEX_TO_WRITE(bt_voice)			bt_voice.which_buf_to_write = bt_voice.which_buf_to_write ? 0 : 1

#define IS_BUFFERn_OK(bt_voice, which)			(bt_voice.buffer_is_ok & (which ? 0x02 : 0x01))
#define SET_BUFFERn_OK_FLAG(bt_voice, which)	bt_voice.buffer_is_ok |= which ? 0x02 : 0x01
#define CLR_BUFFERn_OK_FLAG(bt_voice, which)	bt_voice.buffer_is_ok &= ~(which ? 0x02 : 0x01)

#define REG_SPEECH_IN_LENGTH					480
#define REG_SPEECH_LENGTH_60MS					REG_SPEECH_IN_LENGTH
#define REG_SPEECH_LENGTH_180MS					(REG_SPEECH_LENGTH_60MS * 3)

typedef struct
{
	uint8_t which_buf_to_write;
	uint8_t buffer_is_ok;			// bit8:1-encoding now; bit7:1-decoding now; bit1:1-buffer 1 is OK; bit0:1-buffer 0 is OK
	uint16_t index_to_write;
	uint8_t a_law_voice[2][REG_SPEECH_LENGTH_60MS];
}VOICE_FROM_BT_TYPEDEF;

#define CODEC_RX_BUFFER_SIZE						40/* 8KHz:5ms=40W,A law=40B; defined at local_can.h */
typedef struct
{
	uint8_t which_buf_to_write;
	uint8_t buffer_is_ok;			// bit8:1-encoding now; bit7:1-decoding now; bit1:1-buffer 1 is OK; bit0:1-buffer 0 is OK
	uint8_t index_to_write;
	uint8_t timeout;
	uint8_t vocoder_voice[2][CODEC_RX_BUFFER_SIZE * 2/*SIGNALING_XV_MAX_LENGTH*/];	// to avoid data overflow
}VOCODER_FROM_BT_TYPEDEF;



#define USART_BUFFER_LENGTH				256
#define	CMD_MAX_ARGS					6

#define GPS_BUFFER_LENGTH				256
#define BT_BUFFER_LENGTH				256

#define	CMD_ARG_OK						1
#define	CMD_ARG_ERROR					0

#define	IS_DIGITAL						0x30
#define	IS_UPPER						0x37
#define	IS_LOWER						0x57

#define	putstr(pStr)

#define	MAX_GPS_ARGS					20

// bit1-0:channel status(0-disable,1-once,2-infinite); bit2:uart debug;bit3-stack dump
#define DUMP_TO_CAN_TR_STATUS			0x03
#define DUMP_TO_CAN_UART_DEBUG			0x04
#define DUMP_TO_CAN_STACK_DUMP			0x08
#define DUMP_TO_CAN_TR_SIGNAL			0x10

/** @defgroup STM3210E_EVAL_LCD_Exported_Functions
  * @{
  */

void process_command_line(void);

void timer9to14_initial(uint8_t tim, uint8_t tim_irq_pri, uint32_t t_ms, uint8_t en);
void timer9to14_start(uint8_t tim);
void timer9to14_stop(uint8_t tim);
void usart_dma_configuration(uint8_t com, uint8_t dma_mode);



uint8_t get_gps_link_state(void);
uint8_t get_bt_link_state(void);

// GPS自动波特率检测函数声明
void autoset_gps_module_baudrate(uint8_t flag);
void autoset_gps_module_baudrate_handle(void);
uint32_t switch_gps_uart_baudrate(void);
void check_gps_is_alive(void);
uint8_t is_gps_alive(void);

#ifdef __linux__
// Linux适配：GPS清理函数
void gps_autoset_cleanup(void);
#endif
/** @defgroup
  * @{
  */

/**
  * @}
  */

/**
  * @}
  */
#ifdef __cplusplus
}
#endif

#endif /* __VICTEL_DIGITAL_USART_H__ */
/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/******************* (C) COPYRIGHT 2011 STMicroelectronics *****END OF FILE****/
