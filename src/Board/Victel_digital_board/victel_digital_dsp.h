/**
  ******************************************************************************
  *                Copyright (c) 2011, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    21-December-2012
  * @brief   This file provides
  *            - FSMC(NE2) initial
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __VICTEL_DIGITAL_DSP_H__
#define __VICTEL_DIGITAL_DSP_H__

#ifdef __cplusplus
 extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>

/**
  * @}
  */

/** @defgroup STM3210E_EVAL_LCD_Exported_Macros
  * @{
  */
/**
  * @}
  */

/** @defgroup STM3210E_EVAL_LCD_Exported_Functions
  * @{
  */
/** @defgroup
  * @{
  */

/** @defgroup
  * @{
  */

/**
  * @}
  */

/**
  * @}
  */
#ifdef __cplusplus
}
#endif

#endif /* __VICTEL_DIGITAL_DSP_H__ */
/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/******************* (C) COPYRIGHT 2011 STMicroelectronics *****END OF FILE****/
