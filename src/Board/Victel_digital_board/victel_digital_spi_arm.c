/**
  ******************************************************************************
  *                Copyright (c) 2015, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    21-April-2015
  * @brief   gpio config and software interface
  ******************************************************************************
  */

#include <stdio.h>
#include <string.h>
#include "victel_digital_lcd.h"
#include "victel_digital_flash.h"
#include "victel_digital_spi_arm.h"
#include "victel_digital_usart.h"
#include "global_define.h"
#include "stack_config.h"
#include "user_define.h"
#include "DVSI.h"
#include "time_utils.h"
#include "vlog.h"
#include "aipl.h"


#if defined DEBUG_DSP_INT_OFFSET_OF_SLOT || defined DEBUG_SPI_TX_STRESS_TEST
extern uint32_t dsp_slot_start_1ms;
#endif

extern uint8_t dqi_prev_slot;
extern const int8_t tip_voice_gain_factor[];
extern const int8_t voice_gain_factor[];
extern tim_instant_t ins_aipl;

MCU_INF_TYPEDEF g_mcu_inf_instance, *g_mcu_inf_struct = &g_mcu_inf_instance;
static int8_t speaker_volume_gain[3] = {0, 0, 0};	// speaker/tip/mic
static uint16_t voice_index = 0;


uint8_t voice_is_mute(void)
{
	return (speaker_volume_gain[0] > voice_gain_factor[0]) ? 0 : 1;
}

uint8_t tip_is_mute(void)
{
	return (speaker_volume_gain[1] > tip_voice_gain_factor[0]) ? 0 : 1;
}

int aipl_WMIMsgSendToAipld(unsigned char Id, unsigned char *s, unsigned short n);
void mcu_txdma_start(uint8_t *data, uint32_t len)
{
	if (aipl_WMIMsgSendToAipld(MCU_SPI_ID_RF, data, (uint16_t)len))
		vlog_v("tx->mcu","\taipl send msg fail");
}

uint8_t cal_mcu_inf_checksum(uint8_t *dat, uint16_t len)
{
	uint16_t sum, i;

	for (i = 0, sum = 0; i < len; i++)
		sum += dat[i];

	return (uint8_t)sum;
}

void packet_data_to_mcu_inf_frame(uint8_t *dat, uint16_t len)
{
	mcu_txdma_start(dat, len);
	vlog_i("tx->mcu","\tsend msg(%d %d)=%02d", dat[0], dat[1], len);
/*
	MCU_FRAME_TYPEDEF spi_frm;

	if (len <= MCU_INF_MAX_DATA)
	{
		spi_frm.head = IPC_TAG_HEAD;
		spi_frm.frame_len = MCU_INF_FRAME_HEAD_NUM + len + MCU_INF_FRAME_TAIL_NUM;
		spi_frm.sn = g_mcu_inf_struct->mcu_tx_sn++;
		spi_frm.dst_id = MCU_SPI_ID_RF;
		spi_frm.src_id = MCU_SPI_ID_MCU;
		spi_frm.reserved1 = 0;
		memcpy(&spi_frm.data, dat, len);
		spi_frm.data[len] = 0;	// spi_frm.reserved2
		spi_frm.data[len + 1] = cal_mcu_inf_checksum((uint8_t *)&spi_frm, spi_frm.frame_len - 3); // del tail(2B)+chksum(1B)
		spi_frm.data[len + 2] = (uint8_t)IPC_TAG_TAIL;
		spi_frm.data[len + 3] = (uint8_t)(IPC_TAG_TAIL >> 8);

		mcu_txdma_start((uint8_t *)&spi_frm, spi_frm.frame_len);
		printf("\tsend msg(%d %d)=%02d\r\n", dat[0], dat[1], len);
	}
	else
	{
		printf("\tsend msg(%d %d)=%02d overflow\r\n", dat[0], dat[1], len);
	}
*/
}

void packet_ctrl_to_mcu_inf_frame(e_baseband_control_type_t cmd, uint32_t para1, uint32_t para2)
{
	bb_control_base_struct_t ctrl_struct;

	ctrl_struct.msg_head.msg_type = BB_RF_BUS_DATA_TYPE_CONTROL_DATA;
	ctrl_struct.msg_head.ctrl_type = cmd;				// e_baseband_control_type_t
	ctrl_struct.msg_head.ctrl_type2 = (uint8_t)para1;	// e_tx_power_level_t
	ctrl_struct.msg_head.opcode_type = BASEBAND_CONTROL_OPCODE_TYPE_REQUEST;
	ctrl_struct.msg_head.opcode_rsvd = 0;
	if ((cmd == BASEBAND_TYPE_SET_FREQ) || (cmd == BASEBAND_TYPE_CHANNEL_SCAN))
	{
		memcpy(ctrl_struct.data, &para1, sizeof(uint32_t));
		memcpy(&ctrl_struct.data[sizeof(uint32_t)], &para2, sizeof(uint32_t));
	}

	packet_data_to_mcu_inf_frame((uint8_t *)&ctrl_struct, sizeof(bb_rf_bus_control_msg_header_t) + sizeof(uint32_t) * 2);
    vlog_v("tx->mcu","%s %04d",__FUNCTION__,__LINE__);
}

void packet_status_to_mcu_inf_frame(void)
{
	bb_rf_bus_data_to_modulated_t rssi_struct;

	rssi_struct.msg_type = BB_RF_BUS_DATA_TYPE_BASEBAND_NULL;
	rssi_struct.rssi = speaker_volume_gain[2];
	rssi_struct.reserved = g_mcu_inf_struct->ptt_for_voice;
	packet_data_to_mcu_inf_frame((uint8_t *)&rssi_struct, sizeof(bb_rf_bus_data_to_modulated_t) - PDT_AIR_DATA_LENGTH); // send head only, no data
    vlog_v("tx->mcu","%s %04d",__FUNCTION__,__LINE__);
}

void packet_dsp_tune_to_mcu_inf_frame(uint8_t cmd, uint8_t op_type, uint32_t *paras)	// cmd:e_dsp_tune_command_t; op_type: 1-wr, 2-rd, 3-report
{
	bb_rf_bus_tune_t dsp_tune_struct;

	dsp_tune_struct.msg_type = BB_RF_BUS_DATA_TYPE_TUNE;
	dsp_tune_struct.ctrl_type = op_type;
	dsp_tune_struct.Id = cmd;
	dsp_tune_struct.AckU = 0;
	memcpy(dsp_tune_struct.Param, paras, sizeof(uint32_t) * 6);
	packet_data_to_mcu_inf_frame((uint8_t *)&dsp_tune_struct, sizeof(bb_rf_bus_tune_t));
    vlog_v("tx->mcu","%s %04d",__FUNCTION__,__LINE__);
}

uint8_t check_mcu_inf_frame(MCU_FRAME_TYPEDEF *mcu_inf)	// 0-ok
{
	uint8_t *ptr8, chksum;

	if ((mcu_inf->head == IPC_TAG_HEAD) && (mcu_inf->dst_id == MCU_SPI_ID_MCU))
	{
		ptr8 = (uint8_t *)mcu_inf + mcu_inf->frame_len - 3;	// point to checksum
		chksum = cal_mcu_inf_checksum((uint8_t *)mcu_inf, mcu_inf->frame_len - 3);
		if ((ptr8[1] == (uint8_t)IPC_TAG_TAIL) && (ptr8[2] == (uint8_t)(IPC_TAG_TAIL >> 8)) && (ptr8[0] == chksum))
		{
			return 0;
		}
//#ifdef DEBUG_MCU_INF_SPI
		else
		{
			vlog_v("arm","\trx_err(%03d):%02x/%02x %02x %02x", mcu_inf->sn, ptr8[0], chksum, ptr8[2], ptr8[1]);
//			vlog_v("arm","\tTsn=%d", mcu_inf->sn);
		}
//#endif
	}
//#ifdef DEBUG_MCU_INF_SPI
	else
	{
		vlog_v("arm","\trx_err(%03d):%04x %02x", mcu_inf->sn, mcu_inf->head, mcu_inf->dst_id);
//		vlog_v("arm","\tHsn=%d", mcu_inf->sn);
	}
//#endif

	return 1;
}

static uint8_t mic_voice_save_offset = 0;
void save_mic_voice(void *mic_voice, uint8_t len)	// unit of len is int16_t
{
	int16_t linear16[SELP_FRAME_SIZE], tmp[SELP_FRAME_SIZE];

	if (len < SELP_FRAME_SIZE)
	{
		alaw_to_linear(mic_voice, (short *)linear16, len);

		if (mic_voice_save_offset + len <= SELP_FRAME_SIZE)
		{
			memcpy((void *)((uint32_t)g_mcu_inf_struct->mic_voice + mic_voice_save_offset * sizeof(int16_t)), linear16, len * sizeof(int16_t));
			mic_voice_save_offset += len;
			if (mic_voice_save_offset >= SELP_FRAME_SIZE)
			{
				g_mcu_inf_struct->mic_voice_valid = 1;
			}
		}
		else
		{
			memcpy((void *)tmp, (void *)((uint32_t)g_mcu_inf_struct->mic_voice + len * sizeof(int16_t)), (SELP_FRAME_SIZE - len) * sizeof(int16_t));
			memcpy((void *)g_mcu_inf_struct->mic_voice, (void *)tmp, (SELP_FRAME_SIZE - len) * sizeof(int16_t));
			memcpy((void *)((uint32_t)g_mcu_inf_struct->mic_voice + (SELP_FRAME_SIZE - len) * sizeof(int16_t)), linear16, len * sizeof(int16_t));
		}
	}
	else
	{
//		vlog_v("arm","\tmic len(%d) overflow", len);
	}
}

//#define POC_VOICE_TIME_SLICE	3
//static uint8_t poc_voice_cnt = 0;
void get_mic_voice_from_dsp(uint8_t *alaw_mic)
{
//	static int16_t linear16[SELP_FRAME_SIZE * POC_VOICE_TIME_SLICE];	// 20ms * 3 REG_DA_PCM_LENGTH
//	uint8_t mic_cnt;

	if (alaw_mic)
	{
//		dsp_speech_in_pa_ctrl(1, 0);
//		mic_cnt = poc_voice_cnt & 0x0f;
//		if (mic_cnt >= POC_VOICE_TIME_SLICE)
//		{
			linear_to_alaw(g_mcu_inf_struct->mic_voice, alaw_mic, SELP_FRAME_SIZE/* * POC_VOICE_TIME_SLICE*/);
//			poc_voice_cnt &= 0xf0;
//		}
//		else
//		{
//			memcpy((void *)linear16[SELP_FRAME_SIZE * mic_cnt], (void *)g_mcu_inf_struct->mic_voice, sizeof(int16_t) * SELP_FRAME_SIZE);
//			poc_voice_cnt = (poc_voice_cnt & 0xf0) | ++mic_cnt;
//		}
	}
//	else
//	{
//		dsp_speech_in_pa_ctrl(0, 0);
//	}
}

#ifdef DEBUG_SPI_TX_STRESS_TEST
static uint8_t spi_stress_test_index = 0, spi_stress_test_index2 = 0, spi_stress_test_index3 = 0;

void spi_stress_test_sending(uint8_t flag)
{
	e_baseband_control_type_t cmd = flag ? BASEBAND_TYPE_VERSION : BASEBAND_TYPE_NULL;

	packet_ctrl_to_mcu_inf_frame(cmd, flag ? spi_stress_test_index3 : spi_stress_test_index2, 0);
	packet_ctrl_to_mcu_inf_frame(cmd, flag ? spi_stress_test_index3++ : spi_stress_test_index2++, 0);
}
#endif

void play_voice_with_tip_interface(uint8_t *alaw_voice)
{
	int16_t linear16[SELP_FRAME_SIZE];

	if (alaw_voice)
	{
//		dsp_speech_out_pa_ctrl(1, SPEECH_PA_CTRL_OUT_TIP);
		alaw_to_linear(alaw_voice, (short *)linear16, SELP_FRAME_SIZE);
		packet_spk_voice_to_mcu_inf_frame(SET_SPEAKER_VOICE_VOLUME, linear16);
        vlog_v("tx->mcu","%s %04d",__FUNCTION__,__LINE__);
#ifdef DEBUG_SPI_TX_STRESS_TEST
		linear16[0] = (int16_t)spi_stress_test_index++;
		linear16[1] = (int16_t)GET_SLOT_TO_CURR_TIME();
		packet_spk_voice_to_mcu_inf_frame(SET_SPEAKER_VOICE_VOLUME, linear16);
        vlog_v("tx->mcu","%s %04d",__FUNCTION__,__LINE__);
#endif
	}
//	else
//	{
//		dsp_speech_out_pa_ctrl(0, SPEECH_PA_CTRL_OUT_TIP);
//	}
}

extern int16_t rssi_prev_slot, rssi_prev_slot_noise, rssi_curr_slot;
extern uint8_t dqi_prev_slot;
void mcu_inf_rcv_frame(void)
{
	bb_rf_bus_data_to_modulated_t *air_frame;
	bb_rf_bus_speaker_PCM_t *mic_frame;
	bb_control_base_struct_t *air_ctrl;
	bb_rf_bus_data_timeslot_t *timeslot;
	bb_rf_bus_data_vptt_t *vptt;
	tim_duration_t elapsed;


//	vlog_v("arm","\tinf rx:H=%04x,L=%03d,sn=%03d:%02X %02X %02X %02X",
//		g_mcu_inf_struct->spi_frame.head, g_mcu_inf_struct->spi_frame.frame_len, g_mcu_inf_struct->spi_frame.sn,
//		g_mcu_inf_struct->spi_frame.data[0], g_mcu_inf_struct->spi_frame.data[1], g_mcu_inf_struct->spi_frame.data[2], g_mcu_inf_struct->spi_frame.data[3]);

	if (check_mcu_inf_frame(&g_mcu_inf_struct->spi_frame) == 0)
	{
#ifdef DEBUG_MCU_INF_SPI
		vlog_v("arm","\trx_ok(%03d):%02X %02X %02X %02X-%d", g_mcu_inf_struct->spi_frame.sn, g_mcu_inf_struct->spi_frame.data[0],
			g_mcu_inf_struct->spi_frame.data[1], g_mcu_inf_struct->spi_frame.data[2], g_mcu_inf_struct->spi_frame.data[3], GET_SLOT_TO_CURR_TIME());
#endif
		// vlog_v("arm","rx_ok:%02X:%3d", g_mcu_inf_struct->spi_frame.data[0], g_mcu_inf_struct->spi_frame.sn);

		switch (g_mcu_inf_struct->spi_frame.data[0])		// msg_type
		{
			case BB_RF_BUS_DATA_TYPE_BASEBAND_DATA:
			case BB_RF_BUS_DATA_TYPE_BASEBAND_NULL:
				air_frame = (bb_rf_bus_data_to_modulated_t *)g_mcu_inf_struct->spi_frame.data;
				g_mcu_inf_struct->rssi_bb = air_frame->rssi;
				if (g_mcu_inf_struct->spi_frame.data[0] == BB_RF_BUS_DATA_TYPE_BASEBAND_DATA)
				{
					rssi_prev_slot = (int16_t)air_frame->rssi - 130;
					dqi_prev_slot = air_frame->dqi;
					memcpy((void *)g_mcu_inf_struct->air_signal, (void *)air_frame->data, PDT_AIR_DATA_LENGTH);
					g_mcu_inf_struct->air_signal_valid = AIR_SIGNAL_TYPE_VALID;	// 1-air data
					// vlog_v("arm","\td=%d/%d", rssi_prev_slot, dqi_prev_slot);
				}
				else
				{
					rssi_prev_slot_noise = (int16_t)air_frame->rssi - 130;
					dqi_prev_slot = 0;
					g_mcu_inf_struct->air_signal_valid = AIR_SIGNAL_TYPE_NULL;	// 3-null data(get noise rssi only)
					// vlog_v("arm","\tdn=%d/%d", rssi_prev_slot_noise, dqi_prev_slot);
				}
				// packet_status_to_mcu_inf_frame();

				dsp_slot_middle_interrupt();
				break;

			case BB_RF_BUS_DATA_TYPE_PCM_DATA:
				if ((uart2_use_as_voiceport()/* && own_playing_voice_now()*/) == 0)
				{
					mic_frame = (bb_rf_bus_speaker_PCM_t *)g_mcu_inf_struct->spi_frame.data;
#ifdef DEBUG_MCU_INF_SPI
					if (g_mcu_inf_struct->mic_voice_valid && own_talking_now())
						vlog_v("arm","\tmic voice overflow-%d", GET_SLOT_TO_CURR_TIME());
#endif
//					save_mic_voice((void *)mic_frame->pcm_data, SELP_FRAME_SIZE);
					memcpy((void *)g_mcu_inf_struct->mic_voice, (void *)mic_frame->pcm_data, SELP_FRAME_SIZE * sizeof(int16_t));
					g_mcu_inf_struct->mic_voice_valid = 1;
				}
				break;

			case BB_RF_BUS_DATA_TYPE_CONTROL_DATA:
				air_ctrl = (bb_control_base_struct_t *)g_mcu_inf_struct->spi_frame.data;
				if (air_ctrl->msg_head.ctrl_type == BASEBAND_TYPE_CHANNEL_SCAN)
				{
					rssi_curr_slot = (int16_t)air_ctrl->msg_head.ctrl_type2 - 130;
//					vlog_v("arm","\tn=%d,hz=%d", rssi_curr_slot, *((uint32_t *)(&air_ctrl->data[sizeof(uint32_t)])));
				}
				else
				{
					goto copy_ctrl_tune_frame;
				}
				break;

			case BB_RF_BUS_DATA_TYPE_TUNE:
copy_ctrl_tune_frame:
				memcpy((void *)&g_mcu_inf_struct->rep_fr_bb, (void *)g_mcu_inf_struct->spi_frame.data, sizeof(bb_control_base_struct_t));
				break;

//			case BB_RF_BUS_DATA_TYPE_PC_UPGRADE:	// �ݲ�֧��ͨ����������DSP��ֻ����boot����
//			case BB_RF_BUS_DATA_TYPE_PC_VERIFY:
			case BB_RF_BUS_DATA_TYPE_PC_SET_PARAS:
			case BB_RF_BUS_DATA_TYPE_PC_VERIFY_PARAS:
			case BB_RF_BUS_DATA_TYPE_PC_GET_PARAS:
				memcpy((void *)&g_mcu_inf_struct->rep_fr_bb, (void *)g_mcu_inf_struct->spi_frame.data, MCU_INF_MAX_DATA);
				break;

			case BB_RF_BUS_DATA_TYPE_TIMESLOT:
				timeslot = (bb_rf_bus_data_timeslot_t *)g_mcu_inf_struct->spi_frame.data;
//				tim_instant_elapsed(&ins_aipl, &elapsed);
				// vlog_v("arm","slot num=%d,cnt=%d(%llu)", timeslot->slot_number, timeslot->slot_count, tim_duration_to_usecs(&elapsed));
				dsp_slot_edge_interrupt((uint8_t)(timeslot->slot_number & 0x01));
				break;

			case BB_RF_BUS_DATA_TYPE_VPTT:
				vptt = (bb_rf_bus_data_vptt_t *)g_mcu_inf_struct->spi_frame.data;
				if (vptt->state)
					set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
				else
					set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
				break;

			default:
				break;
		}

//		memset(&g_mcu_inf_struct->spi_frame, 0, sizeof(MCU_FRAME_TYPEDEF));
	}
	else
	{
//		vlog_v("arm","\trx_fail:%02X:%3d", g_mcu_inf_struct->spi_frame.data[0], g_mcu_inf_struct->spi_frame.sn);
	}
}


int16_t *get_mic_voice_buffer(void)
{
	if (g_mcu_inf_struct->mic_voice_valid)
	{
		g_mcu_inf_struct->mic_voice_valid = 0;
		return g_mcu_inf_struct->mic_voice;
	}
	else
	{
		return 0;
	}
}

void save_mic_encode_data(uint8_t *enc_data)
{
	uint8_t temp[MIC_CODEWORD_MAX_NUM - 1][SELP_BYTE_SIZE_FEC];

//	NVIC_DisableIRQ(TMR8_BRK_TMR12_IRQn);	// call dsp_slot_middle_interrupt at tim12 int
	if (g_mcu_inf_struct->mic_codeword_num >= MIC_CODEWORD_MAX_NUM - 1)
	{
		memcpy(temp, g_mcu_inf_struct->mic_codeword[1], (MIC_CODEWORD_MAX_NUM - 1) * SELP_BYTE_SIZE_FEC);
		memcpy(g_mcu_inf_struct->mic_codeword[0], temp, (MIC_CODEWORD_MAX_NUM - 1) * SELP_BYTE_SIZE_FEC);
		g_mcu_inf_struct->mic_codeword_num = MIC_CODEWORD_MAX_NUM - 1;
	}
	memcpy(g_mcu_inf_struct->mic_codeword[g_mcu_inf_struct->mic_codeword_num], enc_data, SELP_BYTE_SIZE_FEC);
#ifdef DEBUG_MCU_INF_SPI
	vlog_v("arm","\tenc->%d-%d", g_mcu_inf_struct->mic_codeword_num, GET_SLOT_TO_CURR_TIME());
#endif
	g_mcu_inf_struct->mic_codeword_num++;
//	NVIC_EnableIRQ(TMR8_BRK_TMR12_IRQn);
}

#define MIC_CODEWORD_OFFSET_WITH_GAP(n)		((SELP_BYTE_SIZE_FEC + 1) * (n))
void get_mic_encode_data(uint8_t *mic_encode)	// 3��9Bת��Ϊ3��10B
{
	uint8_t temp[MIC_CODEWORD_MAX_NUM - MIC_SPK_TIME_SLICE][SELP_BYTE_SIZE_FEC];

	if (g_mcu_inf_struct->mic_codeword_num >= MIC_SPK_TIME_SLICE)
	{
		memcpy(&mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(0)],  g_mcu_inf_struct->mic_codeword[0], SELP_BYTE_SIZE_FEC);
		mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(1) - 1] = 0;
		memcpy(&mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(1)], g_mcu_inf_struct->mic_codeword[1], SELP_BYTE_SIZE_FEC);
		mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(2) - 1] = 0;
		memcpy(&mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(2)], g_mcu_inf_struct->mic_codeword[2], SELP_BYTE_SIZE_FEC);
		mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(3) - 1] = 0;

		g_mcu_inf_struct->mic_codeword_num -= MIC_SPK_TIME_SLICE;
		if (g_mcu_inf_struct->mic_codeword_num)
		{
			memcpy(temp, g_mcu_inf_struct->mic_codeword[MIC_SPK_TIME_SLICE], g_mcu_inf_struct->mic_codeword_num * SELP_BYTE_SIZE_FEC);
			memcpy(g_mcu_inf_struct->mic_codeword[0], temp, g_mcu_inf_struct->mic_codeword_num * SELP_BYTE_SIZE_FEC);
		}
#if defined DEBUG_MCU_INF_SPI || defined DEBUG_TX_SEQ_TIME
		vlog_v("arm","\tget mic012(%d)-%d", g_mcu_inf_struct->mic_codeword_num, GET_SLOT_TO_CURR_TIME());
#endif
	}
	else
	{
		if (g_mcu_inf_struct->mic_codeword_num == 2)
		{
			memset(&mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(0)],  0, SELP_BYTE_SIZE_FEC + 1);
			memcpy(&mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(1)], g_mcu_inf_struct->mic_codeword[0], SELP_BYTE_SIZE_FEC);
			mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(2) - 1] = 0;
			memcpy(&mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(2)], g_mcu_inf_struct->mic_codeword[1], SELP_BYTE_SIZE_FEC);
			mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(3) - 1] = 0;
#if defined DEBUG_MCU_INF_SPI || defined DEBUG_TX_SEQ_TIME
			vlog_v("arm","\tget micx01-%d", GET_SLOT_TO_CURR_TIME());
#endif
		}
		else if (g_mcu_inf_struct->mic_codeword_num == 1)
		{
			memset(&mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(0)],  0, (SELP_BYTE_SIZE_FEC + 1) * 2);
			memcpy(&mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(2)], g_mcu_inf_struct->mic_codeword[0], SELP_BYTE_SIZE_FEC);
			mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(3) - 1] = 0;
#if defined DEBUG_MCU_INF_SPI || defined DEBUG_TX_SEQ_TIME
			vlog_v("arm","\tget micxx0-%d", GET_SLOT_TO_CURR_TIME());
#endif
		}
		else
		{
			memset(&mic_encode[MIC_CODEWORD_OFFSET_WITH_GAP(0)],  0, (SELP_BYTE_SIZE_FEC + 1) * MIC_SPK_TIME_SLICE);
#if defined DEBUG_MCU_INF_SPI || defined DEBUG_TX_SEQ_TIME
			vlog_v("arm","\tget micxxx-%d", GET_SLOT_TO_CURR_TIME());
#endif
		}

		g_mcu_inf_struct->mic_codeword_num = 0;
	}
}

void save_spk_codeword(uint8_t *spk_codeword)
{
	memcpy(g_mcu_inf_struct->spk_codeword[0], &spk_codeword[MIC_CODEWORD_OFFSET_WITH_GAP(0)], SELP_BYTE_SIZE_FEC);
	memcpy(g_mcu_inf_struct->spk_codeword[1], &spk_codeword[MIC_CODEWORD_OFFSET_WITH_GAP(1)], SELP_BYTE_SIZE_FEC);
	memcpy(g_mcu_inf_struct->spk_codeword[2], &spk_codeword[MIC_CODEWORD_OFFSET_WITH_GAP(2)], SELP_BYTE_SIZE_FEC);
	g_mcu_inf_struct->spk_codeword_idx = MIC_SPK_TIME_SLICE;
#ifdef DEBUG_RX_SEQ_TIME
	vlog_v("arm","sav spk:%d", GET_SLOT_TO_CURR_TIME());
#endif
}

uint8_t *get_spk_codeword_buffer(void)
{
	if (g_mcu_inf_struct->spk_codeword_idx)
	{
		g_mcu_inf_struct->spk_codeword_idx--;
		return g_mcu_inf_struct->spk_codeword[MIC_SPK_TIME_SLICE - 1 - g_mcu_inf_struct->spk_codeword_idx];
	}
	else
	{
		return 0;
	}
}

uint8_t get_air_data_buffer(uint8_t *air_data)
{
	uint8_t ret;

	if (g_mcu_inf_struct->air_signal_valid)
	{
		ret = g_mcu_inf_struct->air_signal_valid;
		g_mcu_inf_struct->air_signal_valid = AIR_SIGNAL_TYPE_NONE;
		if (ret != 3)
			memcpy(air_data, g_mcu_inf_struct->air_signal, PDT_AIR_DATA_LENGTH);
		return ret;
	}
	else
	{
		return AIR_SIGNAL_TYPE_NONE;
	}
}

static int level_to_volume(uint8_t level)
{
    if (level > 9) level = 9;
    return (level * 100) / 9;
}

static uint8_t volume_to_level(int volume)
{
    if (volume < 0) volume = 0;
    if (volume > 100) volume = 100;

    uint8_t level = (uint8_t)((volume * 9) / 100);
    if (level > 9) level = 9;

    return level;
}

int8_t set_speaker_volume(uint8_t flag, int8_t vol)	// flag: 0-speaker; 1-tip
{
	speaker_volume_gain[flag] = vol;
	return speaker_volume_gain[flag];
}

int8_t set_mic_voice_gain(int8_t vol)
{
	speaker_volume_gain[2] = vol;
	return speaker_volume_gain[2];
}

#if defined DEBUG_TX_SEQ_TIME || defined DEBUG_RX_SEQ_TIME
extern uint8_t g_talk_tip_count, g_talk_tip_count_total, g_talk_tip_sound;
extern uint32_t dsp_slot_start_1ms;
#endif
void packet_spk_voice_to_mcu_inf_frame(uint8_t flag, int16_t *spk_voice)	// flag: 0-speaker; 1-tip
{
	bb_rf_bus_speaker_PCM_t spk_frm;
	uint8_t i, *ptr8, pcm_alaw[SELP_FRAME_SIZE];

	if (uart2_use_as_voiceport()/* && own_playing_voice_now()*/)
	{
		linear_to_alaw(spk_voice, pcm_alaw, SELP_FRAME_SIZE);
//		if (can_uart == 0)	// to can
//		{
//			send_transcode_buffer_to_can(pcm_alaw, 0, SELP_FRAME_SIZE);
//		}
//		else
		{
			if (uart2_is_new_bt())
			{
				if (get_bt_link_state())
				{
					for (i = 0, ptr8 = pcm_alaw; i < SELP_FRAME_SIZE / BT_DMA_DATA_DLEN; i++, ptr8 += BT_DMA_DATA_DLEN)
						send_bt_frame_to_host(ptr8, BT_DMA_DATA_DLEN, TSC_CTASK_NEW_BT_UNI_CMD, voice_index++, TSC_CTASK_NEW_BT_ADDR_VOICE);
				}
			}
			else
			{
				for (i = 0, ptr8 = pcm_alaw; i < SELP_FRAME_SIZE / BT_DMA_DATA_DLEN; i++, ptr8 += BT_DMA_DATA_DLEN)
					send_bt_frame_to_host(ptr8, BT_DMA_DATA_DLEN, TSC_CTASK_SEND_VOICE, voice_index++, 0);
			}
		}
	}
	else
	{
		spk_frm.msg_type = BB_RF_BUS_DATA_TYPE_PCM_DATA;
		spk_frm.volume = speaker_volume_gain[flag];
		memcpy(spk_frm.pcm_data, spk_voice, SELP_FRAME_SIZE * sizeof(int16_t));
#if defined DEBUG_TX_SEQ_TIME || defined DEBUG_RX_SEQ_TIME
		if (flag == 0)
		{
			vlog_v("arm","spk-%02d:%04X %d", spk_frm.volume, spk_frm.pcm_data[0],
				get_measure_timer_difference(get_timestamp_measure(), dsp_slot_start_1ms));
		}
		else
		{
			vlog_v("arm","tip%d-%02d:%d-%d:%04X %d", g_talk_tip_sound, spk_frm.volume, g_talk_tip_count_total, g_talk_tip_count, spk_frm.pcm_data[0],
				get_measure_timer_difference(get_timestamp_measure(), dsp_slot_start_1ms));
		}
#endif

		packet_data_to_mcu_inf_frame((uint8_t *)&spk_frm, sizeof(bb_rf_bus_speaker_PCM_t));
        vlog_v("tx->mcu","%s %04d",__FUNCTION__,__LINE__);
	}
}

void packet_signaling_to_mcu_inf_frame(uint8_t *signal)
{
	bb_rf_bus_data_to_modulated_t signaling;

	signaling.msg_type = BB_RF_BUS_DATA_TYPE_BASEBAND_DATA;
	signaling.rssi = 0;
	signaling.dqi = 0;
	signaling.reserved = 0;
	memcpy(signaling.data, signal, PDT_AIR_DATA_LENGTH);

	packet_data_to_mcu_inf_frame((uint8_t *)&signaling, sizeof(bb_rf_bus_data_to_modulated_t));
    vlog_v("tx->mcu","%s %04d",__FUNCTION__,__LINE__);
}

void reset_mic_voice_paras(void)
{
	g_mcu_inf_struct->mic_voice_valid = 0;
	g_mcu_inf_struct->mic_codeword_num = 0;
	g_mcu_inf_struct->ptt_for_voice = 1;
}

void clear_ptt_voice_flag(void)
{
	g_mcu_inf_struct->ptt_for_voice = 0;
}

#define READ_DSP_VER_MAX_RETRY		50

#define PUT_DSP_TUNE_RESULT(is_gui_call, str)	if (is_gui_call)\
													put_info_to_ui(4, (uint32_t)str);\
												else\
													printf(str)
void init_mcu_inf_interface(void)
{
	memset(g_mcu_inf_struct, 0, sizeof(MCU_INF_TYPEDEF));
	vlog_v("arm","mcu inf=%dB at 0x%08x,RF paras=%d", sizeof(MCU_INF_TYPEDEF), (uint32_t)g_mcu_inf_struct, sizeof(DB_TUNE_TYPE));
}

uint8_t let_dsp_todo_something(uint8_t gui_call_flag, uint8_t cmd)
{
	uint32_t i = 0, kb_timer_cnt = 0xffffffff;
	char output_buff[240] = "";

	if ((cmd == BASEBAND_TYPE_VERSION) || (cmd == BASEBAND_TYPE_REBOOT))
	{
		do
		{
			if ((g_mcu_inf_struct->rep_fr_bb.msg_head.msg_type == BB_RF_BUS_DATA_TYPE_CONTROL_DATA) &&
				(g_mcu_inf_struct->rep_fr_bb.msg_head.opcode_type == BASEBAND_CONTROL_OPCODE_TYPE_REPLY) &&
				(g_mcu_inf_struct->rep_fr_bb.msg_head.ctrl_type == cmd))
			{
				if (cmd == BASEBAND_TYPE_VERSION)
				{
					sprintf(output_buff, "(%d)DSP ver: %s", i, (char *)g_mcu_inf_struct->rep_fr_bb.data + 8); 	// offset of the string == 8
					PUT_DSP_TUNE_RESULT(gui_call_flag, output_buff);
					get_dsp_compile_date_version(g_mcu_inf_struct->rep_fr_bb.data);
				}
				else
				{
					sprintf(output_buff, "(%d)reboot DSP OK", i);
					PUT_DSP_TUNE_RESULT(gui_call_flag, output_buff);
				}
				memset(&g_mcu_inf_struct->rep_fr_bb.msg_head, 0, sizeof(bb_rf_bus_control_msg_header_t));
				return 1;
			}
			else if ((kb_timer_cnt == 0xffffffff) || (get_measure_timer_difference(get_timestamp_measure(), kb_timer_cnt) >= 40))
			{
				kb_timer_cnt = get_timestamp_measure();
				i = READ_DSP_VER_MAX_RETRY;	// i++;
				packet_ctrl_to_mcu_inf_frame((e_baseband_control_type_t)cmd, 0, 0);
			}
		} while (i < READ_DSP_VER_MAX_RETRY);
	}

	return 0;
}

#define READ_DSP_TUNE_RETRY		5
const uint8_t dsp_tune_paras_len[DSP_TUNE_MAX_TYPE] = {0, 1, 2, 2, 2, 1, 1, 2, 5, 1, 1, 4};	// index 0 & 128 not used

uint8_t dsp_tune_process(uint8_t gui_call_flag, uint8_t cmd, uint32_t *paras)
{
	uint32_t i = 0, kb_timer_cnt = 0xffffffff, cmd_para_num;
	bb_rf_bus_tune_t *p_dsp_tune_rep = (bb_rf_bus_tune_t *)&g_mcu_inf_struct->rep_fr_bb;
	char output_buff[240] = "";

	do
	{
		if ((p_dsp_tune_rep->msg_type == BB_RF_BUS_DATA_TYPE_TUNE) && (p_dsp_tune_rep->Id == cmd))
		{
			p_dsp_tune_rep->msg_type = 0;
			if (p_dsp_tune_rep->ctrl_type == DSP_TUNE_OP_TYPE_WR)			// set/write paras
			{
				cmd_para_num = dsp_tune_paras_len[(cmd == DSP_TUNE_TYPE_BER_CTRL) ? (DSP_TUNE_TYPE_BK4819_SEL + 1) : ((cmd == DSP_TUNE_TYPE_SAVE_DATA) ? 1 : cmd)];
				if ((p_dsp_tune_rep->AckU == 0) && ((cmd == DSP_TUNE_TYPE_SAVE_DATA) || (memcmp(paras, p_dsp_tune_rep->Param, sizeof(uint32_t) * cmd_para_num) == 0)))
				{
					sprintf(output_buff, "(%d)Tune:%d SUCC", i, cmd);
					PUT_DSP_TUNE_RESULT(gui_call_flag, output_buff);
dsp_tune_succ:
					return 1;
				}
				else
				{
dsp_tune_fail:
					sprintf(output_buff, "Tune:ack=%d(n=%d:%08x/%08x %08x/%08x %08x/%08x %08x/%08x %08x/%08x %08x/%08x)",
						p_dsp_tune_rep->AckU, cmd_para_num,
						paras[0], p_dsp_tune_rep->Param[0], paras[1], p_dsp_tune_rep->Param[1],
						paras[2], p_dsp_tune_rep->Param[2], paras[3], p_dsp_tune_rep->Param[3],
						paras[4], p_dsp_tune_rep->Param[4], paras[5], p_dsp_tune_rep->Param[5]);
					PUT_DSP_TUNE_RESULT(gui_call_flag, output_buff);
				}
			}
			else if (p_dsp_tune_rep->ctrl_type == DSP_TUNE_OP_TYPE_REPORT)	// report ber
			{
				if (p_dsp_tune_rep->AckU == 0)
				{
					// �������ʣ�BER=[TBN/(FN*6*216)]*100%����ǰ֡�����ʣ�BER=[CBN/(6*216)]*100%
					sprintf(output_buff, "(%d)T_BER=%3.2f%%,C_BER=%3.2f%%(FN=%08d,TBN=%08d,CBN=%08d,F_dev=%d)", i,
						p_dsp_tune_rep->Param[0] ? (float)p_dsp_tune_rep->Param[1] / (p_dsp_tune_rep->Param[0] * 6 * 216) * 100 : 0,
						(float)p_dsp_tune_rep->Param[2] / (6 * 216) * 100,
						p_dsp_tune_rep->Param[0], p_dsp_tune_rep->Param[1], p_dsp_tune_rep->Param[2], p_dsp_tune_rep->Param[3]);
					PUT_DSP_TUNE_RESULT(gui_call_flag, output_buff);
					goto dsp_tune_succ;
				}
				else
				{
					goto dsp_tune_fail;
				}
			}
			else
			{
				goto dsp_tune_fail;
			}
		}

		if ((kb_timer_cnt == 0xffffffff) || (get_measure_timer_difference(get_timestamp_measure(), kb_timer_cnt) >= 40))
		{
			kb_timer_cnt = get_timestamp_measure();
			i = READ_DSP_TUNE_RETRY; // i++;
			sprintf(output_buff, "(%d)Tune:%d Going...", i, cmd);
			PUT_DSP_TUNE_RESULT(gui_call_flag, output_buff);
			packet_dsp_tune_to_mcu_inf_frame(cmd, ((cmd == DSP_TUNE_TYPE_BER_CTRL) && (paras[0] > 1)) ? DSP_TUNE_OP_TYPE_REPORT : DSP_TUNE_OP_TYPE_WR, paras);
		}
	} while (i < READ_DSP_TUNE_RETRY);

	sprintf(output_buff, "(%d)Tune:%d FAIL", i, cmd);
	PUT_DSP_TUNE_RESULT(gui_call_flag, output_buff);
	return 0;
}

void packet_upgrade_to_mcu_inf_frame(uint8_t which_op, uint16_t sn, uint32_t addr, uint8_t *upgrade_data)	// sn is length
{
	bb_rf_bus_upgrade_t upgrade_struct;
//	uint8_t i;

	upgrade_struct.msg_type = which_op;
	if (which_op == BB_RF_BUS_DATA_TYPE_PC_GET_PARAS)
	{
		upgrade_struct.sn = 0;
		upgrade_struct.num = sn;
		upgrade_struct.offset = addr - FLASH_VIRTUAL_DSP_PARAS_ADDRESS;
		packet_data_to_mcu_inf_frame((uint8_t *)&upgrade_struct, BB_RF_BUS_UPGRADE_HEADER_LEN);
        vlog_v("tx->mcu","%s %04d",__FUNCTION__,__LINE__);
	}
	else
	{
		upgrade_struct.sn = sn;
		if (sn == 0)
		{
			upgrade_struct.num = 0;
			upgrade_struct.offset = addr;
		}
		else
		{
			upgrade_struct.num = DATA_LENGTH_AT_ONE_MAINTAIN_FRAME;
			upgrade_struct.offset = (uint32_t)(sn - 1) * DATA_LENGTH_AT_ONE_MAINTAIN_FRAME;
			memcpy(upgrade_struct.data, upgrade_data, DATA_LENGTH_AT_ONE_MAINTAIN_FRAME);
		}

		packet_data_to_mcu_inf_frame((uint8_t *)&upgrade_struct, BB_RF_BUS_UPGRADE_HEADER_LEN + upgrade_struct.num);
        vlog_v("tx->mcu","%s %04d",__FUNCTION__,__LINE__);
	}

//	for (i = 0; i < 16; i++)
//		vlog_v("arm","%02x ", upgrade_data[i]);
//	vlog_v("arm","%02x %02x", upgrade_data[4 + MAINTAIN_FRAME_TOTAL_LENGTH - 2], upgrade_data[4 + MAINTAIN_FRAME_TOTAL_LENGTH - 1]);
}

const char dsp_op_paras_str[3][8] = {"Wr tune", "Vr tune", "Rd tune"};
uint8_t dsp_op_paras_process(uint8_t gui_call_flag, uint8_t cmd, uint32_t save_addr)
{
	uint32_t kb_timer_cnt = 0xffffffff, data_save_addr, send_to_dsp_sn, send_to_dsp_addr, send_to_dsp_data_addr;
	uint32_t i = 0, op_sn = 0, op_total_frame;
	char output_buff[240] = "", *p_op_str = (char *)dsp_op_paras_str[cmd - BB_RF_BUS_DATA_TYPE_PC_SET_PARAS];
	bb_rf_bus_upgrade_t *dsp_reply = (bb_rf_bus_upgrade_t *)&g_mcu_inf_struct->rep_fr_bb;

	data_save_addr = (save_addr == 0) ? (uint32_t)get_long_message_display_buffer() : save_addr;
	do
	{
		if (dsp_reply->msg_type == cmd)
		{
			dsp_reply->msg_type = 0;
			if (cmd == BB_RF_BUS_DATA_TYPE_PC_GET_PARAS)
			{
				op_total_frame = sizeof(DB_TUNE_TYPE) / DATA_LENGTH_AT_ONE_MAINTAIN_FRAME - 1;		// read 2KB, 4 frames
				if ((dsp_reply->sn == 0) && (dsp_reply->num == DATA_LENGTH_AT_ONE_MAINTAIN_FRAME) && (dsp_reply->offset == op_sn * DATA_LENGTH_AT_ONE_MAINTAIN_FRAME))
				{
					memcpy((void *)(data_save_addr + dsp_reply->offset), dsp_reply->data, dsp_reply->num);
op_dsp_tune_paras_ok:
					vlog_v("arm","%s%d OK", p_op_str, op_sn);
					if (op_sn >= op_total_frame)									// done
					{
						vlog_v("arm","%s DONE!", p_op_str);
						return 1;
					}
					else
					{
						op_sn++;
						i = 0;
						goto op_dsp_tune_paras_send_next_imme;
					}
				}
				else
				{
					vlog_v("arm","%s%d=%d(offset=%d) error, retry...", p_op_str, dsp_reply->sn, dsp_reply->num, dsp_reply->offset);
				}
			}
			else
			{
				op_total_frame = sizeof(DB_TUNE_TYPE) / DATA_LENGTH_AT_ONE_MAINTAIN_FRAME;		// write frame 0 and data 1-4, 5 frames
				if (dsp_reply->sn == op_sn)
				{
					if (op_sn == 0)
					{
						send_to_dsp_sn = 0;							// dsp reply num wanted
						send_to_dsp_addr = sizeof(DB_TUNE_TYPE);	// dsp reply offset wanted
					}
					else
					{
						send_to_dsp_sn = DATA_LENGTH_AT_ONE_MAINTAIN_FRAME;
						send_to_dsp_addr = (op_sn - 1) * DATA_LENGTH_AT_ONE_MAINTAIN_FRAME;
					}

					if ((dsp_reply->num == send_to_dsp_sn) && (dsp_reply->offset == send_to_dsp_addr))
					{
						if (*((uint32_t *)dsp_reply->data) == MAINTAIN_RETURN_SUCCESSFUL)
						{
							goto op_dsp_tune_paras_ok;
						}
						else
						{
							vlog_v("arm","%s%d:num(%d!=%d) or offset(%d!=%d) not match,retry...", p_op_str, dsp_reply->sn,
								dsp_reply->num, send_to_dsp_sn, dsp_reply->offset, send_to_dsp_addr);
						}
					}
				}
				else
				{
					vlog_v("arm","%s:sn(%d!=%d) not match,retry...", p_op_str, dsp_reply->sn, op_sn);
				}
			}
		}

		if ((kb_timer_cnt == 0xffffffff) || (get_measure_timer_difference(get_timestamp_measure(), kb_timer_cnt) >= 40))
		{
op_dsp_tune_paras_send_next_imme:
			kb_timer_cnt = get_timestamp_measure();
			i = READ_DSP_TUNE_RETRY; // i++;
			sprintf(output_buff, "(%d)%s%d Going...", i, p_op_str, op_sn);
			PUT_DSP_TUNE_RESULT(gui_call_flag, output_buff);
			if (cmd == BB_RF_BUS_DATA_TYPE_PC_GET_PARAS)
			{
				send_to_dsp_sn = DATA_LENGTH_AT_ONE_MAINTAIN_FRAME;			// means is num of read
				send_to_dsp_addr = FLASH_VIRTUAL_DSP_PARAS_ADDRESS + op_sn * DATA_LENGTH_AT_ONE_MAINTAIN_FRAME;	// means is abs addr
				send_to_dsp_data_addr = 0;
			}
			else
			{
				send_to_dsp_sn = op_sn;							// means is sn, 0 is prepare, read 1-4
				if (op_sn == 0)
				{
					send_to_dsp_addr = sizeof(DB_TUNE_TYPE); 	// means is total len
					send_to_dsp_data_addr = 0;
				}
				else
				{
					send_to_dsp_addr = 0; 						// ignored; cal the offset addr by op_sn at packet_upgrade_to_mcu_inf_frame()
					send_to_dsp_data_addr = data_save_addr + (op_sn - 1) * DATA_LENGTH_AT_ONE_MAINTAIN_FRAME;
				}
			}
			packet_upgrade_to_mcu_inf_frame(cmd, send_to_dsp_sn, send_to_dsp_addr, (uint8_t *)send_to_dsp_data_addr);
		}
	} while (i < READ_DSP_TUNE_RETRY);

	sprintf(output_buff, "(%d)%s%d FAIL", i, p_op_str, op_sn);
	PUT_DSP_TUNE_RESULT(gui_call_flag, output_buff);
	return 0;
}
