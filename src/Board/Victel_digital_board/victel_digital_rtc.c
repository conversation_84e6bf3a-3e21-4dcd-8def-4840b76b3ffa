/**
  ******************************************************************************
  *                Copyright (c) 2011, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    17-December-2012
  * @brief   This file provides
  *            - RTC operator
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stdio.h"
#include "victel_digital_board.h"
#include "victel_digital_keyboard.h"
#include "global_define.h"


/** @addtogroup STM32F2xx_StdPeriph_Examples
  * @{
  */

/** @addtogroup HW_Calendar
  * @{
  */

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Uncomment the corresponding line to select the RTC Clock source */
#define ERTC_CLOCK_SOURCE_LEXT			1/* select lext as the ertc clock */
//#define ERTC_CLOCK_SOURCE_LICK         /* select lick as the ertc clock */
#define ERTC_DONOT_ENABLE_INT			1

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
extern uint8_t operation_timeout_flag;
uint8_t mcu_running_mode = 0;

/*-----------------------------------------------------------------------*/
/* User defined function to give a current time to fatfs module          */
/* 31-25: Year(0-127 org.2000), 24-21: Month(1-12), 20-16: Day(1-31) */
/* 15-11: Hour(0-23), 10-5: Minute(0-59), 4-0: Second(0-29 *2) */
uint32_t compress_rtc_to_word (void)
{
	uint32_t date;
	uint8_t year, month, day, weekday;
	uint8_t hour, minute, second;

	RTC_GetTD(&year, &month, &day, &weekday, &hour, &minute, &second);	// rtc year: start at 2000
    second /= 2;			// 0-29 only

    date = 0;
	date = (year << 25) | (month << 21) | (day << 16) | (hour << 11) | (minute << 5) | second;

    return date;
}

void printf_time(void)
{
	uint8_t year, month, day, weekday;
	uint8_t hour, minute, second;

	RTC_GetTD(&year, &month, &day, &weekday, &hour, &minute, &second);
	printf("Current time: 20%0.2d-%0.2d-%0.2d(%d) %0.2d:%0.2d:%0.2d",
		year, month, day, weekday, hour, minute, second);
}

/**
  * @brief  Setup the time of RTC
  * @param  None
  * @retval None
  */
void rtc_date_regular(uint8_t year, uint8_t month, uint8_t day, uint8_t weekday)
{
//	if (ertc_date_set(year, month, day, weekday) != SUCCESS)
//		printf("ERTC Set Date failed(%02d-%02d-%02d)", year, month, day);
}

/**
  * @brief  Setup the time of RTC
  * @param  None
  * @retval None
  */
void rtc_time_regular(uint8_t hour, uint8_t min, uint8_t second)
{
//	if (ertc_time_set(hour, min, second, ERTC_AM) != SUCCESS)
//		printf("ERTC Set Time failed(%02d:%02d:%02d)", hour, min, second);
}

/**
  * @brief  Setup the time of RTC
  * @param  None
  * @retval None
  */
void RTC_GetTD(uint8_t *year, uint8_t *month, uint8_t *day, uint8_t *weekday, uint8_t *hour, uint8_t *minute, uint8_t *second)
{
	if (year)
	{
		*year = 25;
		*month = 1;
		*day = 13;
		*weekday = 1;
	}

	*hour = 16;
	*minute = 37;
	*second = 45;
}

void ertc_time_show(void)
{
}

void ertc_alarm_show(void)
{
}

/**
  * @brief  Configure the RTC peripheral by selecting the clock source.
  * @param  None
  * @retval None
  */
void ertc_config(void)
{
}


/***************************interrupt handler***************************/
/**
  * @brief  This function handles RTC Alarms interrupt request.
  * @param  None
  * @retval None
  */

void mcu_enter_sleep_mode(void)
{
	if (operation_timeout_flag && !phone_is_busy_now())
	{
		save_user_data_to_flash(0xae);
	}

	if ((operation_timeout_flag == 0xff) && (mcu_running_mode == 0))
	{
		mcu_sleep_prepare();
		mcu_running_mode = 1;
	}

	if (mcu_running_mode)
	{
		if (operation_timeout_flag != 0xff)
		{
			mcu_sleep_resume();
			mcu_running_mode = 0;
		}
	}
}

void mcu_enter_stop_mode(uint8_t flag)
{
	mcu_stop_prepare();

	mcu_stop_resume();
}


/**
  * @brief  rtc program
  * @param  None
  * @retval None
  */
void rtc_init(void)
{
}

