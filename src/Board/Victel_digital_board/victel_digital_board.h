/**
  **************************************************************************
  * @file     at32f435_437_board.c
  * @brief    header file for at-start board. set of firmware functions to
  *           manage leds and push-button. initialize delay function.
  **************************************************************************
  *                       Copyright notice & Disclaimer
  *
  * The software Board Support Package (BSP) that is made available to
  * download from Artery official website is the copyrighted work of Artery.
  * Artery authorizes customers to use, copy, and distribute the BSP
  * software and its related documentation for the purpose of design and
  * development in conjunction with Artery microcontrollers. Use of the
  * software is governed by this copyright notice and the following disclaimer.
  *
  * THIS SOFTWARE IS PROVIDED ON "AS IS" BASIS WITHOUT WARRANTIES,
  * GUARANTEES OR REPRESENTATIONS OF ANY KIND. ARTERY EXPRESSLY DISCLAIMS,
  * TO THE FULLEST EXTENT PERMITTED BY LAW, ALL EXPRESS, IMPLIED OR
  * STATUTORY OR OTHER WARRANTIES, GUARANTEES OR REPRESENTATIONS,
  * INCLUDING BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY,
  * FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT.
  *
  **************************************************************************
  */

#ifndef __VICTEL_DIGITAL_BOARD_H__
#define __VICTEL_DIGITAL_BOARD_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "stdio.h"
#include "stdint.h"

/** @addtogroup AT32F435_437_board
  * @{
  */

/** @addtogroup BOARD
  * @{
  */

/** @defgroup BOARD_pins_definition
  * @{
  */

/******************** define led ********************/
typedef enum
{
	LEDR									= 0,
	LEDG									= 1,
	LCD_POWER_CTRL_LINE						= 2,
	LCD_RESET_CTRL_LINE						= 3,
	LCD_DC_CTRL_LINE						= 4,
	BT_POWER_CTRL_LINE						= 5,
	GPS_POWER_CTRL_LINE						= 6,
	DEV_POWER_CTRL_LINE						= 7,
	SPI_FRAME_NOTIFY_LINE					= 8,
	AUDIO_PA_CTRL							= 9,
	EARPHONE_PA_CTRL						= 10,
	KEYBOARD_LIGHT							= 11,
	P2_BASE_BKIO							= 12
}led_type;

#define LED_NUM								13


/******************* define button *******************/
typedef enum
{
	PTT_BUTTON								= 0,
	BT_CONNECT_INDICATE						= 1,
	EAR_PHONE_DETECT						= 2,
	ALARM_BUTTON							= 3,
	DIR_CHK_BUTTON1							= 4,
	DIR_CHK_BUTTON2							= 5,
	POWER_ONOFF_BUTTON						= 6,
	KNOB_SURE								= 7,
	DSP_SLOT_INT							= 8,
	DSP_SLOT_MID							= 9,
	GPS_PPS									= 10
} button_type;

typedef enum
{
	BUTTON_MODE_GPIO = 0,
	BUTTON_MODE_EXTI = 1
} button_mode;

#define BUTTON_NUM							11


/**
  * @}
  */
#define set_led_on							set_gpio_high
#define set_led_off							set_gpio_low


/** @defgroup BOARD_exported_functions
  * @{
  */

/******************** functions ********************/
void set_button_interrupt(button_type Button, uint8_t enable);
void at32_button_init(button_type button);
uint8_t at32_button_state(button_type button);
void at32_led_init(led_type led);
void at32_pins_init(uint32_t pins);
void set_gpio_high(led_type led);
void set_gpio_low(led_type led);
void set_gpio_toggle(led_type led);

/* delay function */
void delay_init(void);
void sys_udelay(uint16_t us);
void sys_delay(uint16_t ms);
void sys_sdelay(uint16_t sec);

/* printf uart init function */
//void usart_configuration(uint8_t com, uint32_t baud_rate, usart_data_bit_num_type data_bit, usart_stop_bit_num_type stop_bit, uint8_t dma_mode);
void usart_configuration(uint8_t com, uint32_t baud_rate, uint8_t dma_mode);
void usart_set_baudrate(uint8_t com, uint32_t baud_rate);

/* led operation function */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif // __VICTEL_DIGITAL_BOARD_H__

