/**
  ******************************************************************************
  *                Copyright (c) 2011, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    07-December-2011
  * @brief   This file provides
  *            - USART operator
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stdarg.h"
#include "string.h"
#include "stdlib.h"
#include "stdint.h"
#include "stddef.h"
#include "stdio.h"
#include "math.h"
#include "unistd.h"
#include "pthread.h"
#include "victel_digital_board.h"
#include "victel_digital_usart.h"
#include "victel_digital_flash.h"
#include "victel_digital_lcd.h"
#include "victel_digital_spi_arm.h"
#include "victel_digital_xvmode.h"
#include "stack_config.h"
#include "global_define.h"
#include "vlog.h"
#include "drv.h"


#define BT_CONNECT_STEP_POWERDOWN			0
#define BT_CONNECT_STEP_POWERON				1
#define BT_CONNECT_STEP_ONLINE				2
#define BT_CONNECT_STEP_DEBUG				3
#define BT_CONNECT_STEP_DEBUG_OK			4
#define BT_CONNECT_STEP_CONNECT_OK			5
#define BT_CONNECT_STEP_RESTART				6

#define BT_CONNECT_READ_SCAN_LIST			10
#define BT_CONNECT_READ_SCAN_LIST_DONE		11
#define BT_CONNECT_CONNECTING				12
  #define BT_CONNECT_CONNNECT_DISCONNECTED	0
  #define BT_CONNECT_CONNNECT_STATE_OK		1
  #define BT_CONNECT_CONNNECT_STATE_GOING	2
  #define BT_CONNECT_CONNNECT_STATE_TIMEOUT	3

#define BT_CONNECT_CONNECT_FAIL				14
#define BT_CONNECT_DISCONNECTING			15
#define BT_CONNECT_DISCONNECT_DONE			16
#define BT_CONNECT_DISCONNECT_FAIL			17
#define BT_CONNECT_REBOOT_MODULE			18
#define BT_CONNECT_LINK_BACK_DEVICE			19
#define BT_CONNECT_LINK_BACK_CONNECTING		20

#define BT_CONNECT_STEP_UPGRADE_MODE		100

#define BT_MAC_SECTION_NUM					6

#define GPS_LINKED_CONFIRM()				(gps_state_linked >= GPS_STATE_LINKED_CONFIRM)

extern uint32_t timestamp_1s;
extern RUNTIME_PARAMETERS_TYPEDEF g_runtime_inst;
extern RUNTIME_PARAMETERS_XVBASE_TYPEDEF *g_runtime_inst_xvbase;
extern STATIC_PARAMETERS_TYPEDEF *g_static_ptr;
extern uint8_t interphone_mode;
extern GROUP_BOOKS *g_group_books;
extern uint32_t timestamp_kb_scan;
extern uint8_t zzw_distance_string[];
extern GUI_INTERACTIVE_TYPEDEF *g_gui_interactive;
extern uint8_t g_dial_number_index, g_dial_number[];



void process_command_line(void);
void uart_data_linkto_others(void);


//////////////////////// USART1/DEBUG ////////////////////////
uint8_t console_buffer[USART_BUFFER_LENGTH];				/* console I/O buffer */
uint8_t con_buffer[USART_BUFFER_LENGTH];					/* the round-robin buffer */
uint8_t con_read_index = 0, con_write_index = 0;
#define STACK_DUMP_TXDATA_BUFFER_LEN	1024
static uint8_t stack_dump_buffer_busy = 0, stack_dump_dma_data[STACK_DUMP_TXDATA_BUFFER_LEN];
static uint8_t enable_gps_data_out = 0/*bit7: 1-parsed data, 0-basic data; bit0: 1-enable*/, printf_redirection = UART_DEBUG_INDEX;/* 0: ITM; 1: USART3; else:NULL,DEBUG connect to DSP*/

//////////////////////// USART6/GPS ////////////////////////
#define USART6_DMA_LENGTH			32
static uint8_t gps_buffer[GPS_BUFFER_LENGTH];				/* gps data line */
static uint8_t gps_data_buffer0[USART6_DMA_LENGTH + 4];
static uint8_t gps_data_buffer1[USART6_DMA_LENGTH + 4];
static uint8_t gps_read_index = 0, gps_write_index = 0;
static uint8_t gps_data_buffer[USART_BUFFER_LENGTH];		/* the round-robin buffer */

uint8_t SET_GPS_MODE_ENABLE = 2;	// 0: disable; 1:fake;2-auto detected;3-real&UM220,4-real&�п�΢,5-real&̩��,6-real&GMTK
static uint8_t gps_sentence_locked = 0, gps_sys_setup_state = 0xff;	// 0xff: setup OK; gps_sentence_locked: bit7:another sentence locked state(must modify if USE_RMC_SENTENCE_TO_ANALYSIS_TIME); bit6-0:GGA locked char(absolutely)
static const uint8_t set_gps_sys_string[4][3][20] = {
	{"$CFGSYS,h01", "$CFGSYS,h10", "$CFGSYS,h11"},		// UM220: bit0:GPS L1; bit4:���� B1
	{"$PCAS04,1*18", "$PCAS04,2*1B", "$PCAS04,3*1A"},	// �п�΢
	{"$CCSIR,2,0*4a", "$CCSIR,1,0*49", "$CCSIR,3,0*4b"},// ̩��
	{"PMTK353,1,0,0,0,0", "PMTK353,0,0,0,0,1", "PMTK353,1,0,0,0,1"}	// GMTK
};
static const uint8_t gps_pattern_string[4][12] = {"Unicore"/*UM220*/, "CASIC"/*�п�΢*/, "Techtotop"/*̩��*/, "MTKGPS"/*GMTK*/};
static const uint8_t set_gps_baudrate_casic[3][15] = {"$PCAS01,1*1D"/*9600*/, /*"$PCAS01,2*1E"19200, */"$PCAS01,3*1F"/*38400*/, /*"$PCAS01,4*18"57600, */"$PCAS01,5*19"/*115200*/};
static const uint8_t set_gps_output_casic[2][29] = {"$PCAS03,1,1,1,1,1,1,1,1*02", "$PCAS03,1,1,1,0,1,1,1,1*03"};/*nGGA,nGLL,nGSA,nGSV,nRMC,nVTG,nZDA,nTXT*/
static const uint8_t set_gps_save_para_casic[13] = "$PCAS00*01";

//////////////////////// USART2/BT ////////////////////////
#define USART2_DMA_LENGTH			sizeof(BT_DMA_DATA_TYPEDEF)

#pragma pack(4)
static uint8_t bt_buffer[BT_BUFFER_LENGTH];					/* bt data line */
#pragma pack(4)
static uint8_t bt_data_buffer0[USART2_DMA_LENGTH + 4];
#pragma pack(4)
static uint8_t bt_data_buffer1[USART2_DMA_LENGTH + 4];

static uint8_t bt_data_buffer[USART_BUFFER_LENGTH];		/* the round-robin buffer */
static uint8_t bt_read_index = 0, bt_write_index = 0;

#define BT_TXDATA_BUFFER_LEN	1440
static uint8_t bt_tx_dma_data[BT_TXDATA_BUFFER_LEN], bt_txdata_busy = 0;

static uint8_t config_bt_counter = BT_CONNECT_STEP_POWERDOWN, bt_mac[BT_MAC_SECTION_NUM] = {0, 0, 0, 0, 0, 0};
const uint8_t bt_module_info[] = "RLM-B5N2101, BT5.0";

#define BT_DEVICE_MAX			50
#define BT_DEVICE_NAME_LEN		20
uint8_t bt_dev_num = 0, bt_dev_conn = 0, bt_dev_list[BT_DEVICE_MAX * BT_DEVICE_NAME_LEN];


uint8_t dump_to_can = 0, gps_state_linked = 0, rmt_gps_state = 0;
int16_t gps_height = 0;
GPS_STRUCT_TYPEDEF rmc_data;// , gga_vtg_data;
float calling_dev_position[4] = {0.0, 0.0, 0.0, 0.0};	// own longitude/latitude; rmt longitude/latitude
P_FUN_VOID_VOID p_dbgdata_process = process_command_line;

static const char prompt1[] = "Victel > ";
static const char prompt2[] = "Maintain > ";
static char *prompt = (char *)prompt1;
static const char erase_seq[] = "\b \b";			/* erase sequence	*/
static const char tab_seq[] = "        ";			/* used to expand TABs	*/


uint8_t get_uart_redirection(uint8_t which)
{
	switch (which)
	{
		case STAND_IO_INDEX:
//			if (printf_redirection == 0)
//				return ITM_DEBUG_INDEX;
//			else if (printf_redirection == 1)
//				return UART_DEBUG_INDEX;
//			else
//				return printf_redirection;
			return (printf_redirection == UART_BT_INDEX) ? UART_BT_INDEX : UART_DEBUG_INDEX;

		case UART_DEBUG_INDEX:
			return (printf_redirection >= UART_LINKTO_DSP_INDEX) ? ITM_DEBUG_INDEX : UART_DEBUG_INDEX;

		case UART_LINKTO_DSP_INDEX:
			return (printf_redirection == UART_LINKTO_DSP_INDEX) ? UART_DEBUG_INDEX : ITM_DEBUG_INDEX;

		case UART_LINKTO_SD_INDEX:
			return (printf_redirection == UART_LINKTO_SD_INDEX) ? UART_DEBUG_INDEX : ITM_DEBUG_INDEX;

		case UART_REAL_REMAP_VALUE:
			return printf_redirection;

		default:
			return which;
	}
}

void set_printf_redirection(uint8_t which)
{
	if (which < UART_REAL_REMAP_VALUE)
	{
		printf_redirection = which;
		p_dbgdata_process = (printf_redirection >= UART_LINKTO_SD_INDEX) ? uart_data_linkto_others : process_command_line;
	}
}

int put_data(uint8_t uart_index, uint8_t *dat, uint16_t len)
{
	int fd;
    uint8_t idx = get_uart_redirection(uart_index);

    if (idx == UART_BT_INDEX) {
        fd = g_bt_serial_fd;
    } else if (idx == UART_GPS_INDEX) {
        fd = g_gps_serial_fd;
    } else if (idx == UART_DEBUG_INDEX) {
        fd = g_debug_serial_fd;
    } else {
        return 0;
    }

    if (fd < 0) {
        return 0;
    }

	return write(fd, dat, len);
/*
	int i = 0;
	uint8_t idx;

	idx = get_uart_redirection(uart_index);
	while (i < len)
	{
		while(usart_flag_get(COM_USART[idx], USART_TDBE_FLAG) == RESET)
			;
		usart_data_transmit(COM_USART[idx], (uint16_t)dat[i++]);
	}

	return i;
*/
}

int getch_noblock(uint8_t uart_index, uint8_t *ch)
{
	uint8_t idx = get_uart_redirection(uart_index);

	if (idx == UART_DEBUG_INDEX)
	{
		if (con_write_index == con_read_index)
		{
			return 0;
		}
		else
		{
			*ch = con_buffer[con_read_index++];
			return 1;
		}
	}
	else if (idx == UART_BT_INDEX)
	{
		if (bt_write_index == bt_read_index)
		{
			return 0;
		}
		else
		{
			*ch = bt_data_buffer[bt_read_index++];
			return 1;
		}
	}
	else if (idx == UART_GPS_INDEX)
	{
		if (gps_write_index == gps_read_index)
		{
			return 0;
		}
		else
		{
			*ch = gps_data_buffer[gps_read_index++];
			return 1;
		}
	}
	else
	{
		return 0;
	}
}

int putch_noblock(uint8_t uart_index, uint8_t ch)
{
	return 0;
}
/*
void debug_printf(char *str, ...)
{
	char buf[1024];
	va_list ptr;

	va_start(ptr, str);
	vsprintf(buf, str, ptr);
	put_data(UART_DEBUG_INDEX, (uint8_t *)buf, strlen(buf));
}
*/
void set_prompt(uint8_t flag)
{
	if (flag == 0)
		prompt = (char *)prompt2;
	else
		prompt = (char *)prompt1;
}

void print_prompt(void)
{
	putstr(prompt);
}

// �����������???��������ʽ�̶���ʹ��sscanf����
#define GPS_SECTION_NOT_BREAK(c)		((c) && ((c) != ',') && ((c) != ' '))
int parse_line (uint8_t *line, uint8_t *argv[], uint8_t n_arg, uint16_t num)
{
	int nargs = 0, n_args;
	uint32_t bak_addr_line = (uint32_t)line;

	if ((n_arg <= 0) || (n_arg > MAX_GPS_ARGS))
		n_args = MAX_GPS_ARGS;
	else
		n_args = n_arg;

	while (nargs < n_args)
	{
		/* skip any white space */
		while ((*line == ' ') || (*line == '\t'))
			++line;

		if ((*line == '\0') || (((uint32_t)line - bak_addr_line) > num))		/* end of line, no more args */
		{
			argv[nargs] = NULL;
			return (nargs);
		}

		argv[nargs++] = line;	/* begin of argument string	*/
		if (n_arg && (nargs == n_args))	/* All the remaining characters as a parameter */
			break;

		/* find end of string */
		while (GPS_SECTION_NOT_BREAK(*line))
			++line;

		if ((*line == '\0') || (((uint32_t)line - bak_addr_line) > num))		/* end of line, no more args */
		{
			argv[nargs] = NULL;
			return (nargs);
		}

		*line++ = '\0';			/* terminate current arg */
	}

//	if (nargs >= MAX_GPS_ARGS)	// should never appear
//		printf ("** Too many args (max=%d) **", MAX_GPS_ARGS);

	return (nargs);
}

int readline_into_buffer(void)
{
	return 0;
}

uint8_t get_gps_link_state(void)
{
	return GPS_LINKED_CONFIRM() ? 1 : 0;
}

void set_gps_link_state(uint8_t new_state)
{
	if (dev_is_fixed_gps() == 0)
	{
		gps_state_linked = new_state;
		rmc_data.gps_state &= ~GPS_DATA_VALID_FLAG;
		rmc_data.gps_state |= new_state ? GPS_DATA_VALID_FLAG : 0;
	}
}

uint8_t get_bt_link_state(void)
{
	return (config_bt_counter == BT_CONNECT_STEP_CONNECT_OK) ? 1 : 0;
}

void set_latitude_data(uint8_t *str, GPS_STRUCT_TYPEDEF *dat)
{
	if (str[0])
	{
		dat->lat_degree =  (str[0] - '0') * 10 + str[1] - '0';
		dat->lat_integer = (str[2] - '0') * 10 + str[3] - '0';
		dat->lat_decimal = (str[5] - '0') * 1000 + (str[6] - '0') * 100 + (str[7] - '0') * 10 + str[8] - '0';
	}
}

void set_longitude_data(uint8_t *str, GPS_STRUCT_TYPEDEF *dat)
{
	if (str[0])
	{
		dat->lon_degree =  (str[0] - '0') * 100 + (str[1] - '0') * 10 + str[2] - '0';
		dat->lon_integer = (str[3] - '0') * 10 + str[4] - '0';
		dat->lon_decimal = (str[6] - '0') * 1000 + (str[7] - '0') * 100 + (str[8] - '0') * 10 + str[9] - '0';
	}
}

void set_utc_time(uint8_t *str, GPS_STRUCT_TYPEDEF *dat)
{
	if (str[0])
	{
		dat->utc_hour =   (str[0] - '0') * 10 + str[1] - '0';
		dat->utc_minute = (str[2] - '0') * 10 + str[3] - '0';
		dat->utc_second = (str[4] - '0') * 10 + str[5] - '0';
#if DEBUG_GPS_SYNC_PPS > 2
		vlog_i("usart","\ttime:%d:%d:%d", dat->utc_hour, dat->utc_minute, dat->utc_second);
#endif
	}
}

void cal_pos_float_to_degree(double pos_float, uint8_t *deg, uint8_t *intr, uint16_t *dec, uint8_t flag)	// flag: 0-��/��/�룻1-��/������/��С��
{
	double pos_rem;

	*deg = (uint8_t)pos_float;
	pos_rem = (pos_float - *deg) * 60;
	*intr = (uint8_t)(pos_rem);
	*dec = (uint16_t)((pos_rem - *intr) * (flag ? DEC_ACCURACY : 60));
}

void gps_float_to_deg_int_dec(GPS_STRUCT_TYPEDEF *obj, double lon, double lat)
{
	cal_pos_float_to_degree(lon, &obj->lon_degree, &obj->lon_integer, &obj->lon_decimal, 1);
	cal_pos_float_to_degree(lat, &obj->lat_degree, &obj->lat_integer, &obj->lat_decimal, 1);
//	vlog_i("usart","ORI=%3.6f��%3.6f��,TRANS=%3.6f��%3"r\n", lon, lat,
//		CAL_POS_FLOAT(obj->lon_degree, obj->lon_integer, obj->lon_decimal),
//		CAL_POS_FLOAT(obj->lat_degree, obj->lat_integer, obj->lat_decimal));
}


#if 0
% ������һ��������׼�������壬���ĳ���뾶�?6378.140ǧ�ף����뾶Ϊ 6356.755ǧ�ף�ƽ���뾶6371.004ǧ��
% ������Ǽ��������һ�����������壬��ô���İ뾶���ǵ����ƽ���뾶����ΪR
% ����ȡ���ȵ���ֵ(Longitude)������ȡ���ȸ�ֵ(-Longitude)����γȡ90-γ��ֵ(90- Latitude)����γȡ90+γ��ֵ(90+Latitude)
% ���һ��A�ľ�γ��Ϊ(LonA, LatA)���ڶ���B�ľ�γ��Ϊ(LonB, LatB),�򾭹�����������������㱻���?(MLonA, MLatA)��(MLonB, MLatB)
% ��ô���������Ƶ������Եõ����������������¹�ʽ��
% C = sin(MLatA)*sin(MLatB)*cos(MLonA-MLonB) + cos(MLatA)*cos(MLatB)
% Distance = R*Arccos(C)*Pi/180

function Dis = Distance(ALong, ALat, BLong, BLat)
% ALong, ALat, BLong, BLat�ֱ�ΪA��B����ľ�γ�ȣ��������������ϸ�����λ�Ƕ�???
% �������㷵��A��B�������������???

A.Long = ALong*pi/180;      % ת��Ϊ���ȵ�λ
A.Lat = (90-ALat)*pi/180;   % ת��Ϊ���ȵ�λ
B.Long = BLong*pi/180;      % ת��Ϊ���ȵ�λ
B.Lat = (90-BLat)*pi/180;   % ת��Ϊ���ȵ�λ

% �������???
R = 6371.004; % ����ƽ���뾶
C = sin(A.Lat)*sin(B.Lat)*cos(A.Long-B.Long) + cos(A.Lat)*cos(B.Lat);
Dis = R*acos(C)*1000;

% end %


public static double getDistanceFromXtoY(double lat_a, double lng_a,
   double lat_b, double lng_b)
 {
  double pk = (double) (180 / 3.14169);

  double a1 = lat_a / pk;
  double a2 = lng_a / pk;
  double b1 = lat_b / pk;
  double b2 = lng_b / pk;

  double t1 = Math.cos(a1) * Math.cos(a2) * Math.cos(b1) * Math.cos(b2);
  double t2 = Math.cos(a1) * Math.sin(a2) * Math.cos(b1) * Math.sin(b2);
  double t3 = Math.sin(a1) * Math.sin(b1);
  double tt = Math.acos(t1 + t2 + t3);

  return 6366000 * tt;
 }
#endif

const uint8_t distance_pattern[2][8][7] = {
		{"��", "��", "��", "��", "��λ:", "δ֪", "M", "KM"},
		{"E.", "W.", "S.", "N.", "Dist:", "xxxx", "m", "km"}
};

double get_nearest_with_reference(double ref, double f1, double f2, double f3, uint8_t n)
{
	double ret, f_diff0, f_diff1;

	f_diff0 = fabs(ref - f1);
	f_diff1 = fabs(ref - f2);
	if (f_diff0 < f_diff1)
	{
		if (n == 2)
		{
			ret = f1;
		}
		else
		{
			if (f_diff0 < fabs(ref - f3))
				ret = f1;
			else
				ret = f3;
		}
	}
	else
	{
		if (n == 2)
		{
			ret = f2;
		}
		else
		{
			if (f_diff1 < fabs(ref - f3))
				ret = f2;
			else
				ret = f3;
		}
	}

	return ret;
}

double cal_earth_arc_distance(double lat1, double lon1, double lat2, double lon2)
{
	double radLat1 = DEGREE2RADIAN(lat1), radLat2 = DEGREE2RADIAN(lat2);
	double a = radLat1 - radLat2, b = DEGREE2RADIAN(lon1) - DEGREE2RADIAN(lon2);

	return 2 * asin(sqrt(pow(sin(a / 2), 2) + cos(radLat1) * cos(radLat2) * pow(sin(b / 2), 2))) * EARTH_RADIUS * 1000;
}

void cal_full_lon_lat(GPS_STRUCT_TYPEDEF *own, GPS_STRUCT_TYPEDEF *b, double *lon_1, double *lon_2, double *lat_1, double *lat_2)
{
	double f_tmp;
	uint32_t degree;
	uint8_t lat_degree_rem, lon_degree_rem;

	if (b->gps_state & GPS_DATA_IS_VICTEL4U)
	{
		lat_degree_rem = 8;
		lon_degree_rem = 16;
	}
	else
	{
		lat_degree_rem = 4;
		lon_degree_rem = 8;
	}

	if (b->gps_state & GPS_DATA_IS_FULL)
	{
		*lat_1 = CAL_POS_FLOAT(own->lat_degree, own->lat_integer, own->lat_decimal);
		*lat_2 = CAL_POS_FLOAT(b->lat_degree, b->lat_integer, b->lat_decimal);
		if ((own->gps_state & LATITUDE_NORTH_FLAG) == 0)
			*lat_1 *= -1.0;
		if ((b->gps_state & LATITUDE_NORTH_FLAG) == 0)
			*lat_2 *= -1.0;

		*lon_1 = CAL_POS_FLOAT(own->lon_degree, own->lon_integer, own->lon_decimal);
		*lon_2 = CAL_POS_FLOAT(b->lon_degree, b->lon_integer, b->lon_decimal);
	}
	else
	{
		*lat_2 = CAL_POS_FLOAT(own->lat_degree, own->lat_integer, own->lat_decimal);
		if (own->gps_state & LATITUDE_NORTH_FLAG)
		{
			degree = 90 + own->lat_degree;
			*lat_1 = *lat_2;
		}
		else
		{
			degree = 90 - own->lat_degree;
			*lat_1 = *lat_2 * -1.0;
		}
		// cal remote device's latitude
		*lat_2 = *lat_2 - own->lat_degree + degree;	// for compare with f_tmp
		degree = degree - (degree % lat_degree_rem) + b->lat_degree;
		f_tmp = CAL_POS_FLOAT(degree, b->lat_integer, b->lat_decimal);
		*lat_2 = get_nearest_with_reference(*lat_2, f_tmp, f_tmp + lat_degree_rem, f_tmp - lat_degree_rem, 3);
		if (*lat_2 < 90.0)
			*lat_2 = (90 - *lat_2) * -1.0;
		else
			*lat_2 = *lat_2 - 90;

		*lon_1 = CAL_POS_FLOAT(own->lon_degree, own->lon_integer, own->lon_decimal);
		// cal remote device's longitude
		if ((own->gps_state & LONGITUDE_EAST_FLAG) == (b->gps_state & LONGITUDE_EAST_FLAG)) 	// ͬΪ����������
		{
			degree = own->lon_degree - (own->lon_degree % lon_degree_rem) + b->lon_degree;
			f_tmp = CAL_POS_FLOAT(degree, b->lon_integer, b->lon_decimal);
			*lon_2 = get_nearest_with_reference(*lon_1, f_tmp, f_tmp + lon_degree_rem, f_tmp - lon_degree_rem, (degree < lon_degree_rem) ? 2 : 3);
		}
		else
		{
			if (own->lon_degree < lon_degree_rem)				// ��0����
			{
				*lon_2 = CAL_POS_FLOAT(b->lon_degree, b->lon_integer, b->lon_decimal);
			}
//			else if (own->lon_degree > (180 - lon_degree_rem))	// ��180����
			else
			{
				degree = 180 + b->lon_degree - (180 % lon_degree_rem);
				if ((degree < 180 - lon_degree_rem) || (degree > 180))
					degree -= lon_degree_rem;
				*lon_2 = CAL_POS_FLOAT(degree, b->lon_integer, b->lon_decimal);
			}
		}
	}

	if ((own->gps_state & LONGITUDE_EAST_FLAG) == 0)
		*lon_1 *= -1.0;
	if ((b->gps_state & LONGITUDE_EAST_FLAG) == 0)
		*lon_2 *= -1.0;
}

float cal_angle_from_coordinate(double lat1, double lon1, double lat2, double lon2)
{
	double radLat1 = DEGREE2RADIAN(lat1), radLat2 = DEGREE2RADIAN(lat2);
	double radLon1 = DEGREE2RADIAN(lon1), radLon2 = DEGREE2RADIAN(lon2);

	double dLon = (radLon2 - radLon1);
	double y = sin(dLon) * cos(radLat2);
	double x = cos(radLat1) * sin(radLat2) - sin(radLat1) * cos(radLat2) * cos(dLon);
	double brng = atan2(y, x);

	brng = RADIAN2DEGREE(brng);
//	brng = (brng + 360) % 360;
//	brng = 360 - brng;			// count degrees counter-clockwise - remove to make clockwise
//	return brng;
	return brng < 0.0 ? (360 + brng) : brng;
}

uint32_t cal_2points_distance(GPS_STRUCT_TYPEDEF *own, GPS_STRUCT_TYPEDEF *b, uint8_t *str)
{
	double lon_1, lon_2, lat_1, lat_2, f_tmp;
	uint32_t degree;
	uint8_t lang_type;

	lang_type = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0;
	rmt_gps_state = b->gps_state;

//	if ((own->gps_state & GPS_DATA_VALID_FLAG) && (b->gps_state & GPS_DATA_VALID_FLAG))
	if ((own->lat_degree || own->lat_integer || own->lat_decimal || own->lon_degree || own->lon_integer || own->lon_decimal) &&
		(/*b->lat_degree || */b->lat_integer || b->lat_decimal || /*b->lon_degree || */b->lon_integer || b->lon_decimal))
	{
		cal_full_lon_lat(own, b, &lon_1, &lon_2, &lat_1, &lat_2);

		calling_dev_position[2] = (float)lon_2;
		calling_dev_position[3] = (float)lat_2;

		f_tmp = cal_earth_arc_distance(lat_1, lon_1, lat_2, lon_2);				// Cal with new algorithm=217074.875465(����:����217KM),time=700us
//		f_tmp = cal_earth_arc_distance_original(lat_1, lon_1, lat_2, lon_2);	// Cal with old algorithm new restore=217074.875465(����:����217KM),time=790us
		degree = (uint32_t)f_tmp;

		if (str)
		{
			if (g_runtime_inst.runtime_paras.misc_runtime_config.distance_mode)
			{
				f_tmp = cal_angle_from_coordinate(lat_1, lon_1, lat_2, lon_2);
				sprintf((char *)str, "%s%03d ", distance_pattern[lang_type][4], (uint16_t)f_tmp);  // �á�ʱ����strҪ��10����󳤶��?15��������ʾ��ȫ��
			}
			else
			{
				p_strcpy(str, distance_pattern[lang_type][4]);
				p_strcat(str, distance_pattern[lang_type][(lon_1 < lon_2) ? 0 : 1]);
				p_strcat(str, distance_pattern[lang_type][(lat_1 < lat_2) ? 3 : 2]);
			}

			if (degree < 1000)
				sprintf((char *)(str + 9), "%d%s", degree, distance_pattern[lang_type][6]);
			else if (degree < 10000)
				sprintf((char *)(str + 9), "%d.%d%s", degree / 1000, degree / 100 - (degree / 1000) * 10, distance_pattern[lang_type][7]);
			else
				sprintf((char *)(str + 9), "%d%s", degree / 1000, distance_pattern[lang_type][7]);
		}

	}
	else
	{
		degree = 0xffffffff;
		if (str)
		{
			p_strcpy(str, distance_pattern[lang_type][4]);
			p_strcat(str, distance_pattern[lang_type][5]);
		}
	}

	return degree;
}

static uint8_t unify_utc_regulator = 8;
void init_unify_utc_regulator(void)
{
	uint8_t utc_set = g_runtime_inst.runtime_paras.utc_regulator & 0x0f;

	if (g_runtime_inst.runtime_paras.utc_regulator & 0x80)
	{
		if (utc_set <= 12)
		{
			if (g_runtime_inst.runtime_paras.utc_regulator & 0x10)	// !0: western time zone
				unify_utc_regulator = 24 - (g_runtime_inst.runtime_paras.utc_regulator & 0x0f);
			else
				unify_utc_regulator = g_runtime_inst.runtime_paras.utc_regulator & 0x0f;
		}
		else
		{
			unify_utc_regulator = 0;
		}
	}
	else
	{
		unify_utc_regulator = 8;
	}
}

uint8_t get_local_hour_with_utc(uint8_t utc_hour)
{
	return (utc_hour + unify_utc_regulator) % 24;
}

#define IS_LEAP_YEAE(year)	(((year % 4 == 0) && (year % 100 != 0)) || (year % 400 == 0))
static const uint8_t days_of_month[13] = {7, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};	// unit 0: weekday of 20120101
uint8_t get_weekday_with_date(uint8_t year, uint8_t month, uint8_t day)
{
	uint8_t i, weekday;
	uint16_t y2000, days = 0;

	for (i = 12; i < 99; i++)
	{
		if (i < year)
		{
			y2000 = i + 2000;
			days += IS_LEAP_YEAE(y2000) ? 366 : 365;
		}
		else
		{
			break;
		}
	}

	for (i = 1; i < 13; i++)
	{
		if (i < month)
			days += days_of_month[i];
		else
			break;
	}

	y2000 = year + 2000;
	if ((month > 2) && IS_LEAP_YEAE(y2000))
		days++;

	days += day - 1;

	i = days % 7;
	if (i)
		weekday = i;
	else
		weekday = days_of_month[0];

	return weekday;
}

void set_real_time_with_gps(uint8_t *time, uint8_t *date, GPS_STRUCT_TYPEDEF *dat)
{
	uint8_t year, month, day, weekday;

	if (time[0] && date[0])
	{
//		RTC_GetTD(&year, &month, &day, &weekday, &dat->utc_hour, &dat->utc_minute, &dat->utc_second);
		set_utc_time(time, dat);
		day =   (date[0] - '0') * 10 + date[1] - '0';
		month = (date[2] - '0') * 10 + date[3] - '0';
		year = (date[4] - '0') * 10 + date[5] - '0';

		weekday = get_weekday_with_date(year, month, day);

		update_time_force();
#if DEBUG_GPS_SYNC_PPS > 2
		vlog_i("usart","\tD/T:%d-%d-%d(%d),%d:%d:%d", year, month, day, weekday, dat->utc_hour, dat->utc_minute, dat->utc_second);
#endif
	}
}

void set_speed_state(uint8_t link_flag, uint8_t *str, uint8_t lon_flag, uint8_t lat_flag, GPS_STRUCT_TYPEDEF *dat)
{
	if (link_flag)
	{
		dat->gps_state = GPS_DATA_VALID_FLAG;		// link ok
		if (lon_flag == 'E')
			dat->gps_state |= LONGITUDE_EAST_FLAG;
		if (lat_flag == 'N')
			dat->gps_state |= LATITUDE_NORTH_FLAG;
	}
	else
	{
		dat->gps_state &= ~GPS_DATA_VALID_FLAG;
	}

	if (str)
	{
		dat->speed = (uint8_t)get_integer_from_string(str);
#if DEBUG_GPS_SYNC_PPS > 2
		vlog_i("usart","\tspeed:%d(%s)", dat->speed, (char *)str);
#endif
	}
}

#define USE_GGA_PARSE_SATELLITE_NUMBER		1

uint8_t n_satellite_gps = 0;
#ifndef USE_GGA_PARSE_SATELLITE_NUMBER
uint8_t n_satellite_bd = 0;				// use GSV to parse
#endif
uint8_t get_satellite_number(void)
{
	uint8_t type = maintain_setup_parameter(PARA_OPERATE_READ, GPS_TYPE_PARAS_POS, 0);

#ifndef USE_GGA_PARSE_SATELLITE_NUMBER
	if (type == BAR_TYPE_SATELLITE_GPS)
		return n_satellite_gps;
	else if (type == BAR_TYPE_SATELLITE_BD)
		return n_satellite_bd;
	else
		return n_satellite_gps + n_satellite_bd;
#else
	return n_satellite_gps;
#endif
}

void set_satellite_number(GPS_STRUCT_TYPEDEF *dat)
{
	uint8_t n_satellite = get_satellite_number();

	dat->gps_state &= 0x0f;
	dat->gps_state |= (n_satellite >= 15) ? 0xf0 : (n_satellite << 4);
}

void set_gps_data_output(uint8_t flag)
{
	if (flag == 0xff)
		enable_gps_data_out = (enable_gps_data_out + 1) & 0x81;
	else if (flag <= 1)
		enable_gps_data_out = (enable_gps_data_out & 0x80) | flag;
	else if (flag == 2)
		enable_gps_data_out += 0x80;
	else if (flag == 3)
		enable_gps_data_out = ((enable_gps_data_out & 0x01) ? (enable_gps_data_out & 0x80) : ((enable_gps_data_out & 0x80) ? 0 : 0x80)) | ((enable_gps_data_out & 0x01) ? 0 : 0x01);
	else if (flag == 8)
		toggle_show_gps_information_mode();
	else if (flag == 9)
		set_show_time_rssi_mode(0xff);
}

void print_gps_parse_data(uint8_t *data)
{
	uint8_t tmp[7][MAX_USER_NAME_LEN * 2];

	if ((enable_gps_data_out == 0x01) && data)					// judge data: peroidic_refresh should call it by data==0
	{
		vlog_i("usart","%s", data);
	}
	else if ((enable_gps_data_out == 0x81) && (data == 0))		// judge data for eliminating multi print(GPS data should be output multi line)
	{
		if (rmc_data.gps_state & GPS_DATA_VALID_FLAG)
		{
			parse_gps_data_to_string(1, tmp);
			vlog_i("usart","[Locked]%s, %s%s, %s%s, Speed=%sKM/h, Satellite=%s", tmp[0], tmp[1], tmp[2], tmp[3], tmp[4], tmp[5], tmp[6]);
		}
		else
		{
			vlog_i("usart","==== GPS Not locked ===");
		}
	}
}

void update_gps_work_mode(void)	// 1-gps only; 2-bd only; else-gps&bd
{
	if (SET_GPS_MODE_ENABLE <= 2)
		gps_sys_setup_state = 0xff;
	else
		gps_sys_setup_state = 0;
}

uint8_t config_gps_sentence_output(uint8_t force, uint8_t flag)				// 0-all on; 1-disable some
{
	if (force || (SET_GPS_MODE_ENABLE == 4))
	{
		put_data(UART_GPS_INDEX, (uint8_t *)set_gps_output_casic[flag & 0x01], p_strlen(set_gps_output_casic[flag & 0x01]));
		return 1;
	}

	return 0;
}

#ifdef DEBUG_GPS_SYNC_PPS
extern uint32_t timestamp_gps_2pps;
#endif

//#define USE_RMC_SENTENCE_TO_ANALYSIS_TIME	1

#define RMC_ORDER_UTC_TIME					1
#define RMC_ORDER_VALID_FLAG				2
#define RMC_ORDER_LATITUDE					3
#define RMC_ORDER_LATITUDE_HEMISPHERE		4
#define RMC_ORDER_LONGITUDE					5
#define RMC_ORDER_LONGITUDE_HEMISPHERE		6
#define RMC_ORDER_SPEED						7
#define RMC_ORDER_UTC_DATE					9

#define GGA_ORDER_UTC_TIME					1
#define GGA_ORDER_VALID_FLAG				6
#define GGA_ORDER_LATITUDE					2
#define GGA_ORDER_LATITUDE_HEMISPHERE		3
#define GGA_ORDER_LONGITUDE					4
#define GGA_ORDER_LONGITUDE_HEMISPHERE		5
#define GGA_ORDER_SATELLITE_USED_NUM		7
#define GGA_ORDER_HEIGHT					9

#define IS_RMC_CHAR_VALID(c)				(c == 'A')
#define IS_GGA_CHAR_VALID(c)				((c == '1') || (c == '2'))
#define SIMPLE_COPY_GPS_STRING_LEN			6

#define GPS_SENTENCE_LOCKED_FLAG			0x80


#ifdef USE_RMC_SENTENCE_TO_ANALYSIS_TIME

  #define PARSE_ORDER_UTC_TIME				RMC_ORDER_UTC_TIME
  #define PARSE_ORDER_VALID_FLAG		  	RMC_ORDER_VALID_FLAG
	#define IS_GPS_SENTENCE_VALID(c)		IS_RMC_CHAR_VALID(c)
  #define PARSE_ORDER_LATITUDE				RMC_ORDER_LATITUDE
  #define PARSE_ORDER_LATITUDE_HEMISPHERE	RMC_ORDER_LATITUDE_HEMISPHERE
  #define PARSE_ORDER_LONGITUDE			 	RMC_ORDER_LONGITUDE
  #define PARSE_ORDER_LONGITUDE_HEMISPHERE	RMC_ORDER_LONGITUDE_HEMISPHERE

  #define GPS_SPEED_STRING_DEFINE(ptr)		ptr[RMC_ORDER_SPEED]
  #define GPS_UTC_DATE_STRING_DEFINE(ptr)	ptr[RMC_ORDER_UTC_DATE]

#else

  #define PARSE_ORDER_UTC_TIME			  	GGA_ORDER_UTC_TIME
  #define PARSE_ORDER_VALID_FLAG			GGA_ORDER_VALID_FLAG
	#define IS_GPS_SENTENCE_VALID(c)		IS_GGA_CHAR_VALID(c)
  #define PARSE_ORDER_LATITUDE				GGA_ORDER_LATITUDE
  #define PARSE_ORDER_LATITUDE_HEMISPHERE	GGA_ORDER_LATITUDE_HEMISPHERE
  #define PARSE_ORDER_LONGITUDE				GGA_ORDER_LONGITUDE
  #define PARSE_ORDER_LONGITUDE_HEMISPHERE  GGA_ORDER_LONGITUDE_HEMISPHERE

  static uint8_t gps_sentence_speed_string[SIMPLE_COPY_GPS_STRING_LEN + 1] = "0.0000";
  static uint8_t gps_sentence_date_string[SIMPLE_COPY_GPS_STRING_LEN + 1] = "170121";

  #define GPS_SPEED_STRING_DEFINE(ptr)		gps_sentence_speed_string
  #define GPS_UTC_DATE_STRING_DEFINE(ptr)	gps_sentence_date_string

#endif

#define IS_RMC_SENTENCE_VALID(para, para_num)		para[RMC_ORDER_VALID_FLAG] && IS_RMC_CHAR_VALID(*para[RMC_ORDER_VALID_FLAG])
#define IS_GGA_SENTENCE_VALID(para, para_num)		para[GGA_ORDER_VALID_FLAG] && IS_GGA_CHAR_VALID(*para[GGA_ORDER_VALID_FLAG])
#define IS_RMC_GGA_SENTENCE_VALID(para, para_num)	para[PARSE_ORDER_VALID_FLAG] && IS_GPS_SENTENCE_VALID(*para[PARSE_ORDER_VALID_FLAG])

#ifdef DEBUG_GPS_WINDOW
extern uint8_t zzwpro_marked_slot;
extern uint16_t gps_minute_second;
extern uint16_t gps_time_pluse_1ms;
#endif
void start_to_calibrate_38p4(void);
void set_coor_time_from_gps_data(uint8_t *para[], uint8_t para_num)
{
	if ((IS_RMC_GGA_SENTENCE_VALID(para, para_num)) && (gps_sentence_locked & GPS_SENTENCE_LOCKED_FLAG))
	{
		if (GPS_LINKED_CONFIRM())
		{
			set_latitude_data(para[PARSE_ORDER_LATITUDE], &rmc_data);
			set_longitude_data(para[PARSE_ORDER_LONGITUDE], &rmc_data);
			calling_dev_position[0] = (float)CAL_POS_FLOAT(rmc_data.lon_degree, rmc_data.lon_integer, rmc_data.lon_decimal);
			calling_dev_position[1] = (float)CAL_POS_FLOAT(rmc_data.lat_degree, rmc_data.lat_integer, rmc_data.lat_decimal);
			set_utc_time(para[PARSE_ORDER_UTC_TIME], &rmc_data);
#if DEBUG_GPS_SYNC_PPS == 1 || DEBUG_GPS_SYNC_PPS == 2
			vlog_i("usart","\t%d:%d-%d", rmc_data.utc_minute, rmc_data.utc_second, get_measure_timer_difference(get_timestamp_measure(), timestamp_gps_2pps));
#endif
#ifndef USE_RMC_SENTENCE_TO_ANALYSIS_TIME
			gps_height = get_integer_from_string(para[GGA_ORDER_HEIGHT]);
			n_satellite_gps = get_integer_from_string(para[GGA_ORDER_SATELLITE_USED_NUM]);
			g_gui_interactive->dev_misc_notify2.locked_num = (n_satellite_gps < 31) ? n_satellite_gps : 31;
  #if DEBUG_GPS_SYNC_PPS > 2
			vlog_i("usart","\theight:%d,sate=%d", gps_height, n_satellite_gps);
  #endif
#endif
#ifdef DEBUG_GPS_WINDOW
			vlog_i("usart","\tgga=%d(%d:%d)(%d/%d)", get_measure_timer_difference(get_timestamp_measure(), gps_time_pluse_1ms),
				rmc_data.utc_minute, rmc_data.utc_second, zzwpro_marked_slot, gps_minute_second);
#endif
			set_speed_state(1, GPS_SPEED_STRING_DEFINE(para), para[PARSE_ORDER_LONGITUDE_HEMISPHERE][0], para[PARSE_ORDER_LATITUDE_HEMISPHERE][0], &rmc_data);
			if ((rmc_data.gps_state & LONGITUDE_EAST_FLAG) == 0)
				calling_dev_position[0] *= -1.0;
			if ((rmc_data.gps_state & LATITUDE_NORTH_FLAG) == 0)
				calling_dev_position[1] *= -1.0;
			set_sync_call_stack_type(SYNC_CALL_STACK_GPS_UPDATE);
		}
		else
		{
			gps_state_linked++;
			if (gps_state_linked == GPS_STATE_LINKED_CONFIRM)
			{
#ifndef CHECK_PPS_WITH_CRYSTAL_38P4M
				set_gps_pps_state(1);
#endif
				set_real_time_with_gps(para[PARSE_ORDER_UTC_TIME], GPS_UTC_DATE_STRING_DEFINE(para), &rmc_data);
				if ((interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) && dev_have_base_feature())
					send_v_data_with_message_format_handle(SEND_V_MESSAGE_SEND_NOW | SEND_V_MESSAGE_BASE);

				set_dsp_sync_at_next_second(1);
				start_to_calibrate_38p4();
			}
		}
	}
	else
	{
		if (gps_state_linked)
		{
			if (--gps_state_linked == 0)
			{
				set_dsp_sync_at_next_second(2);
				n_satellite_gps = 0;
				g_gui_interactive->dev_misc_notify2.locked_num = 0;
				vlog_i("usart","\tGPS unlocked(%d)", dev_is_fixed_gps());
#ifdef UPDATE_FIXED_GPS_WHEN_LOCKED
				if (dev_is_fixed_gps())
					set_fixed_gps_to_rmcdata(0);
				else
#endif
				set_speed_state(0, 0, 0, 0, &rmc_data);
				set_sync_call_stack_type(SYNC_CALL_STACK_GPS_UPDATE);
				if ((interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) && dev_have_base_feature())
					send_v_data_with_message_format_handle(SEND_V_MESSAGE_SEND_NOW | SEND_V_MESSAGE_BASE);
			}
		}

		set_gps_pps_state(0); 	// pps state should NOT be set to fail when PPS invalid before RMC/GGA sentence unlock
	}
}

// date: fixed 6B; height&speed: use integer only now, so can copy 6B(large the real max value)
#define copy_gps_section_to_string(str, gps)		memcpy((uint8_t *)str, (uint8_t *)gps, SIMPLE_COPY_GPS_STRING_LEN)
#ifndef copy_gps_section_to_string
void copy_gps_section_to_string(uint8_t *str, uint8_t *gps)
{
	uint16_t i = 0;

	while(GPS_SECTION_NOT_BREAK(gps[i]))
	{
		str[i] = gps[i];
		i++;
	}
	str[i] = 0;
}
#endif

uint8_t paras_num_of_gps_sentence = 0;
void parse_gps_data(uint8_t *data, uint16_t num)
{
//	static uint8_t vtg_speed_str[SIMPLE_COPY_GPS_STRING_LEN + 1] = "0.0000";
	uint8_t *para[MAX_GPS_ARGS];
#if !defined USE_RMC_SENTENCE_TO_ANALYSIS_TIME && (DEBUG_GPS_SYNC_PPS > 2)
	uint8_t rmc_time[SIMPLE_COPY_GPS_STRING_LEN + 1];
#endif
	uint16_t para_num;

	print_gps_parse_data(data);
#ifndef UPDATE_FIXED_GPS_WHEN_LOCKED
	if (dev_is_fixed_gps())
		return;
#endif
/*
GPRMC���Ƽ���С��λ��Ϣ
$GPRMC,081550.00,A,2309.66709,N,11319.90224,E,0.078,,250414,,,A*70
$GPRMC,<1>,<2>,<3>,<4>,<5>,<6>,<7>,<8>,<9>,<10>,<11>,<12>,<13>,<14>*<15><CR><LF>?
<1>	UTC ʱ�䣬��ʽΪhhmmss.sss
<2>	��λ����ָʾ��V=��λ��Ч��A=��λ��Ч
<3>	γ�ȣ���ʽΪddmm.mmmm��ǰ��λ��������0��latitude
<4>	γ�Ȱ���N ��S����γ����γ��
<5>	���ȣ���ʽΪdddmm.mmmm ��ǰ��λ��������0��longitude
<6>	���Ȱ���E ��W��������������
<7>	�������ʣ�knot:������������γ��1�ֵĳ���, 1knot = 1.852km/h
<8>	���溽��000.0~359.9�ȣ����汱Ϊ�ο���׼��ǰ��λ��������0
<9>	UTC���ڣ���ʽΪddmmyy
<10>��ƫ�ǣ�000.0~180.0�ȣ�ǰ��λ��������0
<11>��ƫ�Ƿ���E(��)��W(��)
<12>ģʽָʾ(��NMEA0183 3.00�汾�����A=������λ��D=��֣�E=���㣬N=������Ч)
*/
//	p_strcpy(data, gps_test_data);

//	if (p_strncmp(data, "$GPRMC", 6) == 0)		// $GPRMC $BDRMC $GNRMC
	if (p_strncmp(data + 3, "RMC", 3) == 0)
	{
		para_num = (uint16_t)parse_line(data, para, 10, num);
		if (para_num < 10)
			return;
		paras_num_of_gps_sentence++;
#ifdef USE_RMC_SENTENCE_TO_ANALYSIS_TIME
		set_coor_time_from_gps_data(para, para_num);
#else
		if (IS_RMC_SENTENCE_VALID(para, para_num))
		{
			copy_gps_section_to_string(GPS_SPEED_STRING_DEFINE(para), para[RMC_ORDER_SPEED]);
			copy_gps_section_to_string(GPS_UTC_DATE_STRING_DEFINE(para), para[RMC_ORDER_UTC_DATE]);
  #if DEBUG_GPS_SYNC_PPS > 2
			rmc_time[SIMPLE_COPY_GPS_STRING_LEN] = 0;
			copy_gps_section_to_string(rmc_time, para[RMC_ORDER_UTC_TIME]);
			vlog_i("usart","\tRMC:%s-%d", rmc_time, get_measure_timer_difference(get_timestamp_measure(), timestamp_gps_2pps));
  #endif
			gps_sentence_locked |= GPS_SENTENCE_LOCKED_FLAG;
		}
		else
		{
			gps_sentence_locked &= ~GPS_SENTENCE_LOCKED_FLAG;
		}
#endif
	}
/*
GPGGA��GPS��λ��Ϣ
$GPGGA,081550.00,2309.66709,N,11319.90224,E,1,09,1.02,56.6,M,-5.5,M,,*77
$GPGGA,<1>,<2>,<3>,<4>,<5>,<6>,<7>,<8>,<9>,<10>,<11>,<12>,<13>,<14>*<15><CR><LF>?
<1>	UTC ʱ�䣬��ʽΪhhmmss.sss
<2>	γ�ȣ���ʽΪddmm.mmmm��ǰ��λ��������0��
<3>	γ�Ȱ���N ��S����γ����γ��
<4>	���ȣ���ʽΪdddmm.mmmm ��ǰ��λ��������0��
<5>	���Ȱ���E ��W��������������
<6>	��λ����ָʾ��0=δ��λ��1=�ǲ�ֶ�λ��???2=��ֶ�λ��???3=PPSģʽ����λ��Ч����6=���ڹ���
<7>	����ʹ�õ�������������00��12��ǰ��λ��������0��
<8>	ˮƽ��ȷ�ȣ�0.5 ��99.9
<9>	�����뺣ƽ��ĸ߶�???(���θ߶�)��-9999.9 ��9999.9 ��
<10>�߶ȵ�λ��M ��ʾ��λ��
<11>�����������Ժ�ƽ��ĸ߶ȣ��\999.9 ��9999.9 ��
<12>�߶ȵ�λ��M ��ʾ��λ��
<13>���GPS �������ޣ�RTCM?SC�\104 �����������RTCM ���͵�������
<14>��ֲο���վ��ţ���0000��1023��ǰ��λ��������0��
<15>У���???
*/
//	else if (p_strncmp(data, "$GPGGA", 6) == 0)		// $GPGGA $BDGGA $GNGGA
	else if (p_strncmp(data + 3, "GGA", 3) == 0)
	{
//		vlog_i("usart","%s", data);
		para_num = (uint16_t)parse_line(data, para, 10, num);
		if (para_num < 10)
			return;
		paras_num_of_gps_sentence++;
		gps_sentence_locked &= GPS_SENTENCE_LOCKED_FLAG;
		gps_sentence_locked |= *para[GGA_ORDER_VALID_FLAG] & (~GPS_SENTENCE_LOCKED_FLAG);
#ifdef USE_RMC_SENTENCE_TO_ANALYSIS_TIME
		if (IS_GGA_SENTENCE_VALID(para, para_num))
		{
			gps_height = get_integer_from_string(para[GGA_ORDER_HEIGHT]);
			n_satellite_gps = get_integer_from_string(para[GGA_ORDER_SATELLITE_USED_NUM]);
			g_gui_interactive->dev_misc_notify2.locked_num = (n_satellite_gps < 31) ? n_satellite_gps : 31;
  #if DEBUG_GPS_SYNC_PPS > 2
			vlog_i("usart","\theight:%d,sate=%d", gps_height, n_satellite_gps);
  #endif
			gps_sentence_locked |= GPS_SENTENCE_LOCKED_FLAG;
		}
		else
		{
			n_satellite_gps = 0;
			g_gui_interactive->dev_misc_notify2.locked_num = 0;
			gps_sentence_locked &= ~GPS_SENTENCE_LOCKED_FLAG;
		}
#else
		set_coor_time_from_gps_data(para, para_num);
#endif

/*		if (para[6] && (*para[6] != '0') && (para_num >= 10))
		{
//			set_latitude_data(para[2], &gga_vtg_data);
//			set_longitude_data(para[4], &gga_vtg_data);
//			set_utc_time(para[1], &gga_vtg_data);
//			set_speed_state(1, vtg_speed_str, para[5][0], para[3][0], &gga_vtg_data);
			if (para[7][1] < '0')	// if (para[7][1] == ',')
				n_satellite = (para[7][0] < '0') ? 0 : (para[7][0] - '0');
			else
				n_satellite = (para[7][0] - '0') * 10 + para[7][1] - '0';
		}
		else
		{
//			set_speed_state(0, 0, 0, 0, &gga_vtg_data);
			n_satellite = 0;
		}
//		set_satellite_number(&rmc_data);
*/
	}
/*
// GSV: �ɼ���GNSS���ǣ�ÿ��GSV��Ϣֻ����4�����ǵ���Ϣ����������������4��ʱ�����ջ��������Ͷ���GSV��Ϣ
GPGSV����������״̬������
$GPGSV,4,1,13,13,71,157,29,193,60,045,26,02,56,074,35,05,51,007,20*4A
$GPGSV,4,2,13,41,46,236,38,15,42,210,33,20,37,289,31,29,33,313,32*7B
$GPGSV,4,3,13,06,24,105,17,30,12,082,38,12,10,221,18,25,08,257,13*78
$GPGSV,4,4,13,07,02,054,14*4A<1>	UTC ʱ�䣬��ʽΪhhmmss.sss
<1>	����GSV��������Ŀ
<2>	����GSV����Ǳ���GSV���ĵڼ���
<3>	��ǰ�ɼ�����������00 - 12����ǰ��λ��������0��
<4>	PRN �루α��������룩��???01 - 32����ǰ��λ��������0����Ҳ������Ϊ�����Ǳ��???
<5>	�������ǣ�00 - 90���ȣ�ǰ��λ��������0��
<6>	���Ƿ�λ�ǣ�00 - 359���ȣ�ǰ��λ��������0��
<7>	����ȣ�???00��99��dbHz
<8>	PRN �루α��������룩��???01 - 32����ǰ��λ��������0����Ҳ������Ϊ�����Ǳ��???
<9>	�������ǣ�00 - 90���ȣ�ǰ��λ��������0��
<10>���Ƿ�λ�ǣ�00 - 359���ȣ�ǰ��λ��������0��
<11>����ȣ�???00��99��dbHz
<12>PRN �루α��������룩��???01 - 32����ǰ��λ��������0����Ҳ������Ϊ�����Ǳ��???
<13>�������ǣ�00 - 90���ȣ�ǰ��λ��������0��
<14>���Ƿ�λ�ǣ�00 - 359���ȣ�ǰ��λ��������0��
<15>����ȣ�???00��99��dbHz
<16>PRN �루α��������룩��???01 - 32����ǰ��λ��������0����Ҳ������Ϊ�����Ǳ��???
<17>�������ǣ�00 - 90���ȣ�ǰ��λ��������0��
<18>���Ƿ�λ�ǣ�00 - 359���ȣ�ǰ��λ��������0��
<19>����ȣ�???00��99��dbHz
<20>У���???
*/
#ifndef USE_GGA_PARSE_SATELLITE_NUMBER
	else if (p_strncmp(data + 3, "GSV", 3) == 0)
	{
		para_num = (uint16_t)parse_line(data, para, 4, num);
		if (para_num < 4)
			return;
		if (gps_state_linked)
		{
			if (para[3][1] < '0')	// (para[3][1] == ',')
				para_num = (para[3][0] < '0') ? 0 : (para[3][0] - '0');
			else
				para_num = (para[3][0] - '0') * 10 + para[3][1] - '0';

			if ((para[0][1] == 'G') && (para[0][2] == 'P'))
				n_satellite_gps = para_num;
			else if ((para[0][1] == 'B') && (para[0][2] == 'D'))
				n_satellite_bd = para_num;
		}
		else
		{
			n_satellite_gps = 0;
			n_satellite_bd = 0;
		}
		g_gui_interactive->dev_misc_notify2.locked_num = (n_satellite_gps + n_satellite_bd < 31) ? (n_satellite_gps + n_satellite_bd) : 31;
	}
#else
//	else if (p_strncmp(data + 3, "GSV", 3) == 0)
//	{
//		config_gps_sentence_output(0, 1);
//	}
#endif

/*
GPGLL��������λ��Ϣ
$GPGLL,2309.66709,N,11319.90224,E,081550.00,A,A*60
$GPGLL,<1>,<2>,<3>,<4>,<5>,<6>,<7>,<8>,<9>,<10>,<11>,<12>,<13>,<14>*<15><CR><LF>?
<1>	γ�ȣ���ʽΪddmm.mmmm��ǰ��λ��������0��
<2>	γ�Ȱ���N ��S����γ����γ��
<3>	���ȣ���ʽΪdddmm.mmmm ��ǰ��λ��������0��
<4>	���Ȱ���E ��W��������������
<5>	UTC ʱ�䣬��ʽΪhhmmss.sss
<6>	��λ����ָʾ��V=��λ��Ч��A=��λ��Ч
<7>У���???
	else if (p_strncmp(data, "$GPGLL", 6) == 0)
	{
		para_num = (uint16_t)parse_line(data, para, 7, num);
		if (para_num < 7)
			return;
		if (*para[6] == 'A')
		{
			set_latitude_data(para[1], &gga_vtg_data);
			set_longitude_data(para[3], &gga_vtg_data);
			set_utc_time(para[5], &gga_vtg_data);
			set_speed_state(1, vtg_speed_str, para[4][0], para[2][0], &gga_vtg_data);
		}
		else
		{
			set_speed_state(0, 0, 0, 0, &gga_vtg_data);
		}
	}*/

/*
$GPVTG
$GPVTG,89.68,T,,M,0.00,N,0.0,K*5F
<1>  �˶��Ƕȣ�000 - 359����ǰ��λ��������0��
<2>  T=�汱����ϵ
<3>  �˶��Ƕȣ�000 - 359����ǰ��λ��������0��
<4>  M=�ű�����ϵ
<5>  ˮƽ�˶��ٶȣ�0.00����ǰ��λ��������0��
<6>  N=�ڣ�Knots
<7>  ˮƽ�˶��ٶȣ�0.00����ǰ��λ��������0��
<8>  K=����/ʱ��km/h
<9>  У��ֵ
	else if (p_strncmp(data, "$GPVTG", 6) == 0)
	{
		if (gps_state_linked)
		{
			para_num = (uint16_t)parse_line(data, para, 9, num);
			if (para_num >= 9)
				p_strcpy(vtg_speed_str, para[5]);
		}
	}*/
	else if (gps_sys_setup_state != 0xff)
	{
		if ((gps_sys_setup_state & 0x3F) == 0)
		{
			if (dev_is_bd_only())
				para_num = BAR_TYPE_SATELLITE_BD;
			else
				para_num = (uint16_t)maintain_setup_parameter(PARA_OPERATE_READ, GPS_TYPE_PARAS_POS, 0);
			para_num = ((para_num == BAR_TYPE_SATELLITE_GPS) || (para_num == BAR_TYPE_SATELLITE_BD)) ? (para_num - 1) : 2;
			vlog_i("usart","\t[GPS]%s", (char *)set_gps_sys_string[SET_GPS_MODE_ENABLE - 3][para_num]);
			put_data(UART_GPS_INDEX, (uint8_t *)set_gps_sys_string[SET_GPS_MODE_ENABLE - 3][para_num], p_strlen(set_gps_sys_string[SET_GPS_MODE_ENABLE - 3][para_num]));
			gps_sys_setup_state++;
		}
		else
		{
/*			if (p_strncmp(data, "$CFGSYS", 7) == 0)
			{
				if (p_strncmp(&data[8], "h11", 3) == 0)			// gps&bd, $CFGSYS,h11*5F
					gps_sys_state = 3;
				else if (p_strncmp(&data[8], "h10", 3) == 0)	// bd, $CFGSYS,h10*5E
					gps_sys_state = 2;
				else											// gps, $CFGSYS,h1*6E
					gps_sys_state = 1;
				vlog_i("usart","%s", data);
			}
*/
			if (p_strncmp(data, "$OK", 3) == 0)
			{
//				STM_EVAL_LEDInit(GPS_RESET_LINE);
//				set_gpio_low(GPS_RESET_LINE);
//				vlog_i("usart","[GPS SET]OK[%d] and hard reset[%d] now...", gps_sys_setup_state, timer_initial(1, 200, reset_gps_delay));
				vlog_i("usart","[GPS SET]OK[%d]", gps_sys_setup_state);
set_gps_mode_done:
				gps_sys_setup_state = 0xff;
				config_gps_sentence_output(1, 1);
			}
			else
			{
				if ((gps_sys_setup_state & 0x3F) < 34)
				{
					gps_sys_setup_state++;
				}
				else
				{
					para_num = gps_sys_setup_state >> 6;
					if (para_num < 2)	// retry 3 times
					{
						para_num++;
						gps_sys_setup_state = (uint8_t)((para_num << 6) & 0xC0);
					}
					else
					{
						vlog_i("usart","[GPS SET]completed![%02X]", gps_sys_setup_state);
						goto set_gps_mode_done;
					}
				}
			}
		}
	}
	else if (SET_GPS_MODE_ENABLE == 2)
	{
		if (p_strstr(data, gps_pattern_string[1]) || p_strstr(data, "URANUS"))
		{
			SET_GPS_MODE_ENABLE = 4;
			vlog_i("usart","CASIC module found!");
			config_gps_sentence_output(1, 1);
		}
		else if (p_strstr(data, gps_pattern_string[2]))
		{
			SET_GPS_MODE_ENABLE = 5;
			vlog_i("usart","TECHTOTOP module found!");
		}
		else if (p_strstr(data, gps_pattern_string[0]))
		{
			SET_GPS_MODE_ENABLE = 3;
			vlog_i("usart","UNICORE module found!");
		}
//		else if (p_strstr(data, gps_pattern_string[3]))
//		{
//			SET_GPS_MODE_ENABLE = 6;
//			vlog_i("usart","GMTK module found!");
//		}
		else if (p_strncmp(data + 3, "GSA", 3) == 0)	// output info complete
		{
			SET_GPS_MODE_ENABLE = 1;
			vlog_i("usart","Module can NOT recognize!");
		}

//		if (SET_GPS_MODE_ENABLE >= 3)	// set UM220
		if (SET_GPS_MODE_ENABLE > 3)	// disable the MCU's UART-TX pin; UM220 cannot set mode, so SET_GPS_MODE_ENABLE should be NOT equal 3
		{
			update_gps_work_mode();
		}
	}
}

uint8_t is_gps_rmc_locked(void)
{
#ifndef USE_RMC_SENTENCE_TO_ANALYSIS_TIME
	return (gps_sentence_locked & GPS_SENTENCE_LOCKED_FLAG) ? 1 : 0;
#else
	return 1;	// ��Ҫ�����ж�
#endif
}

uint8_t is_gps_gga_locked(void)
{
	uint8_t gga_act = gps_sentence_locked & (~GPS_SENTENCE_LOCKED_FLAG);

	if ((gga_act == '0') || (gga_act == '1') || (gga_act == '2') || (gga_act == '3') || (gga_act == '6'))
		return gga_act - '0';
	else
		return 7;
}

void set_real_bt_config(uint8_t flag)	// 0-off, else-on
{
	config_bt_counter = flag ? BT_CONNECT_STEP_POWERON : BT_CONNECT_STEP_POWERDOWN;
}

uint8_t get_bt_mac_from_string(uint8_t *mac_str)
{
	uint8_t i = 0, *ptr8;

	if (p_strlen(mac_str) == 3 * BT_MAC_SECTION_NUM - 1)
	{
		for (ptr8 = mac_str; i < BT_MAC_SECTION_NUM; i++, ptr8 += 3)
			string2hexadecimal(ptr8, &bt_mac[i], 2);
	}
	return i;
}

void get_bt_mac_string(uint8_t *bt_mac_addr)
{
	sprintf((char *)bt_mac_addr, "MAC:%02X-%02X-%02X-%02X-%02X-%02X", bt_mac[0], bt_mac[1], bt_mac[2], bt_mac[3], bt_mac[4], bt_mac[5]);
}

uint8_t* get_bt_module_info(void)
{
	return ((bt_mac[0] == 0) && (bt_mac[1] == 0) && (bt_mac[2] == 0) && (bt_mac[3] == 0) && (bt_mac[4] == 0) && (bt_mac[5] == 0)) ? (uint8_t *)0 : (uint8_t *)bt_module_info;
}

//const static uint8_t bt_connect_info[] = "BLE_CONNECTED";
const static uint8_t bt_mac_header[] = "LocalMac:";	// LocalMac:0A-47-A1-C3-B8-49
void proc_real_bt_return_data(uint8_t *bt_data)
{
	uint8_t mac_str[30];

	switch (config_bt_counter)
	{
		case BT_CONNECT_STEP_POWERON:
			if (p_strncmp(bt_data, bt_mac_header, p_strlen(bt_mac_header)) == 0)	// char *strstr(char *str1, const char *str2);
			{
				if (get_bt_mac_from_string(bt_data + p_strlen(bt_mac_header)))
				{
					get_bt_mac_string(mac_str);
					config_bt_counter = BT_CONNECT_STEP_ONLINE;
					vlog_i("usart","[BT]Power on,%s", (char *)mac_str);
				}
				else
				{
					goto print_bt_return_string;
				}
			}
			else
			{
				goto print_bt_return_string;
			}
			break;

		case BT_CONNECT_STEP_DEBUG:
			if (p_strncmp(bt_data, "OK", 2) == 0)
			{
				config_bt_counter = BT_CONNECT_STEP_DEBUG_OK;
				vlog_i("usart","[BT]Debug OK");
			}
			else
			{
				goto print_bt_return_string;
			}
			break;
/*
		case BT_CONNECT_STEP_DEBUG_OK:	// 20220424: use hardware to indicate
			if (p_strncmp(bt_data, bt_connect_info, p_strlen(bt_connect_info)) == 0)
			{
				config_bt_counter = BT_CONNECT_STEP_CONNECT_OK;
				vlog_i("usart","[BT]Connected");
			}
			else
			{
				goto print_bt_return_string;
			}
			break;
*/
		default:
print_bt_return_string:
			vlog_i("usart","[BT]%s(%02X)", bt_data, config_bt_counter);
			break;
	}
}

void process_bt_data(void)
{
	static uint16_t bt_data_index = 0;

	while (getch_noblock(UART_BT_INDEX, &bt_buffer[bt_data_index]))
	{
		if ((bt_buffer[bt_data_index] == 0x0D) || (bt_buffer[bt_data_index] == 0x0A))
		{
			bt_buffer[bt_data_index] = 0;
			if (p_strlen(bt_buffer))
			{
				if (uart2_use_as_bt() && (uart2_is_new_bt() == 0))
					proc_real_bt_return_data(bt_buffer);
				else
					vlog_i("usart","[BT]%s", bt_buffer);
			}
			bt_data_index = 0;
		}
		else if (++bt_data_index >= BT_BUFFER_LENGTH)
		{
			bt_data_index--;
		}
	}
}

const static uint8_t config_bt_get_mac[16] = "AT+LOCALMAC?";
const static uint8_t config_bt_debug[12] = "AT+DEBUG";
//const static uint8_t config_bt_connect[16] = "AT+CONNECT";
void config_real_bt_process(void)
{
	uint8_t bt_indicate;

	bt_indicate = 0; //gpio_input_data_bit_read(BT_CONNECT_INDICATE_GPIO, BT_CONNECT_INDICATE_PIN) ? 0 : 1;	// bt_indicate: 1 is connect STM_EVAL_PBGetState(BT_CONNECT_INDICATE)
//	vlog_i("usart","[BT]connect=%d", bt_indicate);
	if (bt_indicate)
	{
		if (config_bt_counter != BT_CONNECT_STEP_CONNECT_OK)
		{
			if (config_bt_counter == BT_CONNECT_STEP_POWERDOWN)
			{
				bt_power_onoff(0);
				vlog_i("usart","[BT]Powerdown but connect");
			}
			else if (config_bt_counter < BT_CONNECT_STEP_CONNECT_OK)
			{
				usart_configuration(UART_BT_INDEX, 460800, 1);
				set_uart2_use_as_dataport(2);
				vlog_i("usart","[BT]Connected(%d)", config_bt_counter);
				config_bt_counter = BT_CONNECT_STEP_CONNECT_OK;
			}
			else if (config_bt_counter == BT_CONNECT_STEP_RESTART)
			{
				bt_power_onoff(1);
				vlog_i("usart","[BT]Restart but connect");
				config_bt_counter = BT_CONNECT_STEP_CONNECT_OK;
			}
		}
	}
	else
	{
		if (config_bt_counter != BT_CONNECT_STEP_POWERDOWN)
		{
			if (config_bt_counter < BT_CONNECT_STEP_DEBUG_OK)
			{
				process_bt_data();

				if (config_bt_counter == BT_CONNECT_STEP_POWERON)
				{
					put_data(UART_BT_INDEX, (uint8_t *)config_bt_get_mac, p_strlen(config_bt_get_mac));
				}
				else if (config_bt_counter == BT_CONNECT_STEP_ONLINE)
				{
					config_bt_counter = BT_CONNECT_STEP_DEBUG;
					put_data(UART_BT_INDEX, (uint8_t *)config_bt_debug, p_strlen(config_bt_debug));
				}
			}
			else
			{
				if (config_bt_counter == BT_CONNECT_STEP_CONNECT_OK)
				{
					vlog_i("usart","[BT]Disconnected,restart(%d)", config_bt_counter);
					bt_power_onoff(0);
					config_bt_counter = BT_CONNECT_STEP_RESTART;	// ����ص�Դ��ֵ������BT��Դ���config_bt_counter����
				}
				else if (config_bt_counter == BT_CONNECT_STEP_RESTART)
				{
					bt_power_onoff(1);
					config_bt_counter = BT_CONNECT_STEP_POWERON;
				}
			}
		}
	}
}

void get_real_new_bt_info(uint32_t *dev_list, uint8_t *dev_num, uint8_t *dev_fsm)
{
	*dev_list = (uint32_t)bt_dev_list;
	*dev_num = bt_dev_num;
	*dev_fsm = config_bt_counter;
}

void bt_func_process_real_new_bt(BT_DMA_DATA_TYPEDEF *p_btdata)
{
	uint8_t i, *ptr8;

	if (config_bt_counter != BT_CONNECT_STEP_POWERDOWN)
	{

	switch (p_btdata->addr)
	{
		case TSC_CTASK_NEW_BT_ADDR_GET_MAC:
			/*	17 EA 18 EB 00 FB 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
				00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 FB 00 19 EC*/
			if (config_bt_counter == BT_CONNECT_STEP_POWERON)
			{
				config_bt_counter = BT_CONNECT_STEP_ONLINE;
				memcpy(bt_mac, p_btdata->data, BT_MAC_SECTION_NUM);
			}
			break;

		case TSC_CTASK_NEW_BT_ADDR_LINK_BACK:
//			if (config_bt_counter == BT_CONNECT_LINK_BACK_DEVICE)
			{
				if (p_btdata->data[0])	// device for link back exist
				{
//					memset(bt_dev_list, 0, sizeof(uint8_t) * BT_DEVICE_MAX * BT_DEVICE_NAME_LEN);
					memcpy(bt_dev_list, p_btdata->data, BT_DEVICE_NAME_LEN);
					bt_dev_num = 1;
					config_bt_counter = BT_CONNECT_LINK_BACK_CONNECTING;
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
					vlog_i("usart","\t[CBT]link back to <%s>", (char *)p_btdata->data);
#endif
				}
				else
				{
//					config_bt_counter = BT_CONNECT_READ_SCAN_LIST;
					config_bt_counter = BT_CONNECT_CONNECT_FAIL;		// 20240823:��ΪUI/�û�������һ��
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
					vlog_i("usart","\t[CBT]link back device NOT exist");
#endif
				}
			}
			break;


		case TSC_CTASK_NEW_BT_ADDR_READ_SCAN:
			/*	17 EA 18 EB 00 FB 00 00 02 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
				00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 FD 00 19 EC*/
			if (config_bt_counter == BT_CONNECT_READ_SCAN_LIST)
			{
				if (p_btdata->bytes == 0x0000)	// no new dev, or stop scanning
				{
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
					if (p_btdata->data[0] == 0)
					{
						vlog_i("usart","\t[CBT]NO new device");
					}
					else if (p_btdata->data[0] == 0xff)
#else
					if (p_btdata->data[0] == 0xff)
#endif
					{
						config_bt_counter = BT_CONNECT_READ_SCAN_LIST_DONE;
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
						vlog_i("usart","\t[CBT]Scan STOP");
#endif
					}
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
					else
					{
						vlog_i("usart","\t[CBT]Scan:I=%04x,D0=%02x", p_btdata->bytes, p_btdata->data[0]);
					}
#endif
				}
				else
				{
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
					vlog_i("usart","\t[CBT]total=%d(cur=%d),now=%d\r\n\t", p_btdata->bytes >> 8, p_btdata->bytes & 0x00ff, bt_dev_num);
#endif
					for (i = 0; i < BT_DMA_DATA_DLEN / BT_DEVICE_NAME_LEN; i++)
					{
						if (bt_dev_num < BT_DEVICE_MAX)
						{
							ptr8 = p_btdata->data + i * BT_DEVICE_NAME_LEN;
							if (*ptr8)
							{
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
								vlog_i("usart","%s ", (char *)ptr8);
#endif
								memcpy(bt_dev_list + bt_dev_num * BT_DEVICE_NAME_LEN, ptr8, BT_DEVICE_NAME_LEN);
								bt_dev_num++;
							}
						}
						else
						{
							vlog_i("usart","\r\n[CBT]Dev list full");
							i = 4;
						}
					}
					vlog_i("usart","");
				}
			}
			break;

		case TSC_CTASK_NEW_BT_ADDR_CONNECT:
			/*	17 EA 18 EB 00 FB 00 00 03 00 00 00 42 74 5F 64 65 76 30 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
				00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 82 03 19 EC*/
			if ((config_bt_counter == BT_CONNECT_CONNECTING) || (config_bt_counter == BT_CONNECT_LINK_BACK_CONNECTING) ||
					(config_bt_counter == BT_CONNECT_CONNECT_FAIL))
			{
				if (p_btdata->data[0] == BT_CONNECT_CONNNECT_STATE_OK)
				{
					set_uart2_use_as_dataport(2);
					config_bt_counter = BT_CONNECT_STEP_CONNECT_OK;
					set_dsp_pcm_speech_inout(remap_pcm_direct(1));
					vlog_i("usart","\t[CBT]Connect succ");
				}
				else if (p_btdata->data[0] == BT_CONNECT_CONNNECT_DISCONNECTED)
				{
					vlog_i("usart","\t[CBT]Connect:connecting->disconn");
					goto real_new_bt_conn_fail;
				}
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
				else if (p_btdata->data[0] == BT_CONNECT_CONNNECT_STATE_GOING)
				{
					vlog_i("usart","\t[CBT]Connect...");
				}
#endif
				else if (p_btdata->data[0] == BT_CONNECT_CONNNECT_STATE_TIMEOUT)
				{
					vlog_i("usart","\t[CBT]Connect timeout");
real_new_bt_conn_fail:
//					if (config_bt_counter == BT_CONNECT_LINK_BACK_CONNECTING)			// 20240823:��ΪUI/�û�������һ��
//					{
//						set_real_new_bt_todo_something(BT_CONNECT_READ_SCAN_LIST, 0);
//					}
//					else
					{
						config_bt_counter = BT_CONNECT_CONNECT_FAIL;
					}
				}
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
				else
				{
					vlog_i("usart","\t[CBT]CONNing:index=%04x,data0=%02x", p_btdata->bytes, p_btdata->data[0]);
				}
#endif
			}
			else if (config_bt_counter == BT_CONNECT_STEP_CONNECT_OK)
			{
				if (p_btdata->data[0] == BT_CONNECT_CONNNECT_STATE_OK)
				{
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
					vlog_i("usart","\t[CBT]GOOD");
#endif
				}
				else
				{
					set_dsp_pcm_speech_inout(0);
					if (p_btdata->data[0] == BT_CONNECT_CONNNECT_DISCONNECTED)
					{
						vlog_i("usart","\t[CBT]Device disconnected");
						goto real_new_bt_conn_fail;
					}
					else if (p_btdata->data[0] == BT_CONNECT_CONNNECT_STATE_GOING)
					{
						config_bt_counter = BT_CONNECT_CONNECTING;
						vlog_i("usart","\t[CBT]Reconnect...");
					}
					else if (p_btdata->data[0] == BT_CONNECT_CONNNECT_STATE_TIMEOUT)
					{
						vlog_i("usart","\t[CBT]Connect: succ->timeout");
						goto real_new_bt_conn_fail;
					}
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
					else
					{
						vlog_i("usart","\t[CBT]CONNed:index=%04x,data0=%02x", p_btdata->bytes, p_btdata->data[0]);
					}
#endif
				}
			}
			break;

		case TSC_CTASK_NEW_BT_ADDR_DISCONNECT:
			/*	17 EA 18 EB 00 FB 00 00 05 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
				00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 01 19 EC*/
			if (config_bt_counter == BT_CONNECT_DISCONNECTING)
			{
				if ((p_btdata->data[0] == BT_CONNECT_CONNNECT_STATE_OK) || (p_btdata->data[0] == BT_CONNECT_CONNNECT_DISCONNECTED))
				{
					config_bt_counter = BT_CONNECT_DISCONNECT_DONE;
					vlog_i("usart","\t[CBT]Disconnect succ");
				}
				else if (p_btdata->data[0] == BT_CONNECT_CONNNECT_STATE_TIMEOUT)
				{
					config_bt_counter = BT_CONNECT_DISCONNECT_FAIL;
					vlog_i("usart","\t[CBT]Disconnect fail");
				}
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
				else
				{
					vlog_i("usart","\t[CBT]DISCONN:index=%04x,data0=%02x", p_btdata->bytes, p_btdata->data[0]);
				}
#endif
			}
			break;

		case TSC_CTASK_NEW_BT_ADDR_SET_TO_CALL:
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
			vlog_i("usart","\t[CBT]Rcv set ret:%04d", p_btdata->bytes);
#endif
			set_real_new_bt_to_own_calling(0xff);
			break;

		case TSC_CTASK_NEW_BT_ADDR_VOICE:
#if defined DEBUG_REAL_NEW_BT_FUNCTION || (DEBUG_ENC_DEC_FUNCTION >= 1)
//			vlog_i("usart","\t[CBT]PCM:%04d(%02X %02X) %04d", p_btdata->bytes, p_btdata->data[0], p_btdata->data[BT_DMA_DATA_DLEN - 1], get_timestamp_measure());
#endif
			save_bt_voice_frame(p_btdata->data, BT_DMA_DATA_DLEN, 0);
			break;

		case TSC_CTASK_NEW_BT_ADDR_PTT:
			set_sync_call_stack_type(p_btdata->data[0] ? SYNC_CALL_STACK_PTT_PRESSED : SYNC_CALL_STACK_PTT_RELEASED);
			send_bt_frame_to_host((uint8_t *)p_btdata, 0, 0xffff, 0, 0);
			break;

		case TSC_CTASK_NEW_BT_ADDR_UPGRADE:
			config_bt_counter = BT_CONNECT_STEP_UPGRADE_MODE;
			break;


		default:
			break;

	}

	}	// if (config_bt_counter != BT_CONNECT_STEP_POWERDOWN)
}

void send_upgrade_cmd_to_real_new_bt(void)
{
	send_bt_frame_to_host(bt_mac, 0, TSC_CTASK_NEW_BT_UNI_CMD, 0, TSC_CTASK_NEW_BT_ADDR_UPGRADE);
}

void set_real_new_bt_todo_something(uint8_t cmd, uint8_t para)
{
	if (cmd == BT_CONNECT_CONNECTING)			// ����
	{
		if (para < bt_dev_num)
		{
			bt_dev_conn = para;
			config_bt_counter = BT_CONNECT_CONNECTING;
		}
	}
	else if ((cmd == BT_CONNECT_DISCONNECTING) || (cmd == BT_CONNECT_REBOOT_MODULE))	// ֹͣ/�Ͽ����� / ��������ģ��
	{
		config_bt_counter = cmd;
	}
	else if (cmd == BT_CONNECT_READ_SCAN_LIST)
	{
		memset(bt_dev_list, 0, sizeof(uint8_t) * BT_DEVICE_MAX * BT_DEVICE_NAME_LEN);
		bt_dev_num = 0;
		bt_dev_conn = 0;
		config_bt_counter = BT_CONNECT_READ_SCAN_LIST;
	}

}

uint8_t get_mac_new_bt_counter = 0;
void config_real_new_bt_process(void)
{
	uint8_t mac_str[30];

	switch (config_bt_counter)
	{
		case BT_CONNECT_STEP_POWERON:
			send_bt_frame_to_host(bt_mac, 0, TSC_CTASK_NEW_BT_UNI_CMD, 0, TSC_CTASK_NEW_BT_ADDR_GET_MAC);
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
			vlog_i("usart","[CBT]get mac:%d", get_mac_new_bt_counter++);
#endif
			break;

		case BT_CONNECT_STEP_ONLINE:
			config_bt_counter = BT_CONNECT_LINK_BACK_DEVICE;
			get_bt_mac_string(mac_str);
			vlog_i("usart","[CBT]Power on(%d),%s", get_mac_new_bt_counter, (char *)mac_str);
			get_mac_new_bt_counter = 0;
			break;

		case BT_CONNECT_LINK_BACK_DEVICE:
			send_bt_frame_to_host(bt_mac, 0, TSC_CTASK_NEW_BT_UNI_CMD, bt_dev_num, TSC_CTASK_NEW_BT_ADDR_LINK_BACK);
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
			vlog_i("usart","[CBT]link back...%d", get_mac_new_bt_counter++);
#endif
			break;

		case BT_CONNECT_READ_SCAN_LIST:	// next: read scan result
			send_bt_frame_to_host(bt_mac, 0, TSC_CTASK_NEW_BT_UNI_CMD, bt_dev_num, TSC_CTASK_NEW_BT_ADDR_READ_SCAN);
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
			vlog_i("usart","[CBT]read scan list:%d", get_mac_new_bt_counter++);
#endif
			break;

		case BT_CONNECT_READ_SCAN_LIST_DONE:
/*			if (bt_dev_num)									// 20240823:��ΪUI/�û�������һ��
			{
				config_bt_counter = BT_CONNECT_CONNECTING;
			}
			else
			{
				config_bt_counter = BT_CONNECT_READ_SCAN_LIST;
//				set_uart2_use_as_dataport(2);
//				config_bt_counter = BT_CONNECT_STEP_CONNECT_OK;	// �����Լ������ԣ���̨����λ�󣬻�ȡmac������indexΪ0��ʼ��ȡ�б�����ʱ������Ӧ�����¿�ʼ������������ֱ�ӷ���ֹͣ������
			}
*/			break;

		case BT_CONNECT_CONNECTING:
		case BT_CONNECT_LINK_BACK_CONNECTING:
		case BT_CONNECT_STEP_CONNECT_OK:
			memcpy(mac_str, &bt_dev_list[bt_dev_conn * BT_DEVICE_NAME_LEN], BT_DEVICE_NAME_LEN);
			send_bt_frame_to_host(mac_str, BT_DEVICE_NAME_LEN, TSC_CTASK_NEW_BT_UNI_CMD, 0, TSC_CTASK_NEW_BT_ADDR_CONNECT);
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
			vlog_i("usart","[CBT]%s %d", (config_bt_counter == BT_CONNECT_CONNECTING) ? "connecting..." : "connect state", get_mac_new_bt_counter++);
#endif
			break;

//		case BT_CONNECT_STEP_CONNECT_OK:
//			config_bt_counter = BT_CONNECT_DISCONNECTING;
//			break;

		case BT_CONNECT_DISCONNECTING:
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
			vlog_i("usart","[CBT]disconnecting...%d", get_mac_new_bt_counter++);
#endif
			send_bt_frame_to_host(bt_mac, BT_DEVICE_NAME_LEN, TSC_CTASK_NEW_BT_UNI_CMD, 0, TSC_CTASK_NEW_BT_ADDR_DISCONNECT);
			break;

		case BT_CONNECT_DISCONNECT_DONE:
//			bt_dev_num = 0;
//			config_bt_counter = BT_CONNECT_READ_SCAN_LIST;
			break;

		case BT_CONNECT_CONNECT_FAIL:
		case BT_CONNECT_DISCONNECT_FAIL:
		case BT_CONNECT_STEP_UPGRADE_MODE:
/*			20240713��failʱֹͣ
			vlog_i("usart","[CBT]%s fail,reboot BT...", (config_bt_counter == BT_CONNECT_CONNECT_FAIL) ? "CONN" : "DISCONN");
#if ((defined COLOR_TFT_176X220 || defined COLOR_TFT_320X240) && !defined STM32F469_479xx) || defined VICTEL_ZZWPRO_BASE
#else
			set_gpio_high(BT_POWER_CTRL_LINE);
#endif
			config_bt_counter = BT_CONNECT_REBOOT_MODULE;
			bt_dev_num = 0;
			memset(bt_mac, 0, BT_MAC_SECTION_NUM);
*/
			break;

		case BT_CONNECT_REBOOT_MODULE:
#if ((defined COLOR_TFT_176X220 || defined COLOR_TFT_320X240) && !defined STM32F469_479xx) || defined VICTEL_ZZWPRO_BASE
#else
			set_gpio_low(BT_POWER_CTRL_LINE);
#endif
			config_bt_counter = BT_CONNECT_STEP_POWERON;
			break;

		case BT_CONNECT_STEP_POWERDOWN:
			break;

		default:
			bt_dev_num = 0;
			memset(bt_mac, 0, BT_MAC_SECTION_NUM);
			power_real_bt(1);
			break;
	}
}


static uint8_t tim_set_real_new_bt = 0xff, set_real_new_bt_state = 0;
void send_set_real_new_bt_to_own_calling_frame(void)
{
#ifdef DEBUG_REAL_NEW_BT_FUNCTION
	vlog_i("usart","Set to %d(%d)", set_real_new_bt_state, get_mac_new_bt_counter++);
#endif
	send_bt_frame_to_host(bt_mac, 0, TSC_CTASK_NEW_BT_UNI_CMD, set_real_new_bt_state, TSC_CTASK_NEW_BT_ADDR_SET_TO_CALL);
}

void set_real_new_bt_to_own_calling(uint8_t flag)	// flag: 0-stop voice; 1-start voice; else: finish(destroy the timer)
{
	if (flag >= SET_BT_TOTAL_TYPE)
	{
		tim_set_real_new_bt = timer_destroy(tim_set_real_new_bt);
	}
	else if (uart2_is_new_bt() && get_bt_link_state())
	{
		if (flag == SET_BT_FAKE_STOP)		// ֹͣͨ��ʱ������ԭ��־ȷ����ֹͣMIC��������ֹͣ����
		{
			set_real_new_bt_state = (set_real_new_bt_state == SET_BT_START_MIC) ? SET_BT_STOP_MIC : SET_BT_STOP_PLAY;
//			set_real_new_bt_state = 0;					// ά��ԭ״
		}
/*		else if (flag == SET_BT_START_PLAY)	// ��λ����ʱ������Ѿ�����MIC����˵���ǰ���PTT����ʱ����Э��ջ����״̬�����µ�����λ������
		{
			if (flag != SET_BT_START_MIC)
				set_real_new_bt_state = flag;
			else
				return;
		}*/
		else
		{
			set_real_new_bt_state = flag;
//			set_real_new_bt_state = flag ? 1 : 0;		// ά��ԭ״
		}

		if (tim_set_real_new_bt != 0xff)
			tim_set_real_new_bt = timer_destroy(tim_set_real_new_bt);

		send_set_real_new_bt_to_own_calling_frame();
		tim_set_real_new_bt = timer_initial(10, 40, send_set_real_new_bt_to_own_calling_frame);
	}
}

void power_real_bt(uint8_t on_off)	// 0-off
{
	if (uart2_use_as_bt())
		bt_power_onoff(on_off);
}

/*
1 ������MCU��GPS��������Ϊ38400����⵽GPS��������������
2 38400�¼�ⲻ�������е�???9600���ټ�����ݣ�???
3 ��9600�¼�⵽���������GPS����ͼ�����е�38400��Ȼ���Լ����л�38400����9600�������ⲻ�������˳�������̣�???
4 ����GPS�������л������л�38400�������⵽���ݣ��������GPS��������������������У�������л�38400���ⲻ�����ݣ����˳��������???
5 �����������ʶ���ⲻ������ʱ���?11s�����л�һ���Լ��Ĳ����ʼ������???

20240112��Ϊ��
1 ������MCU��GPS��������Ϊ38400����⵽GPS��������������
2 38400�¼�ⲻ������???115200->9600->38400ѭ�����???
3 ��9600�¼�⵽�ұ��������ʶ��Чʱ���������GPS��ͼ�����е�38400��Ȼ���Լ�Ҳ�е�38400��������������ʶ��Ч��˵���л�ʧ�ܣ��˳������Ҳ��������???
4 �ڷ�9600�¼�⵽�ұ��������ʶ��Чʱ���������GPS����������˳����̣�������������ʶ��Ч�򲻱���������˳�����
*/
#define AUTOSET_GPS_BAUD_LOOP_MASK			0x70
#define AUTOSET_GPS_BAUD_SAVE_PARAS			0x08
#define AUTOSET_GPS_IS_ALIVE				0x04
#define AUTOSET_GPS_BAUD_CURR_RATE_MASK		0x03/*0-9600,1-38400,2-115200,no 3*/
#define AUTOSET_GPS_BAUD_TOTAL_NUM			3
#define GET_CURE_RATE_INDEX()				(autoset_gps_baud_stage & AUTOSET_GPS_BAUD_CURR_RATE_MASK)

static uint8_t timer_autoset_gps_baud = 0xff, autoset_gps_baud_stage = 0, autoset_gps_baud_counter = 0;
static const uint32_t bd_table[AUTOSET_GPS_BAUD_TOTAL_NUM] = {AUTOSET_GPS_BAUDRATE0, AUTOSET_GPS_BAUDRATE1, AUTOSET_GPS_BAUDRATE2};
uint32_t switch_gps_uart_baudrate(void)
{
	if (GET_CURE_RATE_INDEX() >= AUTOSET_GPS_BAUD_TOTAL_NUM - 1)
		autoset_gps_baud_stage &= ~AUTOSET_GPS_BAUD_CURR_RATE_MASK;
	else
		autoset_gps_baud_stage++;

	usart_set_baudrate(UART_GPS_INDEX, bd_table[GET_CURE_RATE_INDEX()]);		// usart_configuration(UART_GPS_INDEX, new_baud, 1);
//	vlog_i("usart","\t[%d]B=%d", timestamp_kb_scan, new_baud);
	return bd_table[GET_CURE_RATE_INDEX()];
}


void check_gps_is_alive(void)
{
	if (timer_autoset_gps_baud == 0xff)
	{
		if (autoset_gps_baud_counter++ >= 10)
		{
			autoset_gps_baud_counter = 0;
			if (paras_num_of_gps_sentence >= 5)
			{
				paras_num_of_gps_sentence = 0;
				autoset_gps_baud_stage |= AUTOSET_GPS_IS_ALIVE;
			}
			else
			{
				switch_gps_uart_baudrate();
				if (autoset_gps_baud_stage & AUTOSET_GPS_IS_ALIVE)
				{
					autoset_gps_baud_stage &= ~AUTOSET_GPS_IS_ALIVE;

					set_gps_link_state(0);
					update_gps_work_mode();
					set_sync_call_stack_type(SYNC_CALL_STACK_GPS_UPDATE);
					vlog_i("usart","\tGPS invalid");
				}
			}
		}
	}
}

uint8_t is_gps_alive(void)
{
	return (autoset_gps_baud_stage & AUTOSET_GPS_IS_ALIVE) ? 1 : 0;
}

void autoset_gps_module_baudrate_handle(void)
{
	uint8_t old_rate_idx;

	if (paras_num_of_gps_sentence >= 2)											// receive GPS data
	{
		autoset_gps_baud_counter = 0;
		paras_num_of_gps_sentence = 0;
		old_rate_idx = GET_CURE_RATE_INDEX();
#ifdef AUTOSET_GPS_SET_TO_DEFAULT
		if (old_rate_idx != SET_GPS_DEFAULT_BAUDRATE_IDX)						// receive at non default
#else
		if (old_rate_idx == 0)													// receive at 9600
#endif
		{
			if ((autoset_gps_baud_stage & AUTOSET_GPS_BAUD_SAVE_PARAS) == 0)	// not change before
			{
#ifdef AUTOSET_GPS_SET_TO_DEFAULT
				autoset_gps_baud_stage &= ~AUTOSET_GPS_BAUD_CURR_RATE_MASK;		// set to default
				autoset_gps_baud_stage |= SET_GPS_DEFAULT_BAUDRATE_IDX;
#else
				autoset_gps_baud_stage++;										// set to 38400
#endif
				put_data(UART_GPS_INDEX, (uint8_t *)set_gps_baudrate_casic[GET_CURE_RATE_INDEX()], p_strlen(set_gps_baudrate_casic[GET_CURE_RATE_INDEX()]));	// and set to 38400
				put_data(UART_GPS_INDEX, (uint8_t *)set_gps_baudrate_casic[GET_CURE_RATE_INDEX()], p_strlen(set_gps_baudrate_casic[GET_CURE_RATE_INDEX()]));
				sys_delay(2);	// wait the last char send complete
				usart_set_baudrate(UART_GPS_INDEX, bd_table[GET_CURE_RATE_INDEX()]); 	// usart_configuration(UART_GPS_INDEX, new_baud, 1);
				autoset_gps_baud_stage |= AUTOSET_GPS_BAUD_SAVE_PARAS;
				vlog_i("usart","[%d]Receive at B%d, set to %d and detect again", timestamp_kb_scan, bd_table[old_rate_idx], bd_table[GET_CURE_RATE_INDEX()]);
			}
			else
			{
				vlog_i("usart","[%d]Done at %d(change GPS baud fail)", timestamp_kb_scan, bd_table[old_rate_idx]);
				goto autoset_gps_quit;
			}
		}
		else
		{
			if (autoset_gps_baud_stage & AUTOSET_GPS_BAUD_SAVE_PARAS)
			{
				put_data(UART_GPS_INDEX, (uint8_t *)set_gps_save_para_casic, p_strlen(set_gps_save_para_casic));
				put_data(UART_GPS_INDEX, (uint8_t *)set_gps_save_para_casic, p_strlen(set_gps_save_para_casic));
				sys_delay(2);	// wait the last char send complete
			}
			vlog_i("usart","[%d]Done%s, now baud=%d", timestamp_kb_scan, (autoset_gps_baud_stage & AUTOSET_GPS_BAUD_SAVE_PARAS) ? "/save" : "", bd_table[GET_CURE_RATE_INDEX()]);
autoset_gps_quit:
			autoset_gps_baud_stage |= AUTOSET_GPS_IS_ALIVE;
			timer_autoset_gps_baud = timer_destroy(timer_autoset_gps_baud);
		}
	}
	else
	{
		if (autoset_gps_baud_counter++ > 5)							// 2s can NOT receive
		{
			autoset_gps_baud_counter = 0;
			paras_num_of_gps_sentence = 0;
			vlog_i("usart","[%d]Switch to %d and check...", timestamp_kb_scan, switch_gps_uart_baudrate());
			if (GET_CURE_RATE_INDEX() == SET_GPS_DEFAULT_BAUDRATE_IDX)
			{
				if ((autoset_gps_baud_stage & AUTOSET_GPS_BAUD_LOOP_MASK) < AUTOSET_GPS_BAUD_LOOP_MASK)
					autoset_gps_baud_stage += 0x10;
				else
					timer_autoset_gps_baud = timer_destroy(timer_autoset_gps_baud);
			}
		}
	}
}

void autoset_gps_module_baudrate(uint8_t flag)					// 0-9600; 1-38400; 2-115200
{
	uint8_t  bd_index = flag & AUTOSET_GPS_BAUD_CURR_RATE_MASK;

	if (timer_autoset_gps_baud == 0xff)
	{
		timer_autoset_gps_baud = timer_initial(0, 400, autoset_gps_module_baudrate_handle);
		if (timer_autoset_gps_baud != 0xff)
		{
			paras_num_of_gps_sentence = 0;
			autoset_gps_baud_counter = 0;
			autoset_gps_baud_stage = bd_index;
			usart_set_baudrate(UART_GPS_INDEX, bd_table[bd_index]); 	// usart_configuration(UART_GPS_INDEX, new_baud, 1);
			vlog_i("usart","[%d]Init baud=%d and detect", timestamp_kb_scan, bd_table[bd_index]);
		}
		else
		{
			vlog_i("usart","Apply timer to detece baudrate fail");
		}
	}
}

const char send_binary_string[12] = "send_binary";
const char check_binary_string[12] = "chk_binary";
const char receive_binary_string[12] = "rcv_binary";
static uint8_t bin_msg_num = 0, bin_msg_cont[MAX_ONE_PDT_FRAME_MSG] = "";
static uint32_t bin_msg_caller = 0;
void print_receive_bin_msg(uint8_t num, uint8_t *data, uint32_t caller, uint32_t be_called)
{
	uint32_t i, n;
	uint8_t checksum, n_payload, dat[256], *payload;

	if (num)
	{
		bin_msg_num = num;
		bin_msg_caller = caller;
		memcpy(bin_msg_cont, data, num);
		if (is_individual_id(interphone_mode, be_called))
			bin_msg_caller |= (uint32_t)USER_ID_BACKGROUND << 24;
		else
			bin_msg_caller &= ~((uint32_t)USER_ID_BACKGROUND << 24);
	}

	if (bin_msg_num)
	{
		dat[0] = (bin_msg_caller & ((uint32_t)USER_ID_BACKGROUND << 24)) ? 'I' : 'G';
		id_to_number(interphone_mode, 0, bin_msg_caller, 0, dat + 1);
		n = p_strlen(dat);
		for (checksum = 0, i = 0; i < n; i++)
			checksum += dat[i];

		dat[n] = ' ';
		payload = dat + n + 1;
		n_payload = hexadecimal2string(payload, bin_msg_cont, bin_msg_num * 2, 0);
		for (i = 0; i < n_payload; i++)
			checksum += payload[i];
		n = p_strlen(dat) - 1;		// -1: del the ':'

		vlog_i("usart","%s %d %s %02X", receive_binary_string, n, dat, checksum);	// rcv_binary 25 I328-20-588 1B339C8DEF0008 6B
	}
	else
	{
		vlog_i("usart","%s 0 0 00 00", receive_binary_string);
	}
}

void check_bin_msg_and_convert_to_hex(uint8_t *data)	// send_binary 24 328-20-901 1B339C8DEF0008 17
{
	uint32_t id, n_string_len, num_number, num_content, n_payload;
	uint8_t checksum, dat[USART_BUFFER_LENGTH * 2] = "", dat_reverse[USART_BUFFER_LENGTH * 2] = "";

	if (data)
	{
		sscanf((char *)data, "%d %s %s %x", &n_string_len, dat_reverse, dat, &n_payload);
		num_number = p_strlen(dat_reverse);
		num_content = p_strlen(dat);
		if (n_string_len == num_number + num_content)
		{
			for (checksum = 0, n_string_len = 0; n_string_len < num_number; n_string_len++)
				checksum += dat_reverse[n_string_len];
			for (n_string_len = 0; n_string_len < num_content; n_string_len++)
				checksum += dat[n_string_len];

			if (checksum == (uint8_t)n_payload)
			{
				id = number_to_id(interphone_mode, 0, dat_reverse);
				if (id && num_content && (num_content < ZZWPRO_MSG_MAX_LENGTH))
				{
					n_string_len = string2hexadecimal(dat, dat_reverse, num_content);	// num of char for process ing
					n_string_len = n_string_len / 2 + (n_string_len & 0x01);			// num of hex bytes
//					for (n_payload = 0; n_payload < n_string_len; n_payload++)
//						dat[n_payload] = dat_reverse[n_string_len - n_payload - 1];		// reverse the hex data
					n_payload = send_bin_data_async(id, n_string_len, dat_reverse/*dat*/);
					vlog_i("usart","%s:%s", send_binary_string, n_payload ? "OK" : "overlength");
				}
				else
				{
					vlog_i("usart","%s:id error or payload overflow(%d:%d)", send_binary_string, id, num_content);
				}
			}
			else
			{
				vlog_i("usart","%s:checksum error(%02x)", send_binary_string, checksum);
			}
		}
		else
		{
			vlog_i("usart","%s:length not match(%d:%d+%d)", send_binary_string, n_string_len, num_number, num_content);
		}
	}
	else
	{
		vlog_i("usart","%s:null string", send_binary_string);
	}
}


uint8_t print_can_open_info(void);
void print_adc(void);
void set_auto_switch_rf_power(uint8_t dat);
void set_vad_paras(uint16_t ext_noise, uint16_t mic_diff);
//void sdcard_test(uint16_t flag);
void base_alarm(uint8_t second, uint8_t type);
#ifdef DEBUG_FAN_CTRL_LOGIC
uint8_t set_module_fake_temp(uint8_t ftemp);
#endif
#ifdef FAKE_MOBILE_BE_CALLED
void set_fake_stack_para_aux2(uint8_t flag);
#endif

//extern uint8_t test_key_debounce;
//const uint8_t test_gps_position[13] = {0x00, 0x48, 0xa3, 0xa4, 0x96, 0x4e, 0x3d, 0xfc, 0xd6, 0x64, 0xa7, 0xc5, 0xc0};
const char reportrssi_string[12] = "reportrssi";
void run_command(uint8_t gui_call_flag, uint8_t *info)
{
	uint8_t cmd[USART_BUFFER_LENGTH * 2];
//	uint8_t no[20];
	int num_para, dec_para[CMD_MAX_ARGS];

	num_para = sscanf((char *)info, "%s %d %d %d %d %d %d", cmd,
				dec_para, dec_para + 1, dec_para + 2, dec_para + 3, dec_para + 4, dec_para + 5);

	if(!strcmp((const char *)cmd, "info") && (num_para == 1))
	{
		print_version();
		print_stack_version();
	}
	else if(!strcmp((const char *)cmd, "reboot") && (num_para == 1))
	{
		vlog_i("usart","reboot the device now...");
		system_power_down(2);	// ((void (*)(void))(*((uint32_t *)0x8000004)))();
	}
	else if(!strcmp((const char *)cmd, "shutdown") && (num_para == 1))
	{
		system_power_down(1);
	}
	else if(!strcmp((const char *)cmd, "__produce__administrtor__") && (num_para == 1))
	{
		vlog_i("usart","\r\n open administrtor mode for producing......");
		set_test_mode_flag(0xff);
	}
	else if(!strcmp((const char *)cmd, "disable__admin") && (num_para == 1))
	{
		vlog_i("usart","\r\n close administrtor mode");
		set_test_mode_flag(0);
	}
#ifdef FAKE_MOBILE_BE_CALLED
	else if(!strcmp((const char *)cmd, "fakevs") && (num_para == 2))
	{
		set_fake_stack_para_aux2((uint8_t)dec_para[0]);
	}
#endif
	else if(!strcmp((const char *)cmd, "showtime") && (num_para == 1))
	{
		printf_time();
	}
	else if(!strcmp((const char *)cmd, "getdumpnum") && (num_para == 1))
	{
		vlog_i("usart","[UART]dumpnum=%d", set_dump_data_length(1));
	}
	else if(!strncmp((const char *)cmd, "SSSSS", 5))
	{
		vlog_i("usart","[UART]stackdebug");
		set_output_stack_dump_data(1);
	}
	else if(!strncmp((const char *)cmd, "NNNNN", 5))
	{
		set_output_stack_dump_data(0);
		set_dump_data_length(0);
		vlog_i("usart","[UART]normal");
	}
	else if(!strcmp((const char *)cmd, "audiopa") && (num_para == 2))
	{
		if ((uint8_t)dec_para[0] <= 1)
		{
			audio_pa_manual_config(1);
			audio_pa_ctrl_process((uint8_t)dec_para[0]);
		}
		else
		{
			audio_pa_manual_config(0);
			audio_pa_ctrl_process(0);
		}
	}
	else if (!strcmp((const char *)cmd, reportrssi_string))
	{
//		if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
//		{
//			if ((dev_is_base() == 0) && (dev_have_base_feature() == 0))
			{
				if (num_para != 2)
					dec_para[0] = 0;

				if (setup_report_downlink_rssi(0xff, 0))
					setup_report_downlink_rssi(0, (uint16_t)dec_para[0]);
				else
					setup_report_downlink_rssi(1, (uint16_t)dec_para[0]);
				vlog_i("usart","%s:%s", reportrssi_string, setup_report_downlink_rssi(0xff, 0) ? "enable" : "disable");
			}
//			else
//			{
//				vlog_i("usart","Base not support this function");
//			}
//		}
//		else
//		{
//			vlog_i("usart","Only zzwpro support this function");
//		}
	}
	else if(!strcmp((const char *)cmd, "showcrystal") && (num_para == 1))
	{
		print_crystal();
	}
	else if(!strcmp((const char *)cmd, send_binary_string))		// send_binary 24 328-20-901 1b339c8def0008 b7
	{
		check_bin_msg_and_convert_to_hex(info + p_strlen(send_binary_string));
	}
	else if(!strcmp((const char *)cmd, check_binary_string))
	{
		print_receive_bin_msg(0, 0, 0, 0);
	}
	else if (get_test_mode_flag())
	{

	//////////////////////// require admin mode to execute ////////////////////////

	if(!strcmp((const char *)cmd, "adc") && (num_para == 1))
	{
		if (is_admittest_screen())
		{
			battery_login_sound_warning();
			print_adc();
		}
		else
		{
			toggle_print_adv_state();
		}
	}
	else if(!strcmp((const char *)cmd, "workmode") && (num_para == 2))
	{
		maintain_setup_parameter(PARA_OPERATE_WRITE, WORK_MODE_PARAS_POS, (uint8_t)dec_para[0]);
		vlog_i("usart","set mode as %d", maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0));
		system_power_down(2);
	}
	else if(!strcmp((const char *)cmd, "settime") && (num_para == 4))
	{
		rtc_time_regular(dec_para[0], dec_para[1], dec_para[2]);
		update_time_force();
	}
	else if(!strcmp((const char *)cmd, "setdate") && (num_para == 5))
	{
		rtc_date_regular(dec_para[0], dec_para[1], dec_para[2], dec_para[3]);
	}
	else if(!strcmp((const char *)cmd, "showid") && (num_para == 1))
	{
		maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, (uint32_t *)dec_para);
		vlog_i("usart","Device ID: 0x%06X", (uint32_t)(dec_para[0] & 0xffffff));
		get_esn_from_flash((uint32_t)cmd);
		vlog_i("usart","Device ESN: %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X",
			cmd[0], cmd[1], cmd[2], cmd[3], cmd[4], cmd[5], cmd[6], cmd[7],
			cmd[8], cmd[9], cmd[10], cmd[11], cmd[12], cmd[13], cmd[14], cmd[15]);
	}
/*	else if(!strcmp((const char *)cmd, "fakebt") && (num_para == 1))
	{
//		test_bt_dma_reveive_frame();
		fake_data_port_send_nv_wireless_frame();
	}
*/
	else if(!strcmp((const char *)cmd, "user_dat") && (num_para == 2))
	{
		send_data_async(dec_para[0]);
	}
	else if(!strcmp((const char *)cmd, "pdu_dat") && (num_para == 2))
	{
		send_pdu_data_async((uint8_t *)0, (uint8_t)dec_para[0]);
	}
	else if(!strcmp((const char *)cmd, "send_msg"))
	{
		sscanf((char *)info, "%s %d %d %s", (char *)(dec_para + 2), dec_para, dec_para + 1, (char *)cmd);
		send_message_asynchronous((uint32_t)dec_para[0], (dec_para[1] == 0) ? MESSAGE_TYPE_USER_MSG : MESSAGE_TYPE_BIN_MSG, p_strlen(cmd), cmd);
	}
	else if(!strcmp((const char *)cmd, "ptt") && (num_para == 2))
	{
		if (dec_para[0])
			set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
		else
			set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
	}
	else if(!strcmp((const char *)cmd, "cancel") && (num_para == 2))
	{
		if (dec_para[0])
			set_sync_call_stack_type(SYNC_CALL_STACK_CANCEL_PRESSED);
		else
			set_sync_call_stack_type(SYNC_CALL_STACK_CANCEL_RELEASED);
	}
	else if(!strcmp((const char *)cmd, "autocall") && (num_para == 3))
	{
		toggle_auto_calling((uint16_t)dec_para[0], (uint16_t)dec_para[1], 0xff);
	}
	else if(!strcmp((const char *)cmd, "rssiregulate"))
	{
		if (num_para == 1)
		{
			SwitchPage(show_rssi_regulate_screen);
		}
		else if (num_para == 2)
		{
			if (is_rssi_regulate_screen() && ((dec_para[0] == 0) || (dec_para[0] == 1)))
				rssi_regulate_button_handle((uint16_t)dec_para[0]);
		}
	}
/*	else if(!strcmp((const char *)cmd, "admittest"))
	{
		if ((num_para == 2) && (dec_para[0] >= 200) && (dec_para[0] <= 201))
		{
			set_admit_test_mode_ex(dec_para[0] - 200);
			vlog_i("usart","\tSet to %s module", admit_test_mode_is_ex() ? "EXT" : "NOR");
		}
		else
		{
			if (num_para == 1)
				set_admit_test_mode(1);
			else if (num_para == 2)
				set_admit_test_mode(dec_para[0]);
			else
				set_admit_test_mode(0);

			SwitchPage(show_admitance_testing_screen);
			call_ui_to_do_something(PFUNC_GUI_MAINTAIN_TYPE_JUMP_TO_RF_DEBUG_PAGE);
		}
	}*/
	else if(!strcmp((const char *)cmd, "admittest"))
	{
		ui_enter_admittest_mode(1);
		dsp_op_paras_process(0, BB_RF_BUS_DATA_TYPE_PC_GET_PARAS, 0);
		call_ui_to_do_something(PFUNC_GUI_MAINTAIN_TYPE_JUMP_TO_RF_DEBUG_PAGE);
	}

	else if(!strcmp((const char *)cmd, "setvolume") && (num_para == 2))
	{
		Set_Speak_Volume((uint8_t)dec_para[0]);
	}
	else if(!strcmp((const char *)cmd, "freesetvol") && (num_para == 2))
	{
		vlog_i("usart","Set vol=%d(max:-128~127)", set_speaker_volume_free((int8_t)dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "freesetmic") && (num_para == 2))
	{
		vlog_i("usart","Set mic gain=%d(max:-128~127)", set_mic_gain_free((int8_t)dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "channel") && (num_para == 2))
	{
		config_pll_paras(interphone_mode, 0, (uint16_t)dec_para[0]);
	}
	else if(!strncmp((const char *)cmd, "DDDDD", 5))
	{
		vlog_i("usart","[UART]sdcarddebug");
		set_output_stack_dump_data(UART_LINKTO_SD_INDEX);
	}
	else if(!strncmp((const char *)cmd, "PPPPP", 5))
	{
		vlog_i("usart","[UART]pocupgrade");
		set_output_stack_dump_data(UART_LINKTO_DSP_INDEX);
	}
	else if (!strcmp((const char *)cmd, "gpsconfig") && (num_para == 2))
	{
		if ((dec_para[0] >= 1) && (dec_para[0] <= 3))
		{
			if ((SET_GPS_MODE_ENABLE >= 3) && (SET_GPS_MODE_ENABLE <= 5))
			{
				put_data(UART_GPS_INDEX, (uint8_t *)set_gps_sys_string[SET_GPS_MODE_ENABLE - 3][dec_para[0] - 1], p_strlen(set_gps_sys_string[SET_GPS_MODE_ENABLE - 3][dec_para[0] - 1]));
				vlog_i("usart","[Set mode]%s", (char *)set_gps_sys_string[SET_GPS_MODE_ENABLE - 3][dec_para[0] - 1]);
			}
			else
			{
				vlog_i("usart","GPS NOT recognize");
			}
		}
		else if (dec_para[0] == 9600)
		{
			put_data(UART_GPS_INDEX, (uint8_t *)set_gps_baudrate_casic[0], p_strlen(set_gps_baudrate_casic[0]));
		}
		else if (dec_para[0] == 38400)
		{
			put_data(UART_GPS_INDEX, (uint8_t *)set_gps_baudrate_casic[1], p_strlen(set_gps_baudrate_casic[1]));
		}
		else if (dec_para[0] == 57600)
		{
			put_data(UART_GPS_INDEX, (uint8_t *)set_gps_baudrate_casic[2], p_strlen(set_gps_baudrate_casic[2]));
		}
//		else if (dec_para[0] == 115200)
//		{
//			put_data(UART_GPS_INDEX, (uint8_t *)set_gps_baudrate_casic[4], p_strlen(set_gps_baudrate_casic[4]));
//		}
		else if (dec_para[0] == 10)
		{
			assessories_gps_set_power(0);
		}
		else if (dec_para[0] == 11)
		{
			assessories_gps_set_power(1);
		}
		else if (dec_para[0] == 20)
		{
			if (config_gps_sentence_output(1, 1))
				vlog_i("usart","Close GSV");
			else
				vlog_i("usart","NOT support");
		}
		else if (dec_para[0] == 21)
		{
			if (config_gps_sentence_output(1, 0))
				vlog_i("usart","All sentence on");
			else
				vlog_i("usart","NOT support");
		}
		else if (dec_para[0] == 30)
		{
			autoset_gps_module_baudrate(0);
		}
		else if (dec_para[0] == 31)
		{
			autoset_gps_module_baudrate(1);
		}
		else if (dec_para[0] == 255)
		{
			put_data(UART_GPS_INDEX, (uint8_t *)set_gps_save_para_casic, p_strlen(set_gps_save_para_casic));
			vlog_i("usart","Save GPS para now");
		}
		else
		{
			vlog_i("usart","GPS para invalid");
		}
	}
	else if (!strcmp((const char *)cmd, "gps"))
	{
		set_gps_data_output((num_para == 1) ? 0xff : dec_para[0]);
	}
	else if(!strcmp((const char *)cmd, "setfreqband") && (num_para == 2))
	{
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_FREQ_BAND, (uint32_t *)(&dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "setfreq") && (num_para == 3))
	{
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_FREQ, (uint32_t *)(&dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "setbias26") && (num_para == 3))
	{
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_BIAS_26M, (uint32_t *)(&dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "settv") && (num_para == 3))
	{
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_RCV_TV, (uint32_t *)(&dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "setmod5k") && (num_para == 2))
	{
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_MOD_5K, (uint32_t *)(&dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "setbias19m2") && (num_para == 2))
	{
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_BIAS_19M2, (uint32_t *)(&dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "setrfpower") && (num_para == 3))
	{
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_RF_POWER, (uint32_t *)(&dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "setclimb") && (num_para == 6))
	{
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_CLIMB_CURVE, (uint32_t *)(&dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "setregu") && (num_para == 2))
	{
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_RSSI_REGULATOR, (uint32_t *)(&dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "sel4819") && (num_para == 2))
	{
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_BK4819_SEL, (uint32_t *)(&dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "berctrl") && (num_para == 2))
	{
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_BER_CTRL, (uint32_t *)(&dec_para[0]));
	}
	else if(!strcmp((const char *)cmd, "rdtune") && (num_para == 1))
	{
		dsp_op_paras_process(gui_call_flag, BB_RF_BUS_DATA_TYPE_PC_GET_PARAS, 0);
	}
	else if(!strcmp((const char *)cmd, "rebootdsp") && (num_para == 1))
	{
		let_dsp_todo_something(gui_call_flag, BASEBAND_TYPE_REBOOT);
	}
	else if(!strcmp((const char *)cmd, "savedsp") && (num_para == 1))
	{
		dec_para[0] = 1;
		dsp_tune_process(gui_call_flag, DSP_TUNE_TYPE_SAVE_DATA, (uint32_t *)(&dec_para[0]));
	}
	else
	{
		if (automatic_calibration_command_line_proc(cmd, num_para, dec_para))
		{
		}
		else if ((p_strncmp(cmd, "AT", 2) == 0) && uart2_use_as_bt() && (uart2_is_new_bt() == 0))
		{
			dec_para[0] = p_strlen(cmd);
			cmd[dec_para[0]++] = '\r';
			cmd[dec_para[0]++] = '\n';		// just for uart-can module; bluetooth should not use this byte
			cmd[dec_para[0]] = 0;
//#ifdef USE_DMA_TO_TR_USART2
//			bt_txdma_start((uint8_t *)cmd, dec_para[0]);
//#else
			put_data(UART_BT_INDEX, (uint8_t *)cmd, dec_para[0]);
//#endif
		}
		else if (!strcmp((const char *)cmd, "GPSDEBUG"))
		{
			dec_para[0] = p_strlen(info);
			info[dec_para[0]++] = '\r';
			info[dec_para[0]++] = '\n';
//			info[dec_para[0]] = 0;
			dec_para[1] = p_strlen("GPSDEBUG") + 1; // add one space
			put_data(UART_GPS_INDEX, info + dec_para[1], dec_para[0] - dec_para[1]);
		}
		else if(!strcmp((const char *)cmd, "setgpsbaud") && (num_para == 2))
		{
			if (dec_para[0] == 9600)
				usart_set_baudrate(UART_GPS_INDEX, 9600); 	// usart_configuration(UART_GPS_INDEX, 9600, 1);
			else if (dec_para[0] == 38400)
				usart_set_baudrate(UART_GPS_INDEX, 38400);	// usart_configuration(UART_GPS_INDEX, 38400, 1);
			else if (dec_para[0] == 57600)
				usart_set_baudrate(UART_GPS_INDEX, 57600);	// usart_configuration(UART_GPS_INDEX, 57600, 1);
//			else if (dec_para[0] == 115200)
//				usart_set_baudrate(UART_GPS_INDEX, 115200);	// usart_configuration(UART_GPS_INDEX, 115200, 1);
			else
				vlog_i("usart","This baudrate NOT support");
		}
		else
		{
			vlog_i("usart","\tcommand <%s> not organized!\r\n", info);
		}
//		run_extern_command(cmd, num_para, dec_para[0], dec_para[1], dec_para[2], dec_para[3]);
	}

	}
	else
	{
		vlog_i("usart","\t open PROJECT first or command <%s> not organized!\r\n", info);
	}
}

void process_command_line(void)
{
	if (readline_into_buffer() > 0)
	{
		run_command(0, console_buffer);
	}

//	if (!printf_redirection)
//	{
//		uart_for_dsp_poll();
//	}
}


/***************************maintain mode procedure***************************/

// fsm defined
#define MAINTAIN_FSM_IDLE				0xff
#define MAINTAIN_FSM_PRE1				0
#define MAINTAIN_FSM_PRE2				1
#define MAINTAIN_FSM_PRE3				2
#define MAINTAIN_FSM_PRE4				3
#define MAINTAIN_FSM_CTASK1				4
#define MAINTAIN_FSM_CTASK2				5
#define MAINTAIN_FSM_BYTES1				6
#define MAINTAIN_FSM_BYTES2				7
#define MAINTAIN_FSM_ADDR1				8
#define MAINTAIN_FSM_ADDR2				9
#define MAINTAIN_FSM_ADDR3				10
#define MAINTAIN_FSM_ADDR4				11
#define MAINTAIN_FSM_DATA				12
#define MAINTAIN_FSM_EPI1				0xf0
#define MAINTAIN_FSM_EPI2				0xf1

// frame const defined
#define FRAME_PREFACE1					0x17
#define FRAME_PREFACE2					0xea
#define FRAME_PREFACE3					0x18
#define FRAME_PREFACE4					0xeb
#define FRAME_EPILOG1					0x19
#define FRAME_EPILOG2					0xec

#define FRAME_CTASK_SD_TEST				0x5344
#define FRAME_DATA_MAX_LENGTH			DATA_LENGTH_AT_ONE_MAINTAIN_FRAME
#define FRAME_MIN_LENGTH				(MAINTAIN_FSM_DATA + 2)
#define FRAME_PREFACE					(((uint32_t)FRAME_PREFACE4 << 24) | ((uint32_t)FRAME_PREFACE3 << 16) | ((uint32_t)FRAME_PREFACE2 << 8) | FRAME_PREFACE1)
#define FRAME_EPILOG					(((uint16_t)FRAME_EPILOG2 << 8) | FRAME_EPILOG1)
#define PREFACE_CONTENT					*((uint32_t *)(&mt_mode_buffer[MAINTAIN_FSM_PRE1]))
#define CTASK_CONTENT					*((uint16_t *)(&mt_mode_buffer[MAINTAIN_FSM_CTASK1]))
#define BYTES_CONTENT					*((uint16_t *)(&mt_mode_buffer[MAINTAIN_FSM_BYTES1]))
#define ADDR_CONTENT					*((uint32_t *)(&mt_mode_buffer[MAINTAIN_FSM_ADDR1]))
#define EPILOG1_CONTENT					mt_mode_buffer[MAINTAIN_FSM_DATA + BYTES_CONTENT]
#define EPILOG2_CONTENT					mt_mode_buffer[MAINTAIN_FSM_DATA + BYTES_CONTENT + 1]


#pragma pack(4)
static uint8_t mt_mode_buffer[1024] = {FRAME_PREFACE1, FRAME_PREFACE2, FRAME_PREFACE3, FRAME_PREFACE4};
static uint16_t mt_mode_data_index = 0;

#define SCIF_IO_BUSY_RESULT		-23
#define SCIF_RETURN_LESS_RESULT	-67
#define READ_SDCARD_MAX_TIMES	5
int SD_Comm_CheckRecv(uint8_t *Buf);
void send_poc_test_frame_to_host(uint8_t *dat)
{
	PREFACE_CONTENT = FRAME_PREFACE;
	CTASK_CONTENT = FRAME_CTASK_SD_TEST;
	BYTES_CONTENT = USART2_DMA_LENGTH;
	memcpy(&mt_mode_buffer[MAINTAIN_FSM_DATA], dat, USART2_DMA_LENGTH);
	EPILOG1_CONTENT = FRAME_EPILOG1;
	EPILOG2_CONTENT = FRAME_EPILOG2;

	put_data(printf_redirection, mt_mode_buffer, FRAME_MIN_LENGTH + BYTES_CONTENT);

	mt_mode_data_index = 0;
	memset(mt_mode_buffer, 0, sizeof(mt_mode_buffer));
}

void readdata_into_mtbuffer(uint8_t *data, uint8_t n)
{
	uint8_t reset_to_normal = 0, mt_mode_buffer_tmp[1024], read_sd_times = 0;
	int result, result_size = 0;

	memcpy(mt_mode_buffer + mt_mode_data_index, data, n);
	mt_mode_data_index += n;
	while (mt_mode_data_index >= FRAME_MIN_LENGTH)	// preface(4) + ctask(2) + bytes(2) + addr(4) + epilog(2)
	{
		if ((PREFACE_CONTENT == FRAME_PREFACE) && (CTASK_CONTENT == FRAME_CTASK_SD_TEST))
		{
			if (BYTES_CONTENT <= FRAME_DATA_MAX_LENGTH)
			{
				if (mt_mode_data_index >= FRAME_MIN_LENGTH + BYTES_CONTENT)
				{
					if ((EPILOG1_CONTENT == FRAME_EPILOG1) && (EPILOG2_CONTENT == FRAME_EPILOG2))
					{
						if (BYTES_CONTENT == 0)
						{
							reset_to_normal = 1;
							p_strcpy(mt_mode_buffer_tmp, "[UART]normal");
							BYTES_CONTENT = p_strlen(mt_mode_buffer_tmp);
						}
						else
						{
							if (printf_redirection == UART_LINKTO_DSP_INDEX)
							{
								if (BYTES_CONTENT == USART2_DMA_LENGTH)	// == BT DMA number
								{
									send_bt_frame_to_host(&mt_mode_buffer[MAINTAIN_FSM_DATA], 0, 0xffff, 0, 0);
									BYTES_CONTENT = 0xffff; // not reply host directly
								}
								else
								{
									goto mt_frame_error;
								}
							}
							else
							{
								result = SmartCard_SendAPDU(&mt_mode_buffer[MAINTAIN_FSM_DATA], (int)BYTES_CONTENT);
								if (result == 0)
								{
									do
									{
										sys_delay(100);
										result = SmartCard_GetAPDU(mt_mode_buffer_tmp, &result_size);
										read_sd_times++;
										if (result == 0)
										{
											BYTES_CONTENT = MIN(result_size, 1000);
											read_sd_times = READ_SDCARD_MAX_TIMES;
										}
										else
										{
											// get apdu error
											SmartCard_GetAPDU_ABS(mt_mode_buffer_tmp + 1000, 24);
											if ((result != SCIF_RETURN_LESS_RESULT) || (SD_Comm_CheckRecv(mt_mode_buffer_tmp + 1000) != SCIF_IO_BUSY_RESULT) || (read_sd_times >= READ_SDCARD_MAX_TIMES))
											{
												sprintf((char *)mt_mode_buffer_tmp, "GET SD ERROR:%d.%d(%02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X)", result, read_sd_times,
													mt_mode_buffer_tmp[1000], mt_mode_buffer_tmp[1001], mt_mode_buffer_tmp[1002], mt_mode_buffer_tmp[1003], mt_mode_buffer_tmp[1004], mt_mode_buffer_tmp[1005],
													mt_mode_buffer_tmp[1006], mt_mode_buffer_tmp[1007], mt_mode_buffer_tmp[1008], mt_mode_buffer_tmp[1009], mt_mode_buffer_tmp[1010], mt_mode_buffer_tmp[1011],
													mt_mode_buffer_tmp[1012], mt_mode_buffer_tmp[1013], mt_mode_buffer_tmp[1014], mt_mode_buffer_tmp[1015], mt_mode_buffer_tmp[1016], mt_mode_buffer_tmp[1017],
													mt_mode_buffer_tmp[1018], mt_mode_buffer_tmp[1019], mt_mode_buffer_tmp[1020], mt_mode_buffer_tmp[1021], mt_mode_buffer_tmp[1022], mt_mode_buffer_tmp[1023]);
												BYTES_CONTENT = p_strlen(mt_mode_buffer_tmp);
												read_sd_times = READ_SDCARD_MAX_TIMES;
											}
										}
									} while (read_sd_times < READ_SDCARD_MAX_TIMES);
								}
								else
								{
									// send apdu error
									sprintf((char *)mt_mode_buffer_tmp, "SEND SD ERROR:%d", result);
									BYTES_CONTENT = p_strlen(mt_mode_buffer_tmp);
								}
							}
						}

						if (BYTES_CONTENT != 0xffff)
						{
							memcpy(&mt_mode_buffer[MAINTAIN_FSM_DATA], mt_mode_buffer_tmp, BYTES_CONTENT);
							EPILOG1_CONTENT = FRAME_EPILOG1;
							EPILOG2_CONTENT = FRAME_EPILOG2;
							result_size = FRAME_MIN_LENGTH + BYTES_CONTENT;
							goto mt_frame_finish;
						}
						else
						{
							goto mt_frame_finish_noreply;
						}
					}
					else
					{
						goto mt_frame_error;
					}
				}
				else
				{
					return;		// waiting next frame
				}
			}
			else
			{
mt_frame_error:
				BYTES_CONTENT = 0;
				ADDR_CONTENT = 0xffffffff;
				EPILOG1_CONTENT = FRAME_EPILOG1;
				EPILOG2_CONTENT = FRAME_EPILOG2;
				BYTES_CONTENT = 0xffff;
				result_size = FRAME_MIN_LENGTH;
mt_frame_finish:
				put_data(printf_redirection, mt_mode_buffer, result_size);
mt_frame_finish_noreply:
				mt_mode_data_index = 0;
				memset(mt_mode_buffer, 0, sizeof(mt_mode_buffer));
				if (reset_to_normal)
				{
//					set_output_stack_dump_data(0);
					sys_delay(100);
					system_power_down(2);
				}
			}
		}
		else
		{
			mt_mode_data_index--;
			memcpy(mt_mode_buffer_tmp, mt_mode_buffer + 1, mt_mode_data_index);
			memcpy(mt_mode_buffer, mt_mode_buffer_tmp, mt_mode_data_index);
			mt_mode_buffer[mt_mode_data_index] = 0;
		}
	}
}

void uart_data_linkto_others(void)
{
	uint8_t c, n = 0;

	while (1)
	{
		if (getch_noblock(printf_redirection, &c))
		{
			console_buffer[n++] = c;
			if (n >= 240)
				break;
		}
		else
		{
			break;
		}
	}

	if (n)
	{
		if ((printf_redirection == UART_LINKTO_DSP_INDEX) || (printf_redirection == UART_LINKTO_SD_INDEX))
		{
			readdata_into_mtbuffer(console_buffer, n);
		}
	}
}


/***************************usart interrupt handler***************************/

//////////////////////////////////// DEBUG ////////////////////////////////////
#if 0
void USART1_IRQHandler(void)									// debug
{
//	if(USART1->ctrl1_bit.rdbfien != RESET)						// RDBF interrupt enable
	{
		if(usart_flag_get(USART1, USART_RDBF_FLAG) != RESET)
		{
			/* read one byte from the receive data register */
			con_buffer[con_write_index] = usart_data_receive(USART1);
			if (++con_write_index == con_read_index)
				con_write_index--;
//			if(usart2_rx_counter == usart3_tx_buffer_size)
//			{
//				/* disable the usart2 receive interrupt */
//				usart_interrupt_enable(USART1, USART_RDBF_INT, FALSE);
//			}
		}
	}

//	if(USART1->ctrl1_bit.tdbeien != RESET)						// TDBE interrupt enable
//	{
//		if(usart_flag_get(USART1, USART_TDBE_FLAG) != RESET)
//		{
//			/* write one byte to the transmit data register */
//			usart_data_transmit(USART1, usart2_tx_buffer[usart2_tx_counter++]);
//
//			if(usart2_tx_counter == usart2_tx_buffer_size)
//			{
//				/* disable the usart2 transmit interrupt */
//				usart_interrupt_enable(USART1, USART_TDBE_INT, FALSE);
//			}
//		}
//	}
}
#endif

void debug_txdma_start(uint8_t *data, uint32_t len)
{
	uint16_t i;
	static uint16_t debug_txdata_len_sending = 0, debug_txdata_len_append = 0;

//	NVIC_DisableIRQ(DMA1_Channel1_IRQn);
	if (stack_dump_buffer_busy)				// sending now
	{
		if (len == 0) 						// call by dma interrupt
		{
			if (debug_txdata_len_append)
			{
				for (i = 0; i < debug_txdata_len_append; i++)
					stack_dump_dma_data[i] = stack_dump_dma_data[i + debug_txdata_len_sending];
				debug_txdata_len_sending = debug_txdata_len_append;
				debug_txdata_len_append = 0;
//				goto debug_txdma_restart;
			}
			else
			{
				debug_txdata_len_sending = 0;
				debug_txdata_len_append = 0;
				stack_dump_buffer_busy = 0;
			}
		}
		else if (debug_txdata_len_sending + debug_txdata_len_append + len < STACK_DUMP_TXDATA_BUFFER_LEN)
		{
			memcpy(&stack_dump_dma_data[debug_txdata_len_sending + debug_txdata_len_append], data, len);
			debug_txdata_len_append += len;
		}
	}
	else
	{
		if (len)
		{
			stack_dump_buffer_busy = 1;
			memcpy(stack_dump_dma_data, data, len);
			debug_txdata_len_sending = len;
			debug_txdata_len_append = 0;
//debug_txdma_restart:
//			spi_dma_buf_reinit(UART_DEBUG_TX_DMA, debug_txdata_len_sending, (uint32_t)stack_dump_dma_data);
			/* Enable the SPI DMA Stream*/
//			dma_channel_enable(UART_DEBUG_TX_DMA, TRUE); // auto re-start the dma transfer
		}
	}
//	NVIC_EnableIRQ(DMA1_Channel1_IRQn);
}

///////////////////////////////////// GPS /////////////////////////////////////


void USART6_IRQHandler(void)									// GPS
{
#if 0
	uint8_t edma6_cnt, edma6_which_dma;

	if(usart_flag_get(USART6, USART_RDBF_FLAG) != RESET)
	{
		edma6_cnt = (uint8_t)usart_data_receive(USART6);
//		vlog_i("usart","Com6 rx=%c(%d %d)", edma6_cnt, gps_write_index, gps_read_index);
		gps_data_buffer[gps_write_index] = edma6_cnt;
		if (++gps_write_index == gps_read_index)
			gps_write_index--;
	}

	/*
		���߿��У�Idle flag������⵽���߿���ʱ����λ��Ӳ��������������������???�ȶ� USART_STS���ٶ� USART_DT��

		IDLE: This bit is set by hardware when an Idle Line is detected. An interrupt is generated if the
		IDLEIE=1 in the USART_CR1 register. It is cleared by a software sequence (an read to the
		USART_SR register followed by a read to the USART_DR register).

		Note: The IDLE bit will not be set again until the RXNE bit has been set itself (i.e. a new idle line occurs)
	*/
	if (usart_flag_get(USART6, USART_IDLEF_FLAG))				// only enable at dma mode
	{
		usart_data_receive(USART6);
		edma6_cnt = edma_data_number_get(UART_GPS_RX_DMA);
		edma6_which_dma = edma_memory_target_get(UART_GPS_RX_DMA);
#ifdef DEBUG_RX_DMA_ENABLE_IDLE
		vlog_i("usart","Com6 idle=%d %d", edma6_cnt, edma6_which_dma);
#endif
		if (edma6_cnt != USART6_DMA_LENGTH)
		{
			edma_stream_enable(UART_GPS_RX_DMA, FALSE);

			if (edma6_which_dma == EDMA_MEMORY_0) // ����������ƹ�һ���ġ�???
			{
				process_gps_dma_data(gps_data_buffer0 + 4, USART6_DMA_LENGTH - edma6_cnt);
				edma_memory_addr_set(UART_GPS_RX_DMA, (uint32_t)(gps_data_buffer0 + 4), EDMA_MEMORY_0);
			}
			else
			{
				process_gps_dma_data(gps_data_buffer1 + 4, USART6_DMA_LENGTH - edma6_cnt);
				edma_memory_addr_set(UART_GPS_RX_DMA, (uint32_t)(gps_data_buffer1 + 4), EDMA_MEMORY_1);
			}

			edma_data_number_set(UART_GPS_RX_DMA, USART6_DMA_LENGTH);
			edma_flag_clear(EDMA_FDT2_FLAG);
			edma_stream_enable(UART_GPS_RX_DMA, TRUE);
		}
	}
#endif
}

#ifdef USE_INT_TO_RECEIVE_GPS

void process_gps_data(void)
{
	static uint16_t gps_data_index = 0;

	while (getch_noblock(UART_GPS_INDEX, &gps_buffer[gps_data_index]))
	{
		if (gps_buffer[gps_data_index] == '\n')				// 0x0A, one line complete
		{
			gps_buffer[gps_data_index - 1] = 0;				// set 0x0D -> 0
			vlog_i("usart","%s", (char *)gps_buffer);
//			parse_gps_data(gps_buffer, gps_data_index);
			gps_data_index = 0;
		}
		else
		{
			if (++gps_data_index >= GPS_BUFFER_LENGTH)		// overflow
				gps_data_index--;
		}
	}
}

#endif // USE_INT_TO_RECEIVE_GPS

void process_gps_dma_data(uint8_t *gps_data, uint8_t num)
{
	int i;
	static uint16_t gps_data_index = 0;

	for (i = 0; i < num; i++)
	{
		gps_buffer[gps_data_index] = gps_data[i];
		if (gps_buffer[gps_data_index] == '\n')				// 0x0A, one line complete
		{
			gps_buffer[gps_data_index - 1] = 0;				// set 0x0D -> 0
			parse_gps_data(gps_buffer, gps_data_index);
			gps_data_index = 0;
		}
		else
		{
			if (++gps_data_index >= GPS_BUFFER_LENGTH)		// overflow
				gps_data_index--;
		}
	}
}

void EDMA_Stream2_IRQHandler(void)							// uart6(GPS) receiving DMA int
{
#if 0
	if(edma_flag_get(EDMA_FDT2_FLAG) != RESET)
	{
		edma_flag_clear(EDMA_FDT2_FLAG);

		if (edma_memory_target_get(UART_GPS_RX_DMA) == EDMA_MEMORY_0)	// ��ǰĿ������Ǵ�???��0����DMA_SxM0ADDRѰַ����SENΪ0ʱ����λ����������д�룬Ϊ1���λ����״�?��־
		{
#ifdef DEBUG_RX_DMA_ENABLE_IDLE
			vlog_i("usart","Com6 dma0");
#endif
			process_gps_dma_data(gps_data_buffer1 + 4, USART6_DMA_LENGTH);
		}
		else
		{
#ifdef DEBUG_RX_DMA_ENABLE_IDLE
			vlog_i("usart","Com6 dma1");
#endif
			process_gps_dma_data(gps_data_buffer0 + 4, USART6_DMA_LENGTH);
		}
	}
#endif
}



///////////////////////////////////// BT /////////////////////////////////////

void USART2_IRQHandler(void)									// bt
{
#if 0
//	if(USART2->ctrl1_bit.rdbfien != RESET)						// RDBF interrupt enable
	{
		if(usart_flag_get(USART2, USART_RDBF_FLAG) != RESET)
		{
			/* read one byte from the receive data register */
			bt_data_buffer[bt_write_index] = usart_data_receive(USART2);
			if (++bt_write_index == bt_read_index)
				bt_write_index--;
		}
	}
#endif
}

void bt_txdma_start(uint8_t *data, uint32_t len)
{
	uint16_t i;
	static uint16_t bt_txdata_len_sending = 0, bt_txdata_len_append = 0;

//	__disable_irq();
//	NVIC_DisableIRQ(DMA1_Channel5_IRQn);
	if (bt_txdata_busy)						// sending now
	{
		if (len == 0) 						// call by dma interrupt
		{
			if (bt_txdata_len_append)		// have data to send, so copy to head and send it
			{
				for (i = 0; i < bt_txdata_len_append; i++)
					bt_tx_dma_data[i] = bt_tx_dma_data[i + bt_txdata_len_sending];
				bt_txdata_len_sending = bt_txdata_len_append;
				bt_txdata_len_append = 0;
//				goto bt_txdma_restart;
			}
			else							// all data send completely
			{
				bt_txdata_len_sending = 0;
				bt_txdata_len_append = 0;
				bt_txdata_busy = 0;
			}
		}
		else if (bt_txdata_len_sending + bt_txdata_len_append + len < BT_TXDATA_BUFFER_LEN)
		{
			memcpy(&bt_tx_dma_data[bt_txdata_len_sending + bt_txdata_len_append], data, len);
			bt_txdata_len_append += len;
		}
	}
	else
	{
		if (len)
		{
			bt_txdata_busy = 1;
			memcpy(bt_tx_dma_data, data, len);
			bt_txdata_len_sending = len;
			bt_txdata_len_append = 0;
//bt_txdma_restart:
//			spi_dma_buf_reinit(UART_BT_TX_DMA, bt_txdata_len_sending, (uint32_t)bt_tx_dma_data);
			/* Enable the SPI DMA Stream*/
//			dma_channel_enable(UART_BT_TX_DMA, TRUE);	// auto re-start the dma transfer
		}
	}
//	__enable_irq();
//	NVIC_EnableIRQ(DMA1_Channel5_IRQn);
}

void send_xv_wireless_frame_to_host(uint8_t *data, uint16_t len, uint32_t id_caller, uint32_t id_be_called)
{
	uint32_t xv_wireless_frame[USART2_DMA_LENGTH / sizeof(uint32_t)];

	xv_wireless_frame[0] = 0;
	xv_wireless_frame[1] = id_caller;
	xv_wireless_frame[2] = id_be_called;
	memset(&xv_wireless_frame[3], 0, 5 * sizeof(uint32_t));
	memcpy(&xv_wireless_frame[8], data, len);

	send_bt_frame_to_host((uint8_t *)xv_wireless_frame, len + 12, TSC_CTASK_SEND_XV_WIRELESS, 0, 0);
}

static uint8_t send_bt_frame_busy = 0;
void send_bt_frame_to_host(uint8_t *data, uint16_t len, uint16_t ctask, uint16_t bytes, uint32_t addr)		// ctask: 0xffff/0xfffe-the data is reply frame, and 0xfffe means it must recalcate the checksum
{
	uint8_t *ptr8, bt_frame_for_fill[USART2_DMA_LENGTH];
	uint16_t i, checksum;
	BT_DMA_DATA_TYPEDEF *p_btdata;
	bt_context_t ctx = {0};
	comm_frame_t current_frame = {0};

	ctx.serial_fd = g_bt_serial_fd;
	ctx.debug_mode = 1;
	ctx.continuous = 0;

	if ((g_gui_interactive->dev_misc_notify.limit_dport_data == 0) || ((ctask != TSC_CTASK_DEV_STATUS) && (ctask != TSC_CTASK_SEND_SIGNALING) && (ctask != TSC_CTASK_REMOTE_GPS_INFO)))
	{
		vlog_i("usart","Send bt frame(DMA) %d", timestamp_kb_scan);
		if (ctask >= 0xfffe)
		{
			ptr8 = data;
			if (ctask == 0xfffe)
			{
				p_btdata = (BT_DMA_DATA_TYPEDEF *)ptr8;
bt_frame_send_now:
				for (i = 0, checksum = 0; i < BT_DMA_DATA_DLEN + 8; i++)	// jump over the prefix(4B)
					checksum += ptr8[4 + i];
				p_btdata->data[BT_DMA_DATA_DLEN] = (uint8_t)checksum;
				p_btdata->data[BT_DMA_DATA_DLEN + 1] = (uint8_t)(checksum >> 8);
			}
			if (send_bt_frame_busy)
				return;
			send_bt_frame_busy = 1;
			// bt_txdma_start(ptr8, USART2_DMA_LENGTH);
			bt_serial_send_frame(&ctx, (comm_frame_t *)p_btdata);
			send_bt_frame_busy = 0;
		}
		else if (len <= BT_DMA_DATA_DLEN)
		{
			ptr8 = bt_frame_for_fill;
			p_btdata = (BT_DMA_DATA_TYPEDEF *)bt_frame_for_fill;
			p_btdata->prefix = BT_DMA_DATA_PREFIX;
			p_btdata->ctask = ctask;
			p_btdata->bytes = bytes;
			p_btdata->addr = addr;
			memcpy(p_btdata->data, data, len);
			p_btdata->epilog = BT_DMA_DATA_EPILOG;
			goto bt_frame_send_now;
		}
	}
}

void send_bt_frame_to_host_auto_sel(uint8_t *data, uint16_t len, uint16_t ctask, uint16_t bytes, uint32_t addr)		// ctask: 0xffff/0xfffe-the data is reply frame, and 0xfffe means it must recalcate the checksum
{
	send_bt_frame_to_host(data, len, ctask, bytes, addr);
}

void get_area_info_and_send(BT_DMA_DATA_TYPEDEF *frame, uint16_t flag)	// flag: 0-user books; 1-ctrl channel
{
	uint16_t struct_size[2] = {sizeof(GROUP_TRUNKING), sizeof(GROUP_CONTROL_LIST)};
	uint8_t i, step, *ptr8, maintain_type[2] = {ACTIVE_GROUP_OF_BOOKS, ACTIVE_GROUP_OF_CTRL_LIST}, max_group[2] = {MAX_GROUPS_OF_BOOKS, MAX_GROUPS_OF_CTRL};

	if (flag == TSC_CTASK_GET_USER_AREA)
	{
		step = 0;
		ptr8 = g_group_books->grp_trunking[0].name;
	}
	else
	{
		step = 1;
		ptr8 = g_group_books->grp_ctrl_list[0].name;
	}
	memset(frame->data, 0, BT_DMA_DATA_DLEN);
	frame->data[0] = (uint8_t)maintain_setup_parameter(PARA_OPERATE_READ, maintain_type[step], 0);
	for (i = 0; i < max_group[step]; i++)
	{
		if ((ptr8 + i * struct_size[step])[0])
			frame->data[i + 1] = 1;
	}
	send_bt_frame_to_host_auto_sel((uint8_t *)frame, i + 1, 0xfffe, 0, 0);
}

void get_area_name_and_send(BT_DMA_DATA_TYPEDEF *frame, uint16_t flag)	// flag: 0-user books; 1-ctrl channel
{
	uint16_t struct_size[2] = {sizeof(GROUP_TRUNKING), sizeof(GROUP_CONTROL_LIST)};
	uint8_t i, n, end, step, max_item_of_one_frame, *ptr8, *name, max_group[2] = {MAX_GROUPS_OF_BOOKS, MAX_GROUPS_OF_CTRL};

	max_item_of_one_frame = BT_DMA_DATA_DLEN / MAX_USER_NAME_LEN;
	if (flag == TSC_CTASK_GET_USER_AREA_NAME)
	{
		step = 0;
		ptr8 = g_group_books->grp_trunking[0].name;
	}
	else
	{
		step = 1;
		ptr8 = g_group_books->grp_ctrl_list[0].name;
	}

	memset(frame->data, 0, BT_DMA_DATA_DLEN);
	n = frame->data[0];						// index
	i = max_group[step] - n;				// max area number from the index(frame->data[0]) to end
	end = (n >= max_group[step]) ? 0 : MIN(i, max_item_of_one_frame);
	for (i = 0; i < end; i++)
	{
		name = ptr8 + (i + n) * struct_size[step];
		if (name[0])
			memcpy(&frame->data[i * MAX_USER_NAME_LEN], name, MAX_USER_NAME_LEN);
	}
	send_bt_frame_to_host_auto_sel((uint8_t *)frame, i * MAX_USER_NAME_LEN, 0xfffe, 0, 0);
}

uint32_t get_id_from_bt_frame(uint8_t *data)
{
	uint8_t  flag = data[0] & 0x7f, dial_no[10];
	uint32_t rcode;
	USER_BOOK *book;

	if ((data[0] >= '0') && (data[0] <= '9'))
	{
		p_strncpy(dial_no, data, 8);
		dial_no[8] = 0;
		rcode = dial_number_to_id(dial_no);
	}
	else if (flag == 0)
	{
		book = get_books_item_content(get_watch_id_index(), 1);
		rcode = book ? book->id : 0;
		memcpy(&data[4], &rcode, sizeof(uint32_t));
	}
	else if (flag == 1)
	{
		rcode = *((uint32_t *)&data[4]);
	}
	else if (flag == 2)
	{
		maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, &rcode);
		memcpy(&data[4], &rcode, sizeof(uint32_t));
	}
	else
	{
		rcode = 0;
	}

	return rcode;
}

void get_number_from_bt_frame(uint8_t *data)
{
	if (data[0] == 1)
	{
		id_to_number(interphone_mode, 0, *((uint32_t *)&data[4]), 0, g_dial_number);
		g_dial_number_index = p_strlen(g_dial_number);
	}
	else if ((data[0] >= '0') && (data[0] <= '9'))
	{
		g_dial_number_index = p_strlen(data);
		if (g_dial_number_index < 26)	/* DIAL_NUMBER_MAX_LENGTH */
			p_strcpy(g_dial_number, data);
		else
			goto reset_get_number_from_bt_frame;
	}
	else
	{
reset_get_number_from_bt_frame:
		g_dial_number_index = 0;
		g_dial_number[g_dial_number_index] = 0;
	}
}


typedef struct
{
	uint32_t remote_id;				// Զ�˵�̨ID
	uint32_t obj_id;				// ����Ŀ��ID
	uint8_t  rmt_gps_state;			// bit0:������־,0��Ч1��Ч; bit1:0����1����; bit2:0��γ1��γ; bit3:reserved; bit4-6:������Դ���ͣ�0-�ϱ���������1-�������У�bit7:reserved
	uint8_t  reserved[3];
	float    rmt_gps_pos[2];		// Զ���豸�ľ���/γ��
}REMOTE_GPS_INFO_TYPEDEF;

// MSG��������    : R0=0x0000000A,R1=0x85005947,R2=0x80000025: 6D 05 00 00 04 02 E1 7E FC 05 00 10 88 2C 41 44 00 E3 74 49 B9 41 B2 A9 E2 42 42 01 00 00 00 00 00 01 00 00 72 81 20 10 01 00 00 00 BA 6D 0A 6E 5A 6E AA 6E 00 00 00 00 00 00 00 00 00 00 00 07
// LC-��������    : R0=0x0000000A,R1=0x82005B42,R2=0x80000022: 12 05 00 00 01 01 01 00 10 28 00 10 9D 40 49 34 40 A7 B8 24 00 28 32 A3 10 00 20 DC 00 00 00 00 00 01 00 00 72 81 60 00 00 00 00 00 BA 6D 0A 6E 5A 6E AA 6E 00 00 00 00 00 00 00 00 00 00 00 71
void gps3u_to_gps(PDU_GPS3U *gps3u, GPS_STRUCT_TYPEDEF *gps);
void gps4u_to_gps(PDU_GPS4U *gps4u, GPS_STRUCT_TYPEDEF *gps);
void send_remote_gps_info_to_host(uint8_t *air_data)
{
	uint8_t *ptr8, msg_type;
	uint16_t gps_type = 0;
	double lon_1, lon_2, lat_1, lat_2;
	REMOTE_GPS_INFO_TYPEDEF data;
	V_ReportBaseInfo base;
	PDU_GPS4U gps_4u;
	PDU_GPS3U gps_3u;
	GPS_STRUCT_TYPEDEF speaker_pos;

	if (uart2_use_as_dataport())
	{
		if (ZZWPRO_FRAME_IS_MSG_HEADER(air_data))
		{
			ptr8 = air_data + 4 + 8;	// ǰ4BΪǶ���������Ϊ�����ݵ�һ֡��TYPE BIT IDENT1 IDENT2 DATA0
			msg_type = *(air_data + 4);
			if (((msg_type == ZZWPRO_STACK_SIG_TYPE_DATA_IND_CALL) || (msg_type == ZZWPRO_STACK_SIG_TYPE_DATA_GRP_CALL)) &&
					(ptr8[0] == V_WIRELESS_MANAGER_STARTER) && (ptr8[1] >= (sizeof(V_ReportBaseInfo) + 3)) && (ptr8[2] == V_IDT_BASE_INFO))
			{
				data.obj_id = (msg_type == ZZWPRO_STACK_SIG_TYPE_DATA_IND_CALL) ? (USER_ID_INDIVIDUAL << 24) : 0;
				memcpy(&data.obj_id, air_data + 4 + 2, 3);
				data.remote_id = USER_ID_INDIVIDUAL << 24;
				memcpy(&data.remote_id, air_data + 4 + 5, 3);

				gps_type = 1;
				memcpy(&base, ptr8 + 2, sizeof(V_ReportBaseInfo));
				data.rmt_gps_state = base.power.gps_valid | (base.power.east_west << 1) | (base.power.south_north << 2);
				memset(data.reserved, 0, 3);
				data.rmt_gps_pos[0] = base.lon;
				data.rmt_gps_pos[1] = base.lat;
			}
		}
		else if (ZZWPRO_FRAME_IS_VOICE_HEADER(air_data))
		{
			msg_type = *(air_data + 4);
			if ((msg_type == ZZWPRO_STACK_SIG_TYPE_VOICE_IND_CALL) || (msg_type == ZZWPRO_STACK_SIG_TYPE_VOICE_GRP_CALL))
			{
				data.obj_id = (msg_type == ZZWPRO_STACK_SIG_TYPE_VOICE_IND_CALL) ? (USER_ID_INDIVIDUAL << 24) : 0;
				memcpy(&data.obj_id, air_data + 4 + 2, 3);
				data.remote_id = USER_ID_INDIVIDUAL << 24;
				memcpy(&data.remote_id, air_data + 4 + 5, 3);

				// �ο� parse_xv_gps3u_data()���м�
				ptr8 = air_data + 4 + 8;	// ǰ4BΪǶ���������ΪGPS4U
				gps_type = *ptr8 | ((uint16_t)(*(ptr8 + 1)) << 8);
				ptr8 += 2;
				if (gps_type == ZZWPRO_STACK_SIG_GPS4U_FIXED)
				{
//					zzw_process_air_gps4u_data(ptr8);
					memcpy(&gps_4u, ptr8, sizeof(PDU_GPS4U));
					gps4u_to_gps(&gps_4u, &speaker_pos);
				}
				else if (gps_type == ZZWPRO_STACK_SIG_GPS3U_FIXED)
				{
//					zzw_process_air_gps3u_data(ptr8);
					memcpy(&gps_3u, ptr8, sizeof(PDU_GPS3U));
					gps3u_to_gps(&gps_3u, &speaker_pos);
				}
				else
				{
					gps_type = 0;
				}

				if (gps_type)
				{
					data.rmt_gps_state = (speaker_pos.gps_state & 0x07) | (1 << 4); // 1 << 4: ������Դ
					memset(data.reserved, 0, 3);
					if ((rmc_data.gps_state & GPS_DATA_VALID_FLAG) && (speaker_pos.gps_state & GPS_DATA_VALID_FLAG))
					{
						speaker_pos.gps_state &= ~GPS_DATA_IS_FULL;
						cal_full_lon_lat(&rmc_data, &speaker_pos, &lon_1, &lon_2, &lat_1, &lat_2);
						data.rmt_gps_pos[0] = (float)lon_2;
						data.rmt_gps_pos[1] = (float)lat_2;
					}
					else
					{
						data.rmt_gps_state &= ~GPS_DATA_VALID_FLAG;
						if (speaker_pos.gps_state & GPS_DATA_VALID_FLAG)
						{
							data.rmt_gps_pos[0] = (float)CAL_POS_FLOAT(speaker_pos.lon_degree, speaker_pos.lon_integer, speaker_pos.lon_decimal);
							data.rmt_gps_pos[1] = (float)CAL_POS_FLOAT(speaker_pos.lat_degree, speaker_pos.lat_integer, speaker_pos.lat_decimal);
						}
						else
						{
							data.rmt_gps_pos[0] = 0.0;
							data.rmt_gps_pos[1] = 0.0;
						}
					}
				}
			}
		}

		if (gps_type)
		{
			send_bt_frame_to_host((uint8_t *)&data, sizeof(REMOTE_GPS_INFO_TYPEDEF), TSC_CTASK_REMOTE_GPS_INFO, 0, 0);
#ifdef DEBUG_REAL_BT
			msg_type = (data.rmt_gps_state >> 4) & 0x07;
			vlog_i("usart","[REMOTE:%s]%08x->%08x, GPS=0x%02X(lon=%3.6f, lat=%3.6f)", (msg_type == 0) ? "POLL" : "CALL",
				data.remote_id, data.obj_id, data.rmt_gps_state, data.rmt_gps_pos[0], data.rmt_gps_pos[1]);
#endif
		}
	}
}

const static uint16_t bt_key_func_define[2][4] = {
	{SYNC_CALL_STACK_PTT_RELEASED, SYNC_CALL_STACK_PTT2_RELEASED, SYNC_CALL_STACK_ALARM_RELEASED, SYNC_CALL_STACK_CANCEL_RELEASED},
	{SYNC_CALL_STACK_PTT_PRESSED, SYNC_CALL_STACK_PTT2_PRESSED, SYNC_CALL_STACK_ALARM_PRESSED, SYNC_CALL_STACK_CANCEL_PRESSED}
};
#define BT_VOICE_FRAME_TIMEOUT_SECOND		2
static uint32_t bt_voice_frame_timeout = 0;
static uint8_t bt_key_func_state = 0xff;
void bt_key_func_state_check(void)
{
	if (bt_key_func_state < 0xfe)
	{
		if (bt_voice_frame_timeout < timestamp_1s)
		{
			if (bt_key_func_state < 2)
				set_sync_call_stack_type(bt_key_func_define[0][bt_key_func_state]);
			bt_key_func_state = 0xff;
		}
	}
}

void set_bt_voice_receive_time(void)
{
	bt_voice_frame_timeout = timestamp_1s + BT_VOICE_FRAME_TIMEOUT_SECOND;
}

uint8_t is_rcv_bt_voice_frame(void)
{
	return (bt_voice_frame_timeout >= timestamp_1s) ? 1 : 0;
}

void set_bt_disable(void)
{
}

#if (DEBUG_873_RECEIVED_DATA == 1)
const uint8_t key_type_string[4][6] = {"PTT", "2PTT", "ALARM", "RETU"};
const uint8_t key_operate_string[2][4] = {"OFF", "ON"};
#endif

BT_DMA_DATA_TYPEDEF bt_upgrade_frame = {0, 0, 0, 0};

uint8_t process_bt_frame_now(uint8_t *frame)
{
	BT_DMA_DATA_TYPEDEF *p_btdata = (BT_DMA_DATA_TYPEDEF *)frame;
	uint32_t id, tmp32;
	uint16_t i, checksum;
	uint8_t ret = 0;
	uint8_t msg_buf[LONG_MESSAGE_STRUCT_LENGTH];
	MESSAGE_STRUCT *msg;

	id = ((uint16_t)p_btdata->data[BT_DMA_DATA_DLEN + 1] << 8) + p_btdata->data[BT_DMA_DATA_DLEN];
	if ((p_btdata->prefix == BT_DMA_DATA_CAN_PREFIX) || (p_btdata->ctask == TSC_CTASK_SEND_VOICE) || (p_btdata->ctask == TSC_CTASK_SEND_FAKE_VOICE))
	{
		checksum = (uint16_t)id;
	}
	else
	{
		for (i = 0, checksum = 0; i < BT_DMA_DATA_DLEN + 8; i++)
			checksum += frame[4 + i];
	}

#if (DEBUG_873_RECEIVED_DATA >= 2)
	printf("\t[BT]%04X,byte=%d,addr=%08X,data=%02x %02x %02x %02x %02x %02x %02x %02x,chk=%04x(rcv=%04x),deb=%02x\r\n",
		p_btdata->ctask, p_btdata->bytes, p_btdata->addr, p_btdata->data[0], p_btdata->data[1], p_btdata->data[2],
		p_btdata->data[3], p_btdata->data[4], p_btdata->data[5], p_btdata->data[6], p_btdata->data[7],
		checksum, (uint16_t)id, bt_key_func_state);
#endif
	if (checksum == (uint16_t)id)
	{
		switch (p_btdata->ctask)
		{
			case TSC_CTASK_SHUTDOWN:
			case TSC_CTASK_REBOOT:
				delay_to_reset_module((p_btdata->ctask == TSC_CTASK_SHUTDOWN) ? 0 : 1, 1000);
				p_btdata->addr = MAINTAIN_RETURN_SUCCESSFUL;
				send_bt_frame_to_host_auto_sel(frame, 0, 0xfffe, 0, 0);
				break;

			case TSC_CTASK_SET_RF_POWER:
				setup_rf_power_state(p_btdata->data[0]);
				send_bt_frame_to_host_auto_sel(frame, 1, 0xffff, 0, 0);
				break;
			case TSC_CTASK_GET_RF_POWER:
				p_btdata->data[0] = maintain_setup_parameter(PARA_OPERATE_READ, RF_POWER_PARAS_POS, 0);
				send_bt_frame_to_host_auto_sel(frame, 1, 0xfffe, 0, 0);
				break;

			case TSC_CTASK_SWITCH_WORK_MODE:
				switch_workmode_set_workmode(p_btdata->data[0]);
				send_bt_frame_to_host_auto_sel(frame, 1, 0xffff, 0, 0);
				break;
			case TSC_CTASK_GET_WORK_MODE:
				p_btdata->data[0] = maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0);
				send_bt_frame_to_host_auto_sel(frame, 1, 0xfffe, 0, 0);
				break;

			case TSC_CTASK_SELECT_USER_AREA:
			case TSC_CTASK_SELECT_CTRL_AREA:
				maintain_setup_parameter(PARA_OPERATE_WRITE, (p_btdata->ctask == TSC_CTASK_SELECT_USER_AREA) ? ACTIVE_GROUP_OF_BOOKS : ACTIVE_GROUP_OF_CTRL_LIST, p_btdata->data[0]);
				send_bt_frame_to_host_auto_sel(frame, 1, 0xffff, 0, 0);
				break;
			case TSC_CTASK_GET_USER_AREA:
			case TSC_CTASK_GET_CTRL_AREA:
				get_area_info_and_send(p_btdata, p_btdata->ctask);
				break;
			case TSC_CTASK_GET_USER_AREA_NAME:
			case TSC_CTASK_GET_CTRL_AREA_NAME:
				get_area_name_and_send(p_btdata, p_btdata->ctask);
				break;

			case TSC_CTASK_SET_VOLUME:
				Set_Speak_Volume(p_btdata->data[0]);
				Set_Tip_Volume(p_btdata->data[1]);
				Set_Mic_Gain(p_btdata->data[2]);
				send_bt_frame_to_host_auto_sel(frame, 3, 0xffff, 0, 0);
				break;
			case TSC_CTASK_GET_VOLUME:
				p_btdata->data[0] = maintain_setup_parameter(PARA_OPERATE_READ, VOICE_PARAS_POS, 0);
				p_btdata->data[1] = maintain_setup_parameter(PARA_OPERATE_READ, TIP_SOUND_PARAS_POS, 0);
				p_btdata->data[2] = maintain_setup_parameter(PARA_OPERATE_READ, MICROPHONE_PARAS_POS, 0);
				send_bt_frame_to_host_auto_sel(frame, 3, 0xfffe, 0, 0);
				break;

			case TSC_CTASK_SCAN_CHANNEL:
				maintain_setup_parameter(PARA_OPERATE_WRITE, CTRL_SCANNING_MODE_POS, p_btdata->data[0]);
				maintain_stack_parameter(PARA_OPERATE_WRITE, STACK_PARA_CTRL_INDEX, (void *)(&p_btdata->data[2]));
				send_bt_frame_to_host_auto_sel(frame, 4, 0xffff, 0, 0);
				break;
			case TSC_CTASK_GET_SCAN_CHANNEL:
				p_btdata->data[0] = maintain_setup_parameter(PARA_OPERATE_READ, CTRL_SCANNING_MODE_POS, 0);
				maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_CTRL_INDEX, (void *)(&p_btdata->data[2]));
				send_bt_frame_to_host_auto_sel(frame, 4, 0xfffe, 0, 0);
				break;

			case TSC_CTASK_SET_GPS:
				maintain_setup_parameter(PARA_OPERATE_WRITE, GPS_POWER_PARAS_POS, p_btdata->data[0]);
				gps_power_onoff(p_btdata->data[0]);

				maintain_setup_parameter(PARA_OPERATE_WRITE, GPS_TYPE_PARAS_POS, p_btdata->data[1]);
				set_gps_link_state(0);
				update_gps_work_mode();
				send_bt_frame_to_host_auto_sel(frame, 4, 0xffff, 0, 0);
				break;
			case TSC_CTASK_GET_GPS:
				p_btdata->data[0] = maintain_setup_parameter(PARA_OPERATE_READ, GPS_POWER_PARAS_POS, 0);
				p_btdata->data[1] = maintain_setup_parameter(PARA_OPERATE_READ, GPS_TYPE_PARAS_POS, 0);
				p_btdata->data[2] = get_satellite_number();
				memcpy(&p_btdata->data[4], &rmc_data, sizeof(GPS_STRUCT_TYPEDEF));
				send_bt_frame_to_host_auto_sel(frame, 4 + sizeof(GPS_STRUCT_TYPEDEF), 0xfffe, 0, 0);
				break;

			case TSC_CTASK_SET_VOCODER:
				set_vocoder_setup_content(p_btdata->data[0]);
				send_bt_frame_to_host_auto_sel(frame, 1, 0xffff, 0, 0);
				break;
			case TSC_CTASK_GET_VOCODER:
				p_btdata->data[0] = get_vocoder_setup_content();
				send_bt_frame_to_host_auto_sel(frame, 1, 0xfffe, 0, 0);
				break;

			case TSC_CTASK_KEY_OPERATE:
				if ((p_btdata->data[0] <= 1) && (p_btdata->data[1] <= 3))
				{
#if (DEBUG_873_RECEIVED_DATA == 1)
					printf("\t[BT]%s %s(debounce=%02x)\r\n", key_type_string[p_btdata->data[1]], key_operate_string[p_btdata->data[0]], bt_key_func_state);
#endif
					if (p_btdata->data[0])			// key press
					{
//						if (p_btdata->data[1] <= 1)	// key == ptt/2ptt
//							call_ptt_async(CALL_PTT_ASYNC_CALLING, get_id_from_bt_frame(&p_btdata->data[4]), 0, 0, &p_btdata->data[4]);

						if (p_btdata->data[1] <= 2)	// key == ptt/2ptt/alarm
						{
							set_bt_voice_receive_time();
							if (bt_key_func_state == 0xff)
								bt_key_func_state = (get_real_esn_second_sector() == MOBILE_MODE_PDT_ZZW_816A) ? 0xfe : p_btdata->data[1];
							else
								break;
						}
					}
					else
					{
						bt_key_func_state = 0xff;
					}
					set_sync_call_stack_type(bt_key_func_define[p_btdata->data[0]][p_btdata->data[1]]);
				}
				else if (p_btdata->data[1] == 4)
				{
					get_number_from_bt_frame(&p_btdata->data[4]);
//#if (DEBUG_873_RECEIVED_DATA >= 1)
					printf("\t[BT]Dial=%s\r\n", g_dial_number);
//#endif
				}
#if (DEBUG_873_RECEIVED_DATA == 1)
				else
				{
					printf("\t[BT]D0=%d,D1=%d(debounce=%02x)\r\n", p_btdata->data[0], p_btdata->data[1], bt_key_func_state);
				}
#endif
				break;

			case TSC_CTASK_MSG_SEND:
				checksum = MIN(((uint16_t)p_btdata->data[3] << 8) + p_btdata->data[2], MAX_ONE_PDT_FRAME_MSG);
				if (p_btdata->prefix == BT_DMA_DATA_CAN_PREFIX)	// can sending
				{
					if (p_btdata->addr == 0)					// addrΪ0����data[4]ȡ8B��Ŀ��id����data[12]ȡ���ݣ�
					{
						id = get_id_from_bt_frame(&p_btdata->data[4]);
						i = 12;
					}
					else if (p_btdata->addr == 0xffffffff)
					{
						msg_buf[0] = 0;
						id = get_id_from_bt_frame(msg_buf);		// addrΪ0xffffffff��idΪ��ǰ�غ���id
						i = 4;
					}
					else										// addr��Ϊ0����addrȡĿ��id����data[4]ȡ����
					{
						id = p_btdata->addr;
						i = 4;
					}
				}
				else
				{
					id = get_id_from_bt_frame(&p_btdata->data[4]);
					i = 32;
				}
				p_btdata->data[i + checksum] = 0;				// fill NULL for end the string

				if (p_btdata->data[0] <= 2)
				{
					send_message_asynchronous(id, (p_btdata->data[0] == 0) ? MESSAGE_TYPE_USER_MSG : ((p_btdata->data[0] == 1) ? MESSAGE_TYPE_STATUS_MSG : MESSAGE_TYPE_BIN_MSG),
						checksum, &p_btdata->data[i]);
				}
				else if ((p_btdata->data[0] == 3) && (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC))
				{
					tmp32 = send_v_data_is_busy_now();
					if (tmp32 == 0)
					{
						call_ptt_async(CALL_PTT_ASYNC_MESSAGE, id, MESSAGE_TYPE_BIN_MSG, checksum, &p_btdata->data[i]);
						set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
						set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
					}
					else
					{
						*((uint32_t *)(&p_btdata->data[i])) = tmp32;
						send_bt_frame_to_host_auto_sel(frame, 1, 0xfffe, 0, 0);
//						printf("[BT]BUSY=%08X\r\n", tmp32);
					}
				}
				break;

			case TSC_CTASK_MSG_READ:
				break;

			case TSC_CTASK_SEND_VOICE:
				set_bt_voice_receive_time();
				save_bt_voice_frame(p_btdata->data, BT_DMA_DATA_DLEN, 0);
				break;
			case TSC_CTASK_SEND_FAKE_VOICE:
				set_bt_voice_receive_time();
				break;
			case TSC_CTASK_NEW_BT_UNI_CMD:
				bt_func_process_real_new_bt(p_btdata);
				break;

			case TSC_CTASK_DEV_STATUS:								// enable module's vehicle mode
			case TSC_CTASK_STACK_STATUS:
				if (dev_is_base())
				{
					set_can_control_valid();
					if (p_btdata->ctask == TSC_CTASK_DEV_STATUS)
						set_voice_source(0);
					send_status_to_bt(1, 0);						// only base
				}
				break;

			case TSC_CTASK_GET_REL_DISTANCE:
				if (dev_is_base())
					send_bt_data_to_host_from_can(zzw_distance_string, p_strlen(zzw_distance_string) + 1, TSC_CTASK_GET_REL_DISTANCE, 0, 0);
				break;

			case TSC_CTASK_DEV_REQUEST_RESET_GPS:
				if (p_btdata->addr == 0)
					request_to_reset_gps();
				else if ((p_btdata->addr == 1) || (p_btdata->addr == 2))
					set_dsp_save_power_mode_absolutely(p_btdata->addr - 1);
				else if (p_btdata->addr == 3)
					set_dsp_save_power_paras(p_btdata->data[0], p_btdata->data[1], ((uint16_t)p_btdata->data[3] << 8) | p_btdata->data[2]);
				break;

			case TSC_CTASK_DEV_REQUEST_ALARM:
				base_alarm(p_btdata->data[0], p_btdata->data[1]);
				break;

			case TSC_CTASK_SEND_SIGNALING:
				save_bt_data_to_can_buffer(p_btdata->data);
				break;

			case TSC_CTASK_READ_DATA:
				break;

			case TSC_CTASK_GET_USER_NAME:
				checksum = uart2_use_as_dataport() ? 3 : 1;
				for (i = 0; i < checksum; i++)
				{
					id = get_id_from_bt_frame(&p_btdata->data[4 + i * 8]);
					if (id)
					{
						if ((p_btdata->data[4 + i * 8] & 0x80) == 0x80)
							id_to_number(interphone_mode, 0, id, 0, &p_btdata->data[32 + i * MAX_USER_NAME_LEN]);
						else
							get_be_called_name(id, &p_btdata->data[32 + i * MAX_USER_NAME_LEN]);
					}
					else
					{
						break;
					}
				}
				send_bt_frame_to_host_auto_sel(frame, 32 + i * MAX_USER_NAME_LEN, 0xfffe, 0, 0);
				break;

			case TSC_CTASK_SWITCH_WATCH:
				// bt_func_process_switch_watch(p_btdata);
				break;

			case TSC_CTASK_SET_BT_DISABLE:
				timer_initial(1, 400, set_bt_disable);
				send_bt_frame_to_host_auto_sel(frame, 1, 0xffff, 0, 0);
				break;

			case TSC_CTASK_WRITE_FLASH_ABSOLUTELY:
			case TSC_CTASK_VERIFY_FLASH_ABSOLUTELY:
			case TSC_CTASK_UPGRADE_PROGRAM_ABSOLUTELY:
			case TSC_CTASK_VERIFY_PROGRAM_ABSOLUTELY:
				if (bt_upgrade_frame.prefix == 0)
					memcpy(&bt_upgrade_frame, frame, sizeof(BT_DMA_DATA_TYPEDEF));
				break;

			case TSC_CTASK_SEND_DATA_IAP:
				send_dport_to_ui(p_btdata->data, p_btdata->bytes, p_btdata->addr);
				break;

			default:
				if ((printf_redirection == UART_LINKTO_DSP_INDEX) && ((p_btdata->ctask & 0xff00) == (TSC_CTASK_PC_VER & 0xff00)))
				{
					send_poc_test_frame_to_host(frame);
				}
				else
				{
					ret = 2;	// command not match
#ifdef DEBUG_REAL_BT
					printf("[BT]cmd=%04x(%d)\r\n", p_btdata->ctask, get_timestamp_measure());
#endif
				}
				break;
		}
	}
	else
	{
#if (DEBUG_873_RECEIVED_DATA == 1)
		printf("[BT]checksum error! wanted=%04X, received=%04X\r\n", checksum, id);
#endif
#ifdef DEBUG_REAL_BT
		printf("[BT]CHK=%04X\r\n", checksum);
#endif
		ret = 1;	// checksum error
	}

	return ret;
}



void process_bt_dma_data(uint8_t *bt_data, uint8_t num)
{
	uint8_t i;

	for (i = 0; i < 6; i++)
	{
		vlog_i("usart","\t%02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X",
			bt_data[i * 16 +  0], bt_data[i * 16 +	1], bt_data[i * 16 +  2], bt_data[i * 16 +	3],
			bt_data[i * 16 +  4], bt_data[i * 16 +	5], bt_data[i * 16 +  6], bt_data[i * 16 +	7],
			bt_data[i * 16 +  8], bt_data[i * 16 +	9], bt_data[i * 16 + 10], bt_data[i * 16 + 11],
			bt_data[i * 16 + 12], bt_data[i * 16 + 13], bt_data[i * 16 + 14], bt_data[i * 16 + 15]);
	}
}

void usart_dma_configuration(uint8_t com, uint8_t dma_mode)				// bit0:tx; bit1:rx
{
}

void gen_bt_frame(uint16_t ctask, uint16_t bytes, uint16_t addr, uint8_t *data, uint8_t *frame)
{
	uint8_t test_bt_frame[USART2_DMA_LENGTH];
	uint16_t i, checksum;
	BT_DMA_DATA_TYPEDEF *p_btdata = (BT_DMA_DATA_TYPEDEF *)test_bt_frame;

	p_btdata->prefix = BT_DMA_DATA_PREFIX;
	p_btdata->ctask = ctask;
	p_btdata->bytes = bytes;
	p_btdata->addr = addr;

	if (data == 0)
		memset(p_btdata->data, 0, BT_DMA_DATA_DLEN);
	else
		memcpy(p_btdata->data, data, BT_DMA_DATA_DLEN);

	for (i = 0, checksum = 0; i < BT_DMA_DATA_DLEN + 8; i++)
		checksum += test_bt_frame[4 + i];

	p_btdata->data[BT_DMA_DATA_DLEN] = (uint8_t)checksum;
	p_btdata->data[BT_DMA_DATA_DLEN + 1] = (uint8_t)(checksum >> 8);
	p_btdata->epilog = BT_DMA_DATA_EPILOG;

	memcpy(frame, test_bt_frame, USART2_DMA_LENGTH);
}

void copy_to_console_buffer_and_exec(uint8_t *dat, uint8_t len)
{
	uint8_t  i, found;
	uint16_t console_len = p_strlen(console_buffer);

	for (i = 0, found = 0; i < len; i++)
	{
		if ((dat[i] == '\r') || (dat[i] == '\n') || (dat[i] == 0))
		{
			dat[i++] = 0;
			found = 1;
			break;
		}
	}

	if (console_len + i < USART_BUFFER_LENGTH)
		memcpy(console_buffer + console_len, dat, i);

	if (found)
	{
		run_command(0, console_buffer);
		console_buffer[0] = 0;
	}
}


#ifdef FORWARDING_LOGIC_ENABLE

// 469ƽ̨���Խ����ݿ�����NOR��FLASH_FORWARDING_TABLE_ADDRESS��ʹ�ã������ɽ�ʡ����8KB�ڴ棻207ƽ̨��801�����У�ֻ����д��SPI���������ڴ���ʹ��
#pragma pack(4)
static uint8_t base_forward_table[FLASH_SPI_FORWARDING_TABLE_LENGTH];
void init_forwarding_table(void)
{
	if (g_runtime_inst_xvbase->misc_config_base.forward_forbidden)
	{
		memset(base_forward_table, 0, 8);
#ifdef DEBUG_FORWARDING_LOGIC
		vlog_i("usart","forwarding table has been forbidden");
#endif
	}
	else
	{
		flash_spi_read_data(base_forward_table, FLASH_SPI_FORWARDING_TABLE_ADDRESS, FLASH_SPI_FORWARDING_TABLE_LENGTH);
#ifdef DEBUG_FORWARDING_LOGIC
		vlog_i("usart","forwarding table %s", (*((uint32_t *)base_forward_table) == FORWARDING_TABLE_FLAG) ? "valid" : "invalid");
#endif
	}
}

  #ifndef FORWARDING_LOGIC_NO_GPS

/* ��Ԫ������ľ�γ�ȿ̶�??? */
#define SCALE_PER_UNIT		(float)0.005
#define HALF_LENGTH_SIDE	(float)400
#define POS_DECITION_GRADE	1

/*
 *	���ܣ���������ѡ
 *	����: ��վ����̨�ľ�γ�ȣ�Ԥ�ȴ�����?�������???
 *	����: ת��ʱ϶��0������ת����1���ӳ�һ��ʱ϶ת������������������
 *
 */
#if 0
unsigned char get_forwarding_decision_20210617(float bs_lon, float bs_lat, float rs_lon, float rs_lat)
{
	if (*((uint32_t *)base_forward_table) == FORWARDING_TABLE_FLAG)
//	if (*((uint32_t *)FLASH_FORWARDING_TABLE_ADDRESS) == FORWARDING_TABLE_FLAG)
	{
		/*
		 *	1����ȡ���Ͻǵ�ľ�γ��???
		 *	2�������̨λ�������Ͻǵ����Ծ���???(��γ�Ȳ�ֵ)
		 *	3��ͨ����Ծ�����ҵ���Ӧת����������
		 *	4��������Ų��ҵ��?�������±겢������ѡ���???
		 *	5��������ѡ�������F(gw)ֵ
		 */

		// step 1: ��ȡ���Ͻǵ�ľ�γ��???
		float lon_topleft = bs_lon - SCALE_PER_UNIT * HALF_LENGTH_SIDE;
		float lat_topleft = bs_lat + SCALE_PER_UNIT * HALF_LENGTH_SIDE;

		// step 2: �����̨λ�������Ͻǵ����Ծ���???
		float lon_itv = rs_lon - lon_topleft;
		float lat_itv = lat_topleft - rs_lat;

		// step 3: ͨ����Ծ�����ҵ���Ӧת����������
		int col_idx = (int)(lon_itv / SCALE_PER_UNIT);
		int row_idx = (int)(lat_itv / SCALE_PER_UNIT);
		int rst_idx = row_idx * HALF_LENGTH_SIDE * 2 + col_idx;

		// step 4: ������Ų��ҵ��?�������±겢������ѡ���???
		unsigned char rst_val = base_forward_table[8 + rst_idx / 8];
//		unsigned char rst_val = ((unsigned char *)(FLASH_FORWARDING_TABLE_ADDRESS + 8))[rst_idx / 8];
		unsigned char rst_ret = POS_DECITION_GRADE & (rst_val >> ((((rst_idx + 1) % 8) > 0) ? (8 - (rst_idx + 1) % 8) : 0));

		vlog_i("usart","\tcal(%f,%f->%f,%f)=%d(tl_lon=%f,tl_lat=%f/itv_lon=%f,itv_lat=%f/idx_col=%d,idx_row=%d,idx_rst=%d/rst_val=0x%02X)", rs_lon, rs_lat, bs_lon, bs_lat, rst_ret,
			lon_topleft, lat_topleft, lon_itv, lat_itv, col_idx, row_idx, rst_idx, rst_val);

		// step 5: ������ѡ��������ݳͷ�����F(gw)����������?
		switch (rst_ret)
		{
//			case 0:
//				rst_val = 0;
//				break;
			case 1:
				rst_val = 15;
				break;
			case 2:
				rst_val = 15;
				break;
			case 3:
				rst_val = 15;
				break;
			default:
				rst_val = 0;
				break;
		}

		return rst_val;
	}
	else
	{
		return 0;
	}
}
#endif
unsigned char get_forwarding_decision(float bs_lon, float bs_lat, float rs_lon, float rs_lat)
{
	if (*((uint32_t *)base_forward_table) == FORWARDING_TABLE_FLAG)
//  if (*((uint32_t *)FLASH_FORWARDING_TABLE_ADDRESS) == FORWARDING_TABLE_FLAG)
	{
		/*
		*  1����ȡ���Ͻǵ�ľ�γ��???
		*  2�������̨λ�������Ͻǵ����Ծ���???(��γ�Ȳ�ֵ)
		*  3��ͨ����Ծ�����ҵ���Ӧת����������
		*  4��������Ų��ҵ��?�������±겢������ѡ���???
		*  5��������ѡ�������F(gw)ֵ
		*/

		// step 1: ��ȡ���Ͻǵ�ľ�γ��???
		float lon_topleft = bs_lon - SCALE_PER_UNIT * HALF_LENGTH_SIDE;
		float lat_topleft = bs_lat + SCALE_PER_UNIT * HALF_LENGTH_SIDE;

		// step 2: �����̨λ�������Ͻǵ����Ծ���???
		float lon_itv = rs_lon - lon_topleft;
		float lat_itv = lat_topleft - rs_lat;

		// step 3: ͨ����Ծ�����ҵ���Ӧת����������
		int col_idx = (int)(lon_itv / SCALE_PER_UNIT);
		int row_idx = (int)(lat_itv / SCALE_PER_UNIT);
		int rst_idx = row_idx * HALF_LENGTH_SIDE * 2 + col_idx;

		// step 4: ������Ų��ҵ��?�������±겢������ѡ���???
		//unsigned char rst_val = base_forward_table[8 + rst_idx / 8];
		//unsigned char rst_ret = POS_DECITION_GRADE/*=0x01*/ & (rst_val >> ((((rst_idx + 1) % 8) > 0) ? (8 - (rst_idx + 1) % 8) : 0));
		unsigned int forward_table_length = (unsigned int)((base_forward_table[4] << 24) | (base_forward_table[5] << 16) | (base_forward_table[6] << 8) | base_forward_table[7]);
		unsigned int search_idx = 0;
		unsigned int val_cnt = 0;
		unsigned char rst_val = 0;
		do
		{
			rst_val = (unsigned char)((base_forward_table[8 + search_idx] >> 5) & 0x07);
			val_cnt += (unsigned short)(((base_forward_table[8 + search_idx] & 0x1f) << 8) | base_forward_table[8 + search_idx + 1]);

			// sum(val_cnt) >= rst_idx
			if (val_cnt > rst_idx) {
				break;
			}
			search_idx += 2;

		} while (search_idx < forward_table_length);
#ifdef DEBUG_FORWARDING_LOGIC
		vlog_i("usart","\tCal0619(%f,%f->%f,%f)=%d(tl_lon=%f,tl_lat=%f/itv_lon=%f,itv_lat=%f/idx_col=%d,idx_row=%d,idx_rst=%d/rst_val=0x%02X)", rs_lon, rs_lat, bs_lon, bs_lat, search_idx,
			lon_topleft, lat_topleft, lon_itv, lat_itv, col_idx, row_idx, rst_idx, rst_val);
#endif
		// step 5: ������ѡ��������ݳͷ�����F(gw)����������?
		switch (rst_val)
		{
		case 1:
			rst_val = 5;
			break;
		case 3:
			rst_val = 15;
			break;
		default:
			rst_val = 0;
			break;
		}

		return rst_val;
	}
	else {
		return 0;
	}
}

  #else // FORWARDING_LOGIC_NO_GPS

#define MAX_BS_COUNT	50
unsigned char get_forwarding_decision(unsigned char bs_master, unsigned char bs_oneself)
{
	unsigned char val_dec;
	unsigned char rst_val;

	if (*((uint32_t *)base_forward_table) == FORWARDING_TABLE_FLAG)
	{
		/*
		*  1��������������վ�ģ�����ת����������������վ�ģ�ִ�в���2-4
		*  2��������վ��������վ������ҵ���Ӧ·�����?
		*  3������·����Ϣ�õ�����վ������ת���ȼ�
		*  4����������(����)
		*/
		// step 1: ������������վ��
		if (bs_master == bs_oneself) {
			return 0;
		}

		// step 2: ������վ��������վ������ҵ���Ӧ·�����?
		val_dec = base_forward_table[8 + bs_master * MAX_BS_COUNT + bs_oneself];

		// step 3: ����·����Ϣ�õ�����վ������ת���ȼ�
		rst_val = val_dec & 0x07;

		return rst_val;
	}
	else {
		return 0;
	}
}

  #endif // FORWARDING_LOGIC_NO_GPS

#endif // defined FORWARDING_LOGIC_ENABLE
