/**
  ******************************************************************************
  *                Copyright (c) 2011, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    08-December-2012
  * @brief   This file provides
  *            - flash burn operator
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stdio.h"
#include "string.h"
#include "stddef.h"
#include "victel_digital_board.h"
#include "victel_digital_flash.h"
#include "victel_digital_lcd.h"
#include "victel_digital_spi_flash.h"
#include "stack_config.h"
#include "global_define.h"
#include "vlog.h"


extern RUNTIME_PARAMETERS_XVBASE_TYPEDEF *g_runtime_inst_xvbase;
extern int16_t g_rssi_regulator_ex;

const unsigned char default_user_name[USER_NAME_BMP_LEN] = {0X00,0X01,0X80,0X00,0X35,0X00,
0xce, 0xac, 0xb5, 0xc2, 0xbf, 0xc6, 0xbc, 0xbc, 0x00};		// "ά�¿Ƽ�"

/* Private define ------------------------------------------------------------*/
const uint32_t user_flag_save_sector_address[2] = {0, 0};
const uint32_t xvbase_save_sector_address[2] = {0, 0};
static uint8_t user_flag_save_sector = 0;	// point to the current use(read) sector

#pragma pack(4)
uint8_t g_static_inst[MISC_PARAMETERS_FLASH_LENGTH/*sizeof(STATIC_PARAMETERS_TYPEDEF)*/];
#pragma pack(4)
uint8_t g_user_book_inst[USER_BOOK_DATA_MAX_LENGTH/*sizeof(GROUP_BOOKS)*/];
#pragma pack(4)
uint8_t g_long_message[FLASH_SPI_LONG_MESSAGE_LENGTH];

STATIC_PARAMETERS_TYPEDEF *g_static_ptr;
FACTORY_PARAS_TYPEDEF *g_factory_inst;

RUNTIME_PARAMETERS_TYPEDEF g_runtime_inst;
RUNTIME_PARAMETERS_XVBASE_TYPEDEF_4KB runtime_inst_xvbase_4KB;
uint32_t stack_call_buffer[STACK_CALL_BUFFER_LENGTH / sizeof(uint32_t)];
uint32_t stack_dump_buffer[STACK_DUMP_BUFFER_LENGTH / sizeof(uint32_t)];

/**
  * @brief  Gets the sector of a given address
  * @param  None
  * @retval The sector of a given address
  */
uint8_t get_user_data_flash_sector(uint8_t flag)	// flag: 0-RUNTIME_PARAMETERS_TYPEDEF, 1-RUNTIME_PARAMETERS_XVBASE_TYPEDEF_4KB
{
	user_flag_save_sector = 0;
	return user_flag_save_sector;
}

uint32_t flash_address_remap_misc(uint32_t addr, uint32_t len)	// remap to real address
{
	return addr;
}

uint32_t flash_address_remap(uint32_t addr, uint32_t len)		// remap to the real/operated address
{
	return addr;
}

uint16_t ntohs(uint16_t s)
{
	return ((s >> 8) | (s << 8));
}

uint32_t ntohl(uint32_t l)
{
	return ((l >> 24) & 0x000000ff) | ((l >> 8) & 0x0000ff00) | ((l << 8) & 0x00ff0000) | ((l << 24) & 0xff000000);
}

void ntohs_mul(uint16_t *s, uint16_t num)
{
	uint16_t i, *ptr16;

	for (i = 0, ptr16 = s; i < num; i++, ptr16++)
		*ptr16 = (*ptr16 >> 8) | (*ptr16 << 8);
}

uint32_t convert_bit_stream(uint8_t bits, uint32_t dat)
{
	uint8_t n;
	uint32_t ret;

	if (bits > 1)
	{
		for (n = 0, ret = 0; n < bits; n++)
		{
			if (dat & (1 << n))
				ret |= 1 << (bits - 1 - n);
		}
	}
	else
	{
		ret = dat;
	}
	return ret;
}

uint32_t combine_short_to_long(uint16_t s0, uint16_t s1)
{
	return ((uint32_t)s0 << 16) | s1;
}

uint16_t combine_char_to_short(uint8_t c0, uint8_t c1)
{
	return ((uint16_t)c0 << 8) | c1;
}

uint32_t get_user_name_address(void)
{
	uint32_t user = (uint32_t)(&g_static_ptr->device_name);

	if (is_valid_username(user) == 0)
		user = (uint32_t)default_user_name;

	return user + 6;
}

void get_device_type_address(uint8_t *str)
{
	if (g_static_ptr->device_name.dev_type[0])
	{
		memcpy(str, g_static_ptr->device_name.dev_type, MAX_USER_NAME_LEN - 1);
		str[MAX_USER_NAME_LEN - 1] = 0;
	}
}

#define INVALID_BAND_RANGE_VALUE	(15 * 100 + 15 * 10 + 15)
void get_device_band_range(uint8_t *str)
{
	uint16_t low, high;

	if (g_factory_inst->band_range.valid == 0x6c)
	{
		low = g_factory_inst->band_range.lf_hsb * 100 + g_factory_inst->band_range.lf_msb * 10 + g_factory_inst->band_range.lf_lsb;
		high = g_factory_inst->band_range.hf_hsb * 100 + g_factory_inst->band_range.hf_msb * 10 + g_factory_inst->band_range.hf_lsb;
		if (low && high && (low != INVALID_BAND_RANGE_VALUE) && (high != INVALID_BAND_RANGE_VALUE))
			sprintf((char *)str, "%3d-%3dMHz", low, high);
	}
}

const uint8_t local_calling_name[2][6] = {"����", "LOCAL"};
extern uint16_t watch_index;
extern uint8_t interphone_mode;
void get_local_calling_name(uint8_t *info)
{
	uint32_t user;
#if BE_CALLED_NAME_USE_WHAT != 1
	USER_BOOK *book;

	book = get_books_item_content(watch_index, 0);
	if (book)
	{
  #if BE_CALLED_NAME_USE_WHAT == 2
		p_strcpy(info, book->name);
  #else
		id_to_number(interphone_mode, 0, book->id | ((uint32_t)USER_ID_INDIVIDUAL << 24), 0, info);
  #endif
	}
	else
#else
	{
		user = (uint32_t)(&g_static_ptr->device_name);
		if ((is_valid_username(user) == 0) || (*((char *)(user + 6)) == 0))
			p_strcpy(info, local_calling_name[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0]);
		else
			p_strcpy(info, (char *)(user + 6));
	}
#endif
}

uint32_t get_factory_data_address(void)
{
	return (uint32_t)g_factory_inst;
}

uint32_t get_tune_table_address(void)
{
	return (uint32_t)&g_static_ptr->pll_tune_table;
}

uint32_t get_esn_from_flash(uint32_t obj_address)
{
	uint8_t ret = 16;

	memcpy((void *)obj_address, g_static_ptr->factory_paras.esn, ret);
	return ret;
}

uint8_t get_work_mode_setup_from_flash(void)
{
	return g_factory_inst->pdt_adhoc_setup;
}

uint32_t get_address_books_address(void)
{
	return (uint32_t)g_user_book_inst;
}

uint32_t get_fonts_address_flash(void)
{
	return (uint32_t)FONTS_LIBRARY_FLASH_ADDRESS;
}

uint32_t get_pdt_stack_address(void)
{
	return (uint32_t)PDT_STACK_FLASH_ADDRESS;
}

uint32_t get_zzwx_stack_address(void)
{
	return (uint32_t)ZZWXV_STACK_FLASH_ADDRESS;
}

void reset_runtime_parameters(void)
{
	vlog_i("flash","reset the runtime data, please wait...");

	// message
	init_user_message(1);
	init_user_sent_message(1);

	// air config
	memset(&g_runtime_inst.air_config, 0, sizeof(AIR_RECONF_TYPEDEF));

	// call record
	memset(g_runtime_inst.call_record, 0, sizeof(CALLING_RECORD) * 3);
	g_runtime_inst.call_record[0].calling_valid_flag = ADDRESS_BOOKS_INIT_FLAG;
	g_runtime_inst.call_record[1].calling_valid_flag = ADDRESS_BOOKS_INIT_FLAG;
	g_runtime_inst.call_record[2].calling_valid_flag = ADDRESS_BOOKS_INIT_FLAG;

	// user setup
	memset(&g_runtime_inst.runtime_paras, 0, sizeof(RUNTIME_PARAS_TYPEDEF));
	g_runtime_inst.runtime_paras.rf_power = 1;
	*((uint8_t *)&g_runtime_inst.runtime_paras.voice_para) = 0x1C;
	*((uint8_t *)&g_runtime_inst.runtime_paras.tip_sound) = (7 << 4) | 4;		/* ������ʾ��7������4 */
	*((uint8_t *)&g_runtime_inst.runtime_paras.self_gain) = (8 << 3) | 2;		/* ����: ��������8��MIC����12dB */
	*((uint8_t *)&g_runtime_inst.runtime_paras.pwr_ctrl) = 0x0D;
	g_runtime_inst.runtime_paras.work_mode = 1;
	*((uint8_t *)&g_runtime_inst.runtime_paras.lcd_ctrl) = (20 << 2) | 0;
	g_runtime_inst.runtime_paras.noise_threshold = 13;
	*((uint8_t *)&g_runtime_inst.runtime_paras.earphone_gain) = (8 << 3) | 3;	/* ����: ��������8��MIC����15dB */
	*((uint8_t *)&g_runtime_inst.runtime_paras.zzw_vocoder) = 0x0C;
	*((uint8_t *)&g_runtime_inst.runtime_paras.pdt_vocoder) = 0x0C;

	// password

	// ctrl table index

	// prior ctrl channel

	// stack mode
	set_stack_work_mode(0);

	g_runtime_inst.runtime_paras_sn = 0;
	g_runtime_inst.runtime_paras_valid_flag = USER_FLASH_SAVE_FLAG;

	save_user_data_to_flash(0);
	save_user_data_to_flash(0);
}

void check_factory_and_runtime_data(void)
{
	uint32_t esn[4], offset, factory_valid, runtime1_sn, runtime1_valid, runtime2_sn, runtime2_valid;

	memcpy(esn, g_static_ptr->factory_paras.esn, 16);
	if ((esn[0] == 0xffffffff) && (esn[1] == 0xffffffff) && (esn[2] == 0xffffffff) && (esn[3] == 0xffffffff))
		parameters_valid_checking(4);

	if (g_static_ptr->factory_paras.factory_save_flag != USER_FLASH_SAVE_FLAG)
		parameters_valid_checking(8);

	if ((g_runtime_inst.runtime_paras_sn == 0xffffffff) || (g_runtime_inst.runtime_paras_valid_flag != USER_FLASH_SAVE_FLAG))
		parameters_valid_checking(3);

	if (g_runtime_inst_xvbase->valid_flag != USER_FLASH_SAVE_FLAG)
		parameters_valid_checking(3);
}

void check_the_parameters_version(void)
{
	uint16_t *p_book_init_flag;
	uint32_t book_init_flag, book_version_flag;

	p_book_init_flag = (uint16_t *)g_user_book_inst;	// init flag(4B) sizeof(GROUP_BOOKS)(4B) GROUP_BOOKS
	book_init_flag = combine_short_to_long(ntohs(p_book_init_flag[0]), ntohs(p_book_init_flag[1]));
	book_version_flag = *((uint32_t *)(p_book_init_flag + 4));

	if (book_init_flag == ADDRESS_BOOKS_INIT_FLAG)
	{
		if (g_static_ptr->static_para_valid_flag == USER_FLASH_SAVE_FLAG)
		{
			if (book_version_flag == ADDRESS_BOOKS_VERSION2)					// book version ok
			{
				if (g_static_ptr->static_para_version == ADDRESS_BOOKS_VERSION2)	// static version ok
				{
					vlog_i("flash","user book & static paras OK!");
				}
				else
				{
					vlog_i("flash","paras version not match! book=0x%08x, static=0x%08x", book_version_flag, g_static_ptr->static_para_version);
					parameters_valid_checking(5);
				}
			}
			else
			{
				vlog_i("flash","the version of book is invalid");
				parameters_valid_checking(2);
			}
		}
		else
		{
			vlog_i("flash","the static parameters is invalid");
			parameters_valid_checking(7);
		}
	}
	else
	{
		vlog_i("flash","the user book data is invalid");
		parameters_valid_checking(2);
	}
}

uint8_t check_tune_table_is_valid(uint8_t *table)		// 0: valid
{
	if (p_strncmp(table, "RF2P", 4) && p_strncmp(table, "2PRF", 4))
	{
		vlog_i("flash","tune table flag=%02X %02X %02X %02X(rf_type=%d)", table[0], table[1], table[2], table[3], get_mobile_rf_type());
		return 1;
	}

	return 0;
}


/*
ʵ��дƵģ����������(����2.9.20�������ݷ���)��
	��1�����뱡					128K
	��2����̬���� 				16K
	��3�����в��� 				4K
	��4����������+��г��				4K
	��5���������� 				40K
	��6��ʵ��ESN				128

	��6��ʵ��ESN				4K(ֻ����16B)
	��7����վ���в���				4K
*/
#define PARAS_LENGTH_FACTORY_ESN		128/*SST25VF080_SIZE_OF_SECTOR*/
#define PARAS_LENGTH					(USER_BOOK_DATA_MAX_LENGTH + MISC_PARAMETERS_FLASH_LENGTH + FLASH_SPI_RUNTIME_LENGTH + \
											FLASH_SPI_FACTORY_STATIC_LENGTH + FLASH_SPI_LONG_MESSAGE_LENGTH + PARAS_LENGTH_FACTORY_ESN + \
											0/*FLASH_SPI_XVBASE_RUNTIME_LENGTH*/)
int get_file_size(FILE *f)
{
	int size;

	fseek(f, 0, SEEK_END);
	size = ftell(f);
	fseek(f, 0, SEEK_SET);

	return size;
}

int read_paras_to_ram(void)
{
	FILE *fp_read = NULL;
	char src_file[] = "/victel/db/victel_mobile_misc.dat", offset_string[100] = "0";
	uint8_t sector, temp_buffer[FLASH_SPI_FACTORY_STATIC_LENGTH];
	int offset, size, n_sector, n_read, ret = 0;
	char sector_name[7][16] = {"user book", "static", "runtime", "factory_tune", "long msg", "esn", "xv_runtime"};

	fp_read = fopen(src_file, "rb");
	if (fp_read == NULL)
	{
		vlog_i("flash","source file<%s> can NOT open!", src_file);
		ret = -1;
		goto quit_now;
	}

	size = get_file_size(fp_read);
	offset = 0;	// no flag at original template
	if (size < offset + PARAS_LENGTH)
	{
		vlog_i("flash","source file length <%d> less than expected<%d>!", size, offset + PARAS_LENGTH);
		ret = -2;
		goto quit_now;
	}

//	if (fseek(fp_read, offset, SEEK_SET) != 0)
//	{
//		vlog_i("flash","source file seek <%d> fail", offset);
//		ret = -3;
//		goto quit_now;
//	}

	sector = 0;
	n_sector = USER_BOOK_DATA_MAX_LENGTH;
	n_read = fread((void *)g_user_book_inst, sizeof(char), n_sector, fp_read);
	if (n_read != n_sector)
	{
read_paras_file_fail:
		vlog_i("flash","read %s fail(%d!=%d)", sector_name[sector], n_read, n_sector);
		ret = 1 + sector;
		goto quit_now;
	}

	sector++;
	n_sector = MISC_PARAMETERS_FLASH_LENGTH;
	g_static_ptr = (STATIC_PARAMETERS_TYPEDEF *)g_static_inst;
	g_factory_inst = &g_static_ptr->factory_paras;
	n_read = fread((void *)g_static_inst, sizeof(char), n_sector, fp_read);
	if (n_read != n_sector)
		goto read_paras_file_fail;

	sector++;
	n_sector = FLASH_SPI_RUNTIME_LENGTH;
	n_read = fread((void *)&g_runtime_inst, sizeof(char), n_sector, fp_read);
	if (n_read != n_sector)
		goto read_paras_file_fail;

	sector++;
	n_sector = FLASH_SPI_FACTORY_STATIC_LENGTH;
	n_read = fread((void *)temp_buffer, sizeof(char), n_sector, fp_read);
	if (n_read != n_sector)
	{
		goto read_paras_file_fail;
	}
	else
	{
		memcpy(&g_static_ptr->factory_paras, temp_buffer, 128);
		memcpy(&g_static_ptr->pll_tune_table, temp_buffer + 128, DSP_TUNE_TABLE_MAX_LENGTH);
	}

	sector++;
	n_sector = FLASH_SPI_LONG_MESSAGE_LENGTH;
	n_read = fread((void *)g_long_message, sizeof(char), n_sector, fp_read);
	if (n_read != n_sector)
		goto read_paras_file_fail;

	sector++;
	n_sector = PARAS_LENGTH_FACTORY_ESN;
	n_read = fread((void *)temp_buffer, sizeof(char), n_sector, fp_read);
	if (n_read != n_sector)
		goto read_paras_file_fail;
	else
		memcpy(&g_static_ptr->factory_paras.esn, temp_buffer, 16);

	sector++;
	n_sector = FLASH_SPI_XVBASE_RUNTIME_LENGTH;	// sizeof(RUNTIME_PARAMETERS_XVBASE_TYPEDEF_4KB);
	g_runtime_inst_xvbase = &runtime_inst_xvbase_4KB.xvbase_1028;
	n_read = fread((void *)&runtime_inst_xvbase_4KB, sizeof(char), n_sector, fp_read);
//	if (n_read != n_sector)
//		goto read_paras_file_fail;

quit_now:
	if (fp_read != NULL)
		fclose(fp_read);

	return ret;
}

void copy_system_parameters(void)
{
/*	COMPRESSED_RTC_STRUCT	mcu_bl_compile;
	VERSION_TYPEDEF			mcu_hardware_ver;
	VERSION_TYPEDEF			mcu_soft_ver;
	COMPRESSED_RTC_STRUCT	mcu_compile;

	memcpy(&mcu_bl_compile, &g_runtime_inst.device_information.mcu_bl_compile, sizeof(COMPRESSED_RTC_STRUCT));
	memcpy(&mcu_hardware_ver, &g_runtime_inst.device_information.mcu_hardware_ver, sizeof(VERSION_TYPEDEF));
	memcpy(&mcu_soft_ver, &g_runtime_inst.device_information.mcu_soft_ver, sizeof(VERSION_TYPEDEF));
	memcpy(&mcu_compile, &g_runtime_inst.device_information.mcu_compile, sizeof(COMPRESSED_RTC_STRUCT));
*/
	if (read_paras_to_ram() != 0)
		while (1);
	g_runtime_inst.runtime_paras.work_mode = INTERPHONE_MODE_PDT_TRUNKING;

	check_the_parameters_version();
	check_factory_and_runtime_data();


	vlog_i("flash","\t[MB]runtime=%d,fixed=%d,dyn=%d(used=%d,free=%d)", sizeof(RUNTIME_PARAMETERS_TYPEDEF), RUNTIME_PARAMETERS_FIXED_USED,
			RUNTIME_PARAMETERS_DYNAMIC, RUNTIME_PARAMETERS_DYNAMIC_USED, RUNTIME_PARAMETERS_FREE);
	vlog_i("flash","\t[XV]runtime=%d/%d,free=%d(total=%d,ext_reserved=%d)", sizeof(RUNTIME_PARAMETERS_XVBASE_TYPEDEF), RUNTIME_PARAMETERS_XVBASE_MAX,
			RUNTIME_PARAMETERS_XVBASE_MAX - RUNTIME_PARAMETERS_XVBASE_USED, sizeof(RUNTIME_PARAMETERS_XVBASE_TYPEDEF_4KB), sizeof(RUNTIME_PARAMETERS_XVBASE_TYPEDEF_EXT));
/*
	memcpy(&g_runtime_inst.device_information.mcu_bl_compile, &mcu_bl_compile, sizeof(COMPRESSED_RTC_STRUCT));
	memcpy(&g_runtime_inst.device_information.mcu_hardware_ver, &mcu_hardware_ver, sizeof(VERSION_TYPEDEF));
	memcpy(&g_runtime_inst.device_information.mcu_soft_ver, &mcu_soft_ver, sizeof(VERSION_TYPEDEF));
	memcpy(&g_runtime_inst.device_information.mcu_compile, &mcu_compile, sizeof(COMPRESSED_RTC_STRUCT));
*/
	vlog_i("flash","\tstatic=%d/max=%d,free=%d,ext_free=%d", sizeof(STATIC_PARAMETERS_TYPEDEF), SST25VF080_SIZE_OF_SECTOR * 2,
			SST25VF080_SIZE_OF_SECTOR - offsetof(STATIC_PARAMETERS_TYPEDEF, reserved) - 2 * sizeof(uint32_t),
			SST25VF080_SIZE_OF_SECTOR - sizeof(DSP_DDS_TUNE_TYPEDEF) - sizeof(uint32_t) - sizeof(PWR_CHECK_TABLE));

	if ((g_factory_inst->rssi_regulator_ex == 0) || ((uint16_t)g_factory_inst->rssi_regulator_ex == 0xffff))
		g_rssi_regulator_ex = g_factory_inst->rssi_regulator;
	else
		g_rssi_regulator_ex = g_factory_inst->rssi_regulator_ex;

	set_board_type_parameters();
}

uint32_t memcpy_compare(uint32_t *ram, uint32_t *flash, uint32_t len)	// 0=same; len: unit is uint32_t
{
	uint32_t i;

	for (i = 0; i < len; i++)
	{
		if (ram[i] != flash[i])
			break;
	}
	return len - i;
}

uint32_t check_parameters_is_change(uint8_t flag)	// flag: 0-RUNTIME_PARAMETERS_TYPEDEF, 1-RUNTIME_PARAMETERS_XVBASE_TYPEDEF_4KB
{
	return 1;
}

void set_gui_interactive_pointer(void);
void init_system_parameters(void)
{
	init_user_setup_data_and_hw();

	init_user_book();
	init_message(0);
	dsp_reg_initial();
	set_gui_interactive_pointer();
}


uint32_t erase_some_sectors(uint32_t sector_start, uint32_t sector_num)	// sector_start=sector aligned address(not the number of start sector); sector_num=number of bytes(not the number of sector)
{
	return 0;
}

uint32_t flash_word_program(uint32_t address, uint32_t data)
{
	return 0;
}

void flash_lock(void)
{
}

uint32_t verify_data_at_nor_flash(uint32_t obj_addr, uint32_t *dat_addr, uint32_t length)
{
	return 0;
}

uint32_t program_data_to_nor_flash(uint32_t obj_addr, uint32_t *dat_addr, uint32_t length, uint8_t erase_first, uint8_t locked)	// 0-OK; length: unit is uint8_t
{
	return 0;
}

uint32_t program_one_sector_to_spi_flash(uint32_t addr, uint32_t *data, uint32_t length)	// unit of length is uint8_t
{
	return 0;
}

uint32_t program_user_data_to_flash(uint8_t flag)	// flag: 0-RUNTIME_PARAMETERS_TYPEDEF, 1-RUNTIME_PARAMETERS_XVBASE_TYPEDEF_4KB
{
	return 0;
}

uint8_t set_username_id(uint32_t *save_id, uint8_t *save_name, uint8_t *username_id)	// 0-succ; 1-user_name fail; 2-id fail; 3-all fail
{
}

uint32_t save_static_data_to_flash(void *data, void *data2, uint8_t flag)			// flag: 0-rssi;1-rssi-ex;2-username;3-tune
{
	return 0;
}

uint32_t save_user_data_to_flash(uint8_t flag)	// 0: save now; 1: save at idle/power off; 0xf0: check the paras, save if change; 0xae: check the save flag for saving
{
	return 0;
}




//////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////// MENU CONFIG OPERATION //////////////////////////
//uint32_t g_menu_config[MENU_CONFIG_DATA_MAX_LENGTH / 4] = {GPS_MENU_CONFIG_FLAG, 0x00000020, 0x00000001, SETUP_OTHERS_MENU_CONFIG_FLAG, 0x00000020, 0x00000018};
uint32_t find_a_menu_config_slot(uint32_t start, uint32_t menu_flag, uint32_t *slot)	// menu_flag==0xffffffff: find an empty
{
	uint32_t addr, addr_end;
	MENU_CFG_STRUCT_HEAD *ptr;

	addr = (uint32_t)g_static_ptr->menu_config;
//	addr = (uint32_t)g_menu_config;
	addr_end = MENU_CONF_DATA_END_ADDR(addr);		// data buffer tail address
	ptr = (MENU_CFG_STRUCT_HEAD *)(((start >= addr) && (start < addr_end)) ? start : addr);

	if (menu_flag == 0xffffffff)
	{
		while ((ptr->menu_config_flag & MENU_CONFIG_FLAG_MASK) == MENU_CONFIG_FLAG_PREFIX)	// valid flag
		{
			if ((uint32_t)ptr >= addr_end)
			{
				*slot = CONF_DATA_FULL;
				return 0;														// return 0: fail, and *slot have the error code
			}
			ptr = MENU_CONF_STRUCT_NEXT(ptr);
		}
		*slot = (uint32_t)ptr;
		if (addr_end > *slot + MENU_CONF_HEAD_SIZE)
		{
			return addr_end - *slot - MENU_CONF_HEAD_SIZE;						// return the remainder, unit is char
		}
		else
		{
			*slot = CONF_DATA_NOT_ENOUGH_SPACE;
			return 0;
		}
	}
	else
	{
		while (ptr->menu_config_flag != menu_flag)
		{
			if (((uint32_t)ptr >= addr_end) || ((ptr->menu_config_flag & MENU_CONFIG_FLAG_MASK) != MENU_CONFIG_FLAG_PREFIX))
			{
				if ((uint32_t)ptr >= addr_end)
					*slot = CONF_DATA_FULL;
				else
					*slot = CONF_DATA_NOT_FOUND;
				return 0;
			}
			ptr = MENU_CONF_STRUCT_NEXT(ptr);
		}
		*slot = (uint32_t)ptr;
		return 1;
	}
}

uint16_t menu_config_data_get(uint32_t menu_flag, uint32_t *val, uint16_t num)// return 0xffff: not found; else return the number be got
{
	uint32_t addr, *cfg_data;
	MENU_CFG_STRUCT_HEAD *ptr;
	uint16_t ret, n;

	if (find_a_menu_config_slot(0, menu_flag, &addr))
	{
		ptr = (MENU_CFG_STRUCT_HEAD *)addr;
		cfg_data = (uint32_t *)(addr + MENU_CONF_HEAD_SIZE);
		ret = (num > ptr->menu_config_num) ? ptr->menu_config_num : num;	// item number(bits)
		n = MENU_CONF_DATA_RECALC(ret) / 4;									// bit -> word(32bit)
		for (addr = 0; addr < n; addr++, cfg_data++)
			val[addr] = *cfg_data;
		return ret;		// return the number of the item be got
	}
	else
	{
		return 0xffff;		// return 0xffff: not found
	}
}

uint32_t get_menu_config(uint32_t menu_flag, uint32_t menu_max_num, uint16_t *num)	// return:bit=1: disable;
{
	uint16_t n;
	uint32_t menu_disable;

//	n = menu_config_data_get(menu_flag, &menu_disable, MAIN_MENU_TOTAL_ITEM);
	n = menu_config_data_get(menu_flag, &menu_disable, menu_max_num);
	if (n == 0xffff)				// not found
		menu_disable = 0; 			// all item on
	if (n > menu_max_num)
		n = menu_max_num;
	if (num)
		*num = n;

	return menu_disable;
}

uint32_t read_nor_flash(uint8_t *buf, uint32_t addr, uint32_t len)
{
	memcpy(buf, (void *)addr, len);
	return len;
}

uint32_t write_nor_flash(uint8_t *buf, uint32_t addr, uint32_t len)
{
	return len;
}
