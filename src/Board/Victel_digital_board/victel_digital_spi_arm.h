/**
  ******************************************************************************
  * @file    stm322xg_eval_audio_codec.h
  * <AUTHOR> Application Team
  * @version V5.0.3
  * @date    09-March-2012
  * @brief   This file contains all the functions prototypes for the
  *          stm322xg_eval_audio_codec.c driver.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT 2012 STMicroelectronics</center></h2>
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software
  * distributed under the License is distributed on an "AS IS" BASIS,
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __VICTEL_DIGITAL_SPI_TO_ARM_H__
#define __VICTEL_DIGITAL_SPI_TO_ARM_H__

/* Includes ------------------------------------------------------------------*/
#include "victel_digital_board.h"
#include "DVSI.h"

/** @addtogroup Utilities
  * @{
  */

/** @addtogroup STM32_EVAL
  * @{
  */

/** @addtogroup STM322xG_EVAL
  * @{
  */
#ifndef __WM_FACTORY_H__
#define __WM_FACTORY_H__

#define KHz (1000)
#define MHz (1000*KHz)

typedef enum RF_POWER_ENUM
{
    RF_POWER_MICRO, //微功率
    RF_POWER_LOW,   //低功率
    RF_POWER_MID,   //中功率
    RF_POWER_HIGH,  //高功率
    RF_POWER_N
}RF_POWER_ENUM;

typedef struct RF_FREQ_TUNE_TYPE
{
    unsigned int   Frequency;           //工作频点
    unsigned short TV1;                 //DAC：TV值（通道1）
    unsigned short TV2;                 //DAC：TV值（通道2）
    unsigned short Mod5K;               //DAC：5K调制频偏
    unsigned short Power[RF_POWER_N];   //DAC：发射功率值（微、低、中、高）
}RF_FREQ_TUNE_TYPE;

typedef struct RF_POWER_RAMP_TYPE
{
    /* 爬升曲线：RampUp0+RampUpN <= 120 */
    unsigned short RampUp0;             //功率爬升曲线：0值步数
    unsigned short RampUpN;             //功率爬升曲线：爬升步数

    /* 滚降曲线：RampDown0+RampDownN <= 120 */
    unsigned short RampDownN;           //功率滚降曲线：滚降步数
    unsigned short RampDown0;           //功率滚降曲线：0值步数
}RF_POWER_RAMP_TYPE;

typedef struct WM_FAC_TYPE
{
    /* RF */
    unsigned int       RfFreqBase;                  //基准频点
    unsigned int       RfFreqTuneN;                 //调测频点数
    RF_FREQ_TUNE_TYPE  RfFreqTune[25];              //调测频点参数
    RF_POWER_RAMP_TYPE RfPowerRamp[RF_POWER_N];     //发射功率曲线
    unsigned short     V26MHzBK1;                   //26.0MHz晶振直流偏置校准（通道1）
    unsigned short     V26MHzBK2;                   //26.0MHz晶振直流偏置校准（通道2）
    unsigned short     V19P2MHz;                    //19.2MHz晶振直流偏置校准
    unsigned short     RfRssiCoef;                  //RSSI校准参数

    /* 4FSK */
    unsigned short     ModPeakCoef[4];              //4FSK调制校准系数  -保留
    unsigned short     SyncOffset[16];              //时隙同步校准值    -保留

    /* Audio */
    unsigned char AudioMicGain[16];                 //保留
    unsigned char AudioSpkGain[16];                 //保留
}WM_FAC_TYPE;

extern WM_FAC_TYPE hFAC;


typedef struct DB_TUNE_TYPE
{
	unsigned int	Head;		//0x5A5A5A5A
	unsigned char	Data[2040];
	unsigned int	Tail;		//0XA5A5A5A5
}DB_TUNE_TYPE;

#endif /* __WM_FACTORY_H__ */





/** @defgroup STM322xG_EVAL_AUDIO_CODEC
  * @{
  */


/** @defgroup STM322xG_EVAL_AUDIO_CODEC_Exported_Types
  * @{
  */

/** @defgroup STM322xG_EVAL_AUDIO_CODEC_Exported_Constants
  * @{
  */

#define MCU_SPI_ID_MCU					1
#define MCU_SPI_ID_RF					2
#define MCU_SPI_ID_MANAGER				30

#define IPC_TAG_HEAD					0x5055
#define IPC_TAG_TAIL					0xDCBA
#define MCU_INF_MAX_DATA				536
#define PDT_AIR_DATA_LENGTH				36

typedef struct
{
    uint16_t head;
    uint16_t frame_len;
    uint8_t  sn;
    uint8_t  dst_id;
    uint8_t  src_id;
    uint8_t  reserved1;
    uint8_t  data[MCU_INF_MAX_DATA];
    uint8_t  reserved2;
    uint8_t  chk_sum;
    uint16_t tail;
}MCU_FRAME_TYPEDEF;

#define MCU_INF_MAX_LEN					sizeof(MCU_FRAME_TYPEDEF)
#define MCU_INF_FRAME_HEAD_NUM			8
#define MCU_INF_FRAME_TAIL_NUM			4
#define GET_MCU_INF_DATA_START(frame)	((uint8_t *)(frame) + 8)


// 基带和射频之间总线数据类型
typedef enum
{
    BB_RF_BUS_DATA_TYPE_BASEBAND_DATA = 0,	// 基带数据，后跟36字节
    BB_RF_BUS_DATA_TYPE_BASEBAND_NULL,		// 无实际数据，用于未收到信令时输出底噪
    BB_RF_BUS_DATA_TYPE_PCM_DATA,			// PCM数据，后跟20ms采样数据，8K采样率，16bit
    BB_RF_BUS_DATA_TYPE_CONTROL_DATA,		// 控制消息，格式见下方
    BB_RF_BUS_DATA_TYPE_TUNE,				// 射频调测；注意DSP回复时主控会将回复数据存放于rep_fr_bb，所以最大数据不能大于sizeof(bb_control_base_struct_t)
    BB_RF_BUS_DATA_TYPE_PC_UPGRADE,			// 升级
    BB_RF_BUS_DATA_TYPE_PC_VERIFY,			// 校验升级程序
    BB_RF_BUS_DATA_TYPE_PC_SET_PARAS,		// 写频
    BB_RF_BUS_DATA_TYPE_PC_VERIFY_PARAS,	// 校验写频参数
    BB_RF_BUS_DATA_TYPE_PC_GET_PARAS,		// 读频
	BB_RF_BUS_DATA_TYPE_TIMESLOT,			// 时隙消息
	BB_RF_BUS_DATA_TYPE_VPTT,				// 虚拟PTT按键
} e_BB_RF_bus_data_type_t;

// Control type: 20240104-remove to user_define.h
/*
typedef enum
{
    BASEBAND_TYPE_NULL = 0,     // 保留，可以用于输出一些调试信息
    BASEBAND_TYPE_VERSION,      // 软硬件版本和功能信息
    BASEBAND_TYPE_INITIAL,      // 初始化射频板
    BASEBAND_TYPE_SET_RF_PWR,   // 设置发射功率等级
    BASEBAND_TYPE_SET_BER,      // 设置误码模式
    BASEBAND_TYPE_SET_FREQ,     // 设置收发频率
    BASEBAND_TYPE_REBOOT,       // 复位射频板
    BASEBAND_TYPE_CHANNEL_SCAN, // 背景扫描
} e_baseband_control_type_t;

typedef enum
{
	DSP_TUNE_TYPE_FREQ_BAND = 1,		// 工作频段；			参数1：频率：350000000Hz
	DSP_TUNE_TYPE_FREQ,					// 工作频率；			参数1：1-发射频率，2-接收频率；					参数2：频率：350000000Hz（暂不用此命令，用BASEBAND_TYPE_SET_FREQ进行设置）
	DSP_TUNE_TYPE_BIAS_26M,				// 直流偏置26.0MHz；参数1：1-BK1 2-BK2；							参数2：DAC值：0-32767
	DSP_TUNE_TYPE_RCV_TV,				// 接收TV；			参数1：1-BK1 2-BK2；					参数2：DAC值：0-4095
	DSP_TUNE_TYPE_MOD_5K,				// 5K调制;			参数1：0-32767
	DSP_TUNE_TYPE_BIAS_19M2,			// 直流偏置19.2MHz；参数1：0-4095
	DSP_TUNE_TYPE_RF_POWER,				// 发射功率；			参数1：0-微功率 1-低 2-中 3-高				参数2：功率幅值：0-4095
	DSP_TUNE_TYPE_CLIMB_CURVE,			// 爬坡曲线；			参数1：0-微功率 1-低 2-中 3-高				参数2：爬升0值步数，参数3：爬升步数，参数4：滚降步数，参数5：滚降0值步数
	DSP_TUNE_TYPE_RSSI_REGULATOR,		// RSSI校准；			参数1：校准值：默认170
	DSP_TUNE_TYPE_BK4819_SEL,			// BK4819选择；		参数1：1-BK1 2-BK2；
	DSP_TUNE_TYPE_BER_CTRL = 32,		// 误码率测试；			参数1：接收超帧数FN；						参数2：总错误BIT数TBN：参数3：当前超帧错误BIT数CBN；参数4：接收频偏：0或±1
	DSP_TUNE_TYPE_SAVE_DATA = 128,		// 数据存储；			参数1：1-保存 2-恢复默认
} e_dsp_tune_command_t;
*/


typedef enum
{
    BASEBAND_CONTROL_OPCODE_TYPE_REQUEST = 0,
    BASEBAND_CONTROL_OPCODE_TYPE_REPLY = 1,
    BASEBAND_CONTROL_OPCODE_TYPE_BROADCAST = 2,
} e_baseband_control_opcode_type_t;

typedef enum
{
    TX_PWR_MODE_LOW = 0,
    TX_PWR_MODE_HIGH,
    TX_PWR_MODE_AUTO
} e_tx_power_level_t;


////////////////////////////////////////////// 基带数据和音频数据 //////////////////////////////////
// 空口信令交互数据
typedef struct
{
    uint8_t msg_type;		// BB_RF_BUS_DATA_TYPE_BASEBAND_DATA/BB_RF_BUS_DATA_TYPE_BASEBAND_NULL
    uint8_t rssi;			// 接收场强值，实际场强加上130得到，比如实际为-60dBm，则赋值为70
    uint8_t dqi;			// 接收信号质量（如不支持暂填0）
    uint8_t reserved;		// 主控回复DSP时（不带数据）：bit0：1/0-开始/停止采集MIC；rssi表示MIC增益（0-95）
    uint8_t data[PDT_AIR_DATA_LENGTH];	// C0+C1+C2 + D0~D32
} bb_rf_bus_data_to_modulated_t;

typedef struct
{
    uint8_t msg_type;
	uint8_t reserved[3];
    unsigned int slot_number;
    unsigned int slot_count;
}bb_rf_bus_data_timeslot_t;

typedef struct
{
    uint8_t msg_type;
    uint8_t state; /*0 - relase ptt   !0 - press ptt*/
    uint8_t reserved[2];
}bb_rf_bus_data_vptt_t;

// 音频PCM交互数据
typedef struct
{
	uint8_t msg_type;		// BB_RF_BUS_DATA_TYPE_PCM_DATA
	uint8_t volume;			// 音量增益：0-29
	uint8_t reserved[2];
	int16_t pcm_data[SELP_FRAME_SIZE];
} bb_rf_bus_speaker_PCM_t;


///////////////////////////////////////////////// 控制数据 ////////////////////////////////////
typedef struct
{
    uint8_t msg_type;		// BB_RF_BUS_DATA_TYPE_CONTROL_DATA
    uint8_t ctrl_type;		// e_baseband_control_type_t
    uint8_t ctrl_type2;
    uint8_t opcode_type	: 2;// e_baseband_control_opcode_type_t
    uint8_t opcode_rsvd	: 6;// 0
} bb_rf_bus_control_msg_header_t;

typedef struct
{
    uint8_t  msg_type;			// BB_RF_BUS_DATA_TYPE_TUNE
    uint8_t  ctrl_type;			// 1-Write 2-Read 3-report(only ber-ctrl)
    uint8_t  Id;
    uint8_t  AckU;
    uint32_t Param[6];
} bb_rf_bus_tune_t;


#define BB_RF_BUS_UPGRADE_HEADER_LEN		12
typedef struct
{
	uint8_t  msg_type;						// BB_RF_BUS_DATA_TYPE_PC_UPGRADE / BB_RF_BUS_DATA_TYPE_PC_VERIFY / BB_RF_BUS_DATA_TYPE_PC_SET_PARAS/BB_RF_BUS_DATA_TYPE_PC_VERIFY_PARAS/BB_RF_BUS_DATA_TYPE_PC_GET_PARAS
	uint8_t  reserved[3];
	uint16_t sn;							// 帧序号，DSP须原值返回给主控；读频：DSP可忽略；升级/校验/写频的第0帧无数据内容，仅作为指示DSP进行初始化（如擦除flash）
	uint16_t num;							// 操作长度，DSP须原值返回给主控；升级/校验/写频的第0帧由于无数据，故此值为0
	uint32_t offset;						// 操作地址（偏移地址），DSP须原值返回给主控；读频：从DSP参数块起始地址+offset处读num个数据返回；升级/校验/写频：第0帧表示需操作的数据总长度，第1-4帧：操作的偏移地址
	uint8_t  data[MCU_INF_MAX_DATA - BB_RF_BUS_UPGRADE_HEADER_LEN];	// 数据；长度=num
} bb_rf_bus_upgrade_t;


typedef struct
{
	uint8_t  msg_type;			// BB_RF_BUS_DATA_TYPE_PC_SET_PARAS
    uint8_t  ctrl_type;			// 1-Write 2-Read
	uint8_t  msg_total	: 4;	// 写频总帧数(n)
	uint8_t  msg_index	: 4;	// 当前帧数(0 ~ (n-1))
	uint8_t  reserved;
	uint8_t  data[MCU_INF_MAX_DATA - 4];	// 写频数据；512定长
} bb_rf_bus_set_paras_t;


// 基本控制帧
#define MIC_CODEWORD_MAX_NUM		100
#define MIC_SPK_TIME_SLICE			3
#define SPK_CODEWORD_SAVE_MAX_NUM	(MIC_SPK_TIME_SLICE * 1)
typedef struct
{
	bb_rf_bus_control_msg_header_t msg_head;
	uint8_t data[MCU_INF_MAX_DATA - sizeof(bb_rf_bus_control_msg_header_t)];
} bb_control_base_struct_t;


#define AIR_SIGNAL_TYPE_NONE		0
#define AIR_SIGNAL_TYPE_VALID		1
#define AIR_SIGNAL_TYPE_NULL		3


#define MCU_INF_RX_DMA_LENGTH		(MCU_INF_MAX_LEN + 6)

typedef struct
{
	MCU_FRAME_TYPEDEF spi_frame;				// DMA接收缓存区
	uint8_t reserved_for_dma[7];				// prevent DMA receiving overflow
	uint8_t mcu_tx_sn;

//	PCM(from baseband) -> mic_voice -> encoder -> mic_codeword
	int16_t mic_voice[SELP_FRAME_SIZE];
	uint8_t air_signal[PDT_AIR_DATA_LENGTH];
	bb_control_base_struct_t rep_fr_bb;			// B0=BB_RF_BUS_DATA_TYPE_CONTROL_DATA is valid
	uint8_t mcu_tx_dma_buffer[MCU_INF_MAX_LEN];
	uint8_t mic_codeword[MIC_CODEWORD_MAX_NUM][SELP_BYTE_SIZE_FEC];

	uint8_t mic_codeword_num;
//	stack speaking -> spk_codeword -> decoder -> spi(to baseband)
	uint8_t spk_codeword[SPK_CODEWORD_SAVE_MAX_NUM][SELP_BYTE_SIZE_FEC];

	uint8_t reserved[2];
	uint8_t rssi_bb;
	uint8_t mic_voice_valid		: 1;
	uint8_t air_signal_valid	: 2;	// 0-not receive the frame; 1-air data; 3-null data
	uint8_t mcu_tx_busy			: 1;
	uint8_t spk_codeword_idx	: 2;
	uint8_t ptt_for_voice		: 1;	// notify DSP output mic voice
	uint8_t reserved_bit		: 1;
}MCU_INF_TYPEDEF;

/**
  * @}
  */

/** @defgroup STM322xG_EVAL_AUDIO_CODEC_Exported_Macros
  * @{
  */

/**
  * @}
  */

#endif /* __VICTEL_DIGITAL_SPI_TO_ARM_H__ */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/

