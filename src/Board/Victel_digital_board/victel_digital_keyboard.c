/**
  ******************************************************************************
  *                Copyright (c) 2011, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    16-November-2011
  * @brief   This file provides
  *            - scan keyboard gpio initial
  *            - scan and re-scan the keyboard, return the keyboard status
  *            - encode switch gpio initial
  *            - read the code of switch
  ******************************************************************************
  */



/* Includes ------------------------------------------------------------------*/
#include "stdio.h"
#include "string.h"
#include "victel_digital_board.h"
#include "victel_digital_keyboard.h"
#include "victel_digital_lcd.h"
#include "victel_digital_spi_flash.h"
#include "victel_digital_usart.h"
#include "victel_digital_spi_arm.h"
#include "stack_config.h"
#include "global_define.h"


#define EXIT_OPERATION_TIMEOUT()		do{operation_timeout_flag = 0;keyboard_idle_time = 0;}while(0)


extern uint8_t g_print_adc;
extern RUNTIME_PARAMETERS_TYPEDEF g_runtime_inst;
extern STATIC_PARAMETERS_TYPEDEF *g_static_ptr;
extern RUNTIME_PARAMETERS_XVBASE_TYPEDEF *g_runtime_inst_xvbase;
extern uint8_t interphone_mode;
extern uint16_t check_ext_mic_amp;

#ifndef VAD_KEY_USE_AS_STICK_PTT
const uint8_t vad_tip_defined[2][2][12] =
		{{"�����ѹر�", "�����Ѵ�"},
		 {"VAD: CLOSE", "VAD: OPEN"}
		};
#endif

static KB_DETECT g_kb_inst;
static uint8_t keyboard_misc_status = 0, keyboard_misc_ctrl = LR_KEY_REMAP_ENABLE;
static uint32_t keyboard_val_read, extra_button_value = 0;

static P_FUN_VOID_VOID p_timer3_handle/*, p_exti1_handle*/;
P_FUN_VOID_VOID p_config_bt_process = null_void_void;

uint32_t keyboard_idle_time = 0, keyboard_idle_timeout = 0;	// keyboard_idle_timeout: 10s=10000/KEYBOARD_POLL_PERIOD=250
uint8_t peroidic_refresh_flag = 0, hook_hook_flag = 0, keyboard_locked_flag = 0;		// 0-normal; 1-temp unlocked; 2-locked; 0xf0-pre locked; 0xf1-pre unlocked
uint8_t operation_timeout_flag = 0;	// 0-normal; 1-timeout(5s) but not sleep(disable); 0xff-timeout(exceed keyboard_idle_timeout) & sleep
//uint8_t dev_gate_ctrl = 0xff;		// 0xff-invalid; bit7: 0-terminal; 1-base; bit6-0: ZZWPRO_GATE-base; VEHICLE_GATE-vehicle; MODULE_805_GATE-805
uint8_t g_misc_status = 0;			// bit7:1-fan running; bit6:m1 key op
uint32_t timestamp_1s = 0, timestamp_2s = 0, timestamp_kb_scan = 0, timestamp_10ms = 0;
int32_t knob_turn_times = 0, knob_turn_check = 0, knob_turn_times_m1 = 0, knob_turn_check_m1 = 0;


uint32_t KB_CODE_KEY_LEFT;		// bit12
uint32_t KB_CODE_KEY_RIGHT;		// bit13
uint32_t KB_CODE_KNOB_ENTER;	// bit14

uint32_t KB_CODE_KEY_PTT;		// bit16					(PTT, PA8, thick: upper; thin: upder upper)
uint32_t KB_CODE_KEY_USER1;		// bit17, PTT2			(PRO1, PG6, thick: middle; thin: upper/blue)
uint32_t KB_CODE_KEY_USER2;		// bit18, return/cancel	(PRO2, PG7, thick: bottom; thin: above bottom/orange)
uint32_t KB_CODE_KEY_USER3;		// bit19, not used now		(PRO3, PG5, thin: bottom)
uint32_t KB_CODE_KEY_ALARM;		// bit20					(ALARM, PB9, thick: device top)


#define TIMER_NUMBER	12
TIMER_TYPEDEF g_timer_define[TIMER_NUMBER];


void set_operation_timeout_time(uint8_t seconds)
{
	keyboard_idle_timeout = (uint32_t)seconds * 1000 / KEYBOARD_POLL_PERIOD;
}


void init_lcd_power_ctrl_line(void)
{
	at32_led_init(LCD_POWER_CTRL_LINE);
	set_gpio_high(LCD_POWER_CTRL_LINE);	// power on
}

void led_play(uint8_t flag)
{
	switch (flag)
	{
		case BAR_TYPE_TR_ARROW_UP:
			set_led_off(LEDG);
			set_led_on(LEDR);
			break;

		case BAR_TYPE_TR_ARROW_DOWN:
			set_led_on(LEDG);
			set_led_off(LEDR);
			break;

		case BAR_TYPE_TR_ARROW_UPDOWN:
			set_led_on(LEDG);
			set_led_on(LEDR);
			break;

		default:
			set_led_off(LEDG);
			set_led_off(LEDR);
			break;
	}
}

uint16_t code_to_numeric(uint32_t code)
{
	uint8_t i, n_key;
	uint16_t ret;

	if (code < KB_CODE_KNOB_MASK)
	{
		n_key = 0;
		ret = 0;
		for (i = 0; i < 31; i++)
		{
			if (code & (1 << i))
			{
#if 0
//				if (i == 18)				// user2 -> return
				if ((i == 18) ||			// user2 -> return
					((i == 11) && (is_screen_use_well_key() == 0)))
					ret |= KEY_CODE_VIRT_RETURN << (n_key * 5);
				else if (i == 15)			// middle ->
					ret |= (maintain_setup_parameter(PARA_OPERATE_READ, KEY_FUNC_REMAP_MIDDLE, 0) + 1) << (n_key * 5);
				else if (i == 30)			// disable knob enter
					ret |= KEY_CODE_MIDDLE;
				else
#endif
					ret |= (i + 1) << (n_key * 5);

				if (++n_key >= 3)
				{
					ret |= 0x8000;
					break;
				}
			}
		}
	}
	else
	{
		ret = (uint16_t)(code - KB_CODE_KNOB_MASK);
	}
	return ret;
}


#if KEYBOARD_VAL32_PRINT_DEBUG > 1
void keyboard_debug_output(void)
{
	printf("FSM=%d,val=%08x(read=%08x):stat=%c,remap=%d",
		g_kb_inst.kb_fsm, g_kb_inst.kb_value, keyboard_val_read,
		(keyboard_misc_ctrl & BUTTON_STATUS_MASK) ? 'P' : 'R', (keyboard_misc_ctrl & LR_KEY_REMAP_NOW) ? 1 : 0);
}
#endif

void keyboard_fsm_init(uint8_t flag)
{
	if (flag)
		keyboard_misc_ctrl &= ~BUTTON_STATUS_MASK;

	g_kb_inst.kb_fsm = KB_FSM_IDLE;
	g_kb_inst.kb_value = 0;
	g_kb_inst.kb_press_flg = 0;
	g_kb_inst.kb_release_flg = 0;
	g_kb_inst.kb_timeout_flg = 0;
	g_kb_inst.kb_timeout = 0;
	g_kb_inst.kb_timeout_times = 0;
}

uint8_t is_ptt_pressed(void)
{
	return ((g_kb_inst.kb_fsm != KB_FSM_IDLE) &&
		((g_kb_inst.kb_value == D_KB_CODE_KEY_PTT) || (g_kb_inst.kb_value == D_KB_CODE_KEY_USER1) || (g_kb_inst.kb_value == D_KB_CODE_KEY_ALARM))) ? 1 : 0;
}

#if 0
  #if 1
// if not additional check, multi key must pressed within a delay time at KB_FSM_DETECTED
//#define ADDITIONAL_KEY_CHECK()	if (((keyboard_val_read & g_kb_inst.kb_value) == g_kb_inst.kb_value)\
//							/*&& (g_kb_inst.kb_timeout < KB_CONJOINED_CHECK_TIMEOUT)*/ && (g_kb_inst.kb_timeout_times == 0))
#define ADDITIONAL_KEY_CHECK()	if ((keyboard_val_read & g_kb_inst.kb_value) == g_kb_inst.kb_value)\
				{\
					g_kb_inst.kb_value = keyboard_val_read;\
					g_kb_inst.kb_fsm = KB_FSM_DETECTED;\
					g_kb_inst.kb_timeout = 0;\
					g_kb_inst.kb_timeout_times = 0;\
					/*printf("additional key is pressed, debouncing again...");*/\
				}
  #else
#define ADDITIONAL_KEY_CHECK()	g_kb_inst.kb_fsm = KB_FSM_IDLE;\
								/*printf("[ADD KEY]%08x->%08x", g_kb_inst.kb_value, keyboard_val_read)*/
  #endif
#else

  #define ADDITIONAL_KEY_CHECK		addition_key_check

#endif

void addition_key_check(void)
{
	if ((keyboard_val_read & g_kb_inst.kb_value) == g_kb_inst.kb_value)
	{
		keyboard_misc_ctrl |= MULTI_KEY_DETECTED;
		g_kb_inst.kb_value = keyboard_val_read;
		g_kb_inst.kb_fsm = KB_FSM_DETECTED;
		g_kb_inst.kb_timeout = 0;
		g_kb_inst.kb_timeout_times = 0;
		/*printf("additional key is pressed, debouncing again...");*/
	}
}

// ���ö�����ť�������Ҽ���Ϊ����ʱ����ʾ�����Ҽ�����ť���ܻ�������ʱ��process_input()�н���ӳ��
// ������������Ҽ�������lr_key_remap_ctrl()��ӳ��(��Ϊӳ�����붥����ťһ�£�����Ӧ�ò�û������)
uint8_t is_reverse_knob_clock_lr_key(void)
{
	return keyboard_misc_ctrl & LR_KEY_KNOB_REVERSE_FUNC;
}

void lr_key_remap_ctrl(uint8_t enable)
{
	if ((keyboard_misc_ctrl & LR_KEY_KNOB_REVERSE_FUNC) == 0)
	{
		if (enable && maintain_setup_parameter(PARA_OPERATE_READ, KEY_FUNC_REMAP_VOLUME_IND, 0))
			keyboard_misc_ctrl |= LR_KEY_REMAP_ENABLE;
		else
			keyboard_misc_ctrl &= ~LR_KEY_REMAP_ENABLE;
	}
}

void lr_key_remap(void)
{
	keyboard_misc_ctrl &= ~LR_KEY_REMAP_NOW;
	if (keyboard_misc_ctrl & LR_KEY_REMAP_ENABLE)
	{
//		printf("lr_key_remap");
		if (g_kb_inst.kb_value == D_KB_CODE_KEY_LEFT)
		{
			knob_turn_times--;
			keyboard_misc_ctrl |= LR_KEY_REMAP_NOW;
		}
		else if (g_kb_inst.kb_value == D_KB_CODE_KEY_RIGHT)
		{
			knob_turn_times++;
			keyboard_misc_ctrl |= LR_KEY_REMAP_NOW;
		}
	}
}

uint8_t is_bt_insert(void)
{
	return keyboard_misc_status & EAR_PHONE_CONFIRM;
}

uint8_t is_kb_knob_enter_disable(void)
{
	return KB_CODE_KNOB_ENTER ? 0 : 1;
}

uint8_t keyboard_val2code(uint32_t key)
{
	uint8_t key_val;

	switch (key)
	{
		case KB_CODE_KEY0:
			key_val = KEY_CODE_0;
			break;
		case KB_CODE_KEY1:
			key_val = KEY_CODE_1;
			break;
		case KB_CODE_KEY2:
			key_val = KEY_CODE_2;
			break;
		case KB_CODE_KEY3:
			key_val = KEY_CODE_3;
			break;
		case KB_CODE_KEY4:
			key_val = KEY_CODE_4;
			break;
		case KB_CODE_KEY5:
			key_val = KEY_CODE_5;
			break;
		case KB_CODE_KEY6:
			key_val = KEY_CODE_6;
			break;
		case KB_CODE_KEY7:
			key_val = KEY_CODE_7;
			break;
		case KB_CODE_KEY8:
			key_val = KEY_CODE_8;
			break;
		case KB_CODE_KEY9:
			key_val = KEY_CODE_9;
			break;
		case KB_CODE_KEY_STAR:
			key_val = KEY_CODE_STAR;
			break;
		case KB_CODE_KEY_WELL:
			key_val = KEY_CODE_WELL;
			break;
		case D_KB_CODE_KEY_LEFT:
			key_val = KEY_CODE_LEFT;
			break;
		case D_KB_CODE_KEY_RIGHT:
			key_val = KEY_CODE_RIGHT;
			break;
		case D_KB_CODE_KEY_MIDDLE:
			key_val = KEY_CODE_MIDDLE;
			break;
		case D_KB_CODE_KNOB_ENTER:
			key_val = KEY_CODE_KNOB_ENTER;
			break;
		case 0:
			key_val = 0;
			break;
		default:
//			printf("invalid: %08x", key_val);
			key_val = 0xff;
			break;
	}

	return key_val;
}

uint32_t keyboard_val_remap(uint32_t val)
{
	uint32_t ret = val;

	if (val & D_KB_CODE_KEY_LEFT)
	{
		ret &= ~D_KB_CODE_KEY_LEFT;
		ret |= KB_CODE_KEY_LEFT;
	}
	if (val & D_KB_CODE_KEY_RIGHT)
	{
		ret &= ~D_KB_CODE_KEY_RIGHT;
		ret |= KB_CODE_KEY_RIGHT;
	}

	if (val & D_KB_CODE_KEY_USER1)
	{
		ret &= ~D_KB_CODE_KEY_USER1;
		ret |= KB_CODE_KEY_USER1;
	}

	if (val & D_KB_CODE_KEY_USER2)
	{
		ret &= ~D_KB_CODE_KEY_USER2;
		ret |= KB_CODE_KEY_USER2;
	}

	if (val & D_KB_CODE_KEY_USER3)
	{
		ret &= ~D_KB_CODE_KEY_USER3;
		ret |= KB_CODE_KEY_USER3;
	}

	return ret;
}

uint8_t keyboard_get_one_input(void)
{
	uint32_t key_val, key_val2;

	key_val = 0xffffffff;
	while (key_val == 0xffffffff)
	{
  		key_val = matrix_keyboard_scanning();
  		sys_delay(20);
  		key_val2 = matrix_keyboard_scanning();
//		printf("key check:%08x:%08x", key_val, key_val2);
		key_val = (key_val == key_val2) ? keyboard_val2code(key_val) : 0xffffffff;
	}

	return (uint8_t)key_val;
}

//uint8_t test_key_debounce = 0x81;
DEBOUNCE_KEY_TYPEDEF debounce_key;

uint8_t is_power_off_key_pressed(void)
{
	return POWER_ONOFF_IS_RELEASED() ? 0 : 1;
}

uint8_t is_ptt_key_pressed(void)
{
	return PTT_IS_RELEASED() ? 0 : 1;
}

uint8_t is_alarm_key_pressed(void)
{
	return ALARM_IS_RELEASED() ? 0 : 1;
}

void check_separated_button(void)
{
	if (is_ptt_key_pressed())
		extra_button_value |= KB_CODE_KEY_PTT;
	else
		extra_button_value &= ~KB_CODE_KEY_PTT;

	if (is_alarm_key_pressed())
		extra_button_value |= KB_CODE_KEY_ALARM;
	else
		extra_button_value &= ~KB_CODE_KEY_ALARM;

	if (is_power_off_key_pressed())
		extra_button_value |= D_KB_CODE_KEY_MIDDLE;
	else
		extra_button_value &= ~D_KB_CODE_KEY_MIDDLE;

	if (KNOB_SURE_IS_RELEASED() == 0)
		extra_button_value |= KB_CODE_KNOB_ENTER;
	else
		extra_button_value &= ~KB_CODE_KNOB_ENTER;
}

uint8_t is_vad_config_key(void)
{
#ifdef PTT_KEY_USE_AS_STICK
	return (g_kb_inst.kb_value == D_KB_CODE_KEY_PTT) ? 1 : 0;
#else
	return ((g_kb_inst.kb_value == D_KB_CODE_KEY_USER1) && is_bt_insert() && g_static_ptr->misc_static_config.tactics_radio) ? 1 : 0;
#endif
}

uint8_t keyboard_check(void)
{
	return 0;
}

uint8_t is_keyboard_pressed(uint32_t *code)
{
	if (((keyboard_misc_ctrl & (BUTTON_STATUS_MASK | LR_KEY_REMAP_NOW)) == 0) &&
		((g_kb_inst.kb_fsm == KB_FSM_AFFIRM) || (g_kb_inst.kb_fsm == KB_FSM_TIMEOUT)))
	{
		keyboard_misc_ctrl |= BUTTON_STATUS_MASK;
		if (is_vad_config_key() == 0)
		{
			*code = g_kb_inst.kb_value;
			return 1;
		}
		else
		{
			return 0;
		}
	}
	else if ((keyboard_misc_ctrl & BUTTON_STATUS_MASK) && (keyboard_misc_ctrl & MULTI_KEY_CONFIRMED))
	{
		keyboard_misc_ctrl &= ~(MULTI_KEY_DETECTED | MULTI_KEY_CONFIRMED);
		*code = g_kb_inst.kb_value;
		return 1;
	}
	else
	{
		return 0;
	}
}

void set_dev_gate(uint8_t is_base, uint8_t set_to_low)		// is_base: 0x80-base, 0x00-terminal; set_to_low: 0-low, else-high
{
//	if ((is_base == (dev_gate_ctrl & IS_BASE_GATE)) && (dev_gate_ctrl != 0xff))
//	{
//		if (set_to_low)
//			set_gpio_low((Led_TypeDef)(dev_gate_ctrl & ~IS_BASE_GATE));
//		else
//			set_gpio_high((Led_TypeDef)(dev_gate_ctrl & ~IS_BASE_GATE));
//	}
}

#if defined VAD_KEY_USE_AS_STICK_PTT || defined PTT_KEY_USE_AS_STICK
void set_ptt_stick_pressed(void);
#endif
uint8_t is_keyboard_released(uint32_t *code)
{
	if (((keyboard_misc_ctrl & (BUTTON_STATUS_MASK | LR_KEY_REMAP_NOW)) == BUTTON_STATUS_MASK) &&
		((g_kb_inst.kb_fsm == KB_FSM_IDLE) || (g_kb_inst.kb_fsm == KB_FSM_INVALID)))
	{
		keyboard_misc_ctrl &= ~(BUTTON_STATUS_MASK | MULTI_KEY_DETECTED | MULTI_KEY_CONFIRMED);
		if (is_vad_config_key() == 0)
		{
			*code = g_kb_inst.kb_value;
			return 1;
		}
		else
		{
#if defined VAD_KEY_USE_AS_STICK_PTT || defined PTT_KEY_USE_AS_STICK
			set_ptt_stick_pressed();
#else
			set_speech_vad_enable(is_speech_vad_enable() ? 0 : 1);
			GL_LCD_TipDraw(0, (uint8_t *)vad_tip_defined[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][is_speech_vad_enable()], 0, 1000);
#endif
			return 0;
		}
	}
	else
	{
		return 0;
	}
}

uint16_t kb_timeout_process(uint32_t *code)
{
	if ((g_kb_inst.kb_fsm == KB_FSM_TIMEOUT) && (g_kb_inst.kb_timeout_times != g_kb_inst.kb_timeout_flg) && (is_vad_config_key() == 0))
	{
//		printf("timeout check: times(unit=200ms)=%d, process=%d", g_kb_inst.kb_timeout_times, g_kb_inst.kb_timeout_flg);
		g_kb_inst.kb_timeout_flg = g_kb_inst.kb_timeout_times;
		*code = g_kb_inst.kb_value;
		return g_kb_inst.kb_timeout_flg;
	}
	else
	{
		return 0;
	}
}

uint16_t is_kb_timeout_present(void)
{
	return g_kb_inst.kb_timeout_times;
}

uint8_t is_knob_turn(uint32_t *code)
{
	uint8_t ret = 0;

	if (knob_turn_times != knob_turn_check)
	{
//		printf("times=%d, check=%d", knob_turn_times, knob_turn_check);
		if (code)
		{
			if (knob_turn_times > knob_turn_check)		// turn the knob clockwise
			{
				knob_turn_check++;
				*code = KB_CODE_KNOB_CLOCK;
			}
			else
			{
				knob_turn_check--;
				*code = KB_CODE_KNOB_ANTICLOCK;
			}
			if (keyboard_misc_status & KEYBOARD_ONLINE)
				ret = 1;
		}
		else
		{
			knob_turn_check = knob_turn_times;
		}
	}

	return ret;
}


void timer9to14_initial(uint8_t tim, uint8_t tim_irq_pri, uint32_t t_ms, uint8_t en)	// t9/12 have 2 channels
{
}

uint32_t timer3_pause_counter = 0;
void timer9to14_start(uint8_t tim)
{
}

void timer9to14_stop(uint8_t tim)
{
}

uint8_t is_timer12_running(void)
{
	return 0;
}

void timer9to14_set_timeout_immediately(uint8_t tim)
{
}

uint32_t get_8bit_difference(uint32_t new_8bit, uint32_t old_8bit)
{
	if ((uint8_t)new_8bit < (uint8_t)old_8bit)
		return (new_8bit & 0x000000ff) + 0x000000ff - (uint8_t)old_8bit;
	else
		return (uint8_t)new_8bit - (uint8_t)old_8bit;
}

uint32_t get_timestamp_measure(void)					// 0.5ms
{
	return 0;
}

uint32_t get_timestamp_measure_format(void)				// 1ms
{
	return 0;
}

uint32_t get_measure_timer_difference(uint32_t new_16bit, uint32_t old_16bit)				// ׼ȷ���������ʱ����Ϊ65536/2=32768ms
{
	return 0;
}

uint32_t get_timestamp_40us_measure(void)				// 40us
{
	return 0;
}

uint32_t get_40us_measure_timer_difference(uint32_t new_16bit, uint32_t old_16bit)							// ׼ȷ���������ʱ����Ϊ65536/25=2621ms
{
	return 0;
}

void start_measure_timer(void)
{
}

void stop_measure_timer(void)
{
}

void crystal_measure_init(void)
{
}

uint8_t is_dev_control_fans(void)
{
	return 0;
}

uint32_t set_base_fan_speed(uint8_t ch, uint8_t speed)
{
	return speed;
}

const uint8_t speed_lv_define[FAN_SPEED_TOTAL_LEVEL] = {
	0,
	(uint8_t)(TIM4_PWM_PERIOD * 0.2/* + 1*/),
	(uint8_t)(TIM4_PWM_PERIOD * 0.4/* + 1*/),
	(uint8_t)(TIM4_PWM_PERIOD * 0.6/* + 1*/),
	(uint8_t)(TIM4_PWM_PERIOD * 0.8/* + 1*/),
	(uint8_t)(TIM4_PWM_PERIOD * 1.0/* + 1*/)};
void timer4_pwm_init(void)
{
}

#define SET_LCD_BRIGHTNESS_WITH_6LEVEL	1
void pwm_output_init(void)
{
}

uint8_t peroidic_refresh_check(void)
{
	if (peroidic_refresh_flag)
	{
		peroidic_refresh_flag = 0;
		return 1;
	}
	else
	{
		return 0;
	}
}


/***************************interrupt handler***************************/

void set_dsp_middle_handler(uint32_t p_func)
{
//	p_exti1_handle = (P_FUN_VOID_VOID)p_func;
	p_timer3_handle = (P_FUN_VOID_VOID)p_func;
}

void mcu_sleep_prepare(void)
{
	GL_LCDOff(1);
}

#define MASKED_INT_AT_STOP_MODE 	(DSP_HPI_INT_PIN | DSP_GPIO_INT_PIN)
void mcu_stop_prepare(void)
{
	GL_LCDOff(1);
//	EXTI->IMR &= ~MASKED_INT_AT_STOP_MODE;
}

void mcu_sleep_resume(void)
{
	update_time_force();
	GL_LCDOn();
}

void mcu_stop_resume(void)
{
	update_time_force();
	GL_LCDOn();
//	EXTI->IMR |= MASKED_INT_AT_STOP_MODE;
}

void dsp_interrupt_config(uint8_t flag)	// high 4bit: enable bit(1-enable;0-disable); low 4bit: config mask bit
{
	uint8_t work_mode = maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0);

	if (flag == 0xff)							// config
	{
		at32_button_init(DSP_SLOT_INT);		// 30ms
		at32_button_init(DSP_SLOT_MID);	// rcv spi frame notify from babeband
	}
	else if (flag == 0xf1)						// config
	{
		at32_button_init(DSP_SLOT_INT);		// 30ms
	}
	else if (flag == 0xf2)						// config
	{
		at32_button_init(DSP_SLOT_MID);	// rcv spi frame notify from babeband
	}
	else
	{
//		if (flag & 0x01)
//			set_button_interrupt(DSP_SLOT_MID, flag & 0x10);
		if (flag & 0x02)
			set_button_interrupt(DSP_SLOT_INT, flag & 0x20);
	}
}

void bt_ctrl_pin_config(void)
{
	at32_led_init(BT_POWER_CTRL_LINE);
	bt_power_onoff(1);						// power on
}

//extern STATIC_PARAMETERS_TYPEDEF *g_static_ptr;
void set_key_func_remap(void)
{
	uint8_t tmp, use_zzw_key = ((interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) && (dev_is_base() == 0)) ? 1 : 0;

//	printf("KEY: %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X",
//		g_static_ptr->key_remap_table.vol_independent, g_static_ptr->key_remap_table.func_left, g_static_ptr->key_remap_table.func_right,
//		g_static_ptr->key_remap_table.func_enter, g_static_ptr->key_remap_table.func_middle, g_static_ptr->key_remap_table.func_ptt,
//		g_static_ptr->key_remap_table.func_ptt2, g_static_ptr->key_remap_table.func_return, g_static_ptr->key_remap_table.func_shortcut, g_static_ptr->key_remap_table.func_alarm);
	tmp = maintain_setup_parameter(PARA_OPERATE_READ, KEY_FUNC_REMAP_LEFT, 0);
	KB_CODE_KEY_LEFT =   tmp ? (1 << tmp) : 0;
	tmp = maintain_setup_parameter(PARA_OPERATE_READ, KEY_FUNC_REMAP_RIGHT, 0);
	KB_CODE_KEY_RIGHT =  tmp ? (1 << tmp) : 0;
	tmp = maintain_setup_parameter(PARA_OPERATE_READ, KEY_FUNC_REMAP_ENTER, 0);
	KB_CODE_KNOB_ENTER = tmp ? (1 << tmp) : 0;
	tmp = maintain_setup_parameter(PARA_OPERATE_READ, use_zzw_key ? KEY_FUNC_REMAP_ZZW_PTT : KEY_FUNC_REMAP_PTT, 0);
	KB_CODE_KEY_PTT =    tmp ? (1 << tmp) : 0;
	tmp = maintain_setup_parameter(PARA_OPERATE_READ, use_zzw_key ? KEY_FUNC_REMAP_ZZW_USER1 : KEY_FUNC_REMAP_PTT2, 0);
	KB_CODE_KEY_USER1 =  tmp ? (1 << tmp) : 0;
	tmp = maintain_setup_parameter(PARA_OPERATE_READ, use_zzw_key ? KEY_FUNC_REMAP_ZZW_USER2 : KEY_FUNC_REMAP_RETURN, 0);
	KB_CODE_KEY_USER2 =  tmp ? (1 << tmp) : 0;
	tmp = maintain_setup_parameter(PARA_OPERATE_READ, KEY_FUNC_REMAP_SHORTCUT, 0);
	KB_CODE_KEY_USER3 =  tmp ? (1 << tmp) : 0;
	tmp = maintain_setup_parameter(PARA_OPERATE_READ, KEY_FUNC_REMAP_ALARM, 0);
	KB_CODE_KEY_ALARM =  tmp ? (1 << tmp) : 0;
//	printf("LEFT/RIGHT/ENTER/PTT/U1(PTT2:BULE)/U2(RETURN:ORANGE)/U3(SHORTCUT:SMALL)/ALARM:\r\n\t%08X %08X %08X %08X %08X %08X %08X %08X",
//		KB_CODE_KEY_LEFT, KB_CODE_KEY_RIGHT, KB_CODE_KNOB_ENTER, KB_CODE_KEY_PTT, KB_CODE_KEY_USER1, KB_CODE_KEY_USER2, KB_CODE_KEY_USER3, KB_CODE_KEY_ALARM);
}

uint16_t get_setup_ptt_event_with_ptt_key(void)
{
	if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
		return (KB_CODE_KEY_PTT & (1 << (KEY_CODE_USER1 - 1))) ? SYNC_CALL_STACK_PTT2_PRESSED : ((KB_CODE_KEY_PTT & (1 << (KEY_CODE_3PTT - 1))) ? SYNC_CALL_STACK_PTT3_PRESSED : SYNC_CALL_STACK_PTT_PRESSED);
	else
		return SYNC_CALL_STACK_PTT_PRESSED;
}

uint8_t zzwprobase_send_canframe_to_stack_immediately(void)
{
	return (p_timer3_handle == dsp_slot_middle_xv_check_ccm_data) ? 1 : 0;
}

void set_timer12_handler(uint32_t func)
{
	p_timer3_handle = (P_FUN_VOID_VOID)func;
}

uint8_t mpt_base_is_loopback(void)
{
	return (p_timer3_handle == null_void_void) ? 1 : 0;
}

uint8_t audio_pa_manual_config(uint8_t flag)
{
	if (flag == 1)
		keyboard_misc_status |= KEYBOARD_MANUAL_CTRL_PA;
	else if (flag == 0)
		keyboard_misc_status &= ~KEYBOARD_MANUAL_CTRL_PA;

	return keyboard_misc_status & KEYBOARD_MANUAL_CTRL_PA;
}

void audio_pa_ctrl_process(uint8_t flag)
{
	if (flag == 1)
	{
//		set_gpio_high(is_bt_insert() ? EARPHONE_PA_CTRL : AUDIO_PA_CTRL);
		aipl_pa_ctrl(0, 1);
	}
	else if (flag == 0)
	{
//		set_gpio_low(is_bt_insert() ? EARPHONE_PA_CTRL : AUDIO_PA_CTRL);
		aipl_pa_ctrl(0, 0);
	}
}

void kb_timer_init(void)
{
	uint8_t work_mode = get_real_esn_second_sector();

	keyboard_fsm_init(1);

	at32_led_init(LEDR);
	set_led_off(LEDR);
	at32_led_init(LEDG);
	set_led_off(LEDG);
	at32_led_init(GPS_POWER_CTRL_LINE);
	gps_power_onoff(0);
	at32_led_init(BT_POWER_CTRL_LINE);
	if (uart2_use_as_bt())
	{
		at32_button_init(BT_CONNECT_INDICATE);
		bt_power_onoff(maintain_setup_parameter(PARA_OPERATE_READ, BT_POWER_PARAS_POS, 0));
	}
	else
	{
		maintain_setup_parameter(PARA_OPERATE_WRITE, BT_POWER_PARAS_POS, 0);
		bt_power_onoff(0);
	}

	at32_led_init(SPI_FRAME_NOTIFY_LINE);
	set_gpio_low(SPI_FRAME_NOTIFY_LINE);

	at32_led_init(AUDIO_PA_CTRL);
//	set_gpio_high(AUDIO_PA_CTRL);		// test, high=pa on
	set_gpio_low(AUDIO_PA_CTRL);

	at32_led_init(EARPHONE_PA_CTRL);
//	set_gpio_high(EARPHONE_PA_CTRL);	// test, high=pa on
	set_gpio_low(EARPHONE_PA_CTRL);

	if (dev_is_base())
	{
		at32_led_init(P2_BASE_BKIO);
		set_gpio_low(P2_BASE_BKIO);		// pdt: 3s, set high when GPS's second can be divisible by 3(high 1s, low 2s); zzw: 9s, set high when GPS's (minute*60+second) can be divisible by 9
	}

	at32_button_init(PTT_BUTTON);
	at32_button_init(EAR_PHONE_DETECT);
	at32_button_init(ALARM_BUTTON);
	at32_button_init(KNOB_SURE);

	at32_button_init(DIR_CHK_BUTTON1);	// want connect BT?
	at32_button_init(DIR_CHK_BUTTON2);	// want connect BT?

	keyboard_misc_ctrl &= ~LR_KEY_REMAP_NOW;
	if (is_kb_knob_enter_disable() && (maintain_setup_parameter(PARA_OPERATE_READ, KEY_FUNC_REMAP_VOLUME_IND, 0) == 2))
	{
		keyboard_misc_ctrl |= LR_KEY_KNOB_REVERSE_FUNC;
		keyboard_misc_ctrl &= ~LR_KEY_REMAP_ENABLE;
	}

//	// 20230308:��վ����ʡ�磬����һֱУ������̨����ʡ���ᵼ��У�����󣬹�ֻ�ڿ�����һ�����Ǻ�У��һ��
//	timer2_init();
//	set_check38p4_state(0);

//	У�����壺PA0=TMR2_EXT(MUX1)-crystal��PA1=TMR2_CH2(MUX1)/PA15=TMR2_CH1(MUX1)-PPS������ʹ��CH2��trigger(PA0+PA1)����Ժ�PB0/PB1����
//		��	  PE2=TMR3_EXT(MUX2)-crystal��PE3=TMR3_CH1(MUX2)-PPS������PE2/PE3��PE7/PE8������PE3-8��KEY_ROW��

//	���PWM�� PD14=TMR4_CH3(MUX2)

	*((uint8_t *)&debounce_key) = (uint8_t)maintain_setup_parameter(PARA_OPERATE_READ, DEBOUNCE_KEY_PARA_POS, 0);
	set_key_func_remap();

	if (uart2_use_as_bt())
		p_config_bt_process = uart2_is_new_bt() ? config_real_new_bt_process : config_real_bt_process;

	for (work_mode = 0; work_mode < TIMER_NUMBER; work_mode++)
		timer_destroy(work_mode);

	work_mode = maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0);
	if (work_mode == INTERPHONE_MODE_MPT_TRUNKING)
	{
		timer9to14_initial(TIMER_MISC_INDEX, TIMER_MPT_LED_CCM_IRQ_PREEMPTION_PRIORITY, MPT_TRUNK_STACK_POLL_PERIOD, 0);// for call stack
		p_timer3_handle = mpt_poll_stack;
//		p_exti1_handle = null_void_void;
	}
	else if (work_mode == INTERPHONE_MODE_MPT_CONV)
	{
		timer9to14_initial(TIMER_MISC_INDEX, TIMER_MPT_LED_CCM_IRQ_PREEMPTION_PRIORITY, MPT_CONV_STACK_POLL_PERIOD, 0);	// for voice delay control
		p_timer3_handle = mpt_conv_voice_delay;
//		p_exti1_handle = null_void_void;
	}
	else
	{
//		timer9to14_initial(TIMER_MISC_INDEX, TIMER_LED_PLAYER_IRQ_PREEMPTION_PRIORITY, TIMESTAMP_10MS_PERIOD, 0);		// PDT NOT need(DSP output the check int)
//		p_timer3_handle = null_void_void;
//		p_exti1_handle = dsp_slot_middle_interrupt;
		timer9to14_initial(TIMER_MISC_INDEX, HPI_INT_IRQ_PREEMPTION_PRIORITY, PDT_INQUIRY_POLL_PERIOD, 0);				// P1 have not middle int
		p_timer3_handle = dsp_slot_middle_interrupt;
//		p_exti1_handle = null_void_void;
	}
	timer9to14_initial(TIMER_1S_INDEX, TIMER_1S_IRQ_PREEMPTION_PRIORITY, TIMESTAMP_POLL_PERIOD, 1);						// second timestamp
	timer9to14_initial(TIMER_1MS_INDEX, TIMER_1S_IRQ_PREEMPTION_PRIORITY, TIMESTAMP_MEASURE_PERIOD, 1);					// millisecond timestamp
	timer9to14_initial(TIMER_40MS_INDEX, TIMER_40MS_IRQ_PREEMPTION_PRIORITY, KEYBOARD_POLL_PERIOD, 1);					// keyboard scanning timer
	timer9to14_initial(TIMER_10MS_INDEX, TIMER_MEASURE_IRQ_PREEMPTION_PRIORITY, TIMESTAMP_10MS_PERIOD, 1);				// for UI & led player
}

uint8_t find_free_timer(void)
{
	uint8_t i;

	for (i = 0; i < TIMER_NUMBER; i++)
	{
		if (g_timer_define[i].id == 0xff)
			break;
	}
	return i;
}

#define TIMER_RESET_COUNTER(i)		g_timer_define[i].compval = timestamp_kb_scan + g_timer_define[i].interval
uint8_t timer_initial(uint8_t attr, uint32_t itime, void (*handler)(void))	// attr == 0: infinite; !=0: repeat attr times
{
	return 0xff;

/*	uint8_t i;

	i = find_free_timer();
	if (i < TIMER_NUMBER)
	{
		g_timer_define[i].id = i;
		g_timer_define[i].attr = attr;
		g_timer_define[i].interval = itime / KEYBOARD_POLL_PERIOD;
		TIMER_RESET_COUNTER(i);
		g_timer_define[i].timer_handler = handler;
		return g_timer_define[i].id;
	}
	else
	{
		return 0xff;
	}
*/
}

void timer_reset(uint8_t id)
{
	if (id < TIMER_NUMBER)
		TIMER_RESET_COUNTER(id);
 }

uint8_t timer_destroy(uint8_t id)
{
	if (id < TIMER_NUMBER)
		g_timer_define[id].id = 0xff;
	return 0xff;
}

void dgb_timer_output(uint8_t id, uint32_t itime, char *tip)
{
	char i;

	if (id == 0xff)
	{
		printf("[%s]TIM INIT:%d(ms)[]", tip, itime);
		for (i = 0; i < TIMER_NUMBER; i++)
			printf("%d.%d.%d ", g_timer_define[i].id, g_timer_define[i].attr, g_timer_define[i].interval);
	}
}

void process_timer(void)
{
	uint8_t i;

	for (i = 0; i < TIMER_NUMBER; i++)
	{
//		if (g_timer_define[i].id != 0xff)
		if (g_timer_define[i].id == i)
		{
			if (timestamp_kb_scan >= g_timer_define[i].compval)
			{
				TIMER_RESET_COUNTER(i);
				if (g_timer_define[i].attr)
				{
					if (g_timer_define[i].attr > 1)
						g_timer_define[i].attr--;
					else
						timer_destroy(g_timer_define[i].id);
				}
				g_timer_define[i].timer_handler();
			}
		}
	}
}


uint32_t matrix_keyboard_scanning(void)
{
	return 0;
}
