/**
  ******************************************************************************
  *                Copyright (c) 2015, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    21-April-2015
  * @brief   gpio config and software interface
  ******************************************************************************
  */

#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include "victel_digital_lcd.h"
#include "victel_digital_spi_flash.h"
#include "victel_digital_lcd.h"
#include "global_define.h"

//---------SPI flash command definition---------
// most commands are compatible by SST25VF010A-33-4C-SAE/SST25LF020A-33-4C-SAE/SST25VF020-20-4C-SAE
// except SPI_CMD_READ_HIGH_SPEED: could not apply on SST25VF020(it can be operated at 20MHz only)

#define SPI_FLASH_READID_SST0		0xBF
#define SPI_FLASH_READID_SST1		0x25
#define SPI_FLASH_READID_SST2		0x8E

#define SPI_FLASH_READID_MICRON0	0x20
#define SPI_FLASH_READID_MICRON1	0xBA
#define SPI_FLASH_READID_MICRON2	0x18

#define SPI_FLASH_READID_MXIC0		0xC2
#define SPI_FLASH_READID_MXIC1		0x20
#define SPI_FLASH_READID_MXIC2		0x18

#define SPI_FLASH_MAX_WRITE_ONCE_MICRON	256

#define	SPI_CMD_READ_ID				0x90
#define	SPI_CMD_READ_JEDEC_ID		0x9F
#define	SPI_CMD_READ_NORMAL			0x03
#define	SPI_CMD_READ_HIGH_SPEED		0x0B

#define	SPI_CMD_WRITE_ENABLE		0x06
#define	SPI_CMD_WRITE_DISABLE		0x04

#define	SPI_CMD_ERASE_SECTOR		0x20
#define	SPI_CMD_ERASE_BLOCK			0x52
#define	SPI_CMD_ERASE_LARGE_BLOCK	0xD8
#define	SPI_CMD_ERASE_CHIP			0x60

#define	SPI_CMD_WRITE_BYTE			0x02
#define	SPI_CMD_WRITE_AAI			0xAD

#define	SPI_CMD_READ_STATUS_REG		0x05
#define	SPI_CMD_READ_FLAG_MICRON	0x70
#define	SPI_CMD_WRITE_STATUS_EN		0x50
#define	SPI_CMD_WRITE_STATUS		0x01

#define	SST25LF020A_BUSY_FLAG		0x03
#define	SST25LF020A_AAI_BUSY_FLAG	0x43

#define SPI_CS_ENABLE()
#define SPI_CS_DISABLE()

extern RUNTIME_PARAMETERS_XVBASE_TYPEDEF *g_runtime_inst_xvbase;

static uint8_t SPIFlashStatus = 0;			// 0-fail

uint8_t get_spi_flash_status(void)	// 0: init spi flash ERROR; else: OK/Manufacturer ID
{
	return SPIFlashStatus;
}

uint8_t forbidden_to_change_spi_flash(uint32_t addr)
{
	return 0;
}

uint32_t fpga_spi_tr_word(uint8_t cmd, uint32_t data)
{
	uint32_t ret = 0;
	uint8_t  which_reg = cmd & (~FPGA_REG_OP_WRITE);

	if ((cmd & FPGA_REG_OP_WRITE) == 0)				// 1-write
	{
		if (which_reg  == FPGA_REG_GCR)
			ret = g_runtime_inst_xvbase->fpga_write_gcr;
		else if (which_reg  == FPGA_REG_TCR)
			ret = g_runtime_inst_xvbase->fpga_write_tcr;
	}

	return ret;
}

uint8_t flash_spi_tr_byte(/*spi_type *spi, */uint8_t byte)
{
	return 0;
}

uint32_t spi_unique_read(uint8_t cmd, uint8_t *buf, uint32_t addr, uint32_t len)
{
	return 0;
}

void spi_wait_busy(uint8_t flag)
{
}

uint32_t spi_unique_write(uint8_t cmd, uint8_t *buf, uint32_t addr, uint32_t len)
{
	return 0;
}
/*
void flash_spi_erase_chip(void)
{
	spi_unique_write(SPI_CMD_WRITE_ENABLE, 0, 0, 0);
	spi_unique_write(SPI_CMD_ERASE_CHIP, 0, 0, 0);
	spi_wait_busy(SST25LF020A_BUSY_FLAG);
}
*/

uint32_t get_dsp_2mode_adhoc_image_address(void)
{
	return (uint32_t)FLASH_SPI_ZZW_MOBILE_DSP_ADDRESS;
}

uint32_t get_dsp_64xx_image_address(void)
{
	return (uint32_t)((SPIFlashStatus == SPI_FLASH_READID_SST0) ? FLASH_SPI_ZZW_MOBILE_DSP_ADDRESS : FLASH_SPI_DSP_USED_ADDRESS);
}

uint32_t get_dsp_image_address(void)
{
	return (uint32_t)FLASH_SPI_PDT_MOBILE_DSP_ADDRESS;
}

uint32_t get_dsp_image_nvoc_address(void)
{
	return (uint32_t)FLASH_SPI_PDT_MOBILE_DSP_NVOC;
}

uint32_t get_power_on_logo_address(void)
{
	return (uint32_t)FLASH_SPI_LOGO_BMP_ADDRESS;
}

uint32_t get_qrcode_address(void)
{
	return get_power_on_logo_address() + 1030;	// 6B header+1024B data
}

uint32_t flash_spi_erase_data(uint32_t addr, uint32_t len)
{
	return 0;
}

uint32_t flash_spi_read_data(uint8_t *buf, uint32_t addr, uint32_t len)
{
	return 0;
}
/*
// pattern: ========mobile bootloader V3A28========
#define FLASHT_TYPE_TOTAL		4
#define FLASH_TYPE_SST_1MB		0xBF
#define FLASH_TYPE_MICRON_16MB	0x28
#define FLASH_TYPE_MICRON_32MB	0x29
#define FLASH_TYPE_MICRON_64MB	0x2A
char bootloader_check_spi_flash(void)
{
	char ret = 0xff, ver, *ptr, boot_string[] = "bootloader V", flash_type_string[3];
	unsigned char flash_type, flash_type_def[FLASHT_TYPE_TOTAL] = {FLASH_TYPE_SST_1MB, FLASH_TYPE_MICRON_16MB, FLASH_TYPE_MICRON_32MB, FLASH_TYPE_MICRON_64MB};

	if (strstr(recv_buf, boot_string) != NULL)
	{
		ptr = recv_buf + strlen(boot_string);
		ver = *ptr++;							// version
		if (*ptr++ == 'A')						// 'A':flash OK; 'I':flash invalid
		{
			flash_type_string[0] = *ptr++;
			flash_type_string[1] = *ptr++;
			flash_type_string[2] = 0;
			flash_type = itoa(flash_type_string);
			for (ret = 0; ret < FLASHT_TYPE_TOTAL; ret++)
			{
				if (flash_type == flash_type_def[ret])
					break;
			}
		}
	}

	if (ret == 0xff)
		printf("flash init error or do not search the pattern\n");
	else if (ret >= FLASHT_TYPE_TOTAL)
		printf("flash type not defined\n");

	return ret;
}
*/
uint32_t flash_spi_write_data256B(uint8_t *buf, uint32_t addr, uint32_t len)
{
	return 0;
}

uint32_t flash_spi_write_data(uint8_t *buf, uint32_t addr, uint32_t len)
{
	return 0;
}

uint32_t flash_spi_write_one_byte(uint8_t *buf, uint32_t addr)
{
	return 0;
}

/*
	read:   4096  - 5ms
	write:	4096  - 14ms
			8192  - 27ms
			16384 - 53ms
			32768 - 105ms
*/

uint8_t flash_spi_get_type(void)	// 0-sst 1MB; 1-micron 16MB; 2-mxic 16MB(compatible with micron)
{
	return (SPIFlashStatus == SPI_FLASH_READID_SST0) ? 0 : (((SPIFlashStatus & 0xF0) == (SPI_FLASH_READID_MICRON0 & 0xF0)) ? 1 : 2);
}

const uint8_t flash_type_string[3][16] = {"SST25VF080B", "MT25QL128", "MX25L12833FZN1"};
uint8_t flash_spi_init(void)
{
	SPIFlashStatus = 0;
	return SPIFlashStatus;
}


