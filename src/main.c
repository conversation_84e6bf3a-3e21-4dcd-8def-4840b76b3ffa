/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>

//============================> Libraries Headers <============================
#include "vlog.h"

//============================> Project Headers <============================
#include "stack_thread.h"

/******************************************************************************

                                Defines

 ******************************************************************************/

/******************************************************************************

                                Main Function

 ******************************************************************************/

int main(int argc, char **argv)
{
    (void)argc; /*Unused*/
    (void)argv; /*Unused*/

    // 初始化 EasyLogger，设置日志输出格式，并启动日志输出
    vlog_init();
    // vlog_set_fmt(ELOG_LVL_ASSERT, ELOG_FMT_ALL & ~ELOG_FMT_P_INFO);
    // vlog_set_fmt(ELOG_LVL_ERROR, ELOG_FMT_LVL | ELOG_FMT_TAG);
    // vlog_set_fmt(ELOG_LVL_WARN, ELOG_FMT_LVL | ELOG_FMT_TAG);
    // vlog_set_fmt(ELOG_LVL_INFO, ELOG_FMT_LVL | ELOG_FMT_TAG);
    // vlog_set_fmt(ELOG_LVL_DEBUG, ELOG_FMT_ALL & ~(ELOG_FMT_FUNC | ELOG_FMT_P_INFO));
    // vlog_set_fmt(ELOG_LVL_VERBOSE, ELOG_FMT_ALL & ~(ELOG_FMT_FUNC | ELOG_FMT_P_INFO));
    // vlog_start();

    // 初始化协议栈线程所需的相关资源
    if (stack_thread_init() != 0)
    {
        vlog_e("Main", "Stack thread init fail");
        return -1;
    }

	main_for_linux();

    while (1)
    {
    }

    return 0;
}
