#if !defined(__TIME_UTILS_H__)
#define __TIME_UTILS_H__

#include <stdbool.h>
#include <stdint.h>
#include <sys/time.h>
#include <time.h>


typedef struct
{
    struct timespec ts;
} tim_instant_t;

typedef struct
{
    struct timespec ts;
} tim_duration_t;

void tim_instant_now(tim_instant_t *ins);
void tim_instant_elapsed(const tim_instant_t *ins, tim_duration_t *d);
bool tim_instant_since(const tim_instant_t *a, const tim_instant_t *b, tim_duration_t *d);
void tim_instant_add(const tim_instant_t *a, const tim_duration_t *d, tim_instant_t *r);

void tim_duration_from_msecs(tim_duration_t *d, unsigned long long msecs);
void tim_duration_from_secs(tim_duration_t *d, time_t secs);
bool tim_duration_diff(const tim_duration_t *a, const tim_duration_t *b, tim_duration_t *r);
void tim_duration_sub(tim_duration_t *a, const tim_duration_t *b);
uint64_t tim_duration_to_usecs(const tim_duration_t *d);
uint64_t tim_duration_to_msecs(const tim_duration_t *d);
#endif // __TIME_UTILS_H__
