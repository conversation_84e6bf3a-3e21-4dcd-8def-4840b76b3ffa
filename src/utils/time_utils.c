#include "time_utils.h"

static bool s_timespec_diff(const struct timespec *a, const struct timespec *b, struct timespec *r);
static void s_timespec_add(const struct timespec *a, const struct timespec *b, struct timespec *r);

void tim_instant_now(tim_instant_t *ins)
{
    clock_gettime(CLOCK_MONOTONIC, &ins->ts);
}

void tim_instant_elapsed(const tim_instant_t *ins, tim_duration_t *d)
{
    tim_instant_t a;
    tim_instant_now(&a);
    tim_instant_since(&a, ins, d);
}

bool tim_instant_since(const tim_instant_t *a, const tim_instant_t *b, tim_duration_t *d)
{
    return s_timespec_diff(&a->ts, &b->ts, &d->ts);
}

void tim_instant_add(const tim_instant_t *a, const tim_duration_t *d, tim_instant_t *r)
{
    s_timespec_add(&a->ts, &d->ts, &r->ts);
}

void tim_duration_from_msecs(tim_duration_t *d, unsigned long long msecs)
{
    d->ts.tv_sec = msecs / 1000;
    d->ts.tv_nsec = (msecs % 1000) * 1000000;
}

void tim_duration_from_secs(tim_duration_t *d, time_t secs)
{
    d->ts.tv_sec = secs;
    d->ts.tv_nsec = 0;
}

bool tim_duration_diff(const tim_duration_t *a, const tim_duration_t *b, tim_duration_t *r)
{
    return s_timespec_diff(&a->ts, &b->ts, &r->ts);
}

void tim_duration_sub(tim_duration_t *a, const tim_duration_t *b)
{
    if (s_timespec_diff(&a->ts, &b->ts, &a->ts) == false)
    {
        a->ts.tv_sec = 0;
        a->ts.tv_nsec = 0;
    }
}

uint64_t tim_duration_to_usecs(const tim_duration_t *d)
{
    return d->ts.tv_sec * 1000000 + d->ts.tv_nsec / 1000;
}

uint64_t tim_duration_to_msecs(const tim_duration_t *d)
{
    return d->ts.tv_sec * 1000 + d->ts.tv_nsec / 1000000;
}

static bool s_timespec_diff(const struct timespec *a, const struct timespec *b, struct timespec *r)
{
    time_t sec = a->tv_sec - b->tv_sec;

    if (sec >= 0)
    {
        long ns = a->tv_nsec - b->tv_nsec;
        if (ns < 0)
        {
            if (sec == 0)
            {
                return false;
            }
            r->tv_sec = sec - 1;
            r->tv_nsec = 1000000000 + ns;
        }
        else
        {
            r->tv_sec = sec;
            r->tv_nsec = ns;
        }
        return true;
    }
    return false;
}

static void s_timespec_add(const struct timespec *a, const struct timespec *b, struct timespec *r)
{
    time_t sec = a->tv_sec + b->tv_sec;
    long ns = a->tv_nsec + b->tv_nsec;
    if (ns > 1000000000)
    {
        ns -= 1000000000;
        sec += 1;
    }
    r->tv_sec = sec;
    r->tv_nsec = ns;
}
