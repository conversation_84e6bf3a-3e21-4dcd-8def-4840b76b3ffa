/******************************************************************************
 * DVSI CONFIDENTIAL PROPRIETARY
 ******************************************************************************
 * DVSI AMBE+2 Voice Compression Software
 * (c) Copyright, Digital Voice Systems Inc., 2001-2013
 * All Rights Reserved
 *
 * DVSI claims certain rights, including patent rights under
 * U.S. Patents #6,199,037 B1, #6,161,089, #5,826,222, #5,754,974,
 * #5,715,365, #5,701,390, #5,649,050, #5,630,011, #5,247,579,
 * and #5,226,084, and under other U.S. and foreign patents and
 * patents pending, in the AMBE^(TM) speech coding technology
 * embodied in this software.  Any use of this software or
 * technology requires a separate written license from DVSI.
 *
 * AMBE is a registered trademark of Digital Voice Systems, Inc.
 * AMBE+ and AMBE+2 are trademarks of Digital Voice Systems, Inc.
 ******************************************************************************
 *****************************************************************************/
#ifndef __APCOP2_H__
#define __APCOP2_H__

// Note that "int" type on C6x is 32 bits

				// DSPs with 16 bit data pointers need
				// fewer state words than those with 32
				// bit data pointers. DVSI assumes 32 bit
				// always for consistency

#define ENCSTATE_T_SZ    2930
#define DECSTATE_T_SZ    1020	
			
#ifdef __DVSI_USE_INTERNAL_PROTOTYPES__
typedef ENCSTATE    ENCSTATE_T;
typedef DECSTATE    DECSTATE_T;
#else
typedef struct encstate_t {
	int dummy[ENCSTATE_T_SZ/2];
} ENCSTATE_T;


typedef struct decstate_t {
	int dummy[DECSTATE_T_SZ/2];
} DECSTATE_T;

#endif



/*
 * valid range for source bits, number of samples
 */
#define APCO_HR_SRCBITS_MAX 49
#define APCO_HR_SRCBITS_NOM APCO_HR_SRCBITS_MAX
#define APCO_HR_SRCBITS_MIN 45

#define APCO_SRCBITS        88

/*
 * 10 ms subframe window shift
 */
#define AMBE_WS_MAX         81
#define AMBE_WS_NOM         80
#define AMBE_WS_MIN         79

/*
 * 20 ms frame window shift
 */
#define AMBE_WSHIFT_MAX    (2*AMBE_WS_MAX)
#define AMBE_WSHIFT_NOM    (2*AMBE_WS_NOM)
#define AMBE_WSHIFT_MIN    (2*AMBE_WS_MIN)

/*
 * valid range for encoder noise suppressor gain
 */
#define APCO_NS_GAIN_MIN       0 /* 0.0 */
#define APCO_NS_GAIN_NOM    8192 /* 0.5 */
#define APCO_NS_GAIN_MAX   16384 /* 1.0 */


/*
 * Default AGC settings
 */

#define AGC_SET_DBM0_LEVEL		-22
#define AGC_SET_DBM0_OFFSET		  4
#define AGC_MIN_DBM0_LEVEL		-40
#define AGC_MAX_DB_GAIN			 20
#define AGC_MIN_DB_GAIN			-10
#define AGC_ALPHA0		      16384  
#define AGC_ALPHA1		       3276 
#define AGC_ALPHA2			819  
#define AGC_ALPHA3			163 
#define AGC_DBM0_DELTA			 10
#define AGC_DBM0_SNR_THRESH		 25	


/*
 * Flags for cmode
 */
#define AMBE_DTMF_FRAME_FLAG		(1 << 15)	// 1=encoder detected or decoder received dtmf (output)
#define AMBE_DTMF_SEND_FLAG		(1 << 14)	// 1=encoder transmits or decoder synthesizes tone (input)
#define AMBE_AGC_ON_FLAG		(1 << 13)	// 1=encoder uses AGC (input)
#define AMBE_TONE_DET_ENABLE_FLAG	(1 << 12)	// 1=enables tone detection in encoder (input)
#define AMBE_DTX_ENABLE_FLAG		(1 << 11)	// 1=signals the encoder that DTX is in effect (input)
#define AMBE_VAD_FLAG	                (1 << 10)	// 1=encoder detected voice in frame (output)
#define AMBE_FR_TONE_COMP_FLAG          (1 <<  9)	// 1=encoder detected 1000Hz tone encoded as 1010Hz rather than 990Hz
#define AMBE_RESERVE08_FLAG		(1 <<  8)
#define AMBE_HPITCH_DET_ENABLE_FLAG 	(1 <<  7)	// 1=enables high pitch detection feature (if present in library)
#define AMBE_NS_ENABLE_FLAG		(1 <<  6)	// 1=enables noise suppression in encoder (input)
#define AMBE_DATA_INVALID_FLAG	        (1 <<  5)	// 1=decoder detected invalid frame or forced frame repeat (output)
#define AMBE_ERASURE_FRAME_FLAG	        (1 <<  4)	// 1=FEC decoder detected erasure frame (output)
#define AMBE_CNI_FRAME_FLAG		(1 <<  3)	// 1=forces decoder to insert comfort noise (input)
#define AMBE_LOST_FRAME_FLAG		(1 <<  2)	// 1=forces decoder to generate frame repeat (input)
#define AMBE_VOICE_ACTIVE_FLAG	        (1 <<  1)	// 1=encoder detected or decoder synthesized voice or tone frame (output)
#define AMBE_RESERVED00_FLAG		(1 <<  0)

/*
 * single, DTMF, and call progress tone codes
 */
#define AMBE_SINGLE_TONE_MIN     5
#define AMBE_SINGLE_TONE_MAX   122

#define AMBE_DTMF_0            128
#define AMBE_DTMF_1            129
#define AMBE_DTMF_2            130
#define AMBE_DTMF_3            131
#define AMBE_DTMF_4            132
#define AMBE_DTMF_5            133
#define AMBE_DTMF_6            134
#define AMBE_DTMF_7            135
#define AMBE_DTMF_8            136
#define AMBE_DTMF_9            137
#define AMBE_DTMF_A            138
#define AMBE_DTMF_B            139
#define AMBE_DTMF_C            140
#define AMBE_DTMF_D            141
#define AMBE_DTMF_STAR         142
#define AMBE_DTMF_POUND        143

#define AMBE_KNOX_0            144
#define AMBE_KNOX_1            145
#define AMBE_KNOX_2            146
#define AMBE_KNOX_3            147
#define AMBE_KNOX_4            148
#define AMBE_KNOX_5            149
#define AMBE_KNOX_6            150
#define AMBE_KNOX_7            151
#define AMBE_KNOX_8            152
#define AMBE_KNOX_9            153
#define AMBE_KNOX_A            154
#define AMBE_KNOX_B            155
#define AMBE_KNOX_C            156
#define AMBE_KNOX_D            157
#define AMBE_KNOX_STAR         158
#define AMBE_KNOX_POUND        159

#define AMBE_DIAL_TONE         160
#define AMBE_RING_TONE         161
#define AMBE_BUSY_TONE         162
#define AMBE_CPX_TONE          163

#define AMBE_INVALID_TONE      255

typedef struct tone_info_t {
	short idx;  /* negative if no tone */
	short amp;  /* dBm0 (integer)      */
} TONE_INFO;

typedef struct tone_info_t TONE_DET_INFO;
typedef struct tone_info_t TONE_RCV_INFO;

/*
 * FEC interface and definitions
 */
#include "apcop2fec.h"

/*
 * interface functions 
 */
#ifdef __cplusplus
extern "C"
#endif
   void
   ambe_init_enc( ENCSTATE_T  *es,
		  short      mode,
		  short full_init );

#ifdef __cplusplus
extern "C"
#endif
   void
   ambe_init_agc( ENCSTATE_T  *es,
		  short set_level,
		  short max_gain,
		  short min_gain );

#ifdef __cplusplus
extern "C"
#endif
   void
   ambe_init_agc_dB( ENCSTATE_T  *es,
		     short set_dbm0_level,
		     short max_db_gain,
		     short min_db_gain );

#ifdef __cplusplus
extern "C"
#endif
   void
   ambe_set_agc_info( ENCSTATE_T *es,
		      short set_dbm0_level,
		      short max_db_gain,
		      short min_db_gain,
		      short min_dbm0_level,
		      short alpha0,
		      short alpha1,
		      short alpha2,
		      short alpha3,
		      short dbm0_delta,
		      short snr_dbm0_thresh);

#ifdef __cplusplus
extern "C"
#endif
   void
   ambe_set_enc_mode( ENCSTATE_T *es,
		      short     mode );

#ifdef __cplusplus
extern "C"
#endif
   short
   ambe_tone_xmt( ENCSTATE_T *es,
		  short      idx,
		  short      lvl );

#ifdef __cplusplus
extern "C"
#endif
   void
   ambe_set_vad_update( ENCSTATE_T *es,
			short      update );

#ifdef __cplusplus
extern "C"
#endif
   short
   ambe_voice_enc( short          *buf,
		   short     bit_steal,
		   short         *sbuf,
		   short		 ws,
		   short	      cmode,
		   short      subframe,
		   short       ns_gain,
		   ENCSTATE_T      *es );

#ifdef __cplusplus
extern "C"
#endif
   short
   ambe_get_tone_det_info( TONE_DET_INFO     *t,
			   const ENCSTATE_T *es );

#ifdef __cplusplus
extern "C"
#endif
   short
   ambe_get_enc_mode( ENCSTATE_T *es );

#ifdef __cplusplus
extern "C"
#endif
   short
   ambe_get_enc_sourcebits( const ENCSTATE_T *es );





#ifdef __cplusplus
extern "C"
#endif
   void
   ambe_init_dec( DECSTATE_T *ds,
		  short     mode );

#ifdef __cplusplus
extern "C"
#endif
   void
   ambe_set_dec_mode( DECSTATE_T *ds,
		      short     mode );

#ifdef __cplusplus
extern "C"
#endif
   short
   ambe_tone_gen( DECSTATE_T *ds,
		  short      idx,
		  short      lvl );

#ifdef __cplusplus
extern "C"
#endif
   void
   ambe_set_fecinfo( DECSTATE_T       *ds,
		     const FECINFO *finfo );

#ifdef __cplusplus
extern "C"
#endif
   short
   ambe_voice_dec( short         *sbuf,
		   short            ws,
		   short          *buf,
		   short     bit_steal,
		   short         cmode,
		   short      subframe,
		   DECSTATE_T      *ds );

#ifdef __cplusplus
extern "C"
#endif
   short
   ambe_get_tone_rcv_info( TONE_RCV_INFO     *t,
			   const DECSTATE_T *ds );

#ifdef __cplusplus
extern "C"
#endif
   short
   ambe_get_dec_mode( DECSTATE_T *ds );

#ifdef __cplusplus
extern "C"
#endif
   short
   ambe_get_dec_sourcebits( const DECSTATE_T *ds );



#endif /* _APCOP2_H_ */
