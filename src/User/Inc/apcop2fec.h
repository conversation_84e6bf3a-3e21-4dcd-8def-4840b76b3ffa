/******************************************************************************
 * DVSI CONFIDENTIAL PROPRIETARY
 ******************************************************************************
 * DVSI AMBE+2 Vocoder
 * (c) Copyright, Digital Voice Systems Inc., 2001-2013
 * All Rights Reserved
 *
 * DVSI claims certain rights, including patent rights under
 * U.S. Patents #6,199,037 B1, #6,161,089, #5,870,045, #5,826,222,
 * #5,754,974, #5,715,365, #5,701,390, #5,649,050, #5,630,011,
 * #5,247,579, and #5,226,084, and under other U.S. and foreign
 * patents and patents pending, in the AMBE^(TM) speech coding
 * technology embodied in this software.  Any use of this software
 * or technology requires a separate written license from DVSI.
 * 	
 * AMBE is a registered trademark of Digital Voice Systems, Inc.
 * AMBE+ and AMBE+2 are trademarks of Digital Voice Systems, Inc.
 ******************************************************************************
 *****************************************************************************/
#ifndef __APCOP2FEC_H__
#define __APCOP2FEC_H__

// Note that "int" type on C6x is 32 bits

#define FECSTATE_T_SZ   34

#ifdef __DVSI_USE_INTERNAL_PROTOTYPES__
typedef FECSTATE FECSTATE_T;
#else
typedef struct fecstate_t {
	int dummy[FECSTATE_T_SZ/2];
} FECSTATE_T;
#endif

/*
 * vocoder modes (bit rates)
 */
#define APCO_MODE    0   /* 7200 bps */
#define APCO_HR_MODE 1   /* 3600 bps */

/*
 * interface functions, data structures
 */
typedef struct fecinfo_t {
	int        sd_dist[7];
	int    ttl_sd_dist;
	short      err_rate;
	short        cwerrs[7];
	short      ttl_errs;
	short     fec_cmode;
} FECINFO;

/*
 * FEC definitios for the half-rate coder
 */
#define APCO_HR_FECBITS        23
#define APCO_HR_TOTALBITS_MAX (APCO_HR_SRCBITS_MAX + APCO_HR_FECBITS)
#define APCO_HR_TOTALBITS_MIN (APCO_HR_SRCBITS_MIN + APCO_HR_FECBITS)
#define APCO_HR_TOTALBITS_NOM (APCO_HR_SRCBITS_NOM + APCO_HR_FECBITS)

/*
 * indexes of bits to steal after interleave
 */
#define APCO_HR_BITSTEAL_I0    21
#define APCO_HR_BITSTEAL_I1    42
#define APCO_HR_BITSTEAL_I2    59
#define APCO_HR_BITSTEAL_I3    63
#define APCO_HR_BITSTEAL_I4    67
#define APCO_HR_BITSTEAL_I5    71

/*
 * FEC definitios for the full-rate coder
 */
#define APCO_FECBITS           56
#define APCO_TOTALBITS        (APCO_SRCBITS + APCO_FECBITS)

#ifdef __cplusplus
extern "C"
#endif

short ambe_fec_enc( short      *chanbuf,  const short *srcbuf,
	      short     bit_steal,
	      FECSTATE_T     *fec );

#ifdef __cplusplus
extern "C"
#endif
short
ambe_fec_dec( short        *srcbuf,
	      short      bit_steal,
	      const short *chanbuf,
	      short        sd_bits,
	      short          cmode,
	      FECSTATE_T      *fec );

#ifdef __cplusplus
extern "C"
#endif
short
ambe_get_fecinfo( FECINFO          *finfo,
		  const FECSTATE_T   *fec );

#ifdef __cplusplus
extern "C"
#endif
short
ambe_init_fec( FECSTATE_T *fec,
	       short      mode );

#ifdef __cplusplus
extern "C"
#endif
short
ambe_set_fec_mode( FECSTATE_T *fec,
		   short      mode );

#ifdef __cplusplus
extern "C"
#endif
short
ambe_get_fec_mode( FECSTATE_T *fec );

#ifdef __cplusplus
extern "C"
#endif
short
ambe_get_erasure_frame(short *buf,
			  short no_fec,
			  short mode );
#endif /* _APCOP2FEC_H_ */
