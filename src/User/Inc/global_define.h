/**
  ******************************************************************************
  *                Copyright (c) 2011, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    18-November-2012
  * @brief   This file provides
  *            - system global define
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __SYSTEM_DEFINE_H__
#define __SYSTEM_DEFINE_H__

#include <stdint.h>
#include "victel_digital_hal.h"
#include "gui_common.h"
#include "gui_object.h"

#ifdef __cplusplus
 extern "C" {
#endif

/*************************************************************************************************
	check: NVIC_IRQChannelPreemptionPriority &&
	STM_EVAL_PBInit		&&
	timer234567_init	&&
	uart_rxdma_init		&&
	uart_txdma_init

	0

	1	CAN_RX_IRQ_PREEMPTION_PRIORITY
		SDIO_IRQ_PREEMPTION_PRIORITY
		SDIO_DMA_IRQ_PREEMPTION_PRIORITY

	2	CAN_TX_IRQ_PREEMPTION_PRIORITY
		UART2_RXDMA_IRQ_PREEMPTION_PRIORITY(bt)
		UART234_TXDMA_IRQ_PREEMPTION_PRIORITY
		HPI_INT_IRQ_PREEMPTION_PRIORITY
		DSP_GPIO_IRQ_PREEMPTION_PRIORITY
		TIMER_MPT_LED_CCM_IRQ_PREEMPTION_PRIORITY

	3

	4	UART_IRQ_PREEMPTION_PRIORITY
		UART4_RXDMA_IRQ_PREEMPTION_PRIORITY(gps)
		TIMER_MEASURE_IRQ_PREEMPTION_PRIORITY

	5	TIMER_40MS_IRQ_PREEMPTION_PRIORITY
		sEE_I2C_DMA_PREPRIO
		FPGA_SPI_IRQ_PREEMPTION_PRIORITY

	6	LCD_PARALLEL_DMA_IRQ_PREEMPTION_PRIORITY
		EVAL_AUDIO_IRQ_PREPRIO(lcd_spi_dma_int)
		TIMER_1S_IRQ_PREEMPTION_PRIORITY

	7	RTC_IRQ_PREEMPTION_PRIORITY
		PUSH_BUTTON_IRQ_PREEMPTION_PRIORITY
		TIMER_LED_PLAYER_IRQ_PREEMPTION_PRIORITY
**************************************************************************************************/

#define CAN_RX_IRQ_PREEMPTION_PRIORITY				2
#define CAN_RX_IRQ_SUB_PRIORITY						0

#ifdef USE_DMA_TO_TRANSMIT_CCR2
  #define ADJ_38P4_CRYSTAL_IRQ_PREEMPTION_PRIORITY	11
  #define ADJ_38P4_CRYSTAL_IRQ_SUB_PRIORITY			0
#else
  #define ADJ_38P4_CRYSTAL_IRQ_PREEMPTION_PRIORITY	3
  #define ADJ_38P4_CRYSTAL_IRQ_SUB_PRIORITY			0
#endif

#define SLOT_ADJUST_IRQ_PREEMPTION_PRIORITY			3/*all button's sub priority is 0*/

#define CAN_TX_IRQ_PREEMPTION_PRIORITY				4
#define CAN_TX_IRQ_SUB_PRIORITY						0

#define SDIO_IRQ_PREEMPTION_PRIORITY				4
#define SDIO_IRQ_SUB_PRIORITY						0

#define SDIO_DMA_IRQ_PREEMPTION_PRIORITY			4
#define SDIO_DMA_IRQ_SUB_PRIORITY					0

#define HPI_INT_IRQ_PREEMPTION_PRIORITY				6/*all button's sub priority is 0*/

#define DSP_GPIO_IRQ_PREEMPTION_PRIORITY			HPI_INT_IRQ_PREEMPTION_PRIORITY/*all button's sub priority is 0*/

#define TIMER_MPT_LED_CCM_IRQ_PREEMPTION_PRIORITY	HPI_INT_IRQ_PREEMPTION_PRIORITY/*all timer's sub priority is 0*/

#define UART_DEBUG_IRQ_PREEMPTION_PRIOR				7
#define UART_DEBUG_IRQ_SUB_PRIOR					0

#define UART2_RXDMA_IRQ_PREEMPTION_PRIORITY			7/*uart2-bt,uart3-debug,uart4-gps*/
#define UART2_RXDMA_IRQ_SUB_PRIORITY				0

#define UART234_TXDMA_IRQ_PREEMPTION_PRIORITY		8
#define UART234_TXDMA_IRQ_SUB_PRIORITY				0

#define UART4_RXDMA_IRQ_PREEMPTION_PRIORITY			10/*uart2-bt,uart3-debug,uart4-gps*/
#define UART4_RXDMA_IRQ_SUB_PRIORITY				0

#define sEE_I2C_DMA_PREPRIO              			11
#define sEE_I2C_DMA_SUBPRIO              			0

#define EVAL_AUDIO_IRQ_PREPRIO						13/* Select the preemption priority level(0 is the highest) */
#define EVAL_AUDIO_IRQ_SUBRIO						0/* Select the sub-priority level (0 is the highest) */

#define LCD_PARALLEL_DMA_IRQ_PREEMPTION_PRIORITY	13
#define LCD_PARALLEL_DMA_IRQ_SUB_PRIORITY			0

#define FPGA_SPI_IRQ_PREEMPTION_PRIORITY			14/*all button's sub priority is 0*/
#define TIMER_40MS_IRQ_PREEMPTION_PRIORITY			14/*all timer's sub priority is 0*/

#define TIMER_LED_PLAYER_IRQ_PREEMPTION_PRIORITY	15/*all timer's sub priority is 0*/
#define TIMER_1S_IRQ_PREEMPTION_PRIORITY			15/*all timer's sub priority is 0*/

#define TIMER_MEASURE_IRQ_PREEMPTION_PRIORITY		15/*all timer's sub priority is 0; NOT enable and not use it*/

#define RTC_IRQ_PREEMPTION_PRIORITY					15
#define RTC_IRQ_SUB_PRIORITY						0

#define PUSH_BUTTON_IRQ_PREEMPTION_PRIORITY			15/*all button's sub priority is 0*/



#define IS_RETURN_KEYCODE(code)				((code == KEY_CODE_VIRT_RETURN) || (code == KEY_CODE_KNOB_ENTER) || ((code >= KEY_CODE_0) && (code <= KEY_CODE_WELL)))
#define IS_RETURN_KEYCODE_NO_ENTER(code)	((code == KEY_CODE_VIRT_RETURN) || ((code >= KEY_CODE_0) && (code <= KEY_CODE_WELL)))
#define IS_RETURN_KEYCODE_NO_NUM(code)		((code == KEY_CODE_VIRT_RETURN) || (code == KEY_CODE_KNOB_ENTER))


extern uint16_t front_ground_color, back_ground_color;
extern uint8_t g_static_inst[];
extern uint8_t g_user_book_inst[];

//#define NVCRC_FAIL_IGNORE_NEXT_FRAME	1
//#define CHEAT_STACK_WHEN_CRC_FAIL		1
  #ifdef CHEAT_STACK_WHEN_CRC_FAIL
	#define CORRECT_ALL_Q_CRC_FAIL_AS_VOICE 1
  #endif

//#define USE_INT_TO_RECEIVE_GPS			1
#define AUTOSET_GPS_BAUDRATE0			9600
#define AUTOSET_GPS_BAUDRATE1			38400
#define AUTOSET_GPS_BAUDRATE2			115200
  #define SET_GPS_DEFAULT_BAUDRATE		AUTOSET_GPS_BAUDRATE1
  #define SET_GPS_DEFAULT_BAUDRATE_IDX	1/*must correspond to the SET_GPS_DEFAULT_BAUDRATE defined*/
//#define AUTOSET_GPS_SET_TO_DEFAULT		1/*set to SET_GPS_DEFAULT_BAUDRATE; else-set to AUTOSET_GPS_BAUDRATE1 if at AUTOSET_GPS_BAUDRATE0*/

//#define TEST_NOISE_WITH_LCD_SPI_STOP	1
//#define TEST_LCD_FUNCTION				1
//#define TEST_TFT1P1864_C64_COLOR_BLOCK	1
//#define ANALOG_MODE_DISABLE			1
//#define DEBUG_DATA_FLOW_SIMPLE_INFO	1
//#define AIR_DATA_DEBUG_OUTPUT			1
#define POWER_SAVE_ENABLE_TIP_SOUND		1
//#define VAD_KEY_USE_AS_STICK_PTT		1
//#define VAD_CONTROL_BY_DSP			1
//#define PTT_KEY_USE_AS_STICK			1
#define FORBIDDEN_SWITCH_Q_SPEED		1
#define STACK_USE_NEW_SIGNAL_FORMAT		1
//#define NV_AUTO_TRACE_VOICE_AT_QMODE	1
#define FORWARDING_LOGIC_ENABLE			1
#define FORWARDING_LOGIC_NO_GPS			1
  #ifdef FORWARDING_LOGIC_ENABLE
//    #define DEBUG_FORWARDING_LOGIC		1
  #endif
#define MAIN_DONOT_PROCESS_VO_MODE		1
#define USE_NEW_TIP_MODE_220X176		1
#define ZZWPRO_SIG_BAR_SAME_AS_TRUNK	1
#define BE_CALLED_NAME_USE_WHAT			1/*1-user name,2-watch name,3-watch id*/
#define SHOW_SOLT_DETAIL_WITH_VOICE		1
//#define DISPLAY_SYNC_AT_FIRST_POS		1
#define NVQ_UNIFY_JUMP_FREQ				1/*20220514:ͳһʹ��R2.H2B����Ƶ����Ƶ����*/
//#define EMB_3B_WHEN_NOT_SELECT_CID		1

#define PDT_CONV_VOICE_AT_QMODE			1
#ifdef  PDT_CONV_VOICE_AT_QMODE
  #define NVQ_QMODE_SLOT_IS_30MS		1
#endif
#ifdef NVQ_QMODE_SLOT_IS_30MS
  #define NVQ_SENDING_SWITCH_TX_ONLY	1
#endif

//#define KNOB_SWITCH_GRP_WITHOUT_CONFIRM	1
//#define DEBUG_FAN_CTRL_LOGIC			1
//#define FAN_CTRL_LOGIC_6LEVEL			1

#define AES_CRYPTO_VOICE_DIFF_DATA	  	1
#define AES_CRYPTO_ENC_DEC_MASK			0x8000
  #define AES_CRYPTO_ENCRYPT_FLAG		0x0000
  #define AES_CRYPTO_DECRYPT_FLAG		AES_CRYPTO_ENC_DEC_MASK
#define AES_CRYPTO_VOC_DAT_MASK			0x4000
  #define AES_CRYPTO_VOICE_FLAG			0x0000
  #define AES_CRYPTO_DATA_FLAG			AES_CRYPTO_VOC_DAT_MASK
#define AES_CRYPTO_DATA_LEN_MASK		(~(AES_CRYPTO_ENC_DEC_MASK | AES_CRYPTO_VOC_DAT_MASK))

#ifdef FH_SPECIAL_EDITION
  #define GET_ENCRYPT_TYPE()			ENCRYPT_TYPE_FENGHUO
  #define RF_POWER_LEVEL_FH833			3
#else
  #define GET_ENCRYPT_TYPE()			g_static_ptr->misc_static_config.encrypt_type
#endif

//#define UPDATE_FIXED_GPS_WHEN_LOCKED	1
//#define PDT_TRUNK_REVERSED_SLOT_FLAG	1

//#define BASE_ENABLE_VOCODER_AUTO_TRACK	1
//#define TIMER5_USE_AS_SEPARATED		1
//#define DEBUG_AES256_CRYPTO			1
//#define DEBUG_DSP_SAVE_POWER			1
//#define DEBUG_DSP_SLOT_RSSI			1
//#define DEBUG_INSERT_RECOVERY_30MS	1
//#define DEBUG_DSP_WRITE_REG			2/*>1:print hardware_mode&digital_mode&speech_mode&reg_analog_in_gain*/
//#define DEBUG_DSP_MODE					1
//#define DEBUG_NOCCM_MODE				1/*1��ǿ����CCM�Ҳ���ӡ������Ϣ��2��ǿ�Ʋ���ӡ*/
#if DEBUG_NOCCM_MODE > 1
  #define DEBUG_DSP_TRANSCODE			1
#endif
//#define DEBUG_NVOC_VOCODER				1
//#define DEBUG_DSP_TRANSCODE_VOCODER		1
//#define DEBUG_GPS_SYNC_PPS			1
//#define DEBUG_GPS_SAVE_POWER			1
//#define DEBUG_VOICE_SEQUENCE			1
//#define DEBUG_NV25K_TRACE_INFO		1
//#define DEBUG_NVQ_PRINT_INFO			1
//#define KEYBOARD_VAL32_PRINT_DEBUG	1
//#define USE_MAIN_EMBEDDED_Q_MODE		1
//#define DEBUG_ENC_DEC_FUNCTION		3
//#define NV_TRACE_MULTI_HEARTBEAT		2
//#define CHECK_PPS_WITH_CRYSTAL_38P4M	1
//#define DEBUG_REAL_BT					1
//#define DEBUG_GPS_WINDOW				1
//#define DEBUG_MINISTERIAL_CRYPTO		1
//#define DEBUG_AUTO_DETECT_PPS			1
//#define DEBUG_RCV_RSSI_IS_255			1
//#define DEBUG_DSP_INT_OFFSET_OF_SLOT	1
//#define DEBUG_SPI_TX_STRESS_TEST		1
#ifdef DEBUG_DSP_INT_OFFSET_OF_SLOT
  #define GET_SLOT_TO_CURR_TIME()		get_measure_timer_difference(get_timestamp_measure(), dsp_slot_start_1ms)
  #define DEBUG_RX_SEQ_TIME				1
  #define DEBUG_TX_SEQ_TIME				1
#elif defined DEBUG_SPI_TX_STRESS_TEST
  #define GET_SLOT_TO_CURR_TIME()		get_measure_timer_difference(get_timestamp_measure(), dsp_slot_start_1ms)
#endif
//#define DEBUG_MPT_BASE					1/*1(if define)-force mpt mode; 2-print data; 3-print rdata; 4-print timer; 5-print data&rdata; 6-print data&rdata&timer*/
										 /*7-print can pcm; 8-print rdata&can pcm*/
//#define DEBUG_INQUIRE_AFTER_23MS		1
//#define DEBUG_RX_DMA_ENABLE_IDLE		1
//#define DEBUG_REAL_NEW_BT_FUNCTION	1
//#define DEBUG_MCU_INF_TX_DMA			1


//#define TEST_VOCODER_PERFORMANCE		1
//#define DEBUG_MCU_INF_SPI				1

//#define READ_CODEWORD_DELAY_BY_LC_NUM	1
#define DEBUG_PDT_CONV_ONLY			1
//#define TEST_SD_CARD_SPEED			1
//#define	DEBUG_UI_TIMESTAMPS			1
//#define	DEBUG_UI_DMA_TIMESTAMPS		1


#define USE_DMA_TO_TRANSMIT_UART3		1
#define USE_DMA_TO_RECEIVE_UART4		1
  #ifdef USE_DMA_TO_RECEIVE_UART4
//	#define UART4_FOR_BT_FUNCTION		1
  #endif
//#define USE_DMA_TO_TR_USART2			1	/*NOT use now*/
#define USE_DMA_TO_TR_UART5				1

//#define TEST_CUSTOM_DELAY				1
#ifndef TEST_CUSTOM_DELAY
//	#define SET_WATCHDOG_ENABLE			1
#endif
#ifdef SET_WATCHDOG_ENABLE
//  #define TEST_WATCHDOG_FUNC			1
#endif

// 20210108: ��207ƽ̨����̨������Ѿ�����384KB
// EMWIN��ʽ��ͼƬ��ʽΪGBR444����Ҫ����ת��ΪRGB565����������ʾ���һ���Ҫ��ͷ������8�ֽڵ�ͼƬͷ
// Image2LCDת����ʹ��LCD_DrawColorScreen��ʾ��ˮƽɨ��/16λ���ɫ/����ͼ��ͷ����/�����Ⱥ͸߶���220/176����Ϊ�������ļ�
//  #define SPECIAL_VERSION_DEFINE		1/* modify dsp para*/
//  #define PROJECT_VERSION_DEFINE		1

#define SUPPORT_EXTEND_FUNCTION			1

//  #define CAN_STRESS_TESTING			1
#ifdef CAN_STRESS_TESTING
  #define CAN_8B_FRAME_MAX_STATISTIC	4
#endif

//  #define NO_LCD_PRINT_KEY			1

#define DSP_NEW_FREQ_INTERFACE			1

//  #define MEDIA_SUPPORT_CANBUS		1

// #define RF_DEUBG_ENABLE				1

// #define CUSTOM_SHORT_MESSAGE			1

// #define PDT_DIRECT_SCAN_CHANNEL		1
// #define TEST_STACK_BACKGROUND_TSCC		2	/* 1-print info only; 2-print&test ownself */

// #define BYPASS_DSP_OPERATION			1
//#define FAKE_MOBILE_BE_CALLED			1

#define P1_DEL_UNNECESSARY_KEY_PROC		1



#define CALL_STACK_WAITING_MS			8
#define AMBE_ENCODE_WAITING_MS			10
#define AMBE_DECODE_WAITING_MS			10

//#define XFER_MEASURE_TIME 				1
//#define TEST_VOCODER_FUNCTION			1




//#define SET_WRITE_SPI_DATA_DEBUG_INFO	128
//  #define WRITE_SPI_DATA_DEBUG_INFO_LEN	940

#define MOBILE_MODE_PDT_ONLY			1
#define MOBILE_MODE_ZZW_ONLY			3
#define MOBILE_MODE_PDT_ZZW				18
#define MOBILE_MODE_PDT_ZZW_BT			19
#define MOBILE_MODE_PDT_ZZW_380M		38
#define MOBILE_MODE_PDT_ZZW_THIN		20
#define MOBILE_MODE_2P_150M				15
#define MOBILE_MODE_2P_400M				40
#define MOBILE_CHEJI_MODE_PDT_ZZW		118
#define MOBILE_MODE_PDT_ZZW_380M_THIN	39
#define MOBILE_MODE_2P_150M_THIN		16
#define MOBILE_MODE_2P_400M_THIN		41
#define MOBILE_MODE_PDT_ZZW_873			73
#define MOBILE_MODE_PDT_ZZW_815			64
#define MOBILE_MODE_PDT_ZZW_816A		65
#define MOBILE_MODE_PDT_ZZW_816V		66
#define MOBILE_MODE_PDT_ZZW_816			86
#define MOBILE_MODE_PDT_ZZW_815V2		88
#define MOBILE_MODE_PDT_ZZW_815V3		89
#define MOBILE_MODE_PDT_ZZW_810C		90
#define MOBILE_MODE_PDT_ZZW_811			91
#define MOBILE_MODE_PDT_ZZW_811A		92
#define VICTEL_ZZWPRO_BASE_SN			67
#define MOBILE_MODE_PDT_ZZW_805			105
#define MOBILE_MODE_PDT_ZZW_809			109
#define DEV_IS_805_SERIES(devNo)		((devNo == MOBILE_MODE_PDT_ZZW_805) || (devNo == MOBILE_MODE_PDT_ZZW_809))
#define MOBILE_MODE_PDT_ZZW_815_P1_SN	50
#define VICTEL_DOUBLE_ARM_P2_SN			51
#define MOBILE_MODE_PDT_ZZW_NEW_DSP_K1	45
#define VICTEL_DOUBLE_ARM_P2_BASE		52
#define VICTEL_DOUBLE_CORE_P3			53
#define VICTEL_DOUBLE_CORE_P3_BASE		63

#define RANGE_150_LOW					136
#define RANGE_150_HIGH					174
#define RANGE_350_LOW					351
#define RANGE_350_HIGH					368
#define RANGE_350_LOW_CHEJI				350
#define RANGE_350_HIGH_CHEJI			400
#define RANGE_380_LOW					372
#define RANGE_380_HIGH					389
#define RANGE_400_LOW					400
#define RANGE_400_HIGH					470
#define RANGE_200_LOW					215
#define RANGE_200_HIGH					235

#define MOBILE_RF_TYPE_DDS				0
#define MOBILE_RF_TYPE_2P_150M			1
#define MOBILE_RF_TYPE_2P_400M			2
#define MOBILE_RF_TYPE_2P_FULL_BAND		3

#define MOBILE_DEV_TYPE_MOBILE			0
#define MOBILE_DEV_TYPE_CHEJI			1

#define MOBILE_VMODE_PDT_ONLY1			0x00
#define MOBILE_VMODE_PDT_ONLY2			0xff
#define MOBILE_VMODE_ZZW_ONLY			0x01

#define MODULE_805_VERSION_V1			0
#define MODULE_805_VERSION_V2_BASE		1
#define MODULE_805_VERSION_V2_VEHICLE	2
#define MODULE_805_VERSION_V2_VEHICLE_AUTO_POWER	3
#define MODULE_805_VERSION_MAX_TYPE		4

typedef void (*P_FUN_VOID_VOID)(void);
typedef void (*P_FUN_VOID_U8)(uint8_t);
typedef void (*P_FUN_VOID_U16)(uint16_t);
typedef uint16_t (*P_FUN_U16_U16X2)(uint16_t, uint16_t);
typedef void (*P_FUN_VOID_U16X3)(uint16_t, uint16_t, uint16_t);
typedef uint32_t (*P_FUN_U32_U8PTR_U32_U32)(uint8_t*, uint32_t, uint32_t);
typedef uint16_t (*P_FUN_U16_U16_U16PTR)(uint16_t, uint16_t *);
typedef void (*P_FUN_VOID_U8PTR_U16)(uint8_t *, uint16_t);


#define MAIN_MENU_TOTAL_ITEM			6
#define SWITCH_WORKMODE_MENU_TOTAL_ITEM	10
#define SETUP_OTHERS_MENU_TOTAL_ITEM	16/* 9 at oled vehicle */
#define SETUP_MENU_TOTAL_ITEM			12/* 8 at oled vehicle */
#define SCANNING_MENU_TOTAL_ITEM		4
#define GUI_VOLUME_MENU_LABEL_TOTAL		4
#define PROJECT_MENU_TOTAL_ITEM			10
#define	GPS_MENU_TOTAL_ITEM				5
#define RSSI_DISPLAY_MODE_NUM			3/*Bar/Value/Chan*/
#define RSSI_DISPLAY_MODE_EXT_NUM		2/*LAI/DQI*/
#define VOCODER_SETUP_MENU_TOTAL_ITEM	5

#define BOOK_MODE_TOTAL_TYPE			3
#define BOOK_MODE_IND_NOTWATCH			0
#define BOOK_MODE_INDIVIDUAL			1
#define BOOK_MODE_GROUP					2



////////////////////////////////////////////////////////////
//////////// system configration flag define ///////////////

#define FREE_DATA_FLAG					0x55358900

#define MENU_CONFIG_FLAG_MASK			0xffffff00
#define MAIN_MENU_CONFIG_FLAG			(MENU_CONFIG_FLAG_PREFIX + 1)
#define SETUP_MENU_CONFIG_FLAG			(MENU_CONFIG_FLAG_PREFIX + 2)
#define SETUP_OTHERS_MENU_CONFIG_FLAG	(MENU_CONFIG_FLAG_PREFIX + 3)
#define PROJECT_MENU_CONFIG_FLAG		(MENU_CONFIG_FLAG_PREFIX + 4)
#define SCANNING_MEMU_CONFIG_FLAG		(MENU_CONFIG_FLAG_PREFIX + 5)
#define GPS_MENU_CONFIG_FLAG			(MENU_CONFIG_FLAG_PREFIX + 6)
#define SWITCH_WORKMODE_CONFIG_FLAG		(MENU_CONFIG_FLAG_PREFIX + 7)


//////////// menu configuration define ///////////////
typedef struct
{
	uint32_t menu_config_flag;			// 0x55356d00 - 0x55356d_MAX_USER_SCREEN-1
	uint16_t menu_config_num;			// total menu item at this config
	uint16_t menu_config_reserve;		// 4B aligned
}MENU_CFG_STRUCT_HEAD;


//////////// usart port selector ///////////////
#define UART_DEBUG_INDEX				0
#define UART_BT_INDEX					1
#define UART_GPS_INDEX					2
#define ITM_DEBUG_INDEX					4
#define STAND_IO_INDEX					5
#define UART_LINKTO_DSP_INDEX			6
#define UART_LINKTO_SD_INDEX			7
#define UART_REAL_REMAP_VALUE			8

/* global */
extern int g_bt_serial_fd;
extern int g_gps_serial_fd;
extern int g_debug_serial_fd;

#define UART_DEBUG_TX_DMA				DMA1_CHANNEL1
//#define UART_DEBUG_RX_DMA				DMA1_CHANNEL2/*not use really*/
#define LCD_REFRESH_TX_DMA				DMA1_CHANNEL3
#define GET_CRYSTAL_MEASURE_VAL_DMA		DMA1_CHANNEL4
#define UART_BT_TX_DMA					DMA1_CHANNEL5
#define ADC_CONVERTER_DMA				DMA1_CHANNEL6

#define MCU_INF_TX_DMA					DMA2_CHANNEL1
#define MCU_INF_RX_DMA					DMA2_CHANNEL2

#define UART_BT_RX_DMA					EDMA_STREAM1
#define UART_GPS_RX_DMA					EDMA_STREAM2


//////////// interphone work mode ///////////////
#define INTERPHONE_MODE_PDT_DIRECT		0
#define INTERPHONE_MODE_PDT_CONV		1
#define INTERPHONE_MODE_PDT_TRUNKING	2
#define INTERPHONE_MODE_MPT_TRUNKING	3
#define INTERPHONE_MODE_MPT_CONV		4
#define INTERPHONE_MODE_ZZW_ADHOC		5

#define INTERPHONE_MODE_ZZWPRO_Q		6
#define INTERPHONE_MODE_ZZWPRO_N		7
#define INTERPHONE_MODE_ZZWPRO_V		8
#define INTERPHONE_MODE_ZZWPRO_V25K		9
#define INTERPHONE_MODE_VDT15MS			10
#define INTERPHONE_MODE_ZZWPRO_N4		11
#define INTERPHONE_MODE_LAST_MODE		INTERPHONE_MODE_ZZWPRO_N4


#define BATTERY_POWER_LEVEL				5
#define SIGNAL_LEVEL					6

#define MODULE_INIT_DONE_LCD			0x01
#define MODULE_INIT_DONE_FONT			0x02
#define MODULE_INIT_DONE_DSP			0x04
#define MODULE_INIT_DONE_STACK			0x08
#define MODULE_INIT_DONE_FPGA			0x10
#define MODULE_INIT_DONE_GUI			0x20


#ifdef __cplusplus
}
#endif


#endif /* __SYSTEM_DEFINE_H__ */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
