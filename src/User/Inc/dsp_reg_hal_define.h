#ifndef __VICTEL_DIGITAL_DSP_REG_HAL_DEFINE_H__
#define __VICTEL_DIGITAL_DSP_REG_HAL_DEFINE_H__


#define SIZEOF_MEMB_16BIT(t,memb)			((size_t)sizeof(((t *)0)->memb)/sizeof(uint16_t))
#define OFFSETOF_MEMB_16BIT(t,memb)			(offsetof(t,memb)/sizeof(uint16_t))

#define DSP_REG_MAX_LENGTH					480
#define DSP_RUNNING_FLAG					0x5AA5
#define DSP_ENCRYPT_CHIP_ERROR				0x5A00

/////////////////////// write register ///////////////////////
#define REG_SYSTEM_UPDATE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_system_update))
#define REG_SYSTEM_UPDATE_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_system_update)

#define REG_SPEECH_UPDATE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_update))
#define REG_SPEECH_UPDATE_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_update)

#define REG_VOCODER_UPDATE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_vocoder_update))
#define REG_VOCODER_UPDATE_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_vocoder_update)

#define REG_DIGITAL_UPDATE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_digital_update))
#define REG_DIGITAL_UPDATE_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_digital_update)

#define REG_ANALOG_UPDATE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_update))
#define REG_ANALOG_UPDATE_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_update)

#define REG_HARDWARE_UPDATE_ADDR			(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_hardware_update))
#define REG_HARDWARE_UPDATE_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_hardware_update)

#define REG_SYSTEM_READ_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_system_read))
#define REG_SYSTEM_READ_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_system_read)

#define REG_SPEECH_READ_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_read))
#define REG_SPEECH_READ_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_read)

#define REG_VOCODER_READ_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_vocoder_read))
#define REG_VOCODER_READ_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_vocoder_read)

#define REG_DIGITAL_READ_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_digital_read))
#define REG_DIGITAL_READ_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_digital_read)

#define REG_ANALOG_READ_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_read))
#define REG_ANALOG_READ_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_read)

#define REG_HARDWARE_READ_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_hardware_read))
#define REG_HARDWARE_READ_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_hardware_read)

#define REG_DSP_CMD_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_dsp_cmd))
#define REG_DSP_CMD_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_dsp_cmd)

#define REG_DSP_MODE_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_dsp_mode))
#define REG_DSP_MODE_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_dsp_mode)

#define REG_INTERRUPT_ENABLE_ADDR			(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_interrupt_enable))
#define REG_INTERRUPT_ENABLE_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_interrupt_enable)

#define REG_SPEECH_MODE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_mode))
#define REG_SPEECH_MODE_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_mode)

#define REG_SPEECH_IN_GAIN_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_in_gain))
#define REG_SPEECH_IN_GAIN_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_in_gain)

#define REG_SPEECH_IN_GAIN_SHIFT_ADDR		(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_in_gain_shift))
#define REG_SPEECH_IN_GAIN_SHIFT_LENGTH		SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_in_gain_shift)

#define REG_SPEECH_OUT_GAIN_ADDR			(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_out_gain))
#define REG_SPEECH_OUT_GAIN_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_out_gain)

#define REG_SPEECH_OUT_GAIN_SHIFT_ADDR		(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_out_gain_shift))
#define REG_SPEECH_OUT_GAIN_SHIFT_LENGTH	SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_out_gain_shift)

#define REG_DTMF_OUT_GAIN_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_dtmf_out_gain))
#define REG_DTMF_OUT_GAIN_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_dtmf_out_gain)

#define REG_DTMF_OUT_GAIN_SHIFT_ADDR		(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_dtmf_out_gain_shift))
#define REG_DTMF_OUT_GAIN_SHIFT_LENGTH		SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_dtmf_out_gain_shift)

#define REG_TONE_OUT_GAIN_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_tone_out_gain))
#define REG_TONE_OUT_GAIN_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_tone_out_gain)

#define REG_TONE_OUT_GAIN_SHIFT_ADDR		(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_tone_out_gain_shift))
#define REG_TONE_OUT_GAIN_SHIFT_LENGTH		SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_tone_out_gain_shift)

#define REG_DA_PCM_GAIN_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_da_pcm_gain))
#define REG_DA_PCM_GAIN_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_da_pcm_gain)

#define REG_DA_PCM_GAIN_SHIFT_ADDR			(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_da_pcm_gain_shift))
#define REG_DA_PCM_GAIN_SHIFT_LENGTH		SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_da_pcm_gain_shift)

#define REG_DTMF_GEN_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_dtmf_gen))
#define REG_DTMF_GEN_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_dtmf_gen)

#define REG_TONE_GEN_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_tone_gen))
#define REG_TONE_GEN_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_tone_gen)

#define REG_SPEECH_IN_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_in))
#define REG_SPEECH_IN_LENGTH_ASSIGNED		SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_speech_in)
#define REG_SPEECH_IN_LENGTH				480

#define REG_DA_PCM_ADDR						(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_da_pcm))
#define REG_DA_PCM_LENGTH_ASSIGNED			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_da_pcm)
#define REG_DA_PCM_LENGTH					480

#define REG_VOCODER_MODE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_vocoder_mode))
#define REG_VOCODER_MODE_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_vocoder_mode)

#define REG_VOCODER_DATA_IN_ADDR			(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_vocoder_data_in))
#define REG_VOCODER_DATA_IN_LENGTH_ASSIGNED	SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_vocoder_data_in)
#define REG_VOCODER_DATA_IN_LENGTH			15

#define REG_DIGITAL_MODE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_digital_mode))
#define REG_DIGITAL_MODE_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_digital_mode)

#define REG_PACKET_IN_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_packet_in))
#define REG_PACKET_IN_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_packet_in)

#define REG_VOICE_IN_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_voice_in))
#define REG_VOICE_IN_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_voice_in)

#define REG_INFO_IN_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_info_in))
#define REG_INFO_IN_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_info_in)

#define REG_RC_IN_ADDR						(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_rc_in))
#define REG_RC_IN_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_rc_in)

#define REG_EBS_IN_ADDR						(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ebs_in))
#define REG_EBS_IN_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ebs_in)

#define REG_CACH_IN_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_cach_in))
#define REG_CACH_IN_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_cach_in)

#define REG_CC_IN_ADDR						(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_cc_in))
#define REG_CC_IN_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_cc_in)

#define REG_EMB_IN_ADDR						(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_emb_in))
#define REG_EMB_IN_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_emb_in)

#define REG_TACT_IN_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_tact_in))
#define REG_TACT_IN_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_tact_in)

#define REG_BER_TEST_MODE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ber_test_mode))
#define REG_BER_TEST_MODE_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ber_test_mode)

#define REG_RDATA_LOC_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_rdata_loc))
#define REG_RDATA_LOC_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_rdata_loc)

#define REG_RESYNC_NUM_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_resync_num))
#define REG_RESYNC_NUM_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_resync_num)

#define REG_SYNC_OFFSET_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_sync_offset))
#define REG_SYNC_OFFSET_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_sync_offset)

#define REG_ANALOG_MODE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_mode))
#define REG_ANALOG_MODE_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_mode)

#define REG_ANALOG_IN_GAIN_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_in_gain))
#define REG_ANALOG_IN_GAIN_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_in_gain)

#define REG_ANALOG_IN_GAIN_SHIFT_ADDR		(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_in_gain_shift))
#define REG_ANALOG_IN_GAIN_SHIFT_LENGTH		SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_in_gain_shift)

#define REG_ANALOG_OUT_GAIN_ADDR			(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_out_gain))
#define REG_ANALOG_OUT_GAIN_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_out_gain)

#define REG_ANALOG_OUT_GAIN_SHIFT_ADDR		(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_out_gain_shift))
#define REG_ANALOG_OUT_GAIN_SHIFT_LENGTH	SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_out_gain_shift)

#define REG_ANALOG_AUDIO_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_audio))
#define REG_ANALOG_AUDIO_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_audio)

#define REG_ANALOG_AUDIO_THRESHOLD_ADDR		(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_audio_threshold))
#define REG_ANALOG_AUDIO_THRESHOLD_LENGTH	SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_audio_threshold)

#define REG_ANALOG_SQUELCH_LEVEL_ADDR		(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_squelch_level))
#define REG_ANALOG_SQUELCH_LEVEL_LENGTH		SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_squelch_level)

#define REG_ANALOG_SUB_CTCSS_ADDR			(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_sub_ctcss))
#define REG_ANALOG_SUB_CTCSS_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_sub_ctcss)

#define REG_ANALOG_SUB_DCS_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_sub_dcs))
#define REG_ANALOG_SUB_DCS_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_sub_dcs)

#define REG_ANALOG_SUB_LEVEL_ADDR			(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_sub_level))
#define REG_ANALOG_SUB_LEVEL_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_sub_level)

#define REG_ANALOG_INBAND_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_inband))
#define REG_ANALOG_INBAND_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_inband)

#define REG_ANALOG_MSK_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_msk))
#define REG_ANALOG_MSK_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_msk)

#define REG_ANALOG_MSK_DATA_IN_ADDR			(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_msk_data_in))
#define REG_ANALOG_MSK_DATA_IN_LENGTH		SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_msk_data_in)

#define REG_ANALOG_MSK_LEVEL_ADDR			(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_msk_level))
#define REG_ANALOG_MSK_LEVEL_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_analog_msk_level)

#define REG_HARDWARE_MODE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_hardware_mode))
#define REG_HARDWARE_MODE_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_hardware_mode)

#define REG_MOD_GAIN_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_mod_gain))
#define REG_MOD_GAIN_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_mod_gain)

#define REG_MOD_DC_ADDR						(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_mod_dc))
#define REG_MOD_DC_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_mod_dc)

#define REG_AD9864_TUNE_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ad9864_tune))
#define REG_AD9864_TUNE_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ad9864_tune)

#define REG_CODEC_IN_GAIN_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_codec_in_gain))
#define REG_CODEC_IN_GAIN_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_codec_in_gain)

#define REG_CODEC_OUT_GAIN_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_codec_out_gain))
#define REG_CODEC_OUT_GAIN_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_codec_out_gain)

#define REG_PLL_RX_CONFIG_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_pll_rx_config))
#define REG_PLL_RX_CONFIG_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_pll_rx_config)

#define REG_PLL_TX_CONFIG_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_pll_tx_config))
#define REG_PLL_TX_CONFIG_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_pll_tx_config)

#define REG_PLL_RX_DAC_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_pll_rx_dac))
#define REG_PLL_RX_DAC_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_pll_rx_dac)

#define REG_PLL_TX_DAC_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_pll_tx_dac))
#define REG_PLL_TX_DAC_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_pll_tx_dac)

#define REG_RAMP_UP_HIGH_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_up_high))
#define REG_RAMP_UP_HIGH_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_up_high)

#define REG_RAMP_DOWN_HIGH_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_down_high))
#define REG_RAMP_DOWN_HIGH_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_down_high)

#define REG_RAMP_UP_LOW_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_up_low))
#define REG_RAMP_UP_LOW_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_up_low)

#define REG_RAMP_DOWN_LOW_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_down_low))
#define REG_RAMP_DOWN_LOW_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_down_low)

#define REG_RAMP_UP_MICRO_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_up_micro))
#define REG_RAMP_UP_MICRO_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_up_micro)

#define REG_RAMP_DOWN_MICRO_ADDR			(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_down_micro))
#define REG_RAMP_DOWN_MICRO_LENGTH			SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_ramp_down_micro)

#define REG_CRYSTAL_DC_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_crystal_dc))
#define REG_CRYSTAL_DC_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_crystal_dc)

#define REG_MOD_ATT_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_mod_att))
#define REG_MOD_ATT_LENGTH					SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_mod_att)

#define REG_DELAY_FRAC_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_delay_frac))
#define REG_DELAY_FRAC_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_delay_frac)

#define REG_ADHOC_WIN_ADDR					(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_adhoc_win))
#define REG_ADHOC_WIN_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_adhoc_win)

#define REG_ADHOC_RC_MAX_ADDR				(WRITE_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_WRITE_OBJ,reg_adhoc_rc_max))
#define REG_ADHOC_RC_MAX_LENGTH				SIZEOF_MEMB_16BIT(REG_WRITE_OBJ,reg_adhoc_rc_max)


/////////////////////// read register ///////////////////////
#define REG_DSP_VERSION_ADDR				(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_dsp_version))
#define REG_DSP_VERSION_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_dsp_version)

#define REG_INTERRUPT_STATE_ADDR			(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_interrupt_state))
#define REG_INTERRUPT_STATE_LENGTH			SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_interrupt_state)

#define REG_DSP_STATE_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_dsp_state))
#define REG_DSP_STATE_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_dsp_state)

#define REG_DSP_RUN_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_dsp_run))
#define REG_DSP_RUN_LENGTH					SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_dsp_run)

#define REG_SPEECH_INTERRUPT_STATE_ADDR		(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_speech_interrupt_state))
#define REG_SPEECH_INTERRUPT_STATE_LENGTH	SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_speech_interrupt_state)

#define REG_DTMF_DETECT_ADDR				(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_dtmf_detect))
#define REG_DTMF_DETECT_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_dtmf_detect)

#define REG_TONE_DETECT_ADDR				(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_tone_detect))
#define REG_TONE_DETECT_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_tone_detect)

#define REG_SPEECH_OUT_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_speech_out))
#define REG_SPEECH_OUT_LENGTH_ASSIGNED		SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_speech_out)
#define REG_SPEECH_OUT_LENGTH				480

#define REG_AD_PCM_ADDR						(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_ad_pcm))
#define REG_AD_PCM_LENGTH_ASSIGNED			SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_ad_pcm)
#define REG_AD_PCM_LENGTH					480

#define REG_VOCODER_DATA_OUT_ADDR			(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_vocoder_data_out))
#define REG_VOCODER_DATA_OUT_LENGTH_ASSIGNED	SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_vocoder_data_out)
#define REG_VOCODER_DATA_OUT_LENGTH			15

#define REG_PACKET_OUT_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_packet_out))
#define REG_PACKET_OUT_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_packet_out)

#define REG_VOICE_OUT_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_voice_out))
#define REG_VOICE_OUT_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_voice_out)

#define REG_INFO_OUT_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_info_out))
#define REG_INFO_OUT_LENGTH					SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_info_out)

#define REG_RC_OUT_ADDR						(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_rc_out))
#define REG_RC_OUT_LENGTH					SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_rc_out)

#define REG_EBS_OUT_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_ebs_out))
#define REG_EBS_OUT_LENGTH					SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_ebs_out)

#define REG_CACH_OUT_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_cach_out))
#define REG_CACH_OUT_LENGTH					SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_cach_out)

#define REG_CC_OUT_ADDR						(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_cc_out))
#define REG_CC_OUT_LENGTH					SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_cc_out)

#define REG_EMB_OUT_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_emb_out))
#define REG_EMB_OUT_LENGTH					SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_emb_out)

#define REG_TACT_OUT_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_tact_out))
#define REG_TACT_OUT_LENGTH					SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_tact_out)

#define REG_DIGITAL_RSSI_ADDR				(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_digital_rssi))
#define REG_DIGITAL_RSSI_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_digital_rssi)

#define REG_DEMOD_DQI_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_demod_dqi))
#define REG_DEMOD_DQI_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_demod_dqi)

#define REG_MOD_STATE_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_mod_state))
#define REG_MOD_STATE_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_mod_state)

#define REG_DEMOD_STATE_ADDR				(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_demod_state))
#define REG_DEMOD_STATE_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_demod_state)

#define REG_BER_TEST_STATE_ADDR				(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_ber_test_state))
#define REG_BER_TEST_STATE_LENGTH			SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_ber_test_state)

#define REG_ANALOG_INTERRUPT_STATE_ADDR		(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_analog_interrupt_state))
#define REG_ANALOG_INTERRUPT_STATE_LENGTH	SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_analog_interrupt_state)

#define REG_ANALOG_MSK_MOD_STATE_ADDR		(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_analog_msk_mod_state))
#define REG_ANALOG_MSK_MOD_STATE_LENGTH		SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_analog_msk_mod_state)

#define REG_ANALOG_MSK_DEMOD_STATE_ADDR		(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_analog_msk_demod_state))
#define REG_ANALOG_MSK_DEMOD_STATE_LENGTH	SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_analog_msk_demod_state)

#define REG_ANALOG_MSK_DATA_OUT_ADDR		(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_analog_msk_data_out))
#define REG_ANALOG_MSK_DATA_OUT_LENGTH		SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_analog_msk_data_out)

#define REG_ANALOG_RSSI_ADDR				(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_analog_rssi))
#define REG_ANALOG_RSSI_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_analog_rssi)

#define REG_AD9864_TUNE_READ_ADDR			(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_ad9864_tune_read))
#define REG_AD9864_TUNE_READ_LENGTH			SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_ad9864_tune_read)

#define REG_PLL_RX_LOCK_ADDR				(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_pll_rx_lock))
#define REG_PLL_RX_LOCK_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_pll_rx_lock)

#define REG_PLL_TX_LOCK_ADDR				(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_pll_tx_lock))
#define REG_PLL_TX_LOCK_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_pll_tx_lock)

#define REG_PLL_RX_ADC_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_pll_rx_adc))
#define REG_PLL_RX_ADC_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_pll_rx_adc)

#define REG_PLL_TX_ADC_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_pll_tx_adc))
#define REG_PLL_TX_ADC_LENGTH				SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_pll_tx_adc)

#define REG_TEMP_ADC_ADDR					(READ_BASE_ADDR+OFFSETOF_MEMB_16BIT(REG_READ_OBJ,reg_temp_adc))
#define REG_TEMP_ADC_LENGTH					SIZEOF_MEMB_16BIT(REG_READ_OBJ,reg_temp_adc)


#define REG_SPEECH_LENGTH_60MS				REG_SPEECH_IN_LENGTH
#define REG_SPEECH_LENGTH_40MS				(REG_SPEECH_LENGTH_60MS / 3 * 2)
#define REG_SPEECH_LENGTH_160MS				(REG_SPEECH_LENGTH_60MS / 3 * 8)
#define REG_SPEECH_LENGTH_180MS				(REG_SPEECH_LENGTH_60MS * 3)

#define REG_DSP64_VERSION_ADDR				(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 1)
#define REG_DSP64_ADHOC_PACKET_OUT_ADDR		(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 2)
#define REG_DSP64_REG_PACKET_OUT_NOFEC_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 3)
#define REG_DSP64_FREQ_EST_ADDR				(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 4)
#define REG_DSP64_ADHOC_PACKET_OUT_FEC_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 5)
#define REG_DSP64_SPEECH_INTERRUPT_STATE_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 6)
#define REG_DSP64_CRYSTAL_19M2_ADDR			(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 7)
#define REG_DSP64_READ_VOCODER_MODE_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 8)
#define REG_DSP64_READ_TRANSCODE_OUT_0_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 9)
#define REG_DSP64_READ_TRANSCODE_OUT_1_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 10)
#define REG_DSP64_READ_TRANSCODE_OUT_2_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 11)
#define REG_DSP64_READ_TRANSCODE_OUT_3_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 12)

#define REG_DSP64_REG_PACKET_OUT_EX_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 13)
#define REG_DSP64_REG_DIGITAL_RSSI_EX_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 14)
#define REG_DSP64_REG_DEMOD_DQI_EX_ADDR		(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 15)
#define REG_DSP64_REG_DEMOD_STATE_EX_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 16)
#define REG_DSP64_REG_BER_TEST_STATE_EX_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 17)
#define REG_DSP64_ADHOC_PACKET_OUT_EX_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 18)
#define REG_DSP64_REG_PACKET_OUT_NOFEC_EX_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 19)
#define REG_DSP64_FREQ_EST_EX_ADDR			(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 20)
#define REG_DSP64_ADHOC_PACKET_OUT_FEC_EX_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 21)
#define REG_DSP64_ANALOG_RSSI_EX_ADDR		(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 22)
#define REG_DSP64_AD9864_TUNE_READ_EX_ADDR	(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 23)
#define REG_DSP64_PLL_RX_LOCK_EX_ADDR		(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 24)
#define REG_DSP64_ANALOG_PCM_OUT_ADDR		(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 25)
#define REG_DSP64_SPEECH_EXT_AMP_ADDR		(READ_BASE_ADDR + sizeof(REG_READ_OBJ) + 26)


#define REG_DSP64_ADHOC_PACKET_IN_ADDR		(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 1)
#define REG_DSP64_ADHOC_CC_IN_ADDR			(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 2)
#define REG_DSP64_WAKE_PERIOD_ADDR			(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 3)
#define REG_DSP64_SLEEP_PERIOD_ADDR			(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 4)
#define REG_DSP64_REG_MOD_TYPE_ADDR			(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 5)
#define REG_DSP64_REG_DEMOD_ENABLE_ADDR		(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 6)
#define REG_DSP64_REG_VOCODER_CONFIG_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 7)
#define REG_DSP64_REG_VOCODER_MIX_IN_0_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 8)
#define REG_DSP64_REG_VOCODER_MIX_IN_1_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 9)
#define REG_DSP64_REG_VOCODER_MIX_IN_2_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 10)
#define REG_DSP64_REG_VOCODER_MIX_IN_3_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 11)
#define REG_DSP64_REG_VOCODER_MIX_IN_4_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 12)
#define REG_DSP64_REG_VOCODER_MIX_IN_5_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 13)
#define REG_DSP64_REG_VAD_NUM_ADDR 			(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 14)
#define REG_DSP64_REG_VAD_SHIFT_ADDR 		(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 15)
#define REG_DSP64_REG_GPS_OFFSET_ADDR 		(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 16)
#define REG_DSP64_REG_TRANSCODE_IN_0_ADDR 	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 17)
#define REG_DSP64_REG_TRANSCODE_IN_1_ADDR 	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 18)
#define REG_DSP64_REG_TRANSCODE_IN_2_ADDR 	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 19)
#define REG_DSP64_REG_TRANSCODE_IN_3_ADDR 	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 20)
#define REG_DSP64_REG_TRANSCODE_MODE_ADDR 	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 21)

#define REG_DSP64_REG_DSP_MODE_EX_ADDR		(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 22)
#define REG_DSP64_REG_DEMOD_ENABLE_EX_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 23)
#define REG_DSP64_REG_PLL_RX_CONFIG_EX_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 24)
#define REG_DSP64_REG_BER_TEST_MODE_EX_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 25)

#define REG_DSP64_REG_PCM_GAIN_ADDR			(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 26)
#define REG_DSP64_REG_PCM_GAIN_SHIFT_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 27)

#define REG_DSP64_REG_AGC_ADDR				(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 28)
#define REG_DSP64_REG_ANALOG_PCM_IN_ADDR	(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 29)
#define REG_DSP64_REG_CRYSTAL_STEP_ADDR		(WRITE_BASE_ADDR + sizeof(REG_WRITE_OBJ) + 30)


#endif	// __VICTEL_DIGITAL_DSP_REG_HAL_DEFINE_H__

