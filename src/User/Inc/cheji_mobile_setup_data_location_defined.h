/**
  ******************************************************************************
  *                Copyright (c) 2014, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    22-June-2016
  * @brief   This file define
  *            - the setup data location
  ******************************************************************************
  */


/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __VICTEL_DIGITAL_MOBILE_SETUP_DATA_LOCATION_DEFINED_H__
#define __VICTEL_DIGITAL_MOBILE_SETUP_DATA_LOCATION_DEFINED_H__

#ifdef __cplusplus
 extern "C" {
#endif


#include <stdint.h>


#if 0
#define uint8_t		unsigned char
#define uint16_t	unsigned short
#define int16_t		short
#define uint32_t	unsigned int
#endif


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////// 写频地址定义 //////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////// 内部 FLASH 定义 ////////////////////////////////
#define MISC_PARAMETERS_FLASH_ADDRESS_469		0x08100000
#define MISC_PARAMETERS_FLASH_ADDRESS2_469		0x08104000

#define MISC_PARAMETERS_FLASH_ADDRESS			0x08008000
#define MISC_PARAMETERS_FLASH_ADDRESS2			0x0800C000

#define MISC_PARAMETERS_FLASH_LENGTH			0x4000
#define MISC_PARAMETERS_FLASH_LENGTH2			0x4000

#define PDT_STACK_FLASH_ADDRESS					0x080B0000/*0x08010000*/
#define PDT_STACK_MAX_LENGTH					0x10000

#define FONTS_LIBRARY_FLASH_ADDRESS				0x08180000
#define FONTS_LIBRARY_MAX_LENGTH				0x80000

#define MCU_PROGRAM_FLASH_ADDRESS				0x0800C000
#define MCU_PROGRAM_MAX_LENGTH					0xA0000/*max=A4000*/

#define ICON_LIBRARY_FLASH_ADDRESS				0x08160000
#define ICON_LIBRARY_MAX_LENGTH					0x20000

#define MCU_PROGRAM_V_FLASH_ADDRESS				MCU_PROGRAM_FLASH_ADDRESS
#define MCU_PROGRAM_V_MAX_LENGTH				MCU_PROGRAM_MAX_LENGTH

#define ZZWXV_STACK_FLASH_ADDRESS				0x080C0000
#define ZZWXV_STACK_MAX_LENGTH					0x20000

#define USER_BOOK_DATA_FLASH_ADDRESS			((uint32_t)g_user_book_inst)
#define USER_BOOK_DATA_MAX_LENGTH				0x20000

#define USER_BOOK_EXT_DATA_FLASH_ADDRESS		0x08110000
#define USER_BOOK_EXT_DATA_FLASH_ADDRESS_LENGTH	0x10000


//////////////////////////////// SPI FLASH 定义 ////////////////////////////////
#define SST25VF080_SIZE_OF_SECTOR				(4 * 1024)
#define SST25VF080_SIZE_OF_BLOCK				(32 * 1024)
/*
	24KB  - reserved							0
	40KB  - long message content				24576
	32KB  - 自组网车机参数						65536
	32KB  - 出厂设置							98304
		4KB:  backup ESN(use first 16B only)，写频软件在修改ESN时，必须同时将新的ESN写到此处；地址为FLASH_SPI_FACTORY_PARAS_ADDRESS
		4KB:  reserved
		4KB:  RUNTIME_PARAMETERS_TYPEDEF_1(写频软件读写时，只需操作FLASH_SPI_RUNTIME_ADDRESS地址即可，大小4KB)
		4KB:  RUNTIME_PARAMETERS_TYPEDEF_2
		8KB:  reserved(VICTEL_ZZWPRO_BASE || STM32F469_479xx: RUNTIME_PARAMETERS_XVBASE_TYPEDEF_1(4KB) + RUNTIME_PARAMETERS_XVBASE_TYPEDEF_2(4KB))
		4KB:  出厂参数+调谐表/两点调参数(数据格式: FACTORY_PARAS_TYPEDEF(128B)+DSP_DDS_TUNE_TYPEDEF/DSP_2P_TUNE_TYPEDEF(最大2KB))
		4KB:  6B的图片头+1024B图片内容; 1030-1547: 6B的图片头+512B二维码(MAX=64x64)内容; 1548-END:保留
	256KB - 双模手台的自组网DSP程序				131072		- 在使用EMWIN的车机中，和下一个256KB一起用于存储大字库(占用508KB左右)
	256KB - 双模手台的PDT DSP程序				393216
	256KB - 双模手台的PDT DSP程序(NVOC)			655360		- 在使用EMWIN的车机中，此块用于存储图库和默认开机LOGO(128KB)+自定义开机LOGO(128KB)
	128KB - reserved							917504

	////////// Micron 16MB //////////
	1MB   - fonts(24x24 or 32x32)				1048576
	512KB - icon, pic, etc.						2097152
	256KB - custom logo							2621440
----256KB - reserved							2883584----
		8KB - 基站转发表								2883584 - 20210219
		248KB - reserved						2891776
	1MB   - reserved(buffer for upgrade)		3145728
*/

#define FLASH_SPI_FLASH_ADDRESS_START			0xC0000000
#define FLASH_SPI_FLASH_ADDRESS_1MB_END			(FLASH_SPI_FLASH_ADDRESS_START + 0x100000)
#define FLASH_SPI_REMAP_END_ADDRESS				(FLASH_SPI_FLASH_ADDRESS_START + 128 * 1024)
#define FLASH_SPI_ABS_END_ADDRESS				(FLASH_SPI_FLASH_ADDRESS_START + 0x1000000)
#define IS_SPI_FLASH_OPERATION(addr)			(((addr) & FLASH_SPI_FLASH_ADDRESS_START) == FLASH_SPI_FLASH_ADDRESS_START)

// bit1/0: 00-正常(只写号码薄/静态/动态); bit0:1-写入出厂参数; bit1:1-写入esn
#define FLASH_CONFIGURATION_FAKE_ADDRESS		(FLASH_SPI_FLASH_ADDRESS_START + 0x01000000)
#define IS_CONFIGURATION_FAKE_ADDRESS(addr)		(((addr) & FLASH_CONFIGURATION_FAKE_ADDRESS) == FLASH_CONFIGURATION_FAKE_ADDRESS)

#define FLASH_SPI_RESERVED1_ADDRESS				FLASH_SPI_FLASH_ADDRESS_START
#define FLASH_SPI_RESERVED1_LENGTH				(6 * SST25VF080_SIZE_OF_SECTOR)

#define FLASH_SPI_LONG_MESSAGE_ADDRESS			(FLASH_SPI_RESERVED1_ADDRESS + FLASH_SPI_RESERVED1_LENGTH)
#define FLASH_SPI_LONG_MESSAGE_LENGTH			(10 * SST25VF080_SIZE_OF_SECTOR)

#define FLASH_SPI_ZZW_CHEJI_PARAS_ADDRESS		(FLASH_SPI_LONG_MESSAGE_ADDRESS + FLASH_SPI_LONG_MESSAGE_LENGTH)
#define FLASH_SPI_ZZW_CHEJI_PARAS_LENGTH		SST25VF080_SIZE_OF_BLOCK

#define FLASH_SPI_FACTORY_PARAS_ADDRESS			(FLASH_SPI_ZZW_CHEJI_PARAS_ADDRESS + FLASH_SPI_ZZW_CHEJI_PARAS_LENGTH)
#define FLASH_SPI_FACTORY_PARAS_LENGTH			SST25VF080_SIZE_OF_BLOCK
  #define FLASH_SPI_RUNTIME_ADDRESS				(FLASH_SPI_FACTORY_PARAS_ADDRESS + SST25VF080_SIZE_OF_SECTOR * 2)
  #define FLASH_SPI_RUNTIME_LENGTH				SST25VF080_SIZE_OF_SECTOR
  #define FLASH_SPI_XVBASE_RUNTIME_ADDRESS		(FLASH_SPI_FACTORY_PARAS_ADDRESS + SST25VF080_SIZE_OF_SECTOR * 4)
  #define FLASH_SPI_XVBASE_RUNTIME_LENGTH		SST25VF080_SIZE_OF_SECTOR
  #define FLASH_SPI_FACTORY_STATIC_ADDRESS		(FLASH_SPI_FACTORY_PARAS_ADDRESS + SST25VF080_SIZE_OF_SECTOR * 6)
  #define FLASH_SPI_FACTORY_STATIC_LENGTH		SST25VF080_SIZE_OF_SECTOR
  #define FLASH_SPI_LOGO_BMP_ADDRESS			(FLASH_SPI_FACTORY_PARAS_ADDRESS + SST25VF080_SIZE_OF_SECTOR * 7)
  #define FLASH_SPI_LOGO_BMP_LENGTH				SST25VF080_SIZE_OF_SECTOR

#define FLASH_SPI_ZZW_MOBILE_DSP_ADDRESS		(FLASH_SPI_FACTORY_PARAS_ADDRESS + FLASH_SPI_FACTORY_PARAS_LENGTH)
#define FLASH_SPI_ZZW_MOBILE_DSP_LENGTH			(8 * SST25VF080_SIZE_OF_BLOCK)

#define FLASH_SPI_PDT_MOBILE_DSP_ADDRESS		(FLASH_SPI_ZZW_MOBILE_DSP_ADDRESS + FLASH_SPI_ZZW_MOBILE_DSP_LENGTH)
#define FLASH_SPI_PDT_MOBILE_DSP_LENGTH			(8 * SST25VF080_SIZE_OF_BLOCK)

#define FLASH_SPI_PDT_MOBILE_DSP_NVOC			(FLASH_SPI_PDT_MOBILE_DSP_ADDRESS + FLASH_SPI_PDT_MOBILE_DSP_LENGTH)
#define FLASH_SPI_PDT_MOBILE_DSP_NVOC_LENGTH	(8 * SST25VF080_SIZE_OF_BLOCK)

#define FLASH_SPI_RESERVED2_ADDRESS				(FLASH_SPI_PDT_MOBILE_DSP_NVOC + FLASH_SPI_PDT_MOBILE_DSP_NVOC_LENGTH)
#define FLASH_SPI_RESERVED2_LENGTH				(4 * SST25VF080_SIZE_OF_BLOCK)


#define FLASH_SPI_FONT_SOURCES_ADDRESS			FLASH_SPI_FLASH_ADDRESS_1MB_END
#define FLASH_SPI_FONT_SOURCES_LENGTH			(1024 * 1024)

#define FLASH_SPI_PIC_SOURCES_ADDRESS			(FLASH_SPI_FONT_SOURCES_ADDRESS + FLASH_SPI_FONT_SOURCES_LENGTH)
#define FLASH_SPI_PIC_SOURCES_LENGTH			(512 * 1024)

#define FLASH_SPI_USER_LOGO_ADDRESS				(FLASH_SPI_PIC_SOURCES_ADDRESS + FLASH_SPI_PIC_SOURCES_LENGTH)
#define FLASH_SPI_USER_LOGO_LENGTH				(256 * 1024)

#define FLASH_SPI_FORWARDING_TABLE_ADDRESS		(FLASH_SPI_USER_LOGO_ADDRESS + FLASH_SPI_USER_LOGO_LENGTH)
#define FLASH_SPI_FORWARDING_TABLE_LENGTH		(8 * 1024)

#define FLASH_SPI_AES256_KEY_TABLE_ADDRESS		(FLASH_SPI_FORWARDING_TABLE_ADDRESS + FLASH_SPI_FORWARDING_TABLE_LENGTH)
#define FLASH_SPI_AES256_KEY_TABLE_LENGTH		(4 * 1024)

#define FLASH_SPI_RESERVED3_ADDRESS				(FLASH_SPI_AES256_KEY_TABLE_ADDRESS + FLASH_SPI_AES256_KEY_TABLE_LENGTH)
#define FLASH_SPI_RESERVED3_LENGTH				(256 * 1024 - FLASH_SPI_FORWARDING_TABLE_LENGTH - FLASH_SPI_AES256_KEY_TABLE_LENGTH)

#define FLASH_SPI_RESERVED4_ADDRESS				(FLASH_SPI_RESERVED3_ADDRESS + FLASH_SPI_RESERVED3_LENGTH)
#define FLASH_SPI_RESERVED4_LENGTH				(1024 * 1024)

#define FLASH_SPI_DSP_USED_ADDRESS				(FLASH_SPI_RESERVED4_ADDRESS + FLASH_SPI_RESERVED4_LENGTH)
#define FLASH_SPI_DSP_USED_LENGTH				(4 * 1024 * 1024)

#define FLASH_SPI_RECORD_VOICE_ADDRESS			(FLASH_SPI_DSP_USED_ADDRESS + FLASH_SPI_DSP_USED_LENGTH)
#define FLASH_SPI_RECORD_VOICE_LENGTH			(2 * 1024 * 1024)

#define FLASH_SPI_RESERVED5_ADDRESS				(FLASH_SPI_RECORD_VOICE_ADDRESS + FLASH_SPI_RECORD_VOICE_LENGTH)
#define FLASH_SPI_RESERVED5_LENGTH				(4 * 1024 * 1024)

#define FLASH_SPI_UI_FONT_ADDRESS				(FLASH_SPI_RESERVED5_ADDRESS + FLASH_SPI_RESERVED5_LENGTH)
#define FLASH_SPI_UI_FONT_LENGTH				(2 * 1024 * 1024)

#define FLASH_VIRTUAL_DSP_PARAS_ADDRESS			(FLASH_SPI_UI_FONT_ADDRESS + FLASH_SPI_UI_FONT_LENGTH)
#define FLASH_VIRTUAL_DSP_PARAS_LENGTH			(2 * 1024)


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////// 全局数据结构 //////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define MAX_USER_NAME_LEN			16
#define MAX_GROUPS_OF_BOOKS			40
#define MAX_GROUPS_OF_CTRL			MAX_GROUPS_OF_BOOKS
#define MAX_GROUPS_OF_ZZWPRO_BOOKS	10

typedef struct
{
	uint8_t  name[MAX_USER_NAME_LEN];	// strlen(name)==0时表示此条目为空
	uint32_t id;		// highest byte=USER_TYPE:
						// bit0: 1-常规组; 0-集群组
						// bit1: 1-直通模式; 0-中继模式
						// bit2: 1-参与组(守候组); 0-归属组(响应组)
						// bit3: 1-动态组；0-普通组
						// bit4: 1-单呼号；0-组呼号
						// bit6/5: 00-普通呼叫; 01-紧急呼叫; 10-广播呼叫; 11-未定义
						// bit7: 1-背景组；0-非背景组
						// LOW 3B=GRP_ID(1327ID when MPT trunk)；为模拟常规时，BYTE1为接收的亚音标识和索引；BYTE2为发射的亚音标识和索引；但目前DSP限制了收发亚音必须类型相同，所以BYTE1和BYTE2的bit7必须保持一致
						// 													   bit7=0，表示亚音为模拟亚音，此时索引序号为0-50，超过50表示亚音功能关闭；
						// 													   bit7=1，表示亚音为数字亚音，此时索引序号为0-82，超过82表示亚音功能关闭；
						// 注意：（1）车机无数字亚音，bit7无定义：BYTE1为接收的亚音索引；BYTE2为发射的亚音索引；索引序号为0-49，超过49表示亚音功能关闭
						//       （2）车机使用的亚音表为下表的ctcss_freq且从67.0开始（无ctcss_freq的第0项），共50组数据
	uint32_t bind_ch;	// PDT常规：高2B：基站系统码（可置为0，表示使用PROTOCOL_STACK_TYPEDEF.nid来匹配；20211122（目前此2B未用）：bit31-PQ组属性（1-PQ组）;bit30-频道异化（属性与频率相反；20220706老板说暂时不做）；低2B:本组所需监控的信道，其中高4b为信道色码（一般为0），低12b为信道编号
						// PDT集群：BYTE0：需要绑定的接收组列表索引，0x00或0xff表示禁用，1-254表示开启且对应列表的第0-253项；
						//			BYTE3：bit0-加密控制：开启加密时，1发起加密呼叫，0发起普通呼叫；未开启加密时：发起普通呼叫（不管bit0）
						// MPT集群：bit31-13：未使用；bit12-0:本组1327ID对应的1343队号
						// MPT常规：高2B：实际需要的发射频率*80；低2B：实际需要的接收频率*80；注意：频率间隔12.5K，且收发频率(Hz)必须要被12500整除
						// 自组网：高2B：同步超时时间设置(目前只用了低字节内容，0-表示超时关闭，1-255表示超时时间，步进是10秒)；低2B：信道号(含义和PDT相同)
						// XV自组网：高2B：b2b1b0:0-X跳频组，1-VO组，2-V2跳组，3-V3跳组，4-V6跳组；7-常规组；
						//                 b5b4b3:1~6，绑定时隙1~6，=0，不绑定时隙
						//				   b6:    1-异频(仅VO下使用)，0-同频
						//           低2B：信道号(含义和PDT相同)
}USER_BOOK;
/*
(1) id最高字节补充说明:
	bit1/bit0：00-集群；01-常规；11-直通；10-表明此用户号码属性为模拟常规或自组网（必须联合bit3bit2判定）；b3b2：11-模拟常规；01-自组网且无发射限制；00-自组网且有发射限制；10-XV自组网
		注：当此用户为模拟常规时，id的低3B和bind_ch有其他定义，见上文所述；
	bit2：集群下 - 设置为0时表明本组为响应组（自动接收此ID的呼叫）；用户列表中的响应组+背景组最大为32个，超过的不会被响应
	      直通/数字常规下 - 联合bit3进行时隙绑定：b3b2：=01，绑定时隙1；=10，绑定时隙2；=11，不绑定时隙
	bit3：集群下：写频时固定设置为普通组
		  直通/数字常规下 - 联合bit2进行时隙绑定设置（见上文）
	bit7：只能是集群用户才起作用（优先级最高）；当此位被设置后，自动接收此ID的呼叫，但用户不可见；
(2)
uint8_t ctcss_freq[51]={	// 表里的数字表示频率
                62.5, 67.0, 69.3, 71.9, 74.4,
                77.0, 79.7, 82.5, 85.4, 88.5,
                91.5, 94.8, 97.4,100.0,103.5,
               107.2,110.9,114.8,118.8,123.0,
               127.3,131.8,136.5,141.3,146.2,
               151.4,156.7,159.8,162.2,165.5,
               167.9,171.3,173.8,177.3,179.9,
               183.5,189.9,186.2,192.8,196.6,
               199.5,203.5,206.5,210.7,218.1,
               225.7,229.1,233.6,241.8,250.3,
               254.1};
uint8_t DCS[83] = {			// 表里的数字表示八进制的3位数字
               '023','025','026','031','032','043','047','051',...
               '054','065','071','072','073','074','114','115',...
               '116','125','131','132','134','143','152','155',...
               '156','162','165','172','174','205','223','226',...
               '243','244','245','251','261','263','265','271',...
               '306','311','315','331','343','346','351','364',...
               '365','371','411','412','413','423','431','432',...
               '445','464','465','466','503','506','516','532',...
               '546','565','606','612','624','627','631','632',...
               '654','662','664','703','712','723','731','732',...
               '734','743','754'};
*/

typedef struct
{
	uint16_t ct_day		: 5;
	uint16_t ct_month	: 4;
	uint16_t ct_year	: 7;		// org.2000
}COMPRESSED_DATE;

typedef struct
{
	uint32_t rtc_second : 5;
	uint32_t rtc_minute : 6;
	uint32_t rtc_hour : 5;
	uint32_t rtc_day : 5;
	uint32_t rtc_month : 4;
	uint32_t rtc_year : 7;		// org.2000
}COMPRESSED_RTC_STRUCT;


// 存储逻辑：每次通话的保存地址均从被4096整除的地址开始，并按下述格式存储；
// 录音格式：VOICE_RECORDER_HEADER_TYPEDEF + n * 9B(录音内容/码字) + 9个0xff(表示录音结束；跨区时可能不足9个0xff，此时要先判断下一个扇区起始位置的flag是否为数据有效，有效则同样表示录音结束)
#define RECORD_FLAG_LENGTH					8
typedef struct
{
	uint8_t  record_flag[RECORD_FLAG_LENGTH];	// 8个0表示后续内容为有效录音数据
	COMPRESSED_RTC_STRUCT record_time;			// 录音开始时间
	uint32_t id_be_called;						// 被叫号；bit28：1-单呼，0-组呼；bit31：1-本机主叫，0-被叫
	uint32_t id_caller;							// 主叫号
	uint32_t id_speaker;						// 讲话人号码
}VOICE_RECORDER_HEADER_TYPEDEF;


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////// 运行时参数相关数据结构 /////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef struct
{
	uint8_t voice_mute 		: 1;			/*设备全局静音设置：0-关闭；1-喇叭静音*/
	uint8_t voice_crypto	: 1;			/*设备话音/数据加密设置：0-关闭；1-启用加密功能*/
	uint8_t sdcard_disable	: 1;			/*是否禁止SD卡检测: 0-允许；1-禁止*/
	uint8_t squlch_door_open: 1;			/*是否开启静噪门: 1-关闭；0-开启*/
	uint8_t signaling_tone	: 1;			/*是否允许输出信令音(模拟集群下): 1-允许；0-禁止*/
	uint8_t voice_reserved	: 3;
}VOICE_PARA_TYPEDEF;

typedef struct
{
	uint8_t tip_self 		: 4;			/*本机提示音，范围0-9，默认4（0-静音；9-最大）*/
	uint8_t tip_earphone	: 4;			/*耳机提示音，范围0-9，默认4（0-静音；9-最大）*/
}TIP_SOUND_TYPEDEF;

typedef struct
{
	uint8_t mic_gain 		: 3;			/*麦克风增益控制，分0-4级；分别对应（大概）5dB、10dB、12dB、15dB和20dB*/
	uint8_t sound_gain		: 5;			/*话音增益控制，分0-9级（0-静音，9-最大）*/
}VOICE_GAIN_TYPEDEF;

typedef struct
{
	uint8_t pwr_gps 		: 1;			/*GPS开关：0-关闭；1-开启*/
	uint8_t pwr_bt			: 1;			/*蓝牙开关：0-关闭；1-开启*/
	uint8_t gps_type		: 2;			/*1-GPS only; 2-BD only; else-DUAL*/
	uint8_t batt_type		: 1;			/*0-normal battery; 1-low temp battery*/
	uint8_t pwr_wifi		: 1;			/*WIFI开关：0-关闭；1-开启*/
	uint8_t pwr_reserved	: 2;
}POWER_CTRL_TYPEDEF;

#define VOCODER_TYPE_Q_AMBE		0
#define VOCODER_TYPE_Q_NVOC		1
#define VOCODER_TYPE_Q_SELP1200	2
#define VOCODER_TYPE_Q_SELP600	3
#define VOCODER_TRANS_P2N	1
#define VOCODER_TRANS_N2P	2/*3 is same effect*/
typedef struct
{
	uint8_t vocoder_type 	: 1;			/*0-ambe; 1-nvoc*/
	uint8_t vocoder_speed	: 1;			/*0-2400; 1-2200*/
	uint8_t vocoder_agc		: 1;			/*0-off;  1-on*/
	uint8_t vocoder_ns		: 1;			/*0-off;  1-on*/
	uint8_t vocoder_type_q	: 2;			/*Qmode use it only; 0-abme,1-nvoc,2-selp1200,3-selp600*/
	uint8_t reserved		: 2;
}VOCODER_SETUP_TYPEDEF;

typedef struct
{
	uint8_t lcd_reverse 	: 2;			/*LCD反转显示：0-正常显示；1-反转显示；2-旋转90°显示（暂不支持）；3-旋转270°显示（暂不支持）*/
	uint8_t lcd_turnoff		: 6;			/*屏幕背光：2~20(彩屏设备为0~240)：背光关闭时间，单位为秒，超出此范围时将使用默认值20(彩屏设备为240); 彩屏设备的高2bit放在reserved9的bit3-2*/
}LCD_CTRL_TYPEDEF;

typedef struct
{
	uint8_t prj_permit 		: 1;			/*是否允许打开工程菜单（按#4#）：0-禁止；1-允许*/
	uint8_t conv_position 	: 1;			/*直通和常规下被叫允许显示相对位置信息：0-禁止；1-允许*/
	uint8_t dial_background	: 1;			/*数字集群下允许拨背景组号码进行呼叫：0-禁止；1-允许*/
	uint8_t dial_ind_trunk	: 1;			/*数字集群下禁止拨单呼号码进行呼叫：0-允许；1-禁止*/
	uint8_t dial_ind_conv	: 1;			/*数字常规下禁止拨单呼号码进行呼叫：0-允许；1-禁止*/
	uint8_t crc_filter		: 1;			/*0-不管CRC是否正确都传给协议栈；1-将CRC错误的空口数据帧丢弃；*/
	uint8_t no_forwarding	: 1;			/*0-运行发射；1-禁止发射(手台)/禁止转发(基站)*/
	uint8_t reserved		: 1;
}PROJECT_MODE_PERMIT_TYPEDEF;

#define TIP_CONFIG_MASK_KEY				0x01
#define TIP_CONFIG_MASK_MSG				0x02
#define TIP_CONFIG_MASK_CALL_ESTABLISH	0x04
#define TIP_CONFIG_MASK_PTT_AUTH		0x08
#define TIP_CONFIG_MASK_TALK_FINISH		0x10
#define TIP_CONFIG_MASK_CALL_FINISH		0x20
#define TIP_CONFIG_MASK_LOW_BATTERY		0x40
#define TIP_CONFIG_MASK_LOGOUT_BASE		0x80
typedef struct
{
	uint8_t tip_key			: 1;					// 按键提示音: 0-enable; 1-disable
	uint8_t tip_message		: 1;					// 收发短信提示音
	uint8_t tip_call_establish	: 1;				// 通话建立提示音(被拉入呼叫)
	uint8_t tip_ptt_auth	: 1;					// PTT授权提示音((PTT按下后)允许讲话)
	uint8_t tip_talk_finish	: 1;					// 讲话结束提示音(当前讲话人松开PTT后)
	uint8_t tip_call_finish	: 1;					// 通话结束提示音(拆线)
	uint8_t tip_low_battery	: 1;					// 低电量检测
	uint8_t tip_log_out		: 1;					// 脱网提示音
}TIP_SOUND_CONFIG_TYPEDEF;

typedef struct
{
	uint32_t enable			: 1;					// 是否启用省电模式: 0-禁用，1-启用
	uint32_t wake			: 5;					// 工作时隙数
	uint32_t sleep			: 10;					// 睡眠时隙数
	uint32_t keep			: 16;					// 通话结束后再次开启省电模式延时时隙数
}SAVE_POWER_CONFIG_TYPEDEF;

typedef struct
{
	uint32_t vad_sensitivity	: 3;				/*声控(主控)灵敏度控制，值越大灵敏度越低*/
	uint32_t use_dynamic_gps	: 1;				/*为1时，将stack_conts_gps[0/1]替换掉写频的STACK_CONTS_NGPSU/STACK_CONTS_NDIST再配置CONTS参数*/
	uint32_t remote_stun		: 1;				/*自组网遥晕*/
	uint32_t remote_kill		: 1;				/*自组网遥毙*/
	uint32_t distance_mode		: 1;				/*方位显示模式：0-显示方位，1-显示方位角*/
	uint32_t disable_dynamic_gps: 1;				/*为1时，禁用动态GPS配置（一直使用写频参数）*/
	uint32_t locater_machine	: 1;				/*为1时，表示此设备为定位设备；0为普通设备*/
	uint32_t local_id_at_standby: 1;				/*为1时，待机界面显示本机个号；0：显示守候组组号*/
	uint32_t reserved			: 22;
}MISC_RUNTIME_CONFIG;

#define WATCH_GROUP_EXT_INDEX_NUM	6
typedef struct	// 128B
{
	uint8_t						rf_power;			/*byte0* 射频功率设置：【车机：0-2W; 1-5W; 2-10W; 3-15W; 4-20W; 5-25W，不允许写入其他值】【手台：0-低功率；1-高功率；2-功率自动控制】*/
	VOICE_PARA_TYPEDEF			voice_para;			/*byte1*/
	TIP_SOUND_TYPEDEF			tip_sound;			/*byte2*/
	VOICE_GAIN_TYPEDEF			self_gain;			/*byte3* 本机话音增益设置*/
	uint8_t						language_para;		/*byte4* 语言设置：0-中文；1-英文*/
	POWER_CTRL_TYPEDEF			pwr_ctrl;			/*byte5*/
	uint8_t						active_zzwpro_group_book;	/*byte6* 当前使用的是哪个用户组群（仅XV自组网下使用）*/
	uint8_t						work_mode;			/*byte7* 工作模式：1-数字常规；2-数字集群(PDT)；3:模拟集群(MPT)；4:模拟常规；5:自组网；禁止写入其他值*/
	LCD_CTRL_TYPEDEF			lcd_ctrl;			/*byte8*/
	uint8_t						reserved9;			/*byte9* 原来用于LCD反转控制，目前用bit1-0来保存彩屏车机主题/TFT方屏配色方案，bit3-2保存背光时间的bit7-6*/
	uint8_t						ctrl_tscc_mode;		/*byte10* 控制信道搜索模式：0-短搜索；1-长搜索；2-指定搜索信道；3-手工输入信道号；禁止写入其他值；注：设置为“指定”时，必须同步设置PROTOCOL_STACK_TYPEDEF中的ctrl_index，表明锁定的信道是ctrl_table中的哪一项（index不能超出ctrl_table有效项范围）*/
	uint8_t						active_group_book;	/*byte11* 当前使用的是哪个用户组群（仅数字集群下使用）*/
	uint8_t						noise_threshold;	/*byte12* 模拟常规下的静噪门限：【手台=130+输入值（输入范围-129~0）：比如用户需要设置门限值为-117dB，那么他应该在输入框中输入-117，但写频时必须写入130+(-117)=13】【车机：未使用】*/
	PROJECT_MODE_PERMIT_TYPEDEF	project_mode_permit;/*byte13*/
	VOICE_GAIN_TYPEDEF			earphone_gain;		/*byte14* 耳机话音增益设置*/
	uint8_t						active_group_ctrl;	/*byte15* 当前使用的是哪个控制信道组群（仅数字集群下使用）*/
	uint8_t 					grp0[5];			/*byte16-20* 各模式的守候组编号(0-4: 数字常规/数字集群/模拟集群/模拟常规/自组网)，实际内容从号码薄中查询*/
	uint8_t 					reserved8[6];		/*byte21-26; 21-23:app pwd*/
	VOCODER_SETUP_TYPEDEF		zzw_vocoder;		/*byte27*/
	VOCODER_SETUP_TYPEDEF		pdt_vocoder;		/*byte28*/
	uint8_t 					password[3];		/*byte29-31*/
	TIP_SOUND_CONFIG_TYPEDEF	tip_config;			/*byte32提示音使能配置*/

	uint8_t 					stack_conts_gps[2];	/*byte33-34*/
	uint8_t						a1_watch_abc;		/*byte35,20220426增加但暂不使用*/
	uint8_t 					aes_key_index;		/*byte36*/
	uint8_t						utc_regulator;		/*byte37 bit7：0-北京时间，1-按设定；bit6-5：保留；bit4：0-东区，1-西区；bit3-0：时区 */
	uint8_t 					grp0_ext[WATCH_GROUP_EXT_INDEX_NUM];		/*byte38-43* K1波段开关模式的守候组编号：
																0: 单频数字直通(超视距Q)
																1: 六信道直通集群(V0，收N发N)
																2: 单频自组网3跳2信道(V3 带中继，收N发N)
																3: PDT自组网3跳1信道中继电台(PDT E模式)
																4: 模拟直通(替换为单频自组网6跳1信道？)
																5: 群集电台(NQ中继，收N发Q)
													*/
	uint8_t 					reserved8_2[48 - 38 - WATCH_GROUP_EXT_INDEX_NUM];/*byte44-47*/

	uint16_t					ctrl_index[2];		/*byte48-51* 集群模式下指定的控制信道在信道表中的索引(0-63)(0/1: PDT/MPT)*/
	uint16_t					priro_ctrl[2];		/*byte52-55* 集群模式下优先查找控制信道索引(0/1: PDT/MPT)*/
	uint16_t					adc38p4;			/*byte56-57*/
	uint16_t					reserved16[16 - 5];	/*byte58-79*/

	uint32_t					stack_mode[3];		/*byte80-91* 各模式下协议栈的MODE配置字(注: 将其放置在这里，是因为手台需要在线修改)(0-2: 数字常规/数字集群/模拟集群；为保持兼容性，目前仅用1和2来表示数字或模拟)*/
	uint32_t					stack_xmode;		/*byte92-95* XV模式下协议栈的扩展配置字(bit10-Q模式；bit9-跨区管理；bit8-N模式；bit7-V+模式；bit6-G链路；bit5-P链路；bit4-A链路；bit3-E链路；bit2-高频；bit1-基站模式；bit0-自组网模式) */
	SAVE_POWER_CONFIG_TYPEDEF	save_power;			/*byte96-99*/
	MISC_RUNTIME_CONFIG			misc_runtime_config;/*byte100-103;20201021 define but not use*/
	uint32_t					reserved32[12 - 6];	/*byte104-127*/
}RUNTIME_PARAS_TYPEDEF;


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////// 设备信息数据结构 ////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef struct
{
	uint8_t ver_major;
	uint8_t ver_minor;
}VERSION_TYPEDEF;

typedef struct	// 32B
{
	COMPRESSED_RTC_STRUCT	mcu_bl_compile;		// Bootloader编译时间

	VERSION_TYPEDEF			mcu_hardware_ver;
	VERSION_TYPEDEF			mcu_soft_ver;
	COMPRESSED_RTC_STRUCT	mcu_compile;

	VERSION_TYPEDEF			stack_function;		// 功能码显示格式：xxxx  - ver_major.高4位/ver_major.低4位/ver_minor.高4位/ver_minor.低4位
	VERSION_TYPEDEF			stack_soft_ver;		// 版本号显示格式：xx.xx - ver_major.高4位/ver_major.低4位.ver_minor.高4位/ver_minor.低4位
	COMPRESSED_RTC_STRUCT	stack_compile;

	// 手台使用以下两个成员
	VERSION_TYPEDEF			dsp_soft_ver;
	COMPRESSED_DATE			dsp_compile;
	// 车机使用以下两个成员
	VERSION_TYPEDEF			cheji_dsp_soft_ver;	// 显示格式：x.x.x；major.高4位-major; major.低4位s-sub; minor-minor
	VERSION_TYPEDEF			cheji_fpga_soft_ver;// same as above
	uint32_t				reserved;
}DEVICE_INFORMATION;
/* 注：
	从协议栈中读取的原始功能码格式：xxxx - byte0.高4位/byte0.低4位/byte1.高4位/byte1.低4位
						版本信息：xx.xx  - byte0.高4位/byte0.低4位/byte1.高4位/byte1.低4位
	从手台DSP读取的原始版本信息：bit15-13：年（0-7），start at 2014(e.g. 0==2014)
							 	bit12-9： 月
								bit8-4：  日
								bit3-2：  major：1-ambe；2-selp；3-nvoc；else-invalid
								bit1-0：  minor：0-350M；1-150M；2-400M；3-ZZW
	从车机读取的原始版本(dsp/fpga)信息：byte0.高4位-major; byte0.低4位-sub; byte1-minor
*/


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////// 开机图片数据头数据结构 /////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef struct	// 64B
{
   uint8_t		scan;		// 0x00
   uint8_t		gray;		// 0x01
   uint16_t		w;			// 0x0080
   uint16_t		h;			// 0x0035
   uint8_t		dev_name[18];
   uint8_t		dev_type[MAX_USER_NAME_LEN];
   uint8_t		reserved[64 - 40];
}HEADGRAY;
// 补充说明:(1) 开机图片数据使用Image2Lcd 2.8生成，生成参数为: 水平扫描/单色/包含图像头数据；前6字节即为上述数据结构，也用于判定图片是否有效；
//			(2) 在STATIC_PARAMETERS_TYPEDEF.HEADGRAY中，前6字节必须按HEADGRAY注释的内容填入，dev_name为以0结束的可显示字符串，
//				作为设备默认显示的名字（当未选择有效的守候组时显示），同时也用于显示设备名称（显示为“设备信息->用户名称”）
//			(3) dev_name长度不能超过16个字码（一个ASCII算一个字码，汉字算2个），建议不大于15个，以避免显示不全

typedef struct
{
   unsigned char scan;
   unsigned char gray;
   unsigned short w;
   unsigned short h;
   unsigned char is565;
   unsigned char rgb;
}HEADGRAY_COLOR;

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////// 协议栈数据结构 /////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define ESN_LENGTH						16
#define CONTS_LENGTH					24
#define CTRL_TABLE_TOTAL_ITEM			64
#define CTRL_TABLE_TOTAL_PAGE			4
#define CTRL_TABLE_TOTAL_ITEM_MAX		(CTRL_TABLE_TOTAL_ITEM * CTRL_TABLE_TOTAL_PAGE)
#define CTRL_TABLE_TOTAL_ITEM_EXT1		0x4558F000
#define CTRL_TABLE_TOTAL_ITEM_EXT2		0x4300E000

#define GROUP_TRUNK_TOTAL_PAGE			6
#define GROUP_TRUNK_TOTAL_ITEM_EXT1		0xFF58
#define GROUP_TRUNK_TOTAL_ITEM_EXT2		0x4300

#define ZZWPRO_RX_LIST_EXT_FLAG			0xFE00

#define GROUP_TRUNK_NUM_OF_EXTn			((MAX_USER_NAME_LEN - sizeof(uint16_t) * 2) / sizeof(uint16_t) + MAX_TRUNK_OF_ONE_GROUP - 2)
#define GROUP_TRUNK_TOTAL_ITEM_MAX		((MAX_TRUNK_OF_ONE_GROUP - 2) + (GROUP_TRUNK_TOTAL_PAGE - 2) * GROUP_TRUNK_NUM_OF_EXTn + (GROUP_TRUNK_NUM_OF_EXTn + 2))

#define GROUP_TRUNK_NUM_OF_XV_EXTn		(MAX_TRUNK_OF_ONE_GROUP - 2)
#define GROUP_TRUNK_TOTAL_ITEM_XVMAX	((GROUP_TRUNK_TOTAL_PAGE - 1) * GROUP_TRUNK_NUM_OF_XV_EXTn + MAX_TRUNK_OF_ONE_GROUP)

typedef struct
{
	uint8_t  tx_sub;					// 发射亚音：bit7=0表示CTCSS，范围0-50；bit7=1表示DCS，范围0-82
	uint8_t  rx_sub;					// 接收亚音（同上）
	uint8_t	 base_noise_threshold;		// 模拟常规基站的静噪门限：=130+输入值（输入范围-129~0）：比如用户需要设置门限值为-117dB，那么他应该在输入框中输入-117，但写频时必须写入130+(-117)=13*/
	uint8_t  base_squlch_door_open	: 1;// 模拟常规基站是否开启静噪门: 1-关闭；0-开启
	uint8_t  reserved				: 7;
}MPT_CONV_BASE_TYPEDEF;

typedef struct	// 128B
{
	uint32_t id;					// 个人号码MSI(Mobile Subscriber Identity), 24bit(high 8bit == 0)
	uint32_t stack_emergency_id;	// 紧急呼叫ID，只需对低3字节以及单/组呼标志位赋值即可；注：目前仅手台有紧急呼叫按键，车台暂不支持
	uint32_t reserved32[6];			// ZZWPRO base: U32[0]:network addr(H3B)+canid(1B); U32[1]:调度台ID; U32[2]:NVQ基站目标CAN ID，暂用低3B配置3个ID，最高字节保留为0
	uint8_t  conts[CONTS_LENGTH];	//
	uint8_t  reserved8[30 - CONTS_LENGTH];
	uint8_t  rssi_forward_th;		// 基站(常规)模式下表示常规基站转发的RSSI门限，保存的实际值为门限+130
	uint8_t  dqi_forward_th;		// 基站(常规)模式下表示常规基站转发的DQI门限，保存的实际值即为门限值
	// 上面成员占用64B

	uint16_t province_1343fin;		// PDT:分级组呼的省级区号; MPT:1343编码时的自身队号
	uint16_t ministry_1343fgn;		// PDT:分级组呼的公安部级区号; MPT:1343编码时发出的组呼队号
	/* 频率计算公式: 收频率 = 信道0的开始频率 + 信道号 * 频率步进; 发频率 = 收频率 + 收发频率间隔 */
	uint16_t freq_base_para;		// 信道0的开始频率参数 = 频率 * 80 (如 358.0MHz = 28640)(分PDT和MPT模式独立对应)
	uint16_t freq_step_para;		// 频率步进参数 = 步进频率 * 80 (如 12.5KHz = 1)(分PDT和MPT模式独立对应)
	int16_t  freq_gap_para;			// 收发频率间隔参数 = 间隔频率 * 80 (如 -10MHz = -800)(分PDT和MPT模式独立对应)
	uint16_t ctime;					// 内置通话时间，范围30～240s，缺省180s
	uint16_t nid;					// 网络ID，格式同系统码(无效的CHx=0x00000000)，用于背景扫描控制信道时匹配基站系统码
	uint16_t nidm;					// NID的掩码，如只比较NID的高8b，NIDM=0xFF00
	uint16_t chl;					// 起始信道，背景扫描控制信道起始信道号
	uint16_t chh;					// 结束信道，背景扫描控制信道结束信道号。若CHL=CHH=0，表示只搜索基站控制信道，不进行背景扫描）
	uint16_t color_code;			// 色码，bit15-12有效
	uint16_t rcode;					/* MPT编码方式(PDT下忽略):	b15=0：1343编码，b12~0组分界，大于组分界的ID为组ID；
																b15=1：CPS编码，b11~8=cpsl，b7~4=cpsm，b3~0=cpss */
	uint16_t freq_base_admit;
	int16_t  freq_gap_admit;
	uint16_t conv_base_paras;		// 仅基站用于保存MPT_CONV_BASE_TYPEDEF的前2字节内容
	uint16_t reserved16[32 - 15];
}PROTOCOL_STACK_TYPEDEF;
/*
(0) mpt1343_self_fin - freq_gap_para这5个成员在PDT和MPT模式下的定义是不同的；未定义或保留的空间写频软件禁止改写）
(1) PDT模式下mode各个bit定义:
		1-0	优先级
		2	PDT/DMR
		3	遥晕
		4	遥毙
		5	GPS上拉
		6	PTT授权
		7	PATCS
		8	发送时隙2音频
		9	接收时隙2音频
		10	允许组呼
		11	允许单呼
		12	允许短数据呼叫
		13	允许状态呼叫
		14	允许全系统呼叫
		15	允许市话呼叫
		16	允许紧急呼叫
		17	反向鉴权
		18	CPS信道方案
		19	组附着
		20	信令监测（这个位先不要开放出去）
		21	MOTO
		22	高级鉴权
		23	监听：选择此选项后，电台进入监听模式，自动响应值守基站下的所有组呼，不论该组是否登记。
				  注意：手台监听有两个级别：1 选择协议栈的“监听”选项，此为高优先级全局选项，选中后可监听值守基站下的所有呼叫，且可以正常呼叫当前守候组；
				  							2 在组群中加入一个特殊组，组属性为“非背景/集群/组呼”，且其ID为0（实际使用中将USER_BOOK.id置为0即可），这样选中此组后，手台会将本组群中的所有组都设为响应组进行监听，且不能发起呼叫；
				  							说明：级别1可监听的范围要比级别2的大；级别2中的组群登记的组如果在值守基站中没有登记，那么将不能监听
		31	模拟电台
(2) PDT模式下conts各字节定义（后面数字为系统定义的默认值）:
		conts[0] = 10;	// 随机访问超时时间（2～60s，缺省10s）
		conts[1] = 5;	// 控制信道回落时间（1～15s，缺省5s）
		conts[2] = 10;	// 话音呼叫超时时间（4~60s，缺省10s）
		conts[3] = 5;	// 数据呼叫超时时间（2~20s，缺省5s）
		conts[4] = 5;	// 被叫超时时间（2~60s，缺省5s）
		conts[5] = 10;	// 振铃超时时间（2~30s，缺省10s）
		conts[6] = 2;	// 去登记超时时间（1～3s，缺省2s）		-> tGPS：1B GPS自动上传周期，0取消上传(20180914)
		conts[7] = 60;	// 单次PTT最大时间（10~60s，缺省60s）
		conts[8] = 3;	// 话音信道回落时间（0~20s，缺省3s）
		conts[9] = 6;	// 呼叫重发次数（1~10，缺省6）
		conts[10] = 10;	// 紧急呼叫重发次数（1~20，缺省10）
		conts[11] = 4;	// 撤线信令数（1~5，缺省4）
		conts[12] = 2;	// 系统码容错（1~3，缺省2）
		conts[13] = 50;	// 优先登录信号阈值上限（RSSI）
		conts[14] = 20;	// 优先登录信号阈值下限（RSSI）
		conts[15] = 13;	// 静噪门阈值（RSSI）
		conts[16] = 3;	// 控制信令发送超时时间（1~10s，缺省3s）
		conts[17] = 3;	// 控制信令发送重发次数（1~10，缺省3）
		conts[18] = 6;	// 等待应答时隙数（4~15，缺省6）
		conts[19] = 1;	// LC信令发送次数（1~4，缺省1）
		conts[20] = 1;	// rHome（归属基站守候阈值RSSI）
		conts[21] = 1;	// rRoar（漫游基站守候阈值RSSI）
		conts[22] = 1;	// 保留
		conts[23] = 1;	// 保留->nDIST:自动上传GPS移动距离阈值

(3)MPT下rcode定义：
	b15=0：1343编码，b12~0组分界，大于组分界的ID为组ID；
	b15=1：CPS编码，b11~8=cpsl，b7~4=cpsm，b3~0=cpss
		一级队数量=cpsl，二级队数量=cpsm-cpsl，三级队数量=cpss-cpsm，
		四级队数量=10-cpss，如果全部为一级队，则：cpsl=cpsm=cpss=10
*/

#define Q_MODULE_ID_STARTED			8
#define ZZWPRO_Q_TX_TOTAL_CHAN		8
#define ZZWPRO_Q_MAX_NUMBER			6

typedef struct
{
	uint32_t time_gap : 6;			// 两个电台上报位置的时间间隔，单位为秒，填0表示禁止本机上报
	uint32_t dev_total_num : 10;	// 整个网络的电台数量，取值0-1023, 填0表示禁止本机上报
	uint32_t dev_sn : 10;			// 本机上报位置的流水号(整点开始计时，表示本机是第几个上报)，取值0-1023，其值不能大于网络的电台数量
	uint32_t reserved : 6;
}ZZW_GPS_POLL_PARAS_TYPEDEF;

typedef struct	// 128B
{
	uint32_t id;					// 个人号码MSI(Mobile Subscriber Identity)，24bit；最高字节的bit7：0-正常模式，1-监听模式；bit6：0-不允许单呼；1-允许单呼；bit5: 0-严格ID匹配；1-宽松ID匹配
	uint32_t id_2ptt;				// 2PTT号码(注意可以设置单呼或组呼号，用最高字节的bit4标识)
	ZZW_GPS_POLL_PARAS_TYPEDEF gps_poll;
	uint16_t nvq_tx_freq[ZZWPRO_Q_TX_TOTAL_CHAN];		// NVQ模式下Q的发射频点信道号（4个）；改为从心跳获取频点后不再使用
	uint32_t reserved32[16 - 3 - ZZWPRO_Q_TX_TOTAL_CHAN / 2];
	uint16_t ctime;					// bit7-0:内置通话时间；bit10-8:LC帧发送个数；bit13-11:前置/4U帧发送个数；注:当前置/4U和LC均设为0时，强制为前置/4U为5个，LC为2个; bit15-14: 拆线帧发送个数(实际发送个数为此值加1)
	uint16_t reserved16[20 - 1];
	uint8_t  adhoc_win;				// 判选周期, 以60ms为单位，最小值为1
	uint8_t  rc_max;				// 强插周期, 以次为单位(建议大于3); 为0时表示禁用强插功能
	uint8_t zzw_default_pos[9];		// 自组网手台默认自身位置，内容为GPS_STRUCT_TYPEDEF数据结构中的低9字节(gps_state字段只需设置bit&bit2即可)
	uint8_t zzw_retry_paras;		// bit3-0:呼叫失败重试周期(周期=360+period*120ms,建议默认值6); bit7-4:呼叫失败重试次数(0-15)
	uint8_t zzw_proteced_paras;		// bit3-0:保护时间间隔(间隔=120+period*60ms); bit7-4:心跳帧预保护时间(时间=240+period*60ms,建议默认值8)
	uint8_t zzw_meeting_paras;		// bit3-0:本机参与的会议号(写频界面显示1-6，实际写入0-5); bit4: reserved; bit6-5:会议帧发送个数(实际发送个数为此值加1); bit7: 0-普通设备; 1-指挥设备
	uint8_t  reserved8[24 - 14];
}PROTOCOL_ZZW_TYPEDEF;

typedef struct
{
	uint32_t remote_config_enable		: 1;									// 1-使能远程配置基站跳数
	uint32_t reserved					: 31;
}MISC_STATIC_CONFIG_ZZWPRO;

typedef struct	// 32B
{
	uint8_t  conts[CONTS_LENGTH];	//
//	uint8_t  reserved[4];
	MISC_STATIC_CONFIG_ZZWPRO misc_config_zzwpro;
	uint32_t para_valid_flag;		// [0x55352d96]USER_FLASH_SAVE_FLAG参数有效标识(写频时写频软件写入)
}PROTOCOL_ZZWPRO_TYPEDEF;


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////// 用户定义快捷呼叫号码列表 ////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define MAX_CUSTOM_SHORTCUT_ID_NUMBER	10
typedef struct	// 44B
{
	uint32_t reserved;
	uint32_t shortcut_bind_id[MAX_CUSTOM_SHORTCUT_ID_NUMBER];					// 暂时只用于和预置状态信息绑定，用于长按0-9进行快捷呼叫或发送状态短信:
																				// 其最高字节bit7(即背景标志位)为0时，表示不使能快捷呼叫(长按数字键无动作)，反之使能；使能后，根据最高字节bit3/2(动态/普通组标志位)确定呼叫类型:
																				// bit3/2=00: 呼叫此号码; =10: 通过状态短信发送此状态码到此号码；01-切换组群或守候组(次高字节决定切组群(==0)或守候组(==1)或号码薄用户(==2，此时用最低2B表示索引)；最低字节决定目的组群或守候组索引)；11-保留
}CUSTOM_SHORTCUT_ID_TABLE;


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////// 短信息相关数据结构 ///////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////// 预置短信息/状态信息数据结构 //////////////////////////////////////////////////////////
#define MAX_MESSAGE_PRESET_NUMBER	30
#define MAX_MESSAGE_PRESET_LENGTH	48	/* PDT下每条预置短信最多46个字码（此处1个汉字或1个ASCII字符均算2个字码）；MPT下最多24个ASCII字符（不知道其他手台能否支持中文）*/

typedef struct	// 1444B
{
	uint32_t reserved;
	uint8_t  msg_content[MAX_MESSAGE_PRESET_NUMBER][MAX_MESSAGE_PRESET_LENGTH];	// 预置短信: 为预置短信内容(字符串)；如果为状态短信，则每项的内容的BYTE0用于存储状态代码，剩余字节用于存储对应的状态说明(字符串)
																				// 注意在此数据结构中，字符串必须连续(两条msg_content中间不能有空字符串，否则认为预置消息结束；预置短信字符串从第0字节开始，预置状态消息的从第1字节开始)
}USER_MESSAGE_PRESET;


//////////////////////////////////////////////////////////////// 用户短信息数据结构 ///////////////////////////////////////////////////////////////
#define MAX_MESSAGE_NUMBER			40
#define MAX_MESSAGE_SENT_NUMBER		16
#define MAX_NORMAL_MESSAGE_LENGTH	MAX_MESSAGE_PRESET_LENGTH
#define MAX_MESSAGE_CONTENT_LENGTH	1004
#define	LONG_MESSAGE_STRUCT_LENGTH	1024

typedef struct
{
	uint32_t msg_type		: 3;					// 0-普通短消息；1-状态信息；2-长短信；3-普通加密短信(注意: 只是接收时是加密的，存储内容已解密)
	uint32_t reserved		: 29;
}MESSAGE_MISC_TYPEDEF;

// 此数据结构为存储于spi flash中的实际信息内容，从0x6000开始，最多80条，共占用40KB
typedef struct	// 1024B
{
	COMPRESSED_RTC_STRUCT	msg_time;				// 收到短消息时间
	uint32_t 				id_caller;				// 主叫ID
	uint32_t				id_called;				// 被叫ID
	uint8_t					msg_content[MAX_MESSAGE_CONTENT_LENGTH];// 短消息内容，字符串
	MESSAGE_MISC_TYPEDEF	msg_misc;
	uint32_t				msg_valid_flag;			// [0x55354d00]MESSAGE_INIT_FLAG: 短信保存有效标识(由设备主控软件修改)
}MESSAGE_STRUCT;

typedef struct
{
	uint32_t msg_addr		: 24;					// 指向实际短信息存储地址(即: &MESSAGE_STRUCT)
	uint32_t msg_is_new		: 1;					// 0-已读短信；1-未读短信
	uint32_t msg_reserved	: 7;
}MESSAGE_INDEX_TYPEDEF;

// 实际能查看管理的短信息索引，存放于运行时参数中
typedef struct	// 168B
{
	uint16_t				message_total;
	uint16_t				message_index;
	MESSAGE_INDEX_TYPEDEF	msg_index[MAX_MESSAGE_NUMBER];
	uint32_t				msg_valid_flag;			// [0x55354d00]MESSAGE_INIT_FLAG: 短信索引表有效标识(由设备主控软件修改)
}MESSAGE_TABLE_TYPEDEF;

typedef struct	// 64B
{
	COMPRESSED_RTC_STRUCT	msg_time;				// 收到短消息时间
	uint32_t 				id_caller;				// 主叫ID
	uint32_t				id_called;				// 被叫ID
	MESSAGE_MISC_TYPEDEF	msg_misc;
	uint8_t  msg_content[MAX_MESSAGE_PRESET_LENGTH];
}MESSAGE_SENT_STRUCT;

typedef struct	// 1028B
{
	uint16_t message_total;
	uint16_t message_index;
	MESSAGE_SENT_STRUCT message[MAX_MESSAGE_SENT_NUMBER];
}MESSAGE_SENT_TABLE_TYPEDEF;


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////// 呼入/呼出列表数据结构 /////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define MAX_CALLING_IN_RECORD		20
#define MAX_CALLING_OUT_RECORD		10

typedef struct
{
	COMPRESSED_RTC_STRUCT time;
	uint32_t id_be_called;
	uint32_t id_caller;
	uint16_t times;
	uint16_t reserved;
}CALL_RECORD;

typedef struct	// 492B
{
	uint16_t call_in_total;
	uint16_t call_in_index;
	CALL_RECORD call_in[MAX_CALLING_IN_RECORD];
	uint16_t call_out_total;
	uint16_t call_out_index;
	CALL_RECORD call_out[MAX_CALLING_OUT_RECORD];
	uint32_t calling_valid_flag;	// [0x55356200]ADDRESS_BOOKS_INIT_FLAG: 呼叫列表数据有效标识(由设备主控软件修改)
}CALLING_RECORD;


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////// 空口写频参数数据结构 //////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define MAX_AIR_CONF_BOOK	14
#define MAX_AIR_CONF_CTRL	8
typedef struct	// 416B
{
	USER_BOOK air_dynamic_reconf;				// 动态重组，ID为0时表示当前没有进行动态重组
	USER_BOOK air_dynamic_reconf2;				// 动态重组2
	USER_BOOK air_conf_book[MAX_AIR_CONF_BOOK];	// 空口写频参数，id为0表示此项为空
	uint32_t  air_conf_ctrl[MAX_AIR_CONF_CTRL];	// 空口写频控制信道，必须按序排列，中间不能留空
}AIR_RECONF_TYPEDEF;


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////// 按键功能重映射参数数据结构 ///////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define DMR_FUNC_CONFIG_CALL_ALERT		0x01
#define DMR_FUNC_CONFIG_EMERG_ALERT		0x02
typedef struct
{
	uint8_t call_alert		: 1;					// 是否允许显示呼叫提示界面
	uint8_t emergency_alarm	: 1;					// 是否允许显示紧急告警提示界面
	uint8_t tip_reserved	: 6;
}DMR_FUNC_CONFIG_TYPEDEF;

typedef struct
{
	uint8_t op_times		: 3;					// 延时(s，实际值=写入值+1)/连击次数(实际值=写入值+2)
	uint8_t reserved		: 1;
	uint8_t op_period		: 2;					// 连击速度: 0-快速；1-正常；2-慢速；3-很慢
	uint8_t op_type			: 2;					// 0-正常；1-延时；2-连击
}DEBOUNCE_KEY_TYPEDEF;

typedef struct	// 32B
{
	uint8_t 							vol_independent;	/*0-保持音量调节按键独立，此时左右键仅作为音量调节；1-音量调节按键映射为旋钮，此时在待机和呼叫界面下才作为音量调节（目前手台的默认操作）；2-左右键用作组切换 */
	uint8_t 							func_left;			/*键盘左键绑定功能设置*/
	uint8_t								func_right;			/*键盘右键绑定功能设置*/
	uint8_t								func_enter;			/*旋钮按键绑定功能设置*/
	uint8_t								func_middle;		/*键盘中键绑定功能设置*/
	uint8_t								func_ptt;			/*侧边PTT键（最上）绑定功能设置*/
	uint8_t								func_ptt2;			/*侧边2PTT（中间）键绑定功能设置*/
	uint8_t								func_return;		/*侧边返回键（最下）绑定功能设置*/
	uint8_t								func_shortcut;		/*保留，暂未开放此按键*/
	uint8_t								func_alarm;			/*顶部报警键绑定功能设置*/
	DMR_FUNC_CONFIG_TYPEDEF				dmr_func_config;	/*DMR功能项设置*/
	DEBOUNCE_KEY_TYPEDEF				func_debounce;
	uint8_t								zzw_func_ptt;		/*自组网PTT（侧边中）按键配置*/
	uint8_t								zzw_func_ptt2;		/*自组网侧边上按键配置*/
	uint8_t								zzw_func_return;	/*自组网侧边下按键配置*/
	uint8_t								reserved[32-15];
}KEY_FUNC_REMAP_TABLE_TYPEDEF;

// 按键功能绑定说明：将功能选项代码（BIND_FUNCTION_XXX）填入按键位（KEY_FUNC_REMAP_XXX），即可按下对应按键位的按键时，实现填入的功能
// 不同的按键位可以填入相同的功能选项代码，此时具有相同功能选项代码的按键会表现为相同功能
#define BIND_FUNCTION_VOLUME_DEC	12 /*功能选项：音量减小*/
#define BIND_FUNCTION_VOLUME_INC	13 /*功能选项：音量增大*/
#define BIND_FUNCTION_ENSURE		14 /*功能选项：确定/主菜单*/
#define BIND_FUNCTION_EDIT_MSG		25 /*功能选项：编辑短信*/
#define BIND_FUNCTION_PTT			16 /*功能选项：PTT键，发起呼叫*/
#define BIND_FUNCTION_PTT2			17 /*功能选项：2PTT键，优先时隙呼叫/紧急呼叫*/
#define BIND_FUNCTION_RETURN		18 /*功能选项：拆线/返回*/
#define BIND_FUNCTION_SHORTCUT		(b7b6)|19 /*功能选项：呼出快捷操作界面，此时b7b6定义：00-控制项(卫星开关、用户切换和信道切换)全开，01-仅卫星开关控制，10-仅信道切换，11-仅用户切换*/
#define BIND_FUNCTION_EMG_CALL		20 /*功能选项：对特定号码进行紧急呼叫*/
#define BIND_FUNCTION_SWITCH_MODE	(b7b6)|26 /*功能选项：当低6bit为26时，为快捷切换模式，此时b7b6定义：00-数字集群和数字常规间切换，01-数字集群和模拟集群间切换，10-数字集群和模拟常规间切换，11-数字常规和模拟常规间切换*/
#define BIND_FUNCTION_SCANNING		27 /*功能选项：快捷重新搜索控制信道*/
#define BIND_FUNCTION_PTT3			28 /*功能选项：3PTT键，NQ自组网发起Q呼叫*/


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////// 出厂参数(除ESN和开机图片外)数据结构 //////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

typedef struct
{
	uint32_t valid		: 8;		/* fixed 0x6c */
	uint32_t lf_hsb 	: 4;		/* 3 */
	uint32_t lf_msb 	: 4;		/* 5 */
	uint32_t lf_lsb		: 4;		/* 1 */
	uint32_t hf_hsb 	: 4;		/* 3 */
	uint32_t hf_msb 	: 4;		/* 6 */
	uint32_t hf_lsb		: 4;		/* 8 */
}BAND_RANGE_TYPEDEF;


#define MISC_CONFIG_FACTORY_DATA_MODULE		0x00000001
#define MISC_CONFIG_FACTORY_DATA_PCM_ENABLE	0x00000002
#define MISC_CONFIG_FACTORY_LR_REVERSE		0x00000004
#define MISC_CONFIG_FACTORY_ENABLE_CAN		0x00000008
#define MISC_CONFIG_FACTORY_BT_FUNCTION		0x00000010
#define MISC_CONFIG_FACTORY_SOLAR_CTRL		0x00000020
#define MISC_CONFIG_FACTORY_SWITCH_CTRL		0x00000040
#define MISC_CONFIG_FACTORY_HOOK_AS_KEY		0x00000080
#define MISC_CONFIG_FACTORY_DEV_NO_CCM		0x00000100
#define MISC_CONFIG_FACTORY_PPS_PE4			0x00000200
#define MISC_CONFIG_FACTORY_M1_DEVICE		0x00000400
#define MISC_CONFIG_FACTORY_BEIDOU_ONLY		0x00000800
#define MISC_CONFIG_FACTORY_GENERIC_BT		0x00001000
typedef struct	// 128B
{
	uint8_t		esn[16];
	int16_t		rssi_regulator;		// 场强校正值
	uint16_t	high_power_level;	// 高功率调制电平值
	uint16_t	low_power_level;	// 低功率调制电平值
	uint8_t		pdt_adhoc_setup;	// 双模手台工作模式：0x00或0xff-仅开启PDT模式； 0x01-仅开启自组网模式；else-PDT和自组网模式均开启
	uint8_t		crystal_bias;		// 本振偏置电压
	uint8_t		mod_att;			// 低功率功率衰减值
	uint8_t		rising_points;		// 发射输出功放上升控制寄存器，1-47
	uint8_t		falling_points;		// 发射输出功放下降控制寄存器，1-47
	uint8_t		oled_type;			// low 4bit: 0-旧1.5寸OLED; 1-新1.3寸蓝色OLED; 2-176x176 TFT; 3-176x220 TFT; 4-0.96寸OLED; 5-0.96寸彩屏; high 4bit:0-805V1 1/2/3...other 805 version
	BAND_RANGE_TYPEDEF band_range;
	uint32_t	misc_config_factory;// bit0:1-启用数传功能; bit1-使用外设PCM(bit0启用后此位才有意义)；bit2-52C方案(815基带板用于810C射频)；bit3:1-非基站设备启用有限的CAN功能；
									// bit4:1-启用蓝牙功能（当bit0也置位时，bit0优先）；bit5:1-控制太阳能板；bit6:1-控制开关板；bit7:1-821手咪；bit8:1-无CCM基站；
									// bit9:1-PPS接入到PE4（0为接入PB3）；bit10:1-M1电台；bit11:1-单北斗模式；bit12-通用蓝牙
	uint8_t		base_freq_offset;	// 基准偏移频率，0-250(MHz)；250以上保留（扩展备用）
	uint8_t		factory_reserved;
	int16_t		rssi_regulator_ex;	// 809第二路接收场强校正值
	uint8_t		mic_agc_noise;		// 麦克风自动增益控制噪声门限
	uint8_t		mic_agc_gain;		// 麦克风自动增益控制增益值
	uint8_t		factory_reserved2[2];
	uint8_t		p1_calibrator[64];	// P1磁力计/加速度计校准参数
	uint8_t		reserved2[124 - 44 - 64];
	uint32_t	factory_save_flag;	// [0x55352d96]USER_FLASH_SAVE_FLAG:参数有效标识(写频时写频软件写入)
}FACTORY_PARAS_TYPEDEF;

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////// DDS射频调谐参数(复用于STATIC_PARAMETERS_TYPEDEF.pll_tune_table) ////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define DSP_TUNE_TABLE_MAX_LENGTH	(LONG_MESSAGE_STRUCT_LENGTH * 2)	/* must <= sizeof(long_message_tmp_save)*/
typedef struct
{
	uint8_t rf_dds_tune_table[4];							// 固定填入字符串"xxxM"，且不用以空格结束; xxx为写频时写入的基准频率
	uint8_t rf_dds_tune[DSP_TUNE_TABLE_MAX_LENGTH - 4];		// 频率范围最大51M，51 / (0.0125 * 2) = 2040
}DSP_DDS_TUNE_TYPEDEF;


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////// 两点调射频调谐参数(复用于STATIC_PARAMETERS_TYPEDEF.pll_tune_table) ///////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define RF_2P_MAX_FREQ_SEGMENT			32
typedef struct	// 32B
{
	uint16_t freq;				// 起始频率 = 实际频率 * 80;其值为0时，表示表结束

	// reg0-2: tune parameters, auto calculate
	// reg3:   divider factor, auto fill

	// receive relevant registers
	uint16_t rx_v_preset4;		// 接收预置电压
	uint16_t rx_v_etf5;			// 接收电调滤波器电压
	// reg6:   background tscc, auto fill
	uint16_t rx_reserved[2];	// 接收电调滤波器电压

	// emission related registers
	uint16_t tx_high_power;		// 发射输出高功率电平
	uint16_t tx_low_power;		// 发射输出高功率电平
	uint16_t tx_v_preset4;		// 发射预置电压
	uint16_t tx_reserved1;		// reg5: not used now
	uint16_t tx_mod_gain6;		// 晶振注入增益控制
	uint16_t tx_mod_gain7;		// VCO注入增益控制
	// reg8-9: 参考晶振注入直流电压(-150)/VCO注入直流电压(150), get at global define and auto fill
	uint16_t tx_gain10;			// 增益控制，高8bit为发，低8bit为收，范围都是0-63
	uint16_t tx_reserved2[4];
}DSP_2P_PLL_TYPEDFE;

typedef struct
{
	uint8_t power_tx_gain;		// 发射增益控制, 0-63
	uint8_t power_rx_gain;		// 接收增益控制, 0-63
	uint16_t power_level;		// 功率电平控制, 2400-2900
}BASE_816V_RF_POWER;

#define FP_ADJUST_POINTERS		2
typedef struct
{
	uint8_t  fp_adj_watt[FP_ADJUST_POINTERS];	// 校正功率/*25W & 50W*/
	uint16_t fp_adj_adc[FP_ADJUST_POINTERS];	// 校正时的ADC值；比如上位机控制发25W，此时检测到的ADC为2200，则保存2200
}BASE_816V_FP_ADJUST_TYPEDEF;

#define RF_POWER_LEVEL			    	6
typedef struct
{
	uint8_t rf_2p_tune_table[4];// 固定填入字符串"RF2P"，且不用以空格结束
	uint16_t delay_frac;		// 调制信号延时控制，0-48，默认值0->接收晶体直流电压(2572)，范围0-4095，20200307
	uint8_t  tx_rising_pts;		// 发射输出功放上升沿，1-47，默认值20
	uint8_t  tx_falling_pts;	// 发射输出功放下降沿，1-47，默认值20
	uint16_t tx_mod_dc8;		// 晶振注入直流电压
	uint16_t tx_mod_dc9;		// VCO注入直流电压
	uint8_t  tx_pre_rising_pts;	// 发射输出功放上升沿前置归零点数
	uint8_t  tx_post_falling_pts;// 发射输出功放下降沿后续归零点数
	BASE_816V_RF_POWER power_ctrl[RF_POWER_LEVEL];	// 6档功率控制
	BASE_816V_FP_ADJUST_TYPEDEF fp_adjust;			// Re-alignment 4B later
	uint8_t reserved[128 - 14 - sizeof(BASE_816V_RF_POWER) * RF_POWER_LEVEL - sizeof(BASE_816V_FP_ADJUST_TYPEDEF)];
	DSP_2P_PLL_TYPEDFE dsp_2p_pll[RF_2P_MAX_FREQ_SEGMENT + 1];
}DSP_2P_TUNE_TYPEDEF;
/*
说明：
（1）rf_2p_tune_table-写频软件写入参数标识：
	写频软件每次修改后，必须固定往rf_2p_tune_table填入“RF2P”并写入手台
（2）delay_frac-tx_mod_dc9为两点调手台专用的全局参数；
（3）DSP_2P_PLL_TYPEDFE结构体数组需要进行动态配置：
	-> 当其freq=0时，表示频率段参数到此结束，此索引及其后续的内容均无效；
	-> 每插入一个频点，写频软件需要根据freq的大小进行重新排序（从小到大）；
*/


/*
	协议栈模式：
	“正常”表示正常启用协议栈，N模块需要选“正常”；
	“旁路”表示不启用协议栈，且主控收到的数据发到ccm；
	“VST”表示不启用协议栈，且主控收到的数据发到指定模块（8，9发到4；10，11发到6）；
	“FVST”表示启用协议栈，且协议栈输出的信令发送到配置的目标can ID
*/
typedef struct
{
	uint32_t cont_tx_protect	: 1;	/* 长发保护 */
	uint32_t force_send_sync 	: 1;	/* 强制发送同步信令 */
	uint32_t dsp_sync_mode 		: 1;	/* 设置常规的同步模式(0-跟随基站，1-跟随手台) */
	uint32_t force_disable_gps	: 1;	/* 强制关闭GPS */
	uint32_t trans_voc_type 	: 2;	/* 转码目标声码器：0-disable, 1-ambe,2-nvoc,3-VTEC1200 */
	uint32_t trans_voc_speed 	: 1;	/* 转码目标声码器速率（VTEC时可忽略此设置）：0-2400,1-2200 */
	uint32_t dsp_30ms_mode 		: 2;	/* DSP30ms时钟源：0-强制内置，1-强制外置，2-自动 */
	uint32_t bypass_stack_mode	: 2;	/* 协议栈模式：0-正常，1-旁路，2-VST，3-FVST */
	uint32_t forward_forbidden	: 1;	/* 0-使能转发表，1-禁用转发表 */
	uint32_t config_fec			: 2;	/* 0-自动配置（跟随BA帧），1-强制带FEC，2-强制去FEC，3-保留 */
	uint32_t config_dsp_mode	: 3;	/* 0-自动配置；1-常规终端；2-集群终端；3-基站；4-频分终端 */
	uint32_t pdt_reverse_slot	: 1;	/* PDT时隙标志：0-正常，1-取反 */
	uint32_t config_dsp_mode_ex	: 3;	/* 809第二接收模块DSP模式，取值1-4（无自动配置），其他定义同config_dsp_mode */
	uint32_t work_mode_ex		: 4;	/* 809第二接收模块工作模式，定义同工作模式设置 */
	uint32_t high_rp_ctrl		: 1;	/* 高反控制：0-正常（默认），1-高反停发*/
	uint32_t mpt_voice_format	: 1;	/* 模拟音频格式：0-线性PCM，1-A律 */
	uint32_t anti_pps_shake		: 1;	/* PPS处理模式：0-正常，1-抖动平均*/
	uint32_t reserved			: 4;	/* 写频时置全0 */
}MISC_CONFIG_BASE_TYPEDEF;

typedef struct
{
	uint32_t slot : 3;					// 上传的时隙号，0-5
	uint32_t reserved : 1;				// 保留，默认填0
	uint32_t dev_total : 14;			// 整个网络的电台数量，最大12700，填0表示禁止本机上报
	uint32_t dev_sn : 14;				// 本机上报位置的流水号(整点开始计时，表示本机是第几个上报)，其值不能大于网络的电台数量
}ZZWPRO_GPS_WINDOW_TYPEDEF;

typedef struct
{
	uint16_t belong : 2;									// Q模块归属：0-归属链路模块4，1-归属链路模块6，2&3-保留
	uint16_t canid  : 8;									// Q模块canid；为0即结束（后续再无需配置的模块）
	uint16_t slave  : 1;									// 0-主，1-从（即第二路）
	uint16_t reserved : 5;
}ZZWPRO_SCAN_NOISE_CANID_TYPEDEF;

typedef struct
{
	uint32_t scan_band : 10;								// 扫描频段(单位：MHz)
	uint32_t scan_band_width : 4;							// 扫描带宽(单位：MHz)
	uint32_t scan_config_time : 5;							// 将扫描结果配置到Q模块的时机(0-23点)
	uint32_t scan_reserved : 13;
	ZZWPRO_SCAN_NOISE_CANID_TYPEDEF scan_config_canid[ZZWPRO_Q_TX_TOTAL_CHAN];// 配置扫描结果的CANID设置(目前只用6个，最后两个保留)
}ZZWPRO_SCAN_NOISE_SETUP_TYPEDEF;

typedef struct
{
	uint32_t scan_band_width : 6;							// 扫描带宽(单位：100KHz；如需扫描200K范围，则填入2)
	uint32_t scan_reserved : 4;
	uint32_t scan_band_width_old : 4;						// 非0-使用ZZWPRO_SCAN_NOISE_SETUP_TYPEDEF结构体定义；全0-使用ZZWPRO_SCAN_NOISE_SETUP_TYPEDEF2结构体定义
	uint32_t scan_band : 14;								// 扫描基频(分辨率：100KHz，=基准频率*10；如需从350M开始扫描，则填入3500)
	uint32_t scan_noise_threshold : 2;						// 底噪阈值(0-3表示-110、-115、-120、-125)
	uint32_t scan_version : 2;								// 1-使用ZZWPRO_SCAN_NOISE_SETUP_TYPEDEF2当前定义；其他：保留
	ZZWPRO_SCAN_NOISE_CANID_TYPEDEF scan_config_canid[ZZWPRO_Q_TX_TOTAL_CHAN];// 配置扫描结果的CANID设置(目前只用6个，最后两个保留)
}ZZWPRO_SCAN_NOISE_SETUP_TYPEDEF2;

#define RUNTIME_PARAMETERS_XVBASE_MAX	1028
#define XVBASE_MAX_RESPONSE_GROUP		64
#define RUNTIME_PARAMETERS_XVBASE_USED	(sizeof(uint32_t)*(20+XVBASE_MAX_RESPONSE_GROUP)+sizeof(PROTOCOL_STACK_TYPEDEF)+sizeof(MISC_CONFIG_BASE_TYPEDEF)+\
										+sizeof(ZZWPRO_GPS_WINDOW_TYPEDEF)+sizeof(uint16_t)*(2+ZZWPRO_Q_TX_TOTAL_CHAN+2)+\
										sizeof(ZZWPRO_SCAN_NOISE_SETUP_TYPEDEF)+sizeof(ZZWPRO_SCAN_NOISE_SETUP_TYPEDEF2)+sizeof(uint16_t))
typedef struct	// 1028B, locate at msg_sent_table
{
	uint32_t reserved_for_mobile;	// set to 0 forever to avoide some operation of sent message
	PROTOCOL_STACK_TYPEDEF stack_xv;
	uint32_t stack_mode;			// 同PROTOCOL_STACK_TYPEDEF.stack_mode
	uint32_t base_watch_id;			// bit2-0:0-X跳频组，1-VO组，2-V2跳组，3-V3跳组，4-V6跳组；5-快速V6跳组；7-常规组；bit5-3:b5b4b3:1~6，绑定时隙1~6，=0，不绑定时隙；bit6:1-中继组，0-直通组；high 3B: watchid
	uint32_t fpga_write_gcr;		// 写FPGA_REG_GCR寄存器
	uint32_t fpga_write_tcr;		// 写FPGA_REG_TCR寄存器
	uint16_t f_module_rx;			// 模块全局接收频率: 为0时失效并使用CCM广播的频率，非0时生效；其值为实际频率*80
	uint16_t f_module_tx;			// 模块全局发射频率: 为0时失效并使用CCM广播的频率，非0时生效；其值为实际频率*80
	uint32_t fpga_daca_value;		// bit31:CTL_SEL(0-FPGA,1-MCU); bit30-16:时隙调整门限寄存器，取值0-32767，加1表示调整大概3.333us; bit15-0:16位A通道转换值
	uint32_t fpga_dacb_value;		// bit31:CTL_SEL(0-FPGA,1-MCU); bit30-16:保留; bit15-0:16位B通道转换值
	uint32_t response_group[XVBASE_MAX_RESPONSE_GROUP];	// 协议栈登记的响应组列表。登记的组呼才会在N和V+间转发，否则，只在本地N端或链路V端转发
	MISC_CONFIG_BASE_TYPEDEF misc_config_base;
	ZZWPRO_GPS_WINDOW_TYPEDEF gps_window;
	uint16_t dynamic_q_tx_freq[ZZWPRO_Q_TX_TOTAL_CHAN];	// NVQ模式下Q的发射频点（频率*80）
	uint16_t f_module_rx_ex;		// 809模块2的接收频率
	uint16_t scanning_module;		// Q扫频模块控制：bit9-0：基频，bit12-10：带宽，bit15-13：保留
	uint32_t base_ctrl_line01[2];	// 基站控制信道列表第一第二行
	uint32_t base_ctrl_linean[10];	// 基站控制信道列表第十-十九行
	ZZWPRO_SCAN_NOISE_SETUP_TYPEDEF scan_noise_setup;
	ZZWPRO_SCAN_NOISE_SETUP_TYPEDEF2 scan_noise_setup2;
	uint16_t tRegw;					// 电台登录周期（单位：秒）；0=取消电台周期登录
	uint8_t reserved[RUNTIME_PARAMETERS_XVBASE_MAX - RUNTIME_PARAMETERS_XVBASE_USED];
	uint32_t valid_flag;			// 0x55352d96; 初次上电此值无效时，将使用默认值进行初始化；写频软件可不理会，但不应该改写(除非需要恢复默认值)
}RUNTIME_PARAMETERS_XVBASE_TYPEDEF;

#define RUNTIME_PARAMETERS_XVBASE_NEW_MAX	SST25VF080_SIZE_OF_SECTOR
#define RUNTIME_PARAMETERS_XVBASE_EXT_MAX	(RUNTIME_PARAMETERS_XVBASE_NEW_MAX - sizeof(RUNTIME_PARAMETERS_XVBASE_TYPEDEF) - 2 * sizeof(uint32_t))
typedef struct
{
	uint8_t reserved[RUNTIME_PARAMETERS_XVBASE_EXT_MAX];
}RUNTIME_PARAMETERS_XVBASE_TYPEDEF_EXT;
// 下面这个数据结构仅用于开机参数检测/拷贝和关机参数检查/保存
typedef struct	// 4096
{
	RUNTIME_PARAMETERS_XVBASE_TYPEDEF		xvbase_1028;
	RUNTIME_PARAMETERS_XVBASE_TYPEDEF_EXT	xvbase_ext;
	uint32_t xvbase_paras_sn;				// 参数保存序号(用于区分那块参数为最新，序号大为最后保存的数据)	offset: 4088
	uint32_t xvbase_paras_valid_flag;		// [0x55352d96]USER_FLASH_SAVE_FLAG:参数有效标识(写频时写频软件写入)
}RUNTIME_PARAMETERS_XVBASE_TYPEDEF_4KB;


#define USER_DATA_OPERATION_RUNTIME		0x00
#define USER_DATA_OPERATION_XV_RUNTIME	0x01

typedef struct
{
	uint16_t opt_dist :	1;			/* 0-维持原状；1-改定位上报 */
	uint16_t opt_pwr :	1;			/* 0-维持原状；1-改电量上报 */
	uint16_t opt_rp :	1;			/* 0-维持原状；1-改反向功率上报 */
	uint16_t opt_attach:1;			/* 0-维持原状；1-改组附着配置 */
	uint16_t ig :		2;			/* 0-单终端上拉；1-组上拉；2-全部终端上拉；3-指定组外终端上拉 */
	uint16_t power :	1;			/* 0-电量改变不主动上报; 1-每掉一格电量报一次 */
	uint16_t rp : 		1;			/* 0-反向功率过大不主动上报; 1: 每检测到一次反向功率大于等于设定值就报一次 */
	uint16_t dist : 	3;			/* 0-时间距离改变不上报位置; 1-按时间，2-按距离，3-时间或距离满足其中之一，4-时间距离均满足 */
	uint16_t attach :	1;			/* 0-不报组附着；1-切组时上报组附着 */
	uint16_t reserved :	4;
}ActReport_Conf;
typedef struct
{
	uint8_t  idt;
	uint8_t reserved;
	ActReport_Conf config;

	uint8_t time_val;			/* 单位3s */
	uint8_t dist_val;			/* 单位10m */
	uint8_t rp_val;				/* 反向功率阈值 */
	uint8_t reserved2;
	uint32_t id;				/* 高字节保留 */
}V_ActReportConfig;


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////// 运行时参数顶层数据结构 /////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define NEIGHBOUT_BASE_NUMBER		16
#define RUNTIME_PARAMETERS_FOR_POC	200
#define RUNTIME_PARAMETERS_FIXED_USED	(sizeof(RUNTIME_PARAS_TYPEDEF)+sizeof(MESSAGE_TABLE_TYPEDEF)+sizeof(AIR_RECONF_TYPEDEF)+\
										sizeof(MESSAGE_SENT_TABLE_TYPEDEF)+sizeof(DEVICE_INFORMATION)+sizeof(CALLING_RECORD)*3)
#define RUNTIME_PARAMETERS_DYNAMIC		(4096-sizeof(uint32_t)-sizeof(uint32_t)-RUNTIME_PARAMETERS_FIXED_USED)
#define RUNTIME_PARAMETERS_DYNAMIC_USED	(sizeof(uint16_t)*2*NEIGHBOUT_BASE_NUMBER+MAX_USER_NAME_LEN+sizeof(V_ActReportConfig)+sizeof(uint32_t)+RUNTIME_PARAMETERS_FOR_POC)
#define RUNTIME_PARAMETERS_FREE			(RUNTIME_PARAMETERS_DYNAMIC-RUNTIME_PARAMETERS_DYNAMIC_USED)
typedef struct	// 4096B
{
	RUNTIME_PARAS_TYPEDEF		runtime_paras;					// 用户配置参数														offset: 0
	MESSAGE_TABLE_TYPEDEF		msg_table;						// 短信息索引表														offset: 128
	AIR_RECONF_TYPEDEF			air_config;						// 空口写频参数														offset: 296
	MESSAGE_SENT_TABLE_TYPEDEF	msg_sent_table;					// 已发送短信息表														offset: 712
	DEVICE_INFORMATION			device_information;				// 设备软硬件版本、编译时间等												offset: 1740
	CALLING_RECORD				call_record[3];					// 0-2: 数字常规/数字集群/模拟集群											offset: 1772

	uint16_t					neighbour_base_info[2][NEIGHBOUT_BASE_NUMBER];	// 关机时保存的邻站信息(0-数字，1-模拟)						offset: 3248
	uint8_t						air_config_name[MAX_USER_NAME_LEN];				//												offset: 3312(when neighbour=16)
	V_ActReportConfig			act_report_conf;								// 无线调度配置(目前主要用于V模式)							offset: 3328
	uint32_t					zzwpro_3ppt_id;									// 自组网3PTT号码（为0时使用守候组号码）						offset: 3340
	uint8_t 					reserved_for_poc[RUNTIME_PARAMETERS_FOR_POC];
	uint8_t						reserved[RUNTIME_PARAMETERS_FREE];				// 												offset: 3344
	uint32_t					runtime_paras_sn;				// 参数保存序号(用于区分那块参数为最新，序号大为最后保存的数据)	offset: 4088
	uint32_t					runtime_paras_valid_flag;		// [0x55352d96]USER_FLASH_SAVE_FLAG:参数有效标识(写频时写频软件写入)
}RUNTIME_PARAMETERS_TYPEDEF;



///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////// 静态参数顶层数据结构 /////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define MENU_CONFIG_DATA_MAX_LENGTH		192

typedef struct
{
	uint32_t loginout_notify		: 1;		/*上下线功能:0-禁用；1-开启*/
	uint32_t warning_with_gps		: 1;		/*一键报警时是否同时上传GPS信息: 1-上传；0-不上传*/
	uint32_t login_code 			: 10;
	uint32_t logout_code 			: 10;
	uint32_t reserved 				: 10;
	uint32_t loginout_id;
}LOGIN_OUT_TYPEDEF;

#define ENCRYPT_TYPE_MINISTRY		0
#define ENCRYPT_TYPE_FENGHUO		1
#define ENCRYPT_TYPE_AES256			2
typedef struct
{
	uint32_t reserved				: 16;		/*占用之前定义的GPS省电参数位置*/
	uint32_t dsp_vad_trailing_time	: 6;		/*声控拖尾时间，单位100ms*/
	uint32_t encrypt_type			: 2;		/*加密类型：0-部级；1-FH；2-AES；3-保留*/
	uint32_t dsp_vad_sensitivity	: 3;		/*声控灵敏度控制，值越大灵敏度越低(0:大概离mic 60cm 就能开门，2:大概减少到 40cm)*/
	uint32_t tactics_radio			: 1;		/*0-普通电台，1-战术电台*/
	uint32_t reserved3				: 1;
	uint32_t lcd_on_when_calling	: 1;		/*通话中亮屏设置：0-通话中到达灭屏时间后灭屏；1-通话中屏幕常亮*/
	uint32_t gps_save_power_ctrl_zzw: 1;		/*自组网GPS省电开关，0-开启；1-关闭*/
	uint32_t gps_save_power_ctrl	: 1;		/*常规/集群模式GPS省电开关，0-开启；1-关闭*/
}MISC_STATIC_CONFIG_MOBILE;

typedef struct	// 256B
{
	uint16_t	table_flag;						// 使能标识：0x0000或0xffff表示使用内置表（默认），0x7036表示使用本表，其他表示禁用功率检测（建议填个固定数字，如0x1234）
	uint16_t	reserved;
	uint16_t	front_power_table[90];
	uint16_t	rear_power_table[36];
}PWR_CHECK_TABLE;

typedef struct	// 2048B
{
	PWR_CHECK_TABLE						power_table;								// 功率查找表									offset: 0
	uint8_t								reserved[2048 - 4 - sizeof(PWR_CHECK_TABLE)];//											offset: 256
	uint32_t							ext_static_para_valid_flag;					// [0x55352d96]USER_FLASH_SAVE_FLAG参数有效标识(写频时写频软件写入)offset: 4092
}EXT_STATIC_PARAMETERS_TYPEDEF;

typedef struct	// 8192B
{
	HEADGRAY							device_name;								// 											offset: 0
	uint8_t 							menu_config[MENU_CONFIG_DATA_MAX_LENGTH];	// 预留功能：菜单项自定义配置							offset: 64
//	张家港市局：禁用GPS定位开关；禁止手动指定控制信道号
	PROTOCOL_STACK_TYPEDEF				stack_pdt;									// 协议栈软件PDT模式下的参数							offset: 256
	PROTOCOL_STACK_TYPEDEF				stack_mpt;									// 协议栈软件MPT模式下的参数							offset: 384
	PROTOCOL_ZZW_TYPEDEF				stack_zzw;									// 自组网模式参数配置（手台专用，车机另有定义）	offset: 512
	KEY_FUNC_REMAP_TABLE_TYPEDEF		key_remap_table;							//											offset: 640
	CUSTOM_SHORTCUT_ID_TABLE			shortcut_id_table;							//											offset: 672
	USER_MESSAGE_PRESET					msg_preset;									// 预置短信										offset: 716
	USER_MESSAGE_PRESET					msg_status;									// 预置状态信息									offset: 2160
	FACTORY_PARAS_TYPEDEF				factory_paras;								// 											offset: 3604
	uint8_t								paras_access_pwd[3];						// 写频密码，由写频软件维护使用							offset: 3732
	uint8_t								reserved_1_1B[1];							// 											offset: 3735
	uint8_t								zzw_meeting_name[6][16];					// 自组网会议名称，最多6个会议 STACK_ZZW_TOTAL_MEETING_NUM	offset: 3736
	uint8_t								paras_rw_pwd[16];							// 											offset: 3832
	LOGIN_OUT_TYPEDEF					login_out_setup;							// 											offset: 3848
	PROTOCOL_ZZWPRO_TYPEDEF				stack_zzwpro;								// 											offset: 3856
	MISC_STATIC_CONFIG_MOBILE			misc_static_config;							//											offset: 3888
	uint8_t								reserved[196];								// 											offset: 3892
	uint32_t							static_para_version;						// [0x16062200]ADDRESS_BOOKS_VERSION2版本标识，应该和号码薄中的版本相同offset: 4088
	uint32_t							static_para_valid_flag;						// [0x55352d96]USER_FLASH_SAVE_FLAG参数有效标识(写频时写频软件写入)offset: 4092
	DSP_DDS_TUNE_TYPEDEF				pll_tune_table;								// 写频时，需读取这部分内容，再在写频时回写
//	DSP_2P_TUNE_TYPEDEF					pll_tune_table;								// 注: 上述两个数据结构二选一，分DDS和两点调射频而定
																					// 在DDS时，此表只能在手台上在线生成，写频软件可以读取显示但不能改写；
																					// 在两点调时，DSP_2P_TUNE_TYPEDEF.rf_2p_tune_table必须为"RF2P"，主控程序不会再将其改为"2PRF"，且会直接使用里面的数值
	EXT_STATIC_PARAMETERS_TYPEDEF		ext_static;
}STATIC_PARAMETERS_TYPEDEF;
/* 注: (1) FACTORY_PARAS_TYPEDEF数据结构用于保存备份的默认出厂数据，在第一次烧写新板(SPI无内容)时，主控程序将其拷贝到SPI相应位置(将ESN
           拷贝到backup ESN处，将FACTORY_PARAS_TYPEDEF数据结构拷贝到FLASH_SPI_FACTORY_STATIC_ADDRESS块的相应位置)，以便生产能进行调试；
           注意写频时正确置位FACTORY_PARAS_TYPEDEF.factory_save_flag
       (2) 改写ESN时，除了修改STATIC_PARAMETERS_TYPEDEF.factory_paras.esn之外，还必须将新的ESN写入SPI的backup ESN中(地址为FLASH_SPI_FACTORY_PARAS_ADDRESS)；
       (3) 改写出厂数据时，除了修改STATIC_PARAMETERS_TYPEDEF.factory_paras之外，还必须将新的factory_paras内容写入FLASH_SPI_FACTORY_STATIC_ADDRESS相应位置；
           -> 改写出厂数据时，需要将FLASH_SPI_FACTORY_STATIC_ADDRESS处的整块内容先读出来，然后替换FACTORY_PARAS_TYPEDEF数据结构，再将整块数据写回；

       (4) DSP_DDS_TUNE_TYPEDEF/DSP_2P_TUNE_TYPEDEF数据结构用于保存备份的本机调谐表/默认两点调参数，在第一次烧写新板(SPI无内容)时，主控程序将其拷贝到FLASH_SPI_FACTORY_STATIC_ADDRESS块的相应位置，以便生产能进行调试；
       (5) 改写两点调参数时，除了修改STATIC_PARAMETERS_TYPEDEF.pll_tune_table之外，还必须将新的pll_tune_table内容写入FLASH_SPI_FACTORY_STATIC_ADDRESS相应位置；
           -> 改写两点调参数时，需要将FLASH_SPI_FACTORY_STATIC_ADDRESS处的整块内容先读出来，然后替换DSP_2P_TUNE_TYPEDEF数据结构，再将整块数据写回；
       (6) 如果不是两点调手台，禁止修改FLASH_SPI_FACTORY_STATIC_ADDRESS处的DSP_DDS_TUNE_TYPEDEF/DSP_2P_TUNE_TYPEDEF内容!!!
*/




////////////////////////////////////////////////////////////
//////////// 参数初始化标识 ///////////////
#define ADDRESS_BOOKS_INIT_FLAG		0x55356200
#define ADDRESS_BOOKS_VERSION		0x15041700
#define ADDRESS_BOOKS_VERSION2		0x16062200 /* 写频软件根据此头文件的定义写入设备；设备使用这个标志作为参数版本检测，每当参数定义改变后，且无法和以前的参数兼容时，则同步修改这个标志0x15041700 */
#define USER_SETUP_DATA_INIT_FLAG	0x55357a00	/* no longer to use*/
#define MENU_CONFIG_FLAG_PREFIX		0x55356d00 /* 菜单配置项有效标识(目前未使用)*/
#define MESSAGE_INIT_FLAG			0x55354d00	/* 用户短消息 */
#define MESSAGE_PRESET_INIT_FLAG	0x55354e00	/* no longer to use*/
#define MESSAGE_STATUS_INIT_FLAG	0x55354f00	/* no longer to use*/
#define DSP_IMAGE_FLAG				0x55354900	/* 旧DSP/自组网车台主控程序升级文件标识 */
#define DSP_IMAGE_DM_PDT_FLAG		0x55364900	/* 双模手台的PDT DSP升级文件标识，写入到FLASH_SPI_PDT_MOBILE_DSP_ADDRESS */
#define DSP_IMAGE_DM_ZZW_FLAG		0x55364a00	/* 双模手台的ZZW DSP升级文件标识，写入到FLASH_SPI_ZZW_MOBILE_DSP_ADDRESS */
#define DSP_IMAGE_SM_PDT_FLAG		0x55364b00	/* 单模PDT手台的DSP升级文件标识，写入到FLASH_SPI_PDT_MOBILE_DSP_ADDRESS */
#define DSP_IMAGE_SM_ZZW_FLAG		0x55364c00	/* 单模自组网手台的DSP升级文件标识，写入到FLASH_SPI_PDT_MOBILE_DSP_ADDRESS */
#define DSP_IMAGE_DM_PDT_NVOC_FLAG	0x55364d00 /* 双模PDT手台的NVOC声码器版本的DSP升级文件标识，写入到FLASH_SPI_PDT_MOBILE_DSP_ADDRESS */
#define DSP_IMAGE_NEW_815_FLAG		0x55364e00 /* 新版64xx手台的DSP升级文件标识，写入到FLASH_SPI_PDT_MOBILE_DSP_ADDRESS,占用256*3KB */
#define ZZW_MCU_FLAG				0x55364f00	/* 使用收发模块的车机的自组网ARM主程序 */
#define DSP_IMAGE_NEW_816_FLAG		0x55365100 /* 816方案的手台 */
#define DSP_IMAGE_NEW_816V_FLAG		0x55365200 /* 816方案的车机 */
#define DSP_IMAGE_NEW_816A_FLAG		0x55365300 /* 执法仪模块 */
#define DSP_IMAGE_NEW_815V2_FLAG	0x55365400 /* 二代815(射频方案使用2572) */
#define DSP_IMAGE_NEW_815V3_FLAG	0x55365600	/* 三代815(射频方案使用2572+1950双本振) */
#define DSP_IMAGE_NEW_810C_FLAG		0x55365700	/* 三代810(射频方案使用2572+72351双本振) */
#define DSP_IMAGE_NEW_XV_BASE_FLAG	0x55365500 /* XV基站模块 */
#define FONTS_LIBRARY_FLAG			0x55354300	/* 汉字库标识 */
#define FONTS_LIBRARY_UI_FLAG		0x55364500	/* UI汉字库标识 */
#define DSP_CONFIG_REG_INIT_FLAG	0x55355c00	/* DSP寄存器配置 */
#define UNICODE_GB2312_LUT_FLAG		0x55351e00	/* UNICODE <-> GB 查找表 */
#define MAIN_CONTROL_PROGRAM_FLAG	0x55364100	/* ARM主程序 */
#define MAIN_CONTROL_PROGRAM_FLAG_CHEJI	0x55364200	/* ARM主程序-手台方案的彩屏车机程序 */
#define MAIN_CONTROL_PROGRAM_FLAG_BASE	0x55364300	/* ARM主程序-手台方案的基站程序 */
#define MAIN_CONTROL_PROGRAM_FLAG_COLOR	0x55364400	/* ARM主程序-彩屏手台(815V3) */
#define PDT_STACK_PROGRAM_FLAG		0x55354a00	/* 协议栈软件 */
#define ZZWX_STACK_PROGRAM_FLAG		0x55354b00	/* 自组网X协议栈软件 */
#define ZZWV_STACK_PROGRAM_FLAG		0x55354c00	/* 自组网V协议栈软件 */
#define SEPARATE_GUI_PROGRAM_FLAG	0x55364700	/* P1手台GUI程序 */
#define P1_CALIBRATOR_DATA_FLAG		0x55364800	/* P1磁力计/加速度机校准参数文件标识 */
#define DEVICE_INFO_VALID_FLAG		0x5535170f	/* 设备版本信息*/
#define USER_FLASH_SAVE_FLAG		0x55352d96  /* 参数保存有效标识 */

//#define FORWARDING_TABLE_FLAG		0x55374600	/* 基站转发表文件信息 */
#define FORWARDING_TABLE_FLAG		0x00463755

#define AES256_KEY_TABLE_FLAG		0x55374B00	/* AES256秘钥表 */

#define P1_AMBE_PROGRAM_FLAG		0x55366100	/* AMBE运行库程序标识 */

#define USER_ICON_PIC_FLAG			DSP_IMAGE_FLAG
#define USER_LOGO_FLAG				0x5535424d


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////// 参数区域2：长度128KB，用于保存用户号码薄及控制信道等相关的数据（设备只读） ///////////////////////////////////
////////////////////////////////////////////////////////////////// 号码薄数据结构 /////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#define MAX_USER_BOOK_COUNT			4000
// 用户号码薄补充设计
#define MAX_RX_ID_OF_ONE_CONV		15
#define MAX_CONV_OF_ONE_GROUP		150
#define MAX_TRUNK_OF_ONE_GROUP		30
#define MAX_MTRUNK_OF_ONE_GROUP		150
#define MAX_ZZW_CONV_OF_ONE_GROUP	100

typedef struct
{
	uint32_t user_books_init_flag;					// 用于标识参数的版本（如本版的定义为0x15041700，即年-月-日的BCD编码；如果下一版的参数定义无法和这一版兼容，则新参数版本号升级）
	uint16_t user_count;							// 用户号码薄条目数
	uint16_t user_active;							// 写频时固定为0
	USER_BOOK books[MAX_USER_BOOK_COUNT];			// 号码薄内容
}USER_ADDRESS_BOOKS;


typedef struct
{
	uint16_t index_tx;							// 指向存储于USER_ADDRESS_BOOKS中的某个USER_BOOK的索引，用于设定发送ID及工作频点；注：指向的USER_BOOK类型必须为直通/常规；不足MAX_CONV_OF_ONE_GROUP项时填入0xffff
	uint16_t index_rx[MAX_RX_ID_OF_ONE_CONV];	// 指向存储于USER_ADDRESS_BOOKS中的某些USER_BOOK的索引，用于设定接收组ID；注：指向的USER_BOOK类型必须为直通/常规；不足MAX_RX_ID_OF_ONE_CONV项时填入0xffff
}GROUP_BOOK_CONV;								// 直通/常规用户

typedef struct
{
	uint16_t index_tr;							// 指向存储于USER_ADDRESS_BOOKS中的某个USER_BOOK的索引，用于设定守候ID；注：指向的USER_BOOK类型必须为集群；不足MAX_TRUNK_OF_ONE_GROUP项时填入0xffff
}GROUP_BOOK_TRUNK;								// 集群用户（或MPT常规用户）

typedef struct
{
	uint8_t  name[MAX_USER_NAME_LEN];			// 组群名称；如果name[0]==0，说明此区域的数据无效；考虑到显示效果，name的长度不要超过14B
	GROUP_BOOK_TRUNK grp_trunk[MAX_TRUNK_OF_ONE_GROUP];
}GROUP_TRUNKING;								// 用户组群数据结构（仅数字集群下使用）

typedef struct
{
	uint8_t  name[MAX_USER_NAME_LEN];			// 控制信道名称；如果name[0]==0，说明此区域的数据无效；考虑到显示效果，name的长度不要超过14B
	uint32_t ctrl_table[CTRL_TABLE_TOTAL_ITEM];	// 控制信道表，由写频软件写入。高2B:基站系统码（可置为0）; 低2B:基站控制信道，其中高4b为信道色码（一般为0），低12b为信道编号
}GROUP_CONTROL_LIST;							// 控制信道组群数据结构（数字集群下有MAX_GROUPS_OF_BOOKS个组，模拟集群下仅一个并可以忽略其名字）

typedef struct
{
	uint8_t  name[MAX_USER_NAME_LEN];			// 组群名称；如果name[0]==0，说明此区域的数据无效；考虑到显示效果，name的长度不要超过14B
	GROUP_BOOK_CONV    grp_zzwpro_trunk[MAX_TRUNK_OF_ONE_GROUP];
}GROUP_ZZWPRO_TRUNKING;							// 用户组群数据结构（仅ZZWXV下使用）

#define HETEROTYPE_FREQ_MAX						32
typedef struct
{
	float freq_rx_hz;
	float freq_tx_hz;
}HETEROTYPE_FREQ;

#define TRUNK_RX_LIST_VALID_FLAG			0x21031000
#define TRUNK_RX_LIST_MAX_SIZE				2032
#define TRUNK_RX_LIST_MAX_ITEM_OF_ONE		60
typedef struct
{
	USER_ADDRESS_BOOKS contacts;									// 号码薄内容				96008(4+2+2+4000*24)
	GROUP_TRUNKING     grp_trunking[MAX_GROUPS_OF_BOOKS];			// 数字集群联系人组群		40*76=3040
	GROUP_CONTROL_LIST grp_ctrl_list[MAX_GROUPS_OF_CTRL];			// 数字集群控制信道组群	40*272=10880
	GROUP_BOOK_CONV    grp_conv[MAX_CONV_OF_ONE_GROUP];				// 数字常规联系人列表		150*32=4800
	GROUP_BOOK_TRUNK   grp_mconv[MAX_CONV_OF_ONE_GROUP];			// 模拟常规联系人列表		150*2=300
	GROUP_BOOK_TRUNK   grp_mtrunk[MAX_MTRUNK_OF_ONE_GROUP];			// 模拟集群联系人列表		150*2=300
	GROUP_CONTROL_LIST grp_mctrl_list;								// 模拟集群控制信道组群	272
	GROUP_BOOK_TRUNK   grp_zconv[MAX_ZZW_CONV_OF_ONE_GROUP];		// 自组网联系人列表		100*2=200
	GROUP_ZZWPRO_TRUNKING grp_zzwpro_trunking[MAX_GROUPS_OF_ZZWPRO_BOOKS];		// XV自组网联系人列表		(16+32*30)*10=9760
	uint8_t trunk_rx_group_list[sizeof(uint32_t) + TRUNK_RX_LIST_MAX_SIZE];		// 集群接收组列表（前4B为有效标志）；每个列表最少占用16+1+1+2=20B，最大占用16+1+1+2*TRUNK_RX_LIST_MAX_ITEM=138B
	HETEROTYPE_FREQ    htt_freq[HETEROTYPE_FREQ_MAX];				// 数字常规异型频率库256B(20220903加但未使用)
}GROUP_BOOKS;														// total=115800+9760+2036+256=127852(remainder=128*1024-127852=3220)



/* 号码薄参数布局规划：
	（1）整个号码薄数据存放于区域2，写频软件读写的绝对地址定义为：
		#define USER_BOOK_DATA_FLASH_ADDRESS	0x080E0000
		#define USER_BOOK_DATA_MAX_LENGTH		0x20000
	（2）号码薄数据开始的8字节为号码薄初始化标识及数据长度，定义为：
		BYTE[0]=0X55, BYTE[1]=0X35, BYTE[2]=0X62, BYTE[3]=0X00（此4字节固定填充）；
		BYTE[4]=sizeof(GROUP_BOOKS) >> 24, BYTE[5]=sizeof(GROUP_BOOKS) >> 16, BYTE[6]=sizeof(GROUP_BOOKS) >> 8, BYTE[7]=sizeof(GROUP_BOOKS)（此4字节根据GROUP_BOOKS长度填充）；
	（3）写频软件在更新号码薄数据时，需要将这8个字节发送给设备写入FLASH中（类似升级字库的操作）；
	（4）整个GROUP_BOOKS数据结构大小不能大于128 * 1024 - 8字节；
	注：
		由于用户号码薄需要扩容，并加入组群功能，所以把号码薄相关部分内容从原来的16KB空间移出，由写频软件写入设备的FLASH（定义为参数区域2）中，
		设备对其只能读取，不能改写；且这部分空间和参数区域1的地址是不连续的，写频软件在读写参数时必须和上述的8KB参数分成两次进行读写
*/



#ifdef __cplusplus
}
#endif

#endif /* __VICTEL_DIGITAL_MOBILE_SETUP_DATA_LOCATION_DEFINED_H__ */

