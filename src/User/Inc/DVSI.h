#ifndef __DVSI_H__
#define __DVSI_H__

#include "apcop2.h"
#include "apcop2fec.h"


#define SELP_FRAME_SIZE					160
#define SELP_DATA_SIZE_FEC				72
#define SELP_DATA_SIZE_NOFEC_2150		43
#define SELP_DATA_SIZE_NOFEC_2400		48

#define SELP_BYTE_SIZE_FEC				(SELP_DATA_SIZE_FEC / 8)
#define SELP_BYTE_SIZE_NOFEC_2400		(SELP_DATA_SIZE_NOFEC_2400 / 8)

typedef struct
{
	ENCSTATE_T	es;			/* voice encoder state */
	DECSTATE_T	ds;			/* voice decoder state */
	FECSTATE_T	efec;		/* FEC encoder state   */
	FECSTATE_T	dfec;		/* FEC decoder state   */

	short mode;
	short ws;
	short enc_cmode;
	short dec_cmode;
	short ns_gain;
	short status;
	TONE_RCV_INFO tr;
}AMBE_STRUCT_TYPEDEF;


typedef struct ambe_init_enc_type
{
    ENCSTATE_T  *es;
    short      mode;
    short full_init;
}ambe_init_enc_type;

typedef struct ambe_init_agc_type
{
    ENCSTATE_T  *es;
    short set_level;
    short  max_gain;
    short  min_gain;
}ambe_init_agc_type;

typedef struct ambe_init_agc_dB_type
{
    ENCSTATE_T       *es;
    short set_dbm0_level;
    short    max_db_gain;
    short    min_db_gain;
}ambe_init_agc_dB_type;

typedef struct ambe_set_agc_info_type
{
    ENCSTATE_T        *es;
    short  set_dbm0_level;
    short     max_db_gain;
    short     min_db_gain;
    short  min_dbm0_level;
    short          alpha0;
    short          alpha1;
    short          alpha2;
    short          alpha3;
    short      dbm0_delta;
    short snr_dbm0_thresh;
}ambe_set_agc_info_type;

typedef struct ambe_set_enc_mode_type
{
    ENCSTATE_T *es;
    short     mode;
}ambe_set_enc_mode_type;

typedef struct ambe_tone_xmt_type
{
    ENCSTATE_T *es;
    short      idx;
    short      lvl;
}ambe_tone_xmt_type;

typedef struct ambe_set_vad_update_type
{
    ENCSTATE_T *es;
    short      update;
}ambe_set_vad_update_type;

typedef struct ambe_voice_enc_type
{
    short          *buf;
    short     bit_steal;
    short         *sbuf;
    short            ws;
    short         cmode;
    short      subframe;
    short       ns_gain;
    ENCSTATE_T      *es;
}ambe_voice_enc_type;

typedef struct ambe_get_tone_det_info_type
{
    TONE_DET_INFO     *t;
    const ENCSTATE_T *es;
}ambe_get_tone_det_info_type;

typedef struct ambe_get_enc_mode_type
{
    ENCSTATE_T *es;
    }ambe_get_enc_mode_type;

typedef struct ambe_get_enc_sourcebits_type
{
    const ENCSTATE_T *es;
}ambe_get_enc_sourcebits_type;

typedef struct ambe_init_dec_type
{
    DECSTATE_T *ds;
    short     mode;
}ambe_init_dec_type;

typedef struct ambe_set_dec_mode_type
{
    DECSTATE_T *ds;
    short     mode;
}ambe_set_dec_mode_type;

typedef struct ambe_tone_gen_type
{
    DECSTATE_T *ds;
    short      idx;
    short      lvl;
}ambe_tone_gen_type;

typedef struct ambe_set_fecinfo_type
{
    DECSTATE_T       *ds;
    const FECINFO *finfo;
}ambe_set_fecinfo_type;

typedef struct ambe_voice_dec_type
{
    short         *sbuf;
    short            ws;
    short          *buf;
    short     bit_steal;
    short         cmode;
    short      subframe;
    DECSTATE_T      *ds;
}ambe_voice_dec_type;

typedef struct ambe_get_tone_rcv_info_type
{
    TONE_RCV_INFO     *t;
    const DECSTATE_T *ds;
}ambe_get_tone_rcv_info_type;

typedef struct ambe_get_dec_mode_type
{
    DECSTATE_T *ds;
}ambe_get_dec_mode_type;

typedef struct ambe_get_dec_sourcebits_type
{
    const DECSTATE_T *ds;
}ambe_get_dec_sourcebits_type;

typedef struct ambe_fec_enc_type
{
    short      *chanbuf;
    const short *srcbuf;
    short     bit_steal;
    FECSTATE_T     *fec;
}ambe_fec_enc_type;

typedef struct ambe_fec_dec_type
{
    short        *srcbuf;
    short      bit_steal;
    const short *chanbuf;
    short        sd_bits;
    short          cmode;
    FECSTATE_T      *fec;
}ambe_fec_dec_type;

typedef struct ambe_get_fecinfo_type
{
    FECINFO          *finfo;
    const FECSTATE_T   *fec;
}ambe_get_fecinfo_type;

typedef struct ambe_init_fec_type
{
    FECSTATE_T *fec;
    short      mode;
}ambe_init_fec_type;

typedef struct ambe_set_fec_mode_type
{
    FECSTATE_T *fec;
    short      mode;
}ambe_set_fec_mode_type;

typedef struct ambe_get_fec_mode_type
{
    FECSTATE_T *fec;
}ambe_get_fec_mode_type;

typedef struct ambe_get_erasure_frame_type
{
    short *buf;
    short no_fec;
    short mode;
}ambe_get_erasure_frame_type;

short dvsi_init_enc          (void *lpParam);
short dvsi_init_agc          (void *lpParam);
short dvsi_init_agc_dB       (void *lpParam);
short dvsi_set_agc_info      (void *lpParam);
short dvsi_set_enc_mode      (void *lpParam);
short dvsi_tone_xmt          (void *lpParam);
short dvsi_set_vad_update    (void *lpParam);
short dvsi_voice_enc         (void *lpParam);
short dvsi_get_tone_det_info (void *lpParam);
short dvsi_get_enc_mode      (void *lpParam);
short dvsi_get_enc_sourcebits(void *lpParam);
short dvsi_init_dec          (void *lpParam);
short dvsi_set_dec_mode      (void *lpParam);
short dvsi_tone_gen          (void *lpParam);
short dvsi_set_fecinfo       (void *lpParam);
short dvsi_voice_dec         (void *lpParam);
short dvsi_get_tone_rcv_info (void *lpParam);
short dvsi_get_dec_mode      (void *lpParam);
short dvsi_get_dec_sourcebits(void *lpParam);
short dvsi_fec_enc           (void *lpParam);
short dvsi_fec_dec           (void *lpParam);
short dvsi_get_fecinfo       (void *lpParam);
short dvsi_init_fec          (void *lpParam);
short dvsi_set_fec_mode      (void *lpParam);
short dvsi_get_fec_mode      (void *lpParam);
short dvsi_get_erasure_frame (void *lpParam);

#endif /* __DVSI_H__ */
