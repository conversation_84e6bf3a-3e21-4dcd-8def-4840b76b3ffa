#ifndef __C_ENTRY_H__
#define __C_ENTRY_H__

typedef struct DVSI_ENTRY_TYPE
{
    short (*init_enc          )(void *lpParam);
    short (*init_agc          )(void *lpParam);
    short (*init_agc_dB       )(void *lpParam);
    short (*set_agc_info      )(void *lpParam);
    short (*set_enc_mode      )(void *lpParam);
    short (*tone_xmt          )(void *lpParam);
    short (*set_vad_update    )(void *lpParam);
    short (*voice_enc         )(void *lpParam);
    short (*get_tone_det_info )(void *lpParam);
    short (*get_enc_mode      )(void *lpParam);
    short (*get_enc_sourcebits)(void *lpParam);
    short (*init_dec          )(void *lpParam);
    short (*set_dec_mode      )(void *lpParam);
    short (*tone_gen          )(void *lpParam);
    short (*set_fecinfo       )(void *lpParam);
    short (*voice_dec         )(void *lpParam);
    short (*get_tone_rcv_info )(void *lpParam);
    short (*get_dec_mode      )(void *lpParam);
    short (*get_dec_sourcebits)(void *lpParam);
    short (*fec_enc           )(void *lpParam);
    short (*fec_dec           )(void *lpParam);
    short (*get_fecinfo       )(void *lpParam);
    short (*init_fec          )(void *lpParam);
    short (*set_fec_mode      )(void *lpParam);
    short (*get_fec_mode      )(void *lpParam);
    short (*get_erasure_frame )(void *lpParam);
}DVSI_ENTRY_TYPE;

#endif /* __C_ENTRY_H__ */
