/**
  ******************************************************************************
  *                Copyright (c) 2012, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    06-December-2012
  * @brief   This file provides
  *            - setup screen process
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <victel_digital_board.h>
#include <victel_digital_lcd.h>
#include <victel_digital_usart.h>
#include <victel_digital_spi_flash.h>
#include "stack_config.h"
#include "global_define.h"
#include "vlog.h"


#define DEV_NO_SEARTH_TABLE_TOTAL	2
typedef struct
{
	uint8_t sn2;
	char info[19];
}DEV_NO_SEARTH_TABLE;


//#define USE_DIAL_BOX_TO_SETUP_CRYPTO_KEY	1

GL_Page_TypeDef *page_setup;
static uint8_t g_rf_type, g_dev_type, g_devNo, g_devNo_real, g_805_version, g_test_mode = 0;	// g_dev_type: 0-mobile; 1-cheji; g_rf_type: 0-dds; 1-2points
#ifndef P1_DEL_UNNECESSARY_KEY_PROC
static uint8_t setup_item_selected = 0;
#endif
static uint16_t g_dev_is_something = 0;
uint8_t g_xiepin_pin_config = 0;
//void (*setup_func_list[SETUP_MENU_TOTAL_ITEM])(uint16_t index);

extern STATIC_PARAMETERS_TYPEDEF *g_static_ptr;
extern RUNTIME_PARAMETERS_TYPEDEF g_runtime_inst;
extern RUNTIME_PARAMETERS_XVBASE_TYPEDEF *g_runtime_inst_xvbase;

extern uint8_t interphone_mode;
//extern uint16_t watch_index;
extern uint16_t gps_minute_second;
extern uint32_t timestamp_1s;

extern DSP_2P_TUNE_TYPEDEF *g_rf2p_tune_reg;

#ifndef P1_DEL_UNNECESSARY_KEY_PROC

extern const uint8_t project_menu_label[2][PROJECT_MENU_TOTAL_ITEM][14];

const uint8_t setup_menu_label[2][SETUP_MENU_TOTAL_ITEM][14] = {
		{"使锟矫伙拷锟斤拷", "锟斤拷示模式", "锟斤拷幕锟斤拷锟斤拷", "系统时锟斤拷", "锟斤拷锟斤拷", "锟借备锟斤拷息", "写频锟斤拷锟斤拷锟斤拷", "锟睫革拷锟斤拷锟斤拷", "锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷", "锟斤拷锟斤拷锟斤拷锟斤拷", "锟斤拷锟教诧拷锟斤拷", "锟斤拷锟斤拷"},
		{"Environment", "Display mode", "Backlight", "Set time", "Language", "Device info", "PIN Config", "Password", "Vocoder setup", "Encryption", "Project test", "Return"}
};

const uint8_t voice_crypto_popup_title[2][5][16] = {
		{"锟斤拷锟斤拷锟斤拷锟斤拷", "锟斤拷锟斤拷", "锟截憋拷", "锟斤拷钥", "锟斤拷锟斤拷"},
		{"Crypto setup", "Open", "Close", "KEY", "RET"}
};

const uint8_t cheji_xiepin_pin_config_title[2][3][16] = {
		{"写频锟斤拷锟斤拷锟斤拷", "锟斤拷锟斤拷", "写频"},
		{"PIN Config", "FUNC", "UART"}
};

const uint8_t set_battery_title[2][2][14] = {
		{"锟斤拷锟铰伙拷锟斤拷", "锟斤拷锟铰伙拷锟斤拷"},
		{"Normal", "Low temp."}
};

const uint8_t project_ext_func_title[2][2][10] = {
		{"锟斤拷锟斤拷扫锟斤拷", "RESERVED"},
		{"Scan NS", "RESERVED"}
};

const uint8_t display_mode_label[2][4][12] = {
//		{"锟斤拷锟斤拷锟斤拷示", "锟斤拷锟阶帮拷锟斤拷", "锟阶底猴拷锟斤拷", "锟节底帮拷锟斤拷"},
//		{"Reverse", "Blue+White", "White+Black", "Black+White"}
		{"锟斤拷  锟斤拷", "锟斤拷+锟斤拷", "锟斤拷+锟斤拷", "锟斤拷+锟斤拷"},
		{"Reverse", "Blu+Whi", "Whi+Bla", "Bla+Whi"}
};

const uint8_t module_805_version_info[MODULE_805_VERSION_MAX_TYPE][8] = {"V1", "BASE", "Vehicle", "Veh_Auto"};

#endif	// P1_DEL_UNNECESSARY_KEY_PROC

const DEV_NO_SEARTH_TABLE dev_type_table[DEV_NO_SEARTH_TABLE_TOTAL] = {
	{VICTEL_DOUBLE_CORE_P3,   "Victel P3"},
	{VICTEL_DOUBLE_CORE_P3_BASE, "Victel P3 BASE"},
};

uint8_t get_test_mode_flag(void)
{
	return g_test_mode;
}

uint8_t set_test_mode_flag(uint8_t flag)		// flag: 1-judge the project permit; 0xff-force; 0xfe-temp to set flag(not set watchdog&timeout)
{
	if ((flag == 0xff) || (flag == 0xfe) || (maintain_setup_parameter(PARA_OPERATE_READ, PROJECT_APP_PERMIT, 0)))
		g_test_mode = flag;
	else
		g_test_mode = 0;

	if (g_test_mode && (flag != 0xfe))
	{
#ifdef SET_WATCHDOG_ENABLE
		iwatchdog_init(0xffff);
#endif
	}

	return g_test_mode;
}

void setup_rf_power_state(uint8_t index)
{
	if (maintain_setup_parameter(PARA_OPERATE_READ, RF_POWER_PARAS_POS, 0) != index)
	{
		maintain_setup_parameter(PARA_OPERATE_WRITE, RF_POWER_PARAS_POS, index);
		save_user_data_to_flash(1);
		rf_power_switch(index);
	}
}


#ifndef P1_DEL_UNNECESSARY_KEY_PROC

///////////////////////////////////////////////////////////////////////
/*
void setup_to_workmode(uint16_t index)
{
	setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
	SwitchPage(show_switch_workmode_screen);
}
*/
void set_project_entry_at_shortcut(void)
{
	g_xiepin_pin_config &= ~0x40;
}

uint8_t is_project_entry_at_shortcut(void)
{
	return (g_xiepin_pin_config & 0x40) ? 0 : 1;
}

void set_battery_mode(uint16_t index)
{
	uint8_t lang_type;

	setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
	lang_type = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0;
	g_xiepin_pin_config = 0x20;
	set_popup_paras((maintain_setup_parameter(PARA_OPERATE_READ, BATTERY_MODE_PARA_POS, lang_type) ? (1 << 4) : 0) | 2,
		(uint32_t)show_setup_screen, (uint8_t *)setup_menu_label[lang_type][0],
		(uint8_t *)set_battery_title[lang_type][0], (uint8_t *)set_battery_title[lang_type][1], 0, (uint8_t *)1);
}

void setup_reserve_display(uint16_t index)
{
	uint8_t lang_type = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0;

	setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
	g_xiepin_pin_config = 0x80;
	set_popup_paras(4, (uint32_t)show_setup_screen, (uint8_t *)setup_menu_label[lang_type][1],
		(uint8_t *)display_mode_label[lang_type][0], (uint8_t *)display_mode_label[lang_type][1],
		(uint8_t *)display_mode_label[lang_type][2], (uint8_t *)display_mode_label[lang_type][3]);
}

void setup_to_backlight(uint16_t index)
{
	setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
	SwitchPage(show_backlight_screen);
}

void setup_to_settime(uint16_t index)
{
	setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
	SwitchPage(show_settime_screen);
}

void setup_to_language(uint16_t index)
{
	setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
	SwitchPage(show_language_screen);
}

void setup_to_device_info(uint16_t index)
{
	setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
	SwitchPage(show_device_info_screen);
}

void setup_to_password(uint16_t index)
{
#if 0 // !defined VICTEL_ZZWPRO_BASE && !defined VICTEL_MOBILE_NO_LCD
	setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
	SwitchPage(show_password_screen);
#endif	// VICTEL_ZZWPRO_BASE
}

void setup_to_vocoder_setup(uint16_t index)
{
	setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
	SwitchPage(show_vocoder_setup_screen);
}

void setup_to_voice_crypto(uint16_t index)
{
	uint8_t lang_type = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0;
	uint8_t par = (maintain_setup_parameter(PARA_OPERATE_READ, VOICE_CRYPTO_PRAR_POS, 0) ? 0 : 1) << 4;

	if (((GET_ENCRYPT_TYPE() == ENCRYPT_TYPE_MINISTRY) && smartcard_is_online()) ||
		(GET_ENCRYPT_TYPE() != ENCRYPT_TYPE_MINISTRY))
	{
		setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
		g_xiepin_pin_config &= 0x0f;
		if (GET_ENCRYPT_TYPE() != ENCRYPT_TYPE_MINISTRY)
		{
			par |= 4;
			set_popup_paras(par, (uint32_t)show_setup_screen, (uint8_t *)voice_crypto_popup_title[lang_type][0],
				(uint8_t *)voice_crypto_popup_title[lang_type][1], (uint8_t *)voice_crypto_popup_title[lang_type][2],
				(uint8_t *)voice_crypto_popup_title[lang_type][3], (uint8_t *)voice_crypto_popup_title[lang_type][4]);
		}
		else
		{
			par |= 2;
			set_popup_paras(par, (uint32_t)show_setup_screen, (uint8_t *)voice_crypto_popup_title[lang_type][0],
				(uint8_t *)voice_crypto_popup_title[lang_type][1], (uint8_t *)voice_crypto_popup_title[lang_type][2], 0, 0);
		}
	}
	else
	{
		GL_LCD_TipDraw(0, maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? (uint8_t *)"No crypto card" : (uint8_t *)" 锟斤拷锟饺诧拷锟斤拷芸锟�", 0, 1000);
	}
}

void setup_to_project_test(uint16_t index)
{
	uint8_t lang_type;

	setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
	lang_type = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0;
	g_xiepin_pin_config = 0x40;
	set_popup_paras(0 | 4, (uint32_t)show_setup_screen, (uint8_t *)setup_menu_label[lang_type][10],
		(uint8_t *)project_menu_label[lang_type][2], (uint8_t *)project_menu_label[lang_type][8],
		(uint8_t *)project_ext_func_title[lang_type][0], (uint8_t *)project_ext_func_title[lang_type][1]);
}

void setup_crypto_enable(uint16_t index)
{
	uint16_t sel;

	sel = (index == 0) ? 1 : 0;
	if (sel != maintain_setup_parameter(PARA_OPERATE_READ, VOICE_CRYPTO_PRAR_POS, 0))
	{
		maintain_setup_parameter(PARA_OPERATE_WRITE, VOICE_CRYPTO_PRAR_POS, sel);
		sd_encrypt_setup_by_user();
		save_user_data_to_flash(1);
	}
}

void setup_xiepin_pin_setting(uint16_t index)
{
	uint8_t lang_type = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0;
	uint8_t par = 2;

	setup_item_selected = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[0]->objPTR))->WhichIsActive;
	par |= ((g_xiepin_pin_config & 0x0f) ? 1 : 0) << 4;
	g_xiepin_pin_config &= 0x0f;
	g_xiepin_pin_config |= 0x10;
	set_popup_paras(par, (uint32_t)show_setup_screen, (uint8_t *)cheji_xiepin_pin_config_title[lang_type][0],
		(uint8_t *)cheji_xiepin_pin_config_title[lang_type][1], (uint8_t *)cheji_xiepin_pin_config_title[lang_type][2], 0, 0);
}

void setup_to_setup_others(uint16_t index)
{
	// setup_item_selected = 0;
	SwitchPage(show_setup_others_screen);
}

void setup_screen_poll(uint16_t index, uint32_t code)
{
	uint16_t idx, total;

	idx = 0;	// the control index that wanted to be accepted the operation, it must be a list
	total = ((GL_ListGrp_TypeDef *)(page_setup->PageControls[idx]->objPTR))->ListMemberCount;
	if (IS_RETURN_KEYCODE(code))
	{
		setup_to_setup_others(index);
	}
	else if ((code > KEY_CODE_0) && (code <= KEY_CODE_9) && ((code - KEY_CODE_0) <= total))	// shortcut start at 1
	{
		if ((index >= page_setup->ControlCount) || (index == idx))	// not select any control(e. g. just enter the page) or selected
			relocate_list_display(page_setup, idx, code - KEY_CODE_0 - 1);
			//setup_func_list[code - 1](0);					// only one object, so ignore the parameter index
	}
}

uint32_t get_setup_menu_configuration(uint16_t *num)
{
	uint32_t menu_disable;
	uint16_t n, work_mode;

/*	f_list[0] = set_battery_mode;		// setup_to_workmode;
	f_list[1] = setup_reserve_display;	// reverse display
	f_list[2] = setup_to_backlight;		// backlight
	f_list[3] = setup_to_settime;		// setup time
	f_list[4] = setup_to_language;		// language
	f_list[5] = setup_to_device_info;	// device info
	f_list[6] = setup_xiepin_pin_setting;
	f_list[7] = setup_to_password;		// modify password
	f_list[8] = setup_to_vocoder_setup;	// setup vocoder
	f_list[9] = setup_to_voice_crypto;	// setup crypto
	f_list[10] = setup_to_project_test;	// project test
	f_list[11] = setup_to_setup_others;	// return
*/	menu_disable = get_menu_config(SETUP_MENU_CONFIG_FLAG, SETUP_MENU_TOTAL_ITEM, &n);
//	if ((g_devNo_real != MOBILE_MODE_PDT_ZZW_816V) && !DEV_IS_805_SERIES(g_devNo_real))
		menu_disable |= 64;				// disable item 6
//	else
		menu_disable |= 1;
	work_mode = maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0);
	if (work_mode >= INTERPHONE_MODE_MPT_TRUNKING)
	{
		if (dsp_is_64xx() && (work_mode >= INTERPHONE_MODE_ZZW_ADHOC))
			menu_disable |= 512;		// disable item 9
		else
			menu_disable |= 256 | 512;	// disable item 8&9

	}

//	if (work_mode <= INTERPHONE_MODE_ZZWPRO_Q)
//		menu_disable |= 1024;			// disable item 10

	if (num)
		*num = n;

	return menu_disable;
}

#ifdef USE_DIAL_BOX_TO_SETUP_CRYPTO_KEY
void show_crypto_key_range(void)
{
	uint8_t crypto_info[20];

	sprintf((char *)crypto_info, "%s:1-%3d", maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? "Range" : "锟斤拷围", get_aes_key_total());
	GL_LCD_TipDraw(0, crypto_info, 0, 1000);
}
#endif

uint8_t crypto_error_info[2][12] = {
	"锟睫匡拷锟斤拷锟斤拷钥",
	"No KEY"
};
uint8_t set_crypto_error_info[2][14] = {
	"使锟斤拷锟斤拷锟斤拷KEY",
	"Built-in used"
};

void set_crypto_key_index(signed long set_val)
{
	uint8_t crypto_info[20], crypto_total = get_aes_key_total();

	crypto_info[0] = (uint8_t)(set_val - 1);
	aes256_switch_key_index((crypto_info[0] < crypto_total) ? crypto_info[0] : 255);
	if (g_runtime_inst.runtime_paras.aes_key_index < crypto_total)
		sprintf((char *)crypto_info, "Set key=%d", g_runtime_inst.runtime_paras.aes_key_index + 1);
	else
		p_strcpy(crypto_info, set_crypto_error_info[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0]);

	GL_LCD_TipDraw(0, crypto_info, 0, 1000);
}

void show_setup_screen(void)
{
	uint32_t menu_disable;
	uint16_t n;
	uint8_t i, j, lang_type = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0;
	GL_PageControls_TypeDef *list, *slide;
	void (*f_list[SETUP_MENU_TOTAL_ITEM])(uint16_t index);


#ifdef USE_DIAL_BOX_TO_SETUP_CRYPTO_KEY
	menu_disable = is_dialing_return((uint32_t)show_setup_screen);
	if (menu_disable != 0xffffffff)
	{
		set_crypto_key_index((signed long)menu_disable);
	}
	else if (is_popup_return((uint32_t)show_setup_screen))	// return from popup
#else
	if (is_popup_return((uint32_t)show_setup_screen))		// return from popup
#endif
	{
		i = get_popup_result();								// 0-open; 1-close(0-ptt/hook; 1-uart)
		if (i <= 3)
		{
			if ((g_xiepin_pin_config & 0xf0) == 0x10)
			{
				xiepin_pin_config(i);
				g_xiepin_pin_config &= 0xf0;
				g_xiepin_pin_config |= i;
			}
			else if ((g_xiepin_pin_config & 0xf0) == 0x20)
			{
				maintain_setup_parameter(PARA_OPERATE_WRITE, BATTERY_MODE_PARA_POS, i);
				save_user_data_to_flash(1);
			}
			else if ((g_xiepin_pin_config & 0xf0) == 0x40)
			{
				if (i == 0)
				{
					SwitchPage(show_slot_detail_screen);
					return;
				}
				else if (i == 1)
				{
					set_admit_test_mode(0);
					SwitchPage(show_admitance_testing_screen);
					return;
				}
				else if (i == 2)
				{
					SwitchPage(show_scanning_noise_screen);
					return;
				}
			}
			else
			{
				if (i <= 1)						// open & close
				{
					setup_crypto_enable(i);		// set crypto key index
				}
				else if (i == 2)
				{
					if (get_aes_key_total() == 0)
					{
						GL_LCD_TipDraw(0, crypto_error_info[lang_type], 0, 1000);
					}
					else
					{
#ifdef USE_DIAL_BOX_TO_SETUP_CRYPTO_KEY
						call_dialing_page((uint32_t)show_setup_screen, g_runtime_inst.runtime_paras.aes_key_index + 1);
						timer_initial(1, 400, show_crypto_key_range);
#else
						SwitchPage(show_set_crypto_key_screen);
#endif
						return;
					}
				}
			}
		}
	}

	/* ************** GRAPHIC LIBRARY - SETUP SCREEN ************** */
	page_setup = malloc(sizeof(GL_Page_TypeDef));
	CreatePageObj(page_setup, setup_screen_poll);


	f_list[0] = set_battery_mode;		// setup_to_workmode;
	f_list[1] = setup_reserve_display;	// reverse display
	f_list[2] = setup_to_backlight;		// backlight
	f_list[3] = setup_to_settime;		// setup time
	f_list[4] = setup_to_language;		// language
	f_list[5] = setup_to_device_info;	// device info
	f_list[6] = setup_xiepin_pin_setting;
	f_list[7] = setup_to_password;		// modify password
	f_list[8] = setup_to_vocoder_setup;	// setup vocoder
	f_list[9] = setup_to_voice_crypto;	// setup crypto
	f_list[10] = setup_to_project_test;	// project test
	f_list[11] = setup_to_setup_others;	// return

	menu_disable = get_setup_menu_configuration(&n);

	list = NewListGrp(DEFAULT_FONT_STYLE, BUTTON_ALIGN_LEFT, front_ground_color);

	for (i = 0, j = 0; i < n; i++)						// i: index at the total menu design; j: display index at the configuration
	{
		if ((menu_disable & (1 << i)) == 0)				// insert the menu item
		{
//			setup_func_list[j] = f_list[i];
//			AddListMember(list->objPTR, setup_menu_label[lang_type][i], setup_func_list[j]);
			AddListMember(list->objPTR, setup_menu_label[lang_type][i], f_list[i]);
			j++;
		}
	}
	AddPageControlObj(0, 0, LCD_ACTIVE_HEIGHT + 1, LCD_PIXEL_WIDTH - 12, LCD_PIXEL_HEIGHT - (LCD_ACTIVE_HEIGHT + 1)/*53*/, list, page_setup);

	slide = NewSlidebar(0);
	AddPageControlObj(1, LCD_PIXEL_WIDTH - 11, LCD_ACTIVE_HEIGHT + 1, 9, LCD_PIXEL_HEIGHT - (LCD_ACTIVE_HEIGHT + 2)/*52*/, slide, page_setup);
	conjoin_list_slidebar(list, slide);

	page_setup->ShowPage( page_setup, GL_TRUE );

	relocate_list_display(page_setup, 0, setup_item_selected);
}

#endif	// P1_DEL_UNNECESSARY_KEY_PROC

uint32_t get_log_in_out_id(void)
{
	if (dev_is_base())
	{
		return set_xvbase_dispatchid(0xffffffff);
	}
	else // dev_is_base
	{
		return g_static_ptr->login_out_setup.loginout_id;
	} // dev_is_base
}

uint16_t get_log_in_out_paras(uint8_t flag)	// flag: 0-login, 1-logout, 2-warn with gps; return: bit15:1-enable,0-disable;bit9-0:loginout code
{
	uint16_t ret = 0;

	if (dev_is_base())
	{
		return ((set_xvbase_dispatchid(0xffffffff) & 0x00ffffff) && (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)) ? 0x8000 : 0;
	}
	else // dev_is_base
	{
		if ((interphone_mode == INTERPHONE_MODE_PDT_CONV) || (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC))
		{
			if ((flag == 0) || (flag == 1))
			{
				if (g_static_ptr->login_out_setup.loginout_notify && (get_log_in_out_id() & 0x00ffffff))
					ret = 0x8000 | ((flag == 0) ? g_static_ptr->login_out_setup.login_code : g_static_ptr->login_out_setup.logout_code);
			}
			else if (flag == 2)
			{
				if (g_static_ptr->login_out_setup.warning_with_gps)
					ret = 0x8000;
			}
		}
		return ret;
	} // dev_is_base
}

uint8_t auto_rf_power_is_enable(void)
{
	uint8_t work_mode = maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0);

	return ((work_mode == INTERPHONE_MODE_PDT_TRUNKING) || (work_mode == INTERPHONE_MODE_MPT_TRUNKING) || (work_mode > INTERPHONE_MODE_ZZW_ADHOC)) ? 1 : 0;
}

uint16_t maintain_setup_parameter(uint8_t op, uint8_t type, uint8_t content)	// op==0:read; 1:write
{
	uint8_t operate = op & 0x01, ret = 1;

	switch (type)
	{
		case RF_POWER_PARAS_POS:		/*Byte 0*/
			if (operate)
			{
				g_runtime_inst.runtime_paras.rf_power = content;
				set_auto_switch_rf_power_enable();	// nested Call
			}
			else
			{
				if (is_admittest_screen() && dev_power_ctrl_by_db())
				{
					ret = admit_test_rf_power_level();
				}
				else
				{
					ret = g_runtime_inst.runtime_paras.rf_power;
					if (dev_power_ctrl_by_db())
					{
						if (ret >= RF_POWER_LEVEL)
							ret = RF_POWER_LEVEL - 1;
					}
					else
					{
						if (ret >= 2)	// 0-low; 1-high; else-auto
						{
							if (auto_rf_power_is_enable())
								ret = 2;
							else
								ret = 1;
						}
					}
				}
			}
			break;
		case SPEAKER_MUTE_PARAS_POS:	/*Byte 1*/
			if (operate)
				g_runtime_inst.runtime_paras.voice_para.voice_mute = content ? 1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.voice_para.voice_mute;
			break;

		case VOICE_CRYPTO_PRAR_POS:
			if (operate)
				g_runtime_inst.runtime_paras.voice_para.voice_crypto = content ?  1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.voice_para.voice_crypto;
			break;

		case SD_CARD_INIT_DISABLE_POS:
			if (operate)
				g_runtime_inst.runtime_paras.voice_para.sdcard_disable = content ?  1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.voice_para.sdcard_disable;
			break;

		case SQUELCH_DOOR_SETUP_POS:
			if (operate)
				g_runtime_inst.runtime_paras.voice_para.squlch_door_open = content ?  1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.voice_para.squlch_door_open;
			break;

		case SIGNALING_TONE_OUTPUT_POS:
			if (operate)
				g_runtime_inst.runtime_paras.voice_para.signaling_tone = content ?  1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.voice_para.signaling_tone;
			break;

		case TIP_SOUND_PARAS_POS:	/*Byte 2*/
			if (operate)
			{
				if (is_bt_insert() == 0)
					g_runtime_inst.runtime_paras.tip_sound.tip_self = content & 0x0F;
				else
					g_runtime_inst.runtime_paras.tip_sound.tip_earphone = content & 0x0F;
			}
			else
			{
				if (is_bt_insert() == 0)
					ret = g_runtime_inst.runtime_paras.tip_sound.tip_self;
				else
					ret = g_runtime_inst.runtime_paras.tip_sound.tip_earphone;

				if (ret >= VOICE_GAIN_LEVEL)
					ret = VOICE_GAIN_LEVEL - 1;
			}
			break;

		case MICROPHONE_PARAS_POS:	/*Byte 3: 锟斤拷3bit为锟斤拷锟斤拷MIC锟斤拷锟斤拷, 0-4锟斤拷锟斤拷锟街憋拷锟接�5dB锟斤拷10dB锟斤拷20dB锟斤拷25dB锟斤拷30dB锟斤拷锟斤拷5bit为锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷, 0-9锟斤拷锟斤拷0为锟斤拷锟斤拷锟斤拷9为锟斤拷锟斤拷锟斤拷锟�*/
			if (operate)
			{
				if (is_bt_insert() == 0)
					g_runtime_inst.runtime_paras.self_gain.mic_gain = content & 0x07;		// use Byte3
				else
					g_runtime_inst.runtime_paras.earphone_gain.mic_gain = content & 0x07;	// use Byte14
			}
			else
			{
				if (is_bt_insert() == 0)
					ret = g_runtime_inst.runtime_paras.self_gain.mic_gain;
				else
					ret = g_runtime_inst.runtime_paras.earphone_gain.mic_gain;

				if (ret >= MICROPHONE_GAIN_LEVEL)
					ret = MICROPHONE_GAIN_LEVEL - 1;
			}
			break;

		case LANGUAGE_PARAS_POS:	/*Byte 4*/
			if (operate)
				g_runtime_inst.runtime_paras.language_para = content;
			else
				ret = g_runtime_inst.runtime_paras.language_para;
			break;

		case VOICE_PARAS_POS:		/*Byte 14: 锟斤拷3bit为锟斤拷锟斤拷MIC锟斤拷锟芥；锟斤拷5bit为锟斤拷锟斤拷锟斤拷频锟斤拷锟斤拷*/
			if (operate)
			{
				if (is_bt_insert() == 0)
					g_runtime_inst.runtime_paras.self_gain.sound_gain = content & 0x1f;		// use Byte3
				else
					g_runtime_inst.runtime_paras.earphone_gain.sound_gain = content & 0x1f;	// use Byte14
			}
			else
			{
				if (is_bt_insert() == 0)
					ret = g_runtime_inst.runtime_paras.self_gain.sound_gain;
				else
					ret = g_runtime_inst.runtime_paras.earphone_gain.sound_gain;

				if (ret >= VOICE_GAIN_LEVEL)
					ret = VOICE_GAIN_LEVEL - 1;
			}
			break;

		case GPS_POWER_PARAS_POS:	/*Byte 5*/
			if (operate)
				g_runtime_inst.runtime_paras.pwr_ctrl.pwr_gps = content ?  1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.pwr_ctrl.pwr_gps;
			break;

		case BT_POWER_PARAS_POS:
			if (operate)
				g_runtime_inst.runtime_paras.pwr_ctrl.pwr_bt = content ?  1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.pwr_ctrl.pwr_bt;
			break;

		case WIFI_POWER_PARAS_POS:
			if (operate)
				g_runtime_inst.runtime_paras.pwr_ctrl.pwr_wifi = content ?  1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.pwr_ctrl.pwr_wifi;
			break;

		case GPS_TYPE_PARAS_POS:
			if (operate)
				g_runtime_inst.runtime_paras.pwr_ctrl.gps_type = content;
			else
				ret = g_runtime_inst.runtime_paras.pwr_ctrl.gps_type;
			break;

		case BATTERY_MODE_PARA_POS:
			if (operate)
				g_runtime_inst.runtime_paras.pwr_ctrl.batt_type = content ? 1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.pwr_ctrl.batt_type;
			break;

		case WORK_MODE_PARAS_POS:	/*Byte 7  1:锟斤拷锟街筹拷锟斤拷; 2:锟斤拷锟街硷拷群(PDT); 3:模锟解集群(MPT); 4:模锟解常锟斤拷(MPT); 5:锟斤拷锟斤拷锟斤拷; 6:锟斤拷锟斤拷锟斤拷PRO_8K; 7:锟斤拷锟斤拷锟斤拷PRO_X; 8:锟斤拷锟斤拷锟斤拷PRO_V*/
			if (operate)
			{
				g_runtime_inst.runtime_paras.work_mode = content;
			}
			else
			{
#if defined DEBUG_MPT_BASE
				return INTERPHONE_MODE_MPT_CONV;
#elif defined DEBUG_PDT_CONV_ONLY
				return INTERPHONE_MODE_PDT_CONV;	// INTERPHONE_MODE_PDT_TRUNKING;	// 20240101: debug trunking now
#else
				ret = INTERPHONE_MODE_PDT_CONV;
				if (dev_is_base())
				{
				}
				else // dev_is_base
				{
					if (is_device_mode_only_zzw())
					{
						ret = INTERPHONE_MODE_ZZWPRO_Q;
					}
					else if (is_device_mode_only_pdt())
					{
						if ((g_runtime_inst.runtime_paras.work_mode >= INTERPHONE_MODE_PDT_CONV) &&
							(g_runtime_inst.runtime_paras.work_mode <= INTERPHONE_MODE_MPT_CONV))
						{
							ret = g_runtime_inst.runtime_paras.work_mode;
						}
					}
					else
					{
						if (((g_runtime_inst.runtime_paras.work_mode >= INTERPHONE_MODE_PDT_CONV) &&
							(g_runtime_inst.runtime_paras.work_mode <= INTERPHONE_MODE_MPT_CONV)) ||
							(g_runtime_inst.runtime_paras.work_mode <= INTERPHONE_MODE_ZZWPRO_Q))
							ret = g_runtime_inst.runtime_paras.work_mode;
						else
							ret = INTERPHONE_MODE_PDT_TRUNKING;

					}

				} // dev_is_base
#endif
			}
			break;

		case LCD_REVERSE_DISPLAY_POS:/*Byte 8*/
			if (operate)
				g_runtime_inst.runtime_paras.lcd_ctrl.lcd_reverse = content;
			else
				ret = g_runtime_inst.runtime_paras.lcd_ctrl.lcd_reverse;
			break;

		case LCD_TURN_OFF_TIME_POS:
			if (operate)
			{
				g_runtime_inst.runtime_paras.lcd_ctrl.lcd_turnoff = content;
				g_runtime_inst.runtime_paras.reserved9 &= ~0x0C;
				g_runtime_inst.runtime_paras.reserved9 |= (content >> 4) & 0x0C;
			}
			else
			{
				if (get_test_mode_flag())
				{
					ret = 0;
				}
				else
				{
					ret = g_runtime_inst.runtime_paras.lcd_ctrl.lcd_turnoff;
					ret |= (g_runtime_inst.runtime_paras.reserved9 & 0x0C) << 4;
				}
			}
			break;

		case LCD_DISPLAY_THEME_POS:
			if (operate)
			{
				g_runtime_inst.runtime_paras.reserved9 &= ~0x03;
				g_runtime_inst.runtime_paras.reserved9 |= content & 0x03;
			}
			else
			{
				ret = g_runtime_inst.runtime_paras.reserved9 & 0x03;
			}
			break;

		case CTRL_SCANNING_MODE_POS:/*Byte 10*/
			if (operate)
			{
				g_runtime_inst.runtime_paras.ctrl_tscc_mode = content;
			}
			else
			{
				if (g_runtime_inst.runtime_paras.ctrl_tscc_mode <= SCANNING_MODE_ASSIGNED)
					ret = g_runtime_inst.runtime_paras.ctrl_tscc_mode;
				else
					ret = SCANNING_MODE_SHORT_HUNT;
			}
			break;

		case ACTIVE_GROUP_OF_BOOKS:	/*Byte 11*/
			if (operate)
			{
				g_runtime_inst.runtime_paras.active_group_book = content;
//				g_runtime_inst.runtime_paras.active_group_ctrl &= 0x3F;
//				g_runtime_inst.runtime_paras.active_group_ctrl |= op & (~0x3F);
			}
			else
			{
//				return (((uint16_t)g_runtime_inst.runtime_paras.active_group_ctrl << 2) & 0x300) | g_runtime_inst.runtime_paras.active_group_book;
				ret = g_runtime_inst.runtime_paras.active_group_book;
			}
			break;

		case ACTIVE_ZZWPRO_GROUP_OF_BOOKS:	/*Byte 6*/
			if (operate)
			{
				g_runtime_inst.runtime_paras.active_zzwpro_group_book = content;
//				g_runtime_inst.runtime_paras.active_group_ctrl &= 0x3F;
//				g_runtime_inst.runtime_paras.active_group_ctrl |= op & (~0x3F);
			}
			else
			{
//				return (((uint16_t)g_runtime_inst.runtime_paras.active_group_ctrl << 2) & 0x300) | g_runtime_inst.runtime_paras.active_group_book;
				ret = g_runtime_inst.runtime_paras.active_zzwpro_group_book;
			}
			break;

		case NOISE_THRESHOLD_LEVEL:	/*Byte 12*/
			if (operate)
				g_runtime_inst.runtime_paras.noise_threshold = content;
			else
				ret = g_runtime_inst.runtime_paras.noise_threshold;
			break;

		case PROJECT_APP_PERMIT:	/*Byte 13*/
			if (operate)
				g_runtime_inst.runtime_paras.project_mode_permit.prj_permit = content ? 1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.project_mode_permit.prj_permit;
			break;

		case CONV_DISPLAY_POSITION:
			if (operate)
				g_runtime_inst.runtime_paras.project_mode_permit.conv_position = content ? 1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.project_mode_permit.conv_position;
			break;

		case DIAL_BACKGROUND_PERMIT:
			if (operate)
				g_runtime_inst.runtime_paras.project_mode_permit.dial_background = content ? 1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.project_mode_permit.dial_background;
			break;

		case DIAL_IND_TRUNK_PERMIT:
			if (operate)
				g_runtime_inst.runtime_paras.project_mode_permit.dial_ind_trunk = content ? 0 : 1;
			else
				ret = g_runtime_inst.runtime_paras.project_mode_permit.dial_ind_trunk ? 0 : 1;
			break;

		case DIAL_IND_CONV_PERMIT:
			if (operate)
				g_runtime_inst.runtime_paras.project_mode_permit.dial_ind_conv = content ? 0 : 1;
			else
				ret = g_runtime_inst.runtime_paras.project_mode_permit.dial_ind_conv ? 0 : 1;
			break;

		case HEADPHONE_VOICE_PARAS_POS:	// 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷SPEAKER锟斤拷锟斤拷喜锟斤拷锟斤拷锟斤拷每锟斤拷锟斤拷占锟斤拷4bit锟斤拷锟斤拷锟斤拷锟斤拷(锟斤拷锟斤拷)锟节碉拷
			if (operate)
			{
				g_runtime_inst.runtime_paras.self_gain.sound_gain = content & 0x0f;
				g_runtime_inst.runtime_paras.earphone_gain.sound_gain = (content >> 4) & 0x0f;
			}
			else
			{
				if (g_runtime_inst.runtime_paras.self_gain.sound_gain >= VOICE_GAIN_LEVEL)
					ret = VOICE_GAIN_LEVEL - 1;
				else
					ret = g_runtime_inst.runtime_paras.self_gain.sound_gain;

				if (g_runtime_inst.runtime_paras.earphone_gain.sound_gain >= VOICE_GAIN_LEVEL)
					ret |= ((VOICE_GAIN_LEVEL - 1) << 4) & 0xF0;
				else
					ret |= (g_runtime_inst.runtime_paras.earphone_gain.sound_gain << 4) & 0xF0;
			}
			break;

		case MICROPHONE_VOICE_PARAS_POS:// 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷朔锟斤拷锟斤拷锟较诧拷锟斤拷锟斤拷锟矫匡拷锟斤拷锟秸硷拷锟�4bit锟斤拷锟斤拷锟斤拷锟斤拷(锟斤拷锟斤拷)锟节碉拷
			if (operate)
			{
				g_runtime_inst.runtime_paras.self_gain.mic_gain = content & 0x07;
				g_runtime_inst.runtime_paras.earphone_gain.mic_gain = (content >> 4) & 0x07;
			}
			else
			{
				if (g_runtime_inst.runtime_paras.self_gain.mic_gain >= MICROPHONE_GAIN_LEVEL)
					ret = MICROPHONE_GAIN_LEVEL - 1;
				else
					ret = g_runtime_inst.runtime_paras.self_gain.mic_gain;
				if (g_runtime_inst.runtime_paras.earphone_gain.mic_gain >= MICROPHONE_GAIN_LEVEL)
					ret |= ((MICROPHONE_GAIN_LEVEL - 1) << 4) & 0xF0;
				else
					ret |= (g_runtime_inst.runtime_paras.earphone_gain.mic_gain << 4) & 0xF0;
			}

			break;

		case ACTIVE_GROUP_OF_CTRL_LIST:
			if (operate)
			{
//				g_runtime_inst.runtime_paras.active_group_ctrl &= ~0x3F;
//				g_runtime_inst.runtime_paras.active_group_ctrl |= content & 0x3F;
				g_runtime_inst.runtime_paras.active_group_ctrl = content;
			}
			else
			{
//				ret = g_runtime_inst.runtime_paras.active_group_ctrl & 0x3F;
				ret = g_runtime_inst.runtime_paras.active_group_ctrl;
				if (ret >= MAX_GROUPS_OF_CTRL)
				{
//					g_runtime_inst.runtime_paras.active_group_ctrl &= ~0x3F;
					g_runtime_inst.runtime_paras.active_group_ctrl = 0;
					ret = 0;
				}
			}
			break;

		case KEY_FUNC_REMAP_VOLUME_IND:
			if (operate == 0)
				ret = g_static_ptr->key_remap_table.vol_independent;
			else
				ret = 0;
			break;

//		case KEY_FUNC_REMAP_PTT:	// 20220425:锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷ptt锟斤拷锟斤拷锟矫凤拷N/Q
//			ret = 16;
//			break;

		case KEY_FUNC_REMAP_LEFT:
		case KEY_FUNC_REMAP_RIGHT:
		case KEY_FUNC_REMAP_ENTER:
		case KEY_FUNC_REMAP_MIDDLE:
		case KEY_FUNC_REMAP_PTT:
		case KEY_FUNC_REMAP_PTT2:
		case KEY_FUNC_REMAP_RETURN:
		case KEY_FUNC_REMAP_SHORTCUT:
		case KEY_FUNC_REMAP_ALARM:
			if (operate == 0)
			{
				ret = *(&g_static_ptr->key_remap_table.vol_independent + type - KEY_FUNC_REMAP_VOLUME_IND);

				if ((ret & 0x3f) == (KEY_CODE_SWITCH_MODE - 1))
				{
					set_shortcut_switch_workmode((ret >> 6) & 0x03);
					ret = KEY_CODE_SWITCH_MODE - 1;
				}
				else if ((ret & 0x3f) == (KEY_CODE_USER3 - 1))
				{
//					set_shortcut_items((ret >> 6) & 0x03);
//					ret = KEY_CODE_USER3 - 1;
					ret = 0;
				}
//				else if (ret == KEY_CODE_SCANNING - 1)	// just for test when no software can config it
//				{
//					ret = KEY_CODE_3PTT - 1;
//					vlog_i("screen","\t%d:SCAN->3PTT", type);
//				}
				else if ((ret >= 12) && (ret <= 31))
					ret = ret;
				else
					ret = 0;
			}
			else
			{
				ret = 0;
			}
			break;

		case KEY_FUNC_REMAP_ZZW_PTT:
		case KEY_FUNC_REMAP_ZZW_USER1:
		case KEY_FUNC_REMAP_ZZW_USER2:
			if (operate == 0)
				ret = *(&g_static_ptr->key_remap_table.zzw_func_ptt + type - KEY_FUNC_REMAP_ZZW_PTT);
			else
				ret = 0;
			break;

		case DMR_FUNC_CONFIG_PARA_POS:
			if (operate)
				*((uint8_t *)&g_static_ptr->key_remap_table.dmr_func_config) = content;
			else
				ret = *((uint8_t *)&g_static_ptr->key_remap_table.dmr_func_config);
			break;

		case DEBOUNCE_KEY_PARA_POS:
			if (operate)
				*((uint8_t *)&g_static_ptr->key_remap_table.func_debounce) = content;
			else
				ret = *((uint8_t *)&g_static_ptr->key_remap_table.func_debounce);
			break;

		case VOCODER_ZZW_SETUP_POS:
			if (operate)
				*((uint8_t *)&g_runtime_inst.runtime_paras.zzw_vocoder) = content & 0x3f;
			else
				ret = *((uint8_t *)&g_runtime_inst.runtime_paras.zzw_vocoder) & 0x3f;
			break;

		case VOCODER_ZZW_SETUP_TYPE:
			if (operate)
				g_runtime_inst.runtime_paras.zzw_vocoder.vocoder_type = content ? 1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.zzw_vocoder.vocoder_type;
			break;

		case VOCODER_ZZW_SETUP_SPEED:
			if (operate)
				g_runtime_inst.runtime_paras.zzw_vocoder.vocoder_speed = content ? 1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.zzw_vocoder.vocoder_speed;
			break;

		case VOCODER_SETUP_POS:
			if (operate)
				*((uint8_t *)&g_runtime_inst.runtime_paras.pdt_vocoder) = content & 0x0f;
			else
				ret = *((uint8_t *)&g_runtime_inst.runtime_paras.pdt_vocoder) & 0x0f;
			break;

		case VOCODER_PDT_SETUP_TYPE:
			if (operate)
				g_runtime_inst.runtime_paras.pdt_vocoder.vocoder_type = content ? 1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.pdt_vocoder.vocoder_type;
			break;

		case VOCODER_PDT_SETUP_SPEED:
			if (operate)
				g_runtime_inst.runtime_paras.pdt_vocoder.vocoder_speed = content ? 1 : 0;
			else
				ret = g_runtime_inst.runtime_paras.pdt_vocoder.vocoder_speed;
			break;

		case PASSWORD_NUM1:
		case PASSWORD_NUM2:
		case PASSWORD_NUM3:
			if (operate)
				g_runtime_inst.runtime_paras.password[type - PASSWORD_NUM1] = content;
			else
				ret = g_runtime_inst.runtime_paras.password[type - PASSWORD_NUM1];
			break;

		case TIP_CONFIG_PARA_POS:
			if (operate)
				*((uint8_t *)&g_runtime_inst.runtime_paras.tip_config) = content;
			else
				ret = *((uint8_t *)&g_runtime_inst.runtime_paras.tip_config);
			break;

		default:
			ret = 0;
			break;
	}

	return (uint16_t)ret;
}

uint8_t get_vocoder_setup_content(void)
{
	uint8_t tmp = 0;

	tmp = (maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0) >= INTERPHONE_MODE_ZZW_ADHOC) ? VOCODER_ZZW_SETUP_POS : VOCODER_SETUP_POS;
	return maintain_setup_parameter(PARA_OPERATE_READ, tmp, 0);
}

uint8_t get_vocoder_content_with_work_mode(uint8_t work_mode)
{
	uint8_t tmp = 0;

	tmp = (work_mode >= INTERPHONE_MODE_ZZW_ADHOC) ? VOCODER_ZZW_SETUP_POS : VOCODER_SETUP_POS;
	return maintain_setup_parameter(PARA_OPERATE_READ, tmp, 0);
}

void set_vocoder_setup_content(uint8_t cont)
{
	uint8_t tmp;

	tmp = (maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0) >= INTERPHONE_MODE_ZZW_ADHOC) ? VOCODER_ZZW_SETUP_POS : VOCODER_SETUP_POS;
	maintain_setup_parameter(PARA_OPERATE_WRITE, tmp, cont);
}

uint8_t get_q_vocoder_speed(void)
{
	return g_runtime_inst.runtime_paras.zzw_vocoder.vocoder_type_q;
}

uint8_t get_pdt_vocoder_type(void)		// 0-ambe, 1-nvoc
{
	return g_runtime_inst.runtime_paras.pdt_vocoder.vocoder_type;
}


///////////////////// initial the setup paras //////////////////////////////
uint8_t get_mobile_hardware_mode(void)
{
	return g_devNo;
}

uint8_t get_mobile_rf_type(void)
{
	return g_rf_type;
}

uint8_t get_mobile_dev_type(void)
{
	return g_dev_type;
}

uint8_t get_real_esn_second_sector(void)
{
	return g_devNo_real;
}

uint8_t get_805_version(void)
{
	return g_805_version;
}

#define DEV_IS_SOMETHING_64XX			0x0001
#define DEV_IS_SOMETHING_THIN			0x0002
#define DEV_IS_SOMETHING_380M			0x0004
#define DEV_IS_SOMETHING_NOLCD			0x0008
#define DEV_IS_SOMETHING_POWER_DB		0x0010
#define DEV_IS_SOMETHING_UART2_DAT		0x0020
#define DEV_IS_SOMETHING_UART2_VOC		0x0040
#define DEV_IS_SOMETHING_SHOW_BATTERY	0x0080
#define DEV_IS_SOMETHING_FIXED_GPS		0x0100
#define DEV_IS_SOMETHING_UART2_BT		0x0200
#define DEV_IS_SOMETHING_USE_XVSTACK	0x0400
#define DEV_IS_SOMETHING_FORCE_THROUGHOUT	0x0800
#define DEV_IS_SOMETHING_NO_CCM			0x1000
#define DEV_IS_SOMETHING_BYPASS_STACK	0x2000
#define DEV_IS_SOMETHING_M1_DEVICE		0x4000
#define DEV_IS_SOMETHING_UART2_NEW_BT	0x8000

uint32_t dsp_is_64xx(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_64XX;
}

uint32_t dev_is_thin(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_THIN;
}

uint32_t dev_is_380m(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_380M;
}

uint32_t dev_havenot_lcd(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_NOLCD;
}

uint32_t dev_power_ctrl_by_db(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_POWER_DB;
}

uint32_t dev_is_bd_only(void)
{
	return (g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_BEIDOU_ONLY) ? 1 : 0;
}

uint32_t uart2_use_as_dataport(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_UART2_DAT;
}

void set_uart2_use_as_dataport(uint8_t en)	// 0-锟斤拷锟矫ｏ拷1-锟斤拷锟斤拷锟斤拷锟矫ｏ拷锟斤拷DATA同时锟斤拷锟斤拷VOC锟斤拷锟斤拷锟叫雌碉拷锟斤拷锟絍OC锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟矫ｏ拷锟斤拷锟接猴拷锟斤拷锟斤拷锟矫ｏ拷锟斤拷锟斤拷2-锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟矫ｏ拷锟斤拷锟斤拷DATA锟斤拷VOC锟斤拷锟斤拷锟叫雌碉拷锟斤拷锟絍OC锟斤拷锟斤拷
{
	if (en == 0)
	{
		g_dev_is_something &= ~(DEV_IS_SOMETHING_UART2_DAT | DEV_IS_SOMETHING_UART2_VOC);
		vlog_i("screen","uart2 init(0)=%04x", g_dev_is_something);
	}
	else if (en == 1)
	{
		if (g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_GENERIC_BT)
		{
			g_dev_is_something |= DEV_IS_SOMETHING_UART2_DAT | DEV_IS_SOMETHING_UART2_BT | DEV_IS_SOMETHING_UART2_NEW_BT;	// | DEV_IS_SOMETHING_UART2_VOC;	// set voc at bt connected
		}
		else if (g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_BT_FUNCTION)
		{
			g_dev_is_something |= DEV_IS_SOMETHING_UART2_DAT | DEV_IS_SOMETHING_UART2_BT;
		}
		else if (g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_DATA_MODULE)
		{
			g_dev_is_something |= DEV_IS_SOMETHING_UART2_DAT;
			if (g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_DATA_PCM_ENABLE)
				g_dev_is_something |= DEV_IS_SOMETHING_UART2_VOC;
		}
		vlog_i("screen","uart2 init(1),DAT=%d,Alaw=%d,BT=%d,CBT=%d(%04X)",
			(g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_DATA_MODULE) ? 1 : 0,
			(g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_DATA_PCM_ENABLE) ? 1 : 0,
			(g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_BT_FUNCTION) ? 1 : 0,
			(g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_GENERIC_BT) ? 1 : 0, g_dev_is_something);
	}
	else
	{
		// 只锟斤拷锟斤拷锟斤拷锟斤拷锟接猴拷呕锟斤拷锟斤拷en=2锟斤拷锟斤拷锟斤拷en=2锟斤拷锟斤拷味锟斤拷DEV_IS_SOMETHING_UART2_BT/MISC_CONFIG_FACTORY_GENERIC_BT锟斤拷锟斤拷位
		g_dev_is_something |= ((g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_DATA_PCM_ENABLE) || uart2_use_as_bt()) ? DEV_IS_SOMETHING_UART2_VOC : 0;
		vlog_i("screen","uart2:EN->%d,Alaw=%d(%04x)", en, (g_dev_is_something & DEV_IS_SOMETHING_UART2_VOC) ? 1 : 0, g_dev_is_something);
	}
}

uint32_t uart2_use_as_bt(void)
{
	return g_dev_is_something & (DEV_IS_SOMETHING_UART2_BT | DEV_IS_SOMETHING_UART2_NEW_BT);
}

uint32_t uart2_is_new_bt(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_UART2_NEW_BT;
}

uint32_t uart2_use_as_voiceport(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_UART2_VOC;
}

uint32_t dev_is_showing_battery(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_SHOW_BATTERY;
}

uint8_t dev_enable_can(void)
{
	return g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_ENABLE_CAN;
}

uint8_t dev_is_809(void)
{
	return (g_devNo_real == MOBILE_MODE_PDT_ZZW_809) ? 1 : 0;
}

void set_dev_fixed_gps(uint8_t flag)
{
	if (flag)
		g_dev_is_something |= DEV_IS_SOMETHING_FIXED_GPS;
	else
		g_dev_is_something &= ~DEV_IS_SOMETHING_FIXED_GPS;
}

uint32_t dev_is_fixed_gps(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_FIXED_GPS;
}

void set_dev_use_which_stack(uint8_t flag)	// 0-pdt stack; 1-xv stack
{
	if (flag)
		g_dev_is_something |= DEV_IS_SOMETHING_USE_XVSTACK;
	else
		g_dev_is_something &= ~DEV_IS_SOMETHING_USE_XVSTACK;
}

uint32_t dev_is_use_xv_stack(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_USE_XVSTACK;
}

void set_dev_force_throughout(uint8_t flag)	// 0-follow interphone_mode; 1-force tx=rx
{
	if (flag)
		g_dev_is_something |= DEV_IS_SOMETHING_FORCE_THROUGHOUT;
	else
		g_dev_is_something &= ~DEV_IS_SOMETHING_FORCE_THROUGHOUT;
}

uint32_t dev_is_force_throughout(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_FORCE_THROUGHOUT;
}

void set_dev_no_ccm(uint8_t flag)	// 0-have ccm; 1-no ccm
{
	if (flag)
		g_dev_is_something |= DEV_IS_SOMETHING_NO_CCM;
	else
		g_dev_is_something &= ~DEV_IS_SOMETHING_NO_CCM;
}

uint32_t dev_is_no_ccm(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_NO_CCM;
}

void set_dev_bypass_stack(uint8_t flag)	// 0-normal(use external stack); 1-use embedded stack(pdt only)
{
	if (flag)
		g_dev_is_something |= DEV_IS_SOMETHING_BYPASS_STACK;
	else
		g_dev_is_something &= ~DEV_IS_SOMETHING_BYPASS_STACK;
}

uint32_t dev_is_bypass_stack(void)
{
	return g_dev_is_something & DEV_IS_SOMETHING_BYPASS_STACK;
}

uint8_t dev_is_base(void)
{
	return (g_devNo_real == VICTEL_DOUBLE_ARM_P2_BASE) ? 1 : 0;
}

uint8_t dev_have_base_feature(void)
{
	return ((g_devNo_real == VICTEL_DOUBLE_ARM_P2_BASE) && STACK_SET_FORWARD_FRAME()) ? 1 : 0;
}

uint8_t dev_is_vehicle(void)
{
	return 0;
}

uint8_t dev_is_auto_power(void)
{
	return (g_devNo_real == VICTEL_DOUBLE_ARM_P2_BASE) ? 1 : 0;
}

uint8_t dev_is_one_knob_operation(void)
{
	return 0;
}

uint8_t get_real_esn_second_sector_at_spi(void)
{
	uint32_t addr;
	uint16_t facNo;
	uint8_t dev_no;

	get_device_esn_info(&facNo, &dev_no, &addr);
	return dev_no;
}

const char dev_type_invalid[8] = "INVALID";
char *search_dev_no_type(uint8_t sn2)
{
	uint8_t i;

	for (i = 0; i < DEV_NO_SEARTH_TABLE_TOTAL; i++)
	{
		if (sn2 == dev_type_table[i].sn2)
			break;
	}

	return (i < DEV_NO_SEARTH_TABLE_TOTAL) ? (char *)dev_type_table[i].info : (char *)dev_type_invalid;
}

void set_board_type_parameters(void)
{
	uint16_t tmp16 = (uint16_t)get_freq_base();
	char *dev_name;

	g_dev_is_something = DEV_IS_SOMETHING_64XX | DEV_IS_SOMETHING_NOLCD;
//	g_devNo_real = get_real_esn_second_sector_at_spi();
	g_devNo_real = VICTEL_DOUBLE_CORE_P3;
	dev_name = search_dev_no_type(g_devNo_real);
	g_devNo = ((uint32_t)dev_name == (uint32_t)dev_type_invalid) ? 0xff : MOBILE_MODE_PDT_ZZW;
	g_rf_type = MOBILE_RF_TYPE_2P_FULL_BAND;

	set_uart2_use_as_dataport(1);

	g_805_version = 0xff;

	vlog_i("screen","\tESN=%d(remap to %d):%s", g_devNo_real, g_devNo, dev_name);

	if (g_devNo == 0xff)
		parameters_valid_checking(1);

//#ifdef DEBUG_REAL_BT
//	g_dev_is_something &= ~(DEV_IS_SOMETHING_UART2_DAT | DEV_IS_SOMETHING_UART2_VOC);
//	g_dev_is_something |= DEV_IS_SOMETHING_UART2_BT;
//#endif
}

extern GPS_STRUCT_TYPEDEF rmc_data;
extern uint32_t slot_rising_edge_int_times, slot_falling_edge_int_times, slot_middle_int_times;
#ifdef DEBUG_GPS_SYNC_PPS
extern uint32_t timestamp_gps_2pps;
#endif
void set_base_dsp_sync(void)
{
	if (is_gps_pps_ok() == 0)
	{
		gps_minute_second = 1;
	}
#ifdef CHECK_PPS_WITH_CRYSTAL_38P4M
	else if (get_gps_link_state())
#else
	else	// 未锟斤拷锟斤拷CHECK_PPS_WITH_CRYSTAL_38P4M时锟斤拷锟斤拷锟斤拷==PPS OK
#endif
	{
		gps_minute_second = (uint16_t)rmc_data.utc_minute * 60 + rmc_data.utc_second;
	}
}

#define IS_BASE_AT_SYNC_TIME()		(((gps_minute_second + 1) % ((g_runtime_inst_xvbase->fpga_write_gcr & FPGA_REG_GCR_SYNC39_SEL_MASK) ? 9 : 3)) == 0)
void set_base_bkio(void)
{
	if (dev_is_base())
	{
		if (IS_BASE_AT_SYNC_TIME())
			set_gpio_high(P2_BASE_BKIO);
		else
			set_gpio_low(P2_BASE_BKIO);
	}
}

void set_base_sync(uint8_t high)
{
}

void toggle_base_sync(void)
{
}

void init_user_setup_data_and_hw(void)
{
	g_rf2p_tune_reg = (DSP_2P_TUNE_TYPEDEF *)get_tune_table_address();
	stack_paras_initial();
}

const uint8_t sd_card_init_disable_tip[2][14] = {
	"锟斤拷锟斤拷锟斤拷锟絊D锟斤拷", "锟斤拷止锟斤拷锟絊D锟斤拷"
};
uint8_t* sd_init_control(void)
{
	uint8_t mode;

	mode = maintain_setup_parameter(PARA_OPERATE_READ, SD_CARD_INIT_DISABLE_POS, 0);
	mode = mode ? 0 : 1;
	maintain_setup_parameter(PARA_OPERATE_WRITE, SD_CARD_INIT_DISABLE_POS, mode);

	return (uint8_t *)sd_card_init_disable_tip[mode];
}
