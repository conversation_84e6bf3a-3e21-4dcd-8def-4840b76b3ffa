/**
  ******************************************************************************
  * @file    Project/STM32F2xx_StdPeriph_Template/main.c
  * <AUTHOR> Application Team
  * @version V1.1.0
  * @date    13-April-2012
  * @brief   Main program body
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT 2012 STMicroelectronics</center></h2>
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software
  * distributed under the License is distributed on an "AS IS" BASIS,
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdio.h>
#include <string.h>
#include "compile_timestamp_autogenerated.h"
#include "victel_digital_board.h"
#include "victel_digital_lcd.h"
#include "victel_digital_usart.h"
#include "victel_digital_flash.h"
//#include "victel_digital_spi_flash.h"
#include "global_define.h"
#include "stack_config.h"
#include "vlog.h"


/** @addtogroup Template_Project
  * @{
  */

/* Private typedef -----------------------------------------------------------*/
void auto_calling_test(void);
int aipl_main(void);
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

// uint16_t test_attr[3] __attribute__((at(0x2001fe00)));	// set the variable at the absolute address
// __align(4) uint16_t test_attr[3];						// align define
const char compile_name[] = "Victel digital P3";
const char compile_paltform[] = "AM6231";

extern P_FUN_VOID_VOID p_dbgdata_process;
extern RUNTIME_PARAMETERS_TYPEDEF g_runtime_inst;
extern uint32_t timestamp_1s, timestamp_2s, timestamp_kb_scan, timestamp_10ms;

P_FUN_VOID_VOID gui_execute = null_void_void;
uint32_t check_20ms_timestamp = 0, check_2s_timestamp;



void print_version(void)
{
	char watchdog_info[40] = "";

#ifndef SET_WATCHDOG_ENABLE
	p_strcat(watchdog_info, "[watchdog:disable]");
#endif
#ifdef RF_DEUBG_ENABLE
	p_strcat(watchdog_info, "[RF debug:enable]");
#endif

	vlog_i("version","========%s========", compile_name);
	vlog_i("version","LOADER: %04d-%02d-%02d %02d:%02d:%02d", 2000 + g_runtime_inst.device_information.mcu_bl_compile.rtc_year,
						g_runtime_inst.device_information.mcu_bl_compile.rtc_month,
						g_runtime_inst.device_information.mcu_bl_compile.rtc_day,
						g_runtime_inst.device_information.mcu_bl_compile.rtc_hour,
						g_runtime_inst.device_information.mcu_bl_compile.rtc_minute,
						g_runtime_inst.device_information.mcu_bl_compile.rtc_second);
	vlog_i("version","hardware version: %d.%d", g_runtime_inst.device_information.mcu_hardware_ver.ver_major,
						g_runtime_inst.device_information.mcu_hardware_ver.ver_minor);
	vlog_i("version","MAIN: %04d-%02d-%02d %02d:%02d:%02d%s", 2000 + g_runtime_inst.device_information.mcu_compile.rtc_year,
						g_runtime_inst.device_information.mcu_compile.rtc_month,
						g_runtime_inst.device_information.mcu_compile.rtc_day,
						g_runtime_inst.device_information.mcu_compile.rtc_hour,
						g_runtime_inst.device_information.mcu_compile.rtc_minute,
						g_runtime_inst.device_information.mcu_compile.rtc_second,
						watchdog_info);
}



#ifdef LINUX_VERSION
int main_for_linux(void)
#else
int main(void)
#endif
{
#ifdef DEBUG_UI_TIMESTAMPS
	uint32_t timer_1ms, timer_1ms_gui;
#endif

	system_initial();
	show_standby_screen();
	set_system_pdt_dmr_mode(1);
	Set_Speak_Volume(4);
	Set_Tip_Volume(3);


	vocoder_thread_start();
	// init_bt_serial_thread();
	while (get_module_init_done_flag(MODULE_INIT_DONE_STACK) == 0)
		;
	{
//		vocoder_init();
		aipl_main();
	}

	/* Infinite loop */
	while (1)
	{
		set_10ms_timer();
//		dsp_slot_edge_interrupt(0);
//		usleep(30 * 1000);

//		dsp_slot_edge_interrupt(1);
//		usleep(30 * 1000);

#ifdef TEST_VOCODER_FUNCTION
//		test_ipc_ambe_vocoder();
//		usleep(30 * 1000);
#endif

		if (timestamp_10ms >= check_20ms_timestamp + 2)
		{
			check_20ms_timestamp = timestamp_10ms;
			dsp_sout_int_handle();
		}
/*
		if (timestamp_1s >= check_2s_timestamp + 5)
		{
			check_2s_timestamp = timestamp_1s;
//			send_tip_sound((check_2s_timestamp & 0x01) ? TIP_TYPE_CALL_FINISH : TIP_TYPE_KEYBOARD, 1);
//			send_tip_sound(TIP_TYPE_CALL_FINISH, 1);
		}
*/
		peroidic_refresh();
		process_input();
#ifdef USE_INT_TO_RECEIVE_GPS
		process_gps_data();
#endif

#ifdef DEBUG_UI_TIMESTAMPS
		timer_1ms = get_timestamp_measure();
#endif
		(*gui_execute)();	// ((void (*)(void))g_gui_interactive->gui_exec)();
#ifdef DEBUG_UI_TIMESTAMPS
		timer_1ms_gui = get_timestamp_measure();
		vlog_v("main","\texe=%d", get_measure_timer_difference(timer_1ms_gui, timer_1ms));
#endif

//		dump_stack_to_sd();
		process_timer();

		(*p_dbgdata_process)();
//#ifndef USE_DMA_TO_TR_USART2
//		process_bt_data();
//#endif

		if (dev_is_base())
		{
			LBusRxFrame_Handle();	//�ж��Ƿ���δ�����ı������߽���֡�����������յ���֡��
		}
		else // dev_is_base
		{
//			mcu_enter_sleep_mode();
			auto_calling_test();
		} // dev_is_base

//		vocoder_working_process();
	}
}


#ifdef  USE_FULL_ASSERT

/**
  * @brief  Reports the name of the source file and the source line number
  *   where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t* file, uint32_t line)
{
  /* User can add his own implementation to report the file name and line number,
     ex: vlog_i("version","Wrong parameters value: file %s on line %d", file, line) */

  /* Infinite loop */
  while (1)
  {
  }
}
#endif



/**
  * @}
  */


/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
