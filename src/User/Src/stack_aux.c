/**
 * @file stack_aux.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version V1.001
 * @date 2025-04-27
 *
 * @copyright Copyright 2009 - 2025, victel Ltd.
 *
 * @par modifies history:
 * Date            Version   Author      Description
 * 2025-04-27      V1.001    liming
*/
#include <stdlib.h>
#include "vlog.h"
#include <stdint.h>
#include "stack_config.h"

typedef struct _ts_stack_interface_ret_type
{
    uint16_t code;
    uint8_t *info;
}ts_stack_interface_ret_type;

ts_stack_interface_ret_type g_stack_ret_infos[] =
{
    // {STACK_RETURN_TYPE_NULL,"STACK_RETURN_TYPE_NULL"},
    {STACK_RETURN_TYPE_VERSION,"STACK_RETURN_TYPE_VERSION"},
    // {STACK_RETURN_TYPE_STATE,"STACK_RETURN_TYPE_STATE"},
    {STACK_RETURN_TYPE_SIGNALING,"STACK_RETURN_TYPE_SIGNALING"},
    {STACK_RETURN_TYPE_VOICE,"STACK_RETURN_TYPE_VOICE"},
    {STACK_RETURN_TYPE_CALLING_STAGE,"STACK_RETURN_TYPE_CALLING_STAGE"},
    {STACK_RETURN_TYPE_UPDATE_CTRL,"STACK_RETURN_TYPE_UPDATE_CTRL"},
    {STACK_RETURN_TYPE_POWER_DOWN,"STACK_RETURN_TYPE_POWER_DOWN"},
    {STACK_RETURN_TYPE_AUTH_REQUEST,"STACK_RETURN_TYPE_AUTH_REQUEST"},
    {STACK_RETURN_TYPE_PDU_DATA,"STACK_RETURN_TYPE_PDU_DATA"},
    {STACK_RETURN_TYPE_SIN_MONITOR,"STACK_RETURN_TYPE_SIN_MONITOR"}
};

uint8_t *stack_print_ret_info(uint16_t code)
{
    int i = 0;

    for(i = 0; i < sizeof(g_stack_ret_infos)/sizeof(g_stack_ret_infos[0]);i++)
    {
        if(code == g_stack_ret_infos[i].code)
        {
            return  g_stack_ret_infos[i].info;
        }
    }

    return NULL;
}

/*end of the file:stack_aux.c*/
