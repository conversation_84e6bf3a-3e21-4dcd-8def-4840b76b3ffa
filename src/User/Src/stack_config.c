/**
  ******************************************************************************
  *                Copyright (c) 2013, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    15-October-2013
  * @brief   This file provides
  *            - protocol stack function interface
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stdio.h"
#include "string.h"
#include "stdlib.h"
#include "global_define.h"
#include "dsp_reg_define.h"
#include "stack_config.h"
#include "victel_digital_keyboard.h"
#include "victel_digital_spi_flash.h"
#include "victel_digital_flash.h"
#include "victel_digital_usart.h"
#include "victel_digital_xvmode.h"
#include "regDefine.h"
#include "vlog.h"


// #define TEST_STACK_CALLING				1


#define STACK_ZZW_VOICE_DELAY_SLOT		6

#define GET_DIGITAL_ANALOG_MODE_INDEX()		((interphone_mode <= INTERPHONE_MODE_PDT_TRUNKING) ? 1 : 2)
#define GET_STACK_MODE_INDEX()				((interphone_mode == INTERPHONE_MODE_MPT_TRUNKING) ? 2 : 1)
#define GET_VARIOUS_MODE_INDEX()			((interphone_mode <= INTERPHONE_MODE_PDT_CONV) ? 0 : ((interphone_mode <= INTERPHONE_MODE_ZZW_ADHOC) ? (interphone_mode - 1) : (INTERPHONE_MODE_ZZW_ADHOC - 1)))

extern uint8_t tx_icon_delay_clean;
extern uint8_t dsp_edge_slot;
extern uint16_t dsp_save_power_counter9s, dsp_save_power_counter;
extern uint8_t dsp_save_power_status;
extern uint8_t rmt_gps_state;
extern uint32_t num_of_frame_received;
extern uint32_t timestamp_1s, timestamp_kb_scan, slot_rising_edge_int_times, slot_falling_edge_int_times, slot_middle_int_times;
extern GPS_STRUCT_TYPEDEF rmc_data;
extern uint8_t gps_state_linked;
extern RUNTIME_PARAMETERS_TYPEDEF g_runtime_inst;
extern STATIC_PARAMETERS_TYPEDEF *g_static_ptr;
extern GROUP_BOOKS *g_group_books;
extern RUNTIME_PARAMETERS_XVBASE_TYPEDEF *g_runtime_inst_xvbase;
extern uint32_t delay_to_reply_tm[3];
extern float calling_dev_position[4];
extern char external_calling_number[EXTERNAL_NUMBER_MAX_DISPLAY];
extern uint8_t dump_to_can;
extern int16_t gps_height;
extern uint8_t auto_trace_q_vocoder;
extern uint8_t zzwpro_marked_slot;
#ifdef READ_CODEWORD_DELAY_BY_LC_NUM
extern uint8_t delay_to_read_vocoder;
#endif
extern uint16_t auto_trace_nv25k;

extern uint16_t nvq_tx_freq_index;

extern uint32_t stack_call_buffer[STACK_CALL_BUFFER_LENGTH / sizeof(uint32_t)];
extern uint32_t stack_dump_buffer[STACK_DUMP_BUFFER_LENGTH / sizeof(uint32_t)];

extern GUI_INTERACTIVE_TYPEDEF *g_gui_interactive;
extern uint8_t g_long_message[];

uint32_t (*p_stack_call_interface)(uint32_t type, uint32_t *aux1, uint32_t *aux2, uint32_t buff_addr, uint16_t buff_len);

static uint8_t dump_number = STACK_DUMP_DEFAULT_DATA_LEN, dump_level = 0xff;/* 0xff-disable, 0x01-enable */
static uint8_t remote_ctrl_call_jump = 0;
/* switch_watch_flag: bit31:		1-switch page flag(bit31-30 == 10(air write group), the bit15-8 should be 0)
                      bit30:		0-air add group; 1-air del group
                      bit15-8:		add: 0x80-add background; else-response or participattion; del: the index of the deleted
                      bit7-0:		the total number of the air group
*/
uint32_t switch_watch_flag = 0, stack_message_buffer[STACK_CALL_BUFFER_LENGTH / sizeof(uint32_t)];
uint8_t current_stack_slot, interphone_mode, search_the_base = 0, loggin_the_base = 0, link_status_flag = 0;	// link_status_flag: bit0-base link info; bit1-gps upload(time); bit2-gps upload(distance)
float freq_base, freq_step, freq_gap;
uint16_t chan_start_index, chan_end_index, dynamic_q_tx_freq[ZZWPRO_Q_TX_TOTAL_CHAN];
uint8_t mpt_rmode_is_cps = 0, mpt_cpsl = 0, mpt_cpsm = 0, mpt_cpss = 0;
uint16_t mpt_fleet_boundary = 0;
uint8_t ring_call_reason = 0, zzw_fail_reason = 7;
uint8_t send_message_flag_timer = 0xff;
static uint16_t new_msg_incoming = 0;
static uint32_t sync_call_stack = 0, sync_call_stack_bak = 0;	// 0: no calling; else: see sync_call_stack bit define

static uint32_t my_stack_id_watch = 0;

static uint32_t prev_report_time = 0, dispatch_reply_ret_id = 0, manual_probe_id = 0, single_call_ring_id = 0;
static uint8_t prev_pos_save[9], poll_v_message_timer = 0xff, single_call_ring_type = 0;
static V_DispatchPoll v_dispatch_poll;

uint8_t  device_is_sync = 0, pre_device_sync = 0, g_neighbour_info[8], g_neighbour_opcode, g_neighbour_oppara;

static uint32_t zzw_distance = 0xffffffff;
uint8_t zzw_distance_string[19] = "����:δ֪";
#ifdef FORWARDING_LOGIC_ENABLE
uint8_t zzw_forward_level = 0, zzw_forward_downcount = 0;
#endif

static PROTOCOL_STACK_TYPEDEF *p_stack_parameters;
static PROTOCOL_ZZW_TYPEDEF *p_zzw_parameters;
USER_BOOK inst_conv_struct, *inst_conv = &inst_conv_struct;
const uint8_t inst_conv_name[16] = "ָ����û�";
//__align(4) static uint8_t stack_call_buffer[512];
uint32_t stack_para_aux1, stack_para_aux2;
uint32_t individual_ring_calling_id = 0, g_neighbour_id = 0, last_call_timestamp = 0, send_message_flag = 0;	// send_message_flag: use 24bit only because send_v_data_is_busy_now() use high 8bit

// fail reason: <�ն˱�׼2013-6-5.pdf> ��¼A A.1.1
#define CALL_FAIL_REASON_TOTAL	(19+2)
const uint8_t call_fail_reason[2][CALL_FAIL_REASON_TOTAL][16] = {
	{"����������",     "�ش��������", "���г�ʱ",     "����ȡ��",    "��վ�ܾ�",     "���оܾ�",    "����֧��",
	"����δ��Ȩ",   "����δ�Ǽ�",     "���в��ɴ�",     "ϵͳ����", "�ǼǾܾ�",   "�ǼǷ���", "����δ�Ǽ�",
	"����æ",      "������ת��",     "IP����ʧ��", "��·ҵ��֧��", "�豸æ�ܾ�",
	"��������ֹ", "PTTδ��Ȩ"},

	{"Ringing...", "Retrans over", "Call timeout", "Call cancel", "Base refused", "Be rejected", "Not support",
		"Unauthorized", "Not registered", "Can not arrive", "Overload", "Refuse reg", "Deny reg", "Not registered",
		"Called busy", "Be transferred", "IP not act", "Line fail", "Device busy",
		"Calling end", "PTT forbidden"}
};

const uint8_t call_succ_reason[2][4][16] = {
	{"���ʹ�", "Ŀ����Ӧ��", "���ͳɹ�", "���гɹ�"},
	{"Delivered", "Available", "Successful", "Successful"}
};

STACK_STATE_STRUCT stack_state;
STACK_STAGE_STRUCT stack_stage;
BACK_TSCC_STRUCT   stack_back_tscc;
uint32_t send_message_buffer[80];	// type(4B) id(4B) CODE(1B) BYTES(1B) MSG_TYPE(1B) gap(5B) paras...


void stack_calling_asm_setup(void);		// define at victel_asm_interface.s
uint32_t stack_call_interface(uint32_t type, uint32_t *aux1, uint32_t *aux2, uint32_t buff_addr);
PROTOCOL_STACK_TYPEDEF *set_xvbase_stack(PROTOCOL_STACK_TYPEDEF *stk);
uint32_t stack_call_interface_conv_base(uint32_t call_type, uint32_t *aux1, uint32_t *aux2, uint32_t buff);
uint32_t stack_call_interface_zzwpro_upgrade(uint32_t type, uint32_t *aux1, uint32_t *aux2, uint32_t buff);
uint32_t stack_call_interface_zzwpro_q(uint32_t type, uint32_t *aux1, uint32_t *aux2, uint32_t buff);
void zzw_gps_poll_init(uint8_t flag);

uint8_t interphone_mode_is_dmr(void)
{
	uint8_t pdt_dmr, patcs;

	get_stack_dmode_ctype(&pdt_dmr, &patcs);
	return (pdt_dmr == 0) ? 1 : 0;
}

uint8_t interphone_mode_is_dmr_conv(void)
{
	return ((interphone_mode <= INTERPHONE_MODE_PDT_CONV) && interphone_mode_is_dmr()) ? 1 : 0;
}

void get_device_esn_info(uint16_t *facNo, uint8_t *devNo, uint32_t *sn)
{
	uint8_t device_esn[16];

	get_esn_from_flash((uint32_t)device_esn);

	//���Һ�, 9bit
	*facNo = (((uint16_t)device_esn[0])<<1)+(((uint16_t)device_esn[1]>>7)&0x01);
	//�豸��, 8bit
	*devNo = ((((uint16_t)device_esn[1])&0x7F)<<1)+(((uint16_t)device_esn[2]>>7)&0x01);
	//�������к�, 30b
	*sn = (((uint32_t)device_esn[2])<<24)+(((uint32_t)device_esn[3])<<16) + (((uint32_t)device_esn[4])<<8) + ((uint32_t)device_esn[5]);
	*sn = (*sn>>1)&0x3FFFFFFF; //ȡ�����һλ����ȥ��30b
}

GROUP_CONTROL_LIST *get_ctrl_table_list(void)
{
	GROUP_CONTROL_LIST *ctrl_list;

	if ((interphone_mode <= INTERPHONE_MODE_PDT_TRUNKING) || (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC))
		ctrl_list = &(g_group_books->grp_ctrl_list[maintain_setup_parameter(PARA_OPERATE_READ, ACTIVE_GROUP_OF_CTRL_LIST, 0)]);
	else if (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING)
		ctrl_list = &(g_group_books->grp_mctrl_list);
	else
		ctrl_list = (GROUP_CONTROL_LIST *)0;

	return ctrl_list;
}

uint16_t get_air_books_config_count(void)
{
	uint16_t i;

	for (i = 0; i < MAX_AIR_CONF_BOOK; i++)
	{
		if ((g_runtime_inst.air_config.air_conf_book[i].id & 0x00ffffff) == 0)
			break;
	}
	return i;
}

uint8_t* get_air_books_config_name(void)
{
	return g_runtime_inst.air_config_name;
}

uint8_t get_interphone_mode(uint32_t cs)	// 0: throughout; 1: conventional(repeat); 2: pdt trunking; 3: mpt trunking
{
	uint8_t ret;

	if (interphone_mode >= INTERPHONE_MODE_ZZW_ADHOC)
	{
		ret = interphone_mode;
	}
	else
	{
		if (cs & STACK_CS_CONVENTIONAL_TRUNK)			// 1==CONV
		{
			if (cs & STACK_CS_THROUGH_REPEAT)			// 1==DIRECT
				ret = INTERPHONE_MODE_PDT_DIRECT;
			else
				ret = INTERPHONE_MODE_PDT_CONV;
		}
		else
		{
			ret = INTERPHONE_MODE_PDT_TRUNKING;
		}
	}

	return ret;
}

uint8_t search_air_books_item_name(uint32_t id, uint8_t *name)
{
	uint16_t i;
	uint32_t id_cmp, id_mask;

	name[0] = 0;
	id_mask = USER_ID_CMP_MASK;
	id_cmp = id & id_mask;
	for (i = 0; i < MAX_AIR_CONF_BOOK; i++)
	{
		if ((g_runtime_inst.air_config.air_conf_book[i].id & id_mask) == id_cmp)
		{
			p_strcpy(name, g_runtime_inst.air_config.air_conf_book[i].name);
			break;
		}
	}
	return (i >= MAX_AIR_CONF_BOOK) ? 0 : 1;
}


uint8_t arrangement_air_user_books(void)
{
	uint8_t i, index;
	USER_BOOK air_conf_temp[MAX_AIR_CONF_BOOK];

	for (i = 0, index = 0; i < MAX_AIR_CONF_BOOK; i++)
	{
		if (g_runtime_inst.air_config.air_conf_book[i].id & 0x00ffffff)
		{
			memcpy(&(air_conf_temp[index]), &(g_runtime_inst.air_config.air_conf_book[i]), sizeof(USER_BOOK));
			index++;
		}
	}
	memcpy(&(g_runtime_inst.air_config.air_conf_book[0]), &(air_conf_temp[0]), sizeof(USER_BOOK) * index);
	memset(&(g_runtime_inst.air_config.air_conf_book[index]), 0, sizeof(USER_BOOK) * (MAX_AIR_CONF_BOOK - index));

	if (index == 0)
		p_strcpy(get_air_books_config_name(), "�տ�дƵ��Ⱥ");

	return index;
}

uint16_t get_air_ctrl_table_count(void)
{
	uint16_t i;

	i = 0;
	if (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING)
	{
		for (; i < MAX_AIR_CONF_CTRL; i++)
		{
			if ((g_runtime_inst.air_config.air_conf_ctrl[i] & 0x00000fff) == 0)
				break;
		}
	}
	return i;
}

uint8_t arrangement_air_ctrl_table(void)
{
	uint8_t i, index;
	uint32_t air_conf_temp[MAX_AIR_CONF_CTRL];

	for (i = 0, index = 0; i < MAX_AIR_CONF_CTRL; i++)
	{
		if (g_runtime_inst.air_config.air_conf_ctrl[i] & 0x00000fff)
		{
			air_conf_temp[index] = g_runtime_inst.air_config.air_conf_ctrl[i];
			index++;
		}
	}
	memcpy(&(g_runtime_inst.air_config.air_conf_ctrl[0]), &(air_conf_temp[0]), sizeof(uint32_t) * index);
	memset(&(g_runtime_inst.air_config.air_conf_ctrl[index]), 0, sizeof(uint32_t) * (MAX_AIR_CONF_CTRL - index));
	return index;
}

uint32_t stack_call_interface_null(uint32_t type, uint32_t *aux1, uint32_t *aux2, uint32_t buff, uint16_t buff_len)
{
	return STACK_RETURN_TYPE_NULL_REALLY;
}

static uint32_t (*p_stack_call_backup)(uint32_t type, uint32_t *aux1, uint32_t *aux2, uint32_t buff_addr, uint16_t buff_len) = stack_call_interface_null;
void set_poc_mode(void)
{
	if (g_gui_interactive->dev_misc_notify.poc_mode)
	{
		if (p_stack_call_backup == stack_call_interface_null)
		{
			p_stack_call_backup = p_stack_call_interface;
			p_stack_call_interface = stack_call_interface_null;
		}
		vlog_v("stack","\tTo POC");
	}
	else
	{
		if (p_stack_call_backup != stack_call_interface_null)
		{
			p_stack_call_interface = p_stack_call_backup;
			p_stack_call_backup = stack_call_interface_null;
		}
		vlog_v("stack","\tExit POC");
	}
}

void set_poc_mode_bypass_stack(uint8_t bypass)	// 0-to normal; else-bypass stack
{
	if (g_gui_interactive->dev_misc_notify.poc_mode)
	{
		if (bypass)
		{
			if (p_stack_call_interface != stack_call_interface_null)
			{
				p_stack_call_backup = p_stack_call_interface;
				p_stack_call_interface = stack_call_interface_null;
				vlog_v("stack","\tbypass stack");
			}
		}
		else
		{
			if (p_stack_call_interface == stack_call_interface_null)
			{
				p_stack_call_interface = p_stack_call_backup;
				p_stack_call_backup = stack_call_interface_null;
				vlog_v("stack","\tnormal stack");
			}
		}
	}
}

void set_stack_call_interface_pointer(uint8_t mode)		// 0-reset to normal stack; 1-set as my zzw stack; 2-set as my pdt conv; 3-set as xv mode upgrade; 4-set as my Q stack
{
	p_stack_call_interface = stack_call_interface;
/*
	if (mode == 2)
	{
		p_stack_call_interface = stack_call_interface_conv_base;
	}
	else if (mode == 3)
	{
		p_stack_call_interface = stack_call_interface_zzwpro_upgrade;
	}
#ifdef USE_MAIN_EMBEDDED_Q_MODE
	else if (mode == 4)
	{
		p_stack_call_interface = stack_call_interface_zzwpro_q;
	}
#endif
	else
	{
		if (dev_is_base())
		{
			p_stack_call_interface = dev_is_bypass_stack() ? stack_call_interface_conv_base : stack_call_interface;
//			vlog_v("stack","Set stack=%s", dev_is_bypass_stack() ? "stack_call_interface_conv_base" : "stack_call_interface");
		}
		else // dev_is_base
		{
			p_stack_call_interface = stack_call_interface;
		}
	}
*/
}

void set_dev_use_which_stack(uint8_t flag);
void set_dev_no_ccm(uint8_t flag);
void set_dev_bypass_stack(uint8_t flag);
uint32_t get_stack_flash_address(void)					// call it only at stack_calling_asm_setup
{
	uint32_t ret;

	uint8_t noccm = (g_static_ptr->factory_paras.misc_config_factory & MISC_CONFIG_FACTORY_DEV_NO_CCM) ? 1 : 0;

	if (dev_is_base())
	{
		ret = get_zzwx_stack_address();						// ��վҪô���ã��õĻ��϶�ʹ��xvЭ��ջ
		if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
		{
			set_dev_no_ccm(noccm);
			set_dev_bypass_stack(0);						// ����������Э��ջ
			set_dev_use_which_stack(1); 					// ��������վ����xvЭ��ջ
		}
		else
		{
			if ((g_runtime_inst_xvbase->misc_config_base.bypass_stack_mode == 1) || (g_runtime_inst_xvbase->misc_config_base.bypass_stack_mode == 2))
			{
				set_dev_no_ccm(0);							// ��·ʱʹ������Э��ջ����ʱ��֧����CCM
				set_dev_bypass_stack(1);
				set_dev_use_which_stack(0); 				// ��·ʱʹ������Э��ջ����ʱ�ı�־���ǲ�ʹ��xvЭ��ջ
			}
			else
			{
				set_dev_no_ccm(noccm);
				set_dev_bypass_stack(0);
				set_dev_use_which_stack(1); 				// ���������վʹ�õ�Э��ջ����xvЭ��ջ
			}
		}
		vlog_v("stack","Base:stack=0x%08x,bypass=%d(%d),ccm=%d(%d)", ret,
				dev_is_bypass_stack() ? 1 : 0, g_runtime_inst_xvbase->misc_config_base.bypass_stack_mode, dev_is_no_ccm() ? 0 : 1, noccm ? 0 : 1);
	}
	else // dev_is_base
	{
		set_dev_no_ccm(0);									// Ĭ�ϣ��ǻ�վ����ʽ��Ϊ��CCM����Ϊ�˱���ת��ʱ���л������
		set_dev_bypass_stack(0);							// �ն˱���Э��ջ
		if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
		{
			ret = get_zzwx_stack_address();
			set_dev_use_which_stack(1);
		}
		else if (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING)
		{
			ret = get_pdt_stack_address();
			set_dev_use_which_stack(0);
		}
		else
		{
			if (g_runtime_inst_xvbase->misc_config_base.bypass_stack_mode == 3)
			{
				ret = get_zzwx_stack_address();
				set_dev_use_which_stack(1);
			}
			else
			{
				ret = get_pdt_stack_address();
				set_dev_use_which_stack(0);
			}
		}
	} // dev_is_base

	return ret;
}

uint8_t set_fixed_gps_to_rmcdata(uint8_t judge)
{
	memcpy(&rmc_data, p_zzw_parameters->zzw_default_pos, 9);
	if ((judge == 0) || (rmc_data.lat_degree || rmc_data.lat_integer || rmc_data.lat_decimal || rmc_data.lon_degree || rmc_data.lon_integer || rmc_data.lon_decimal))
	{
		rmc_data.gps_state &= LONGITUDE_EAST_FLAG | LATITUDE_NORTH_FLAG;
		rmc_data.gps_state |= GPS_DATA_VALID_FLAG;
		rmc_data.speed = 0;
		rmc_data.utc_hour = 1;
		rmc_data.utc_minute = 1;
		rmc_data.utc_second = 1;
		gps_state_linked = GPS_STATE_LINKED_CONFIRM;
		set_dev_fixed_gps(1);
		return 1;
	}
	else
	{
		return 0;
	}
}

const uint8_t remote_op_type[3][8] = {"stunned", "killed", "revived"};
void set_device_remote_operation_status(uint8_t op_type)
{
	uint8_t index = 0xff;

	if (op_type == MISC_OP_TYPE_STUN)
	{
		index = 0;
		switch_watch_flag = DYNAMIC_REMOTE_STUN;
	}
	else if (op_type == MISC_OP_TYPE_KILL)
	{
		index = 1;
		switch_watch_flag = DYNAMIC_REMOTE_KILL;
	}
	else if (op_type == MISC_OP_TYPE_REVIVE)
	{
		index = 2;
		switch_watch_flag = DYNAMIC_REMOTE_REVIVED;
	}

	if (index != 0xff)
		vlog_v("stack","this device has been %s!", (char *)remote_op_type[index]);
}

void stack_paras_initial(void)
{
	uint8_t i;
	uint32_t mode;

	p_zzw_parameters = &g_static_ptr->stack_zzw;

	for (i = 0; i < MAX_AIR_CONF_BOOK; i++)
	{
		if (g_runtime_inst.air_config.air_conf_book[i].id == 0xffffffff)
			g_runtime_inst.air_config.air_conf_book[i].id = 0;
	}
	arrangement_air_user_books();
	for (i = 0; i < MAX_AIR_CONF_CTRL; i++)
	{
		if (g_runtime_inst.air_config.air_conf_ctrl[i] == 0xffffffff)
			g_runtime_inst.air_config.air_conf_ctrl[i] = 0;
	}
	arrangement_air_ctrl_table();

	interphone_mode = maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0);
//	if (interphone_mode <= INTERPHONE_MODE_ZZW_ADHOC)
	if (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING)
	{
		p_strcpy(g_runtime_inst.air_config.air_dynamic_reconf.name, (uint8_t *)"��̬�����û�");
		g_runtime_inst.air_config.air_dynamic_reconf.id = 0;
		g_runtime_inst.air_config.air_dynamic_reconf.bind_ch = 0;

		p_strcpy(g_runtime_inst.air_config.air_dynamic_reconf2.name, (uint8_t *)"2PTT�û�");
//		g_runtime_inst.air_config.air_dynamic_reconf2.id = 0;
//		g_runtime_inst.air_config.air_dynamic_reconf2.bind_ch = 0;
	}
	else
	{
		if (g_runtime_inst.air_config.air_dynamic_reconf.name[0] == 0)
			p_strcpy(g_runtime_inst.air_config.air_dynamic_reconf.name, (uint8_t *)"��̬�����û�");
		if (g_runtime_inst.air_config.air_dynamic_reconf2.name[0] == 0)
			p_strcpy(g_runtime_inst.air_config.air_dynamic_reconf2.name, (uint8_t *)"2PTT�û�");
	}
	if (is_enter_instruction_to_conv())
	{
		if (get_instruction_to_conv_exec_flag() == (uint8_t)DYNAMIC_CONFIG_RECONF)	// execute already
			memset(inst_conv, 0, sizeof(USER_BOOK));
//		else
//			interphone_mode = get_interphone_mode(inst_conv->id >> 23);
	}

	if (dev_is_vehicle())
//	if (((interphone_mode == INTERPHONE_MODE_ZZW_ADHOC) || (interphone_mode >= INTERPHONE_MODE_ZZWPRO_Q)) && dev_is_vehicle())
//	if ((interphone_mode == INTERPHONE_MODE_ZZW_ADHOC) || (interphone_mode >= INTERPHONE_MODE_ZZWPRO_Q))
	{
		if (set_fixed_gps_to_rmcdata(1) == 0)
			memset(&rmc_data, 0, sizeof(GPS_STRUCT_TYPEDEF));
	}
	else
	{
		memset(&rmc_data, 0, sizeof(GPS_STRUCT_TYPEDEF));
	}

	get_stack_flash_address();
	if (interphone_mode == INTERPHONE_MODE_ZZWPRO_Q)
	{
		set_stack_call_interface_pointer(4);
	}
	else
	{
		set_stack_call_interface_pointer(0);
		set_lcd_print_zzw_slot(PRINT_ZZW_FLAG_ZZW_SLOT_MASK, (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) ? 1 : 0);
	}

	if ((interphone_mode == INTERPHONE_MODE_MPT_TRUNKING) || (interphone_mode == INTERPHONE_MODE_MPT_CONV))
	{
		p_stack_parameters = &g_static_ptr->stack_mpt;
		mpt_rmode_is_cps = get_mpt_rcode_paras(&mpt_fleet_boundary, &mpt_cpsl, &mpt_cpsm, &mpt_cpss);
	}
	else
	{
		if (dev_is_base())
		{
			p_stack_parameters = set_xvbase_stack(&g_static_ptr->stack_pdt);
		}
		else // dev_is_base
		{
			p_stack_parameters = &g_static_ptr->stack_pdt;
			set_xvbase_stack(&g_static_ptr->stack_pdt);
		} // dev_is_base
	}

	set_freq_cal_paras(0);
	zzw_gps_poll_init(1);

	if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
	{
		mode = 0;
		if (g_runtime_inst.runtime_paras.misc_runtime_config.remote_kill)
			mode |= STACK_CS_REMOTE_KILL;
		else if (g_runtime_inst.runtime_paras.misc_runtime_config.remote_stun)
			mode |= STACK_CS_REMOTE_STUN;
	}
	else
	{
		mode = get_stack_work_mode();
	}

	if (mode & STACK_CS_REMOTE_KILL)
		set_device_remote_operation_status(MISC_OP_TYPE_KILL);
	else if (mode & STACK_CS_REMOTE_STUN)
		set_device_remote_operation_status(MISC_OP_TYPE_STUN);
}

uint8_t get_logo_setup_char(void)
{
	return (uint8_t)(g_static_ptr->stack_pdt.id >> 24);
}

const uint8_t clear_dynamic_conf_data_tip[14] = "����տ�дƵ";
uint8_t* clear_dynamic_conf_data(void)
{
	memset(&g_runtime_inst.air_config, 0, sizeof(AIR_RECONF_TYPEDEF));
	return (uint8_t *)clear_dynamic_conf_data_tip;
}

uint8_t is_mpt_open_analog_voice(void);
void send_voice_to_module(void)
{
//	dsp_send_cmd(DSP_CMD_SPEECH_IN_ON);	// delay turn on speech_in at the end of tip playing
	dsp_speech_in_pa_ctrl(1, 0);
	if ((interphone_mode == INTERPHONE_MODE_MPT_CONV) || (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING))
		set_dsp_analog_ptt_on_off(1);	// voice
}

void send_voice_to_module_stop(void)
{
	dsp_speech_in_pa_ctrl(0, 0);
	if ((interphone_mode == INTERPHONE_MODE_MPT_CONV) || (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING))
		set_dsp_analog_ptt_on_off(0);	// idle
}

#define EVENT_OF_TRANSMIT(type)		((type == SYNC_CALL_STACK_PTT_PRESSED) || (type == SYNC_CALL_STACK_PTT2_PRESSED) || (type == SYNC_CALL_STACK_ALARM_PRESSED))
void set_sync_call_stack_type(uint32_t type)
{
	sync_call_stack |= type;

	if (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING)
	{
		if (sync_call_stack_bak)										// ignore all ptt event
		{
			if (EVENT_OF_TRANSMIT(type))
				sync_call_stack &= ~type;
		}
		else if (is_ambience_listening() && EVENT_OF_TRANSMIT(type))	// press ptt at ambience listening, call hang up first
		{
			sync_call_stack_bak = sync_call_stack;
			sync_call_stack = SYNC_CALL_STACK_CANCEL_PRESSED | SYNC_CALL_STACK_CANCEL_RELEASED;
		}
	}

	if (interphone_mode == INTERPHONE_MODE_MPT_CONV)
	{
		if ((type == SYNC_CALL_STACK_PTT_PRESSED) || (type == SYNC_CALL_STACK_PTT2_PRESSED))
		{
			stack_state.vs = STACK_VS_CALLING | STACK_VS_OWN_CALLING | STACK_VS_OWN_SPEAKING;
			dsp_speech_out_pa_ctrl(0, SPEECH_PA_CTRL_OUT_VOICE);
			send_voice_to_module();
		}
		else if ((type == SYNC_CALL_STACK_PTT_RELEASED) || (type == SYNC_CALL_STACK_PTT2_RELEASED))
		{
			stack_state.vs &= ~STACK_VS_OWN_SPEAKING;	// own stop speaking; clear other bits at mpt_conv_voice_delay if necessary
			send_voice_to_module_stop();
		}
		else if (type == SYNC_CALL_STACK_SWITCH_WATCH)
		{
			config_pll_paras(interphone_mode, 0, 0);
		}
	}
}

void set_xmode_with_work_mode(uint8_t work_mode, uint32_t *xmode)
{
	*xmode &= ~(ZZWPRO_STACK_N_MODE | ZZWPRO_STACK_Q_MODE | ZZWPRO_STACK_V25K_MODE);
	if (work_mode == INTERPHONE_MODE_ZZWPRO_Q)
		*xmode |= ZZWPRO_STACK_Q_MODE;
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_N)
		*xmode |= ZZWPRO_STACK_N_MODE;
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_V25K)
		*xmode |= ZZWPRO_STACK_V25K_MODE;
}

uint32_t zzwpro_gps_poll_init(void);
void stack_set_base_parameters(void)
{
	uint32_t *ptr32, gpst_gpsw;
	uint16_t *ptr16;

//	auto_trace_nv25k = DSP_DOUBLE_RECEIVED_NV25K(interphone_mode) ? ZZWPRO_AUTOTRACE_NV_ENABLE_MASK : 0;
	auto_trace_nv25k = 0;	// P1 no N+V
//	if (dev_have_base_feature() == 0)
	{
#ifdef PDT_CONV_VOICE_AT_QMODE
		if (interphone_mode <= INTERPHONE_MODE_PDT_CONV)
			auto_trace_nv25k |= ZZWPRO_AUTOTRACE_PQ_FLAG_MASK;
#endif
#ifdef NV_AUTO_TRACE_VOICE_AT_QMODE
		if ((interphone_mode == INTERPHONE_MODE_ZZWPRO_N) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_V25K))
			auto_trace_nv25k |= ZZWPRO_AUTOTRACE_VQ_ENABLE_MASK;
#endif
	}
	maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_CONST, (void *)stack_call_buffer);
	if (g_runtime_inst.runtime_paras.misc_runtime_config.disable_dynamic_gps == 0)
	{
		if (g_runtime_inst.runtime_paras.misc_runtime_config.use_dynamic_gps == 0)
		{
			vlog_v("stack","Init d_GPS=%d/%d", ((uint8_t *)stack_call_buffer)[STACK_CONTS_NGPSU], ((uint8_t *)stack_call_buffer)[STACK_CONTS_NDIST]);
			g_runtime_inst.runtime_paras.misc_runtime_config.use_dynamic_gps = 1;
			g_runtime_inst.runtime_paras.stack_conts_gps[0] = ((uint8_t *)stack_call_buffer)[STACK_CONTS_NGPSU];
			g_runtime_inst.runtime_paras.stack_conts_gps[1] = ((uint8_t *)stack_call_buffer)[STACK_CONTS_NDIST];
		}
		else
		{
			vlog_v("stack","Set d_GPS=%d/%d(ori=%d/%d)", g_runtime_inst.runtime_paras.stack_conts_gps[0], g_runtime_inst.runtime_paras.stack_conts_gps[1],
				((uint8_t *)stack_call_buffer)[STACK_CONTS_NGPSU], ((uint8_t *)stack_call_buffer)[STACK_CONTS_NDIST]);
			((uint8_t *)stack_call_buffer)[STACK_CONTS_NGPSU] = g_runtime_inst.runtime_paras.stack_conts_gps[0];
			((uint8_t *)stack_call_buffer)[STACK_CONTS_NDIST] = g_runtime_inst.runtime_paras.stack_conts_gps[1];
		}
	}
//	stack_call_buffer[2] |= 4;
	get_esn_from_flash((uint32_t)stack_call_buffer + CONTS_LENGTH);
	ptr32 = (uint32_t *)((uint32_t)stack_call_buffer + CONTS_LENGTH + ESN_LENGTH);
	maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, (void *)ptr32);
	*ptr32++ &= 0x00ffffff;
	*ptr32 = get_stack_work_mode();
//	*ptr32 &= ~STACK_MODE_SET_PDT_DMR;	// set to DMR(1 is PDT)
//	*ptr32 |= STACK_MODE_DIRECT_REPEATER;
//	*ptr32 |= STACK_MODE_ALARM_RESPONSE_DMR;
//	if (smartcard_is_online() == 0)
//		*ptr32 &= ~STACK_MODE_HIGH_AUTHENTICATION;
	ptr16 = (uint16_t *)(++ptr32);
	maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_CTIME, (void *)ptr16++);
	maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_RCODE_MODE, (void *)ptr16++);

	ptr32 = (uint32_t *)ptr16;
//	if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
	{
//		*ptr32 = g_runtime_inst.runtime_paras.stack_xmode | ZZWPRO_STACK_ZZW_MODE;
		*ptr32 = g_runtime_inst.runtime_paras.stack_xmode | ((interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) ? ZZWPRO_STACK_ZZW_MODE : 0);
		if ((auto_trace_nv25k & ZZWPRO_AUTOTRACE_NV_ENABLE_MASK) == 0)
			*ptr32 &= ~ZZWPRO_STACK_N2V_MODE;

		if (dev_is_base())
		{
			g_runtime_inst.runtime_paras.stack_xmode &= ~ZZWPRO_STACK_MIX_MODE;
//			g_runtime_inst.runtime_paras.stack_xmode |= ZZWPRO_STACK_BASE_MODE;	// can NOT force it(network bridge shoule NOT enable it)
		}
		else // dev_is_base
		{
			set_xmode_with_work_mode(interphone_mode, ptr32);
		} // dev_is_base
		vlog_v("stack","\tBind rx=%d", *((uint8_t *)stack_call_buffer + 1));	// conts[1]
	}
//	else
//	{
//		*ptr32 = 0;
//	}

	gpst_gpsw = zzwpro_gps_poll_init();
	ptr32++;
	*((uint16_t *)ptr32) = (uint16_t)gpst_gpsw;				// GPSW:��̨�ϴ�GPS��Vʱ϶��180ms����=0��ȡ��GPS�Զ��ϴ�����
	*((uint8_t *)ptr32 + 2) = (uint8_t)(gpst_gpsw >> 16);	// GPST:��̨�ϴ�GPS��ʱ϶��0~5
	*((uint8_t *)ptr32 + 3) = (uint8_t)(gpst_gpsw >> 24);	// GPSP:��̨����ϴ�GPS�����ڣ��Ի�վ��Ч��=0��ȡ������ϴ����ܣ��ϴ�����ʱ��=GPSP x 9s
	vlog_v("stack","GPSW(V slot)=%d,GPST(slot)=%d,GPSP(period)=%d", *((uint16_t *)ptr32), *((uint8_t *)ptr32 + 2), *((uint8_t *)ptr32 + 3));

	ptr32++;
	*((uint16_t *)ptr32) = g_runtime_inst_xvbase->tRegw;	// tRegw:��̨��¼���ڣ���λ���룩��0=ȡ����̨���ڵ�¼
	vlog_v("stack","tRegw=%d", *((uint16_t *)ptr32));

	stack_para_aux1 = 0;
	stack_para_aux2 = 0;
	CALL_STACK_PROCESS(STACK_CALL_TYPE_SET_PARAS);
}

uint8_t search_ctrl_chan_at_table(uint32_t cont, uint32_t *table, uint32_t total)	// return 1 if content at table
{
	uint32_t i, c;

	for (i = 0, c = cont & 0x00000fff; i < total; i++)
	{
		if (c == (table[i] & 0x00000fff))
			break;
	}

	return (i < total) ? 1 : 0;
}

void merge_neighbour_with_ctrl_table(uint16_t *neigh, uint32_t *ctrl)
{
	uint32_t i, append, temp[CTRL_TABLE_TOTAL_ITEM];

	memset(temp, 0, sizeof(uint32_t) * CTRL_TABLE_TOTAL_ITEM);
	temp[0] = ctrl[0];	// attributive base
	temp[1] = ctrl[1];
	append = 0;
	for (i = 0; (i < NEIGHBOUT_BASE_NUMBER) && (neigh[i] & 0x0fff); i++)
	{
		if (search_ctrl_chan_at_table((uint32_t)neigh[i], temp, 2 + append) == 0)		// not found
		{
			temp[2 + append] = (uint32_t)neigh[i];
			append++;
		}
	}

	for (i = 2; (i < CTRL_TABLE_TOTAL_ITEM) && ((2 + append) < CTRL_TABLE_TOTAL_ITEM) && (ctrl[i] & 0x00000fff); i++)
	{
		if (search_ctrl_chan_at_table(ctrl[i], temp, 2 + append) == 0)		// not found
		{
			temp[2 + append] = (uint32_t)ctrl[i];
			append++;
		}
	}

	memcpy(ctrl, temp, sizeof(uint32_t) * CTRL_TABLE_TOTAL_ITEM);
}

void stack_set_network_parameters(void)
{
	uint16_t chan, *ptr16;
	uint32_t i, n_ctrl = 0, *ptr32;

	memset((void *)stack_call_buffer, 0, STACK_CALL_BUFFER_LENGTH);
	ptr16 = (uint16_t *)stack_call_buffer;
	maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_NID, (void *)ptr16++);
	maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_NIDM, (void *)ptr16++);
	i = maintain_setup_parameter(PARA_OPERATE_READ, CTRL_SCANNING_MODE_POS, 0);
	if (i == SCANNING_MODE_LONG_HUNT)
	{
		maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_SCANNING, (void *)ptr16);	// chl & chh
		ptr16 += 2;
	}
	else
	{
		*ptr16++ = 0;
		*ptr16++ = 0;
	}

	memset(dynamic_q_tx_freq, 0, sizeof(uint16_t) * ZZWPRO_Q_TX_TOTAL_CHAN);

	ptr32 = (uint32_t *)ptr16;

	if (dev_is_base())
	{
		memcpy(ptr32, g_runtime_inst_xvbase->base_ctrl_line01, sizeof(uint32_t) * 2);
		n_ctrl += 2 + ZZWPRO_Q_TX_TOTAL_CHAN;
		ptr32[2] = g_runtime_inst_xvbase->dynamic_q_tx_freq[0]; // line 3
		ptr32[3] = g_runtime_inst_xvbase->dynamic_q_tx_freq[1]; // line 4
		ptr32[4] = g_runtime_inst_xvbase->dynamic_q_tx_freq[2]; // line 5
		ptr32[5] = g_runtime_inst_xvbase->dynamic_q_tx_freq[3]; // line 6
		ptr32[6] = g_runtime_inst_xvbase->dynamic_q_tx_freq[4]; // line 7
		ptr32[7] = g_runtime_inst_xvbase->dynamic_q_tx_freq[5]; // line 8
		ptr32[8] = g_runtime_inst_xvbase->dynamic_q_tx_freq[6]; // line 9
//		ptr32[8] = zzwpro_tx_freq_to_chan(g_runtime_inst_xvbase->dynamic_q_tx_freq[6]);
		ptr32[9] = g_runtime_inst_xvbase->dynamic_q_tx_freq[7]; // line 10
		n_ctrl += 10;	// 20220805: �������õ�10-19��
		memcpy(&ptr32[10], g_runtime_inst_xvbase->base_ctrl_linean, sizeof(uint32_t) * 10);
	}
	else // dev_is_base
	{
		if ((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) && (i == SCANNING_MODE_ASSIGNED))
//		if (i == SCANNING_MODE_ASSIGNED)
		{
			maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_CTRL_INDEX, &chan);
			get_ctrl_table_items(0, chan, ptr32);
			*ptr32 &= 0x00000fff;
//			memset(ptr32 + 1, 0, (CTRL_TABLE_TOTAL_ITEM - 1) * sizeof(uint32_t));
		}
		else
		{
			n_ctrl = get_ctrl_table_items(0, 0xffff, ptr32);
			if (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING)
			{
				maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_PRIOR_CTRL_CHAN, &chan);
				if (chan < n_ctrl)
					move_one_u32_item_to_top(ptr32, (uint32_t)chan);

//				if (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING)	// can disable after V208
//				{
					for (chan = 0; chan < 2; chan++)	// move the attributive base channel to top
					{
						for (i = chan; i < n_ctrl; i++)
						{
							if (ptr32[i] & 0x80000000)
							{
								move_one_u32_item_to_top(&ptr32[chan], i - chan);
								ptr32[chan] &= 0x00000fff;
								break;
							}
						}
						if (i >= n_ctrl)				// no attributive base channel
						{
							move_one_u32_item_to_top(&ptr32[chan], (uint32_t)n_ctrl);	// insert one item and set to 0
							ptr32[chan] = 0;
						}
					}
//					merge_neighbour_with_ctrl_table(g_runtime_inst.neighbour_base_info[GET_DIGITAL_ANALOG_MODE_INDEX() - 1], ptr32);
//				}
			}
		}
	}

	vlog_v("stack","Set ctrl table=%d(0-9=%08x/%08x/%08x/%08x/%08x/%08x/%08x/%08x/%08x/%08x)", n_ctrl,
		ptr32[0], ptr32[1], ptr32[2], ptr32[3], ptr32[4], ptr32[5], ptr32[6], ptr32[7], ptr32[8], ptr32[9]);

	stack_para_aux1 = 0;
	stack_para_aux2 = 0;
	CALL_STACK_PROCESS(STACK_CALL_TYPE_SET_NETWORK);
}

void stack_set_response(uint16_t index)
{
	memset((void *)stack_call_buffer, 0, STACK_CALL_BUFFER_LENGTH);
	if (set_response_group(interphone_mode, index, stack_call_buffer + 1, 63) & 0x8000)
	{
		stack_para_aux1 = (uint32_t)get_stack_scan_global_freq();
		stack_para_aux2 = 0;
		CALL_STACK_PROCESS(STACK_CALL_TYPE_SET_RESPONSE);
	}
}

void stack_set_ctrl_channel(uint8_t set_ctrl)
{
	uint8_t flag;
	uint16_t index, mask;
	uint32_t chan;

//	mask = (get_mobile_rf_type() == MOBILE_RF_TYPE_DDS) ? 0x0fff : 0xffff;
	mask = 0x0fff;

	if (!set_ctrl)
	{
//		vlog_v("stack","[set chan]to %d", stack_state.chan & mask);
		config_pll_paras(interphone_mode, 0, get_current_chan());
	}
	else
	{
		flag = maintain_setup_parameter(PARA_OPERATE_READ, CTRL_SCANNING_MODE_POS, 0);
		if (flag == SCANNING_MODE_ASSIGNED)
		{
			stack_para_aux1 = 0;	// point to index 0 because only one item at stack's ctrl table when the scanning mode is assigned
			CALL_STACK_PROCESS(STACK_CALL_TYPE_SWITCH_CTRL);
			maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_CTRL_INDEX, &index);
			get_ctrl_table_items(0, index, &chan);
			config_pll_paras(interphone_mode, 0, (uint16_t)(chan & mask));
		}
		else
		{
			get_ctrl_table_items(0, 0, &chan);
			config_pll_paras(interphone_mode, 0, (uint16_t)(chan & mask));
		}
	}
}


uint8_t update_dsp_work_mode_2(uint8_t work_mode, uint8_t print_info, uint32_t vmode_xcs)
{
	char print_string[60];
	uint8_t mode = work_mode;

	if (mode == INTERPHONE_MODE_ZZWPRO_Q)
	{
		sprintf(print_string, "\tZZW Pro Q BASE,xcs=0x%x", vmode_xcs);
	}
	else if (mode == INTERPHONE_MODE_ZZWPRO_N)
	{
		sprintf(print_string, "\tZZW Pro %s BASE,xcs=0x%x", (auto_trace_nv25k & ZZWPRO_AUTOTRACE_NV_ENABLE_MASK) ? "N+V25K" : "N", vmode_xcs);
	}
	else if (mode == INTERPHONE_MODE_ZZWPRO_V)
	{
		sprintf(print_string, "\tZZW Pro V BASE,xcs=0x%x", vmode_xcs);
	}
	else if (mode == INTERPHONE_MODE_ZZWPRO_V25K)
	{
		sprintf(print_string, "\tZZW Pro %s BASE,xcs=0x%x", (auto_trace_nv25k & ZZWPRO_AUTOTRACE_NV_ENABLE_MASK) ? "V25K+N" : "V25K", vmode_xcs);
	}
	else
	{
		mode = INTERPHONE_MODE_PDT_CONV;
		sprintf(print_string, "\tPDT Conv BASE");
	}

	dsp_work_mode_switch(0x80000000 | mode);
	interphone_mode = mode;

	if (print_info)
		vlog_v("stack","%s", print_string);
	return interphone_mode;
}

uint8_t update_dsp_work_mode_1(uint8_t work_mode, uint8_t print_info, uint32_t vmode_xcs);
uint8_t update_dsp_work_mode(uint8_t work_mode, uint8_t print_info, uint32_t vmode_xcs)
{
	if (dev_is_base())
		return update_dsp_work_mode_2(work_mode, print_info, vmode_xcs);
	else
		return update_dsp_work_mode_1(work_mode, print_info, vmode_xcs);
}

uint8_t update_dsp_work_mode_1(uint8_t work_mode, uint8_t print_info, uint32_t vmode_xcs)
{
	char print_string[60];

	if (interphone_mode != INTERPHONE_MODE_MPT_TRUNKING)
	{
		interphone_mode = work_mode;
		if (work_mode == INTERPHONE_MODE_PDT_TRUNKING)
		{
			sprintf(print_string, "\tPDT trunking(cs=0x%x,mode=%d)", stack_para_aux1, work_mode);
			dsp_work_mode_switch(INTERPHONE_MODE_PDT_TRUNKING);
		}
		else if ((work_mode < INTERPHONE_MODE_PDT_CONV) || (work_mode == INTERPHONE_MODE_ZZW_ADHOC))
		{
			sprintf(print_string, "\t%s(cs=0x%x,mode=%d)",
				(work_mode == INTERPHONE_MODE_ZZW_ADHOC) ? "ZZW HoC" : "PDT Direct", stack_para_aux1, work_mode);
			dsp_work_mode_switch(INTERPHONE_MODE_PDT_DIRECT);
		}
		else if (work_mode == INTERPHONE_MODE_ZZWPRO_Q)
		{
			sprintf(print_string, "\tZZW Pro Q(cs=0x%x,mode=%d,xcs=0x%x)", stack_para_aux1, work_mode, vmode_xcs);
			dsp_work_mode_switch(INTERPHONE_MODE_ZZWPRO_Q);
		}
		else if (work_mode == INTERPHONE_MODE_ZZWPRO_N)
		{
			sprintf(print_string, "\tZZW Pro %s(cs=0x%x,mode=%d,xcs=0x%x)", (auto_trace_nv25k & ZZWPRO_AUTOTRACE_NV_ENABLE_MASK) ? "N+V25K" : "N", stack_para_aux1, work_mode, vmode_xcs);
			dsp_work_mode_switch(INTERPHONE_MODE_ZZWPRO_N);
		}
		else if (work_mode == INTERPHONE_MODE_ZZWPRO_V)
		{
			sprintf(print_string, "\tZZW Pro V(cs=0x%x,mode=%d,xcs=0x%x)", stack_para_aux1, work_mode, vmode_xcs);
			dsp_work_mode_switch(INTERPHONE_MODE_ZZWPRO_V);
		}
		else if (work_mode == INTERPHONE_MODE_ZZWPRO_V25K)
		{
			sprintf(print_string, "\tZZW Pro %s(cs=0x%x,mode=%d,xcs=0x%x)", (auto_trace_nv25k & ZZWPRO_AUTOTRACE_NV_ENABLE_MASK) ? "V25K+N" : "V25K", stack_para_aux1, work_mode, vmode_xcs);
			dsp_work_mode_switch(INTERPHONE_MODE_ZZWPRO_V25K);
		}
		else
		{
			sprintf(print_string, "\tPDT Conventional(cs=0x%x,mode=%d)", stack_para_aux1, work_mode);
			dsp_work_mode_switch(INTERPHONE_MODE_PDT_CONV);
		}
	}
	else
	{
		sprintf(print_string, "\tMPT trunking(cs=0x%x,mode=%d)", stack_para_aux1, work_mode);
		dsp_work_mode_switch(INTERPHONE_MODE_MPT_TRUNKING);
	}

	if (print_info)
		vlog_v("stack","%s", print_string);
	return interphone_mode;
}

void set_time_gap_for_insert_slot_sync(uint8_t gap);
void set_book_struct_with_base_set_id(USER_BOOK *book)
{
	if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)								// 20220622: ������������ɫ�루16bit�������ŵ��ţ���������Ƶ���󣨴��飩
	{
		book->bind_ch = rx_freq_to_chan((float)get_xvbase_rx_freq() / 80);
	}
	else
	{
		book->bind_ch = rx_freq_to_chan((float)get_xvbase_rx_freq() / 80) & 0x0fff; // 20220614: �����4bit�Դ����վɫ��
		book->bind_ch |= p_stack_parameters->color_code & 0xf000;
	}
//	book->bind_ch |= (g_runtime_inst_xvbase->base_watch_id & 0x07) << 16;			// 3bit��Ƶ������
	book->bind_ch |= (g_runtime_inst_xvbase->base_watch_id & 0x7f) << 16;			// 3bit��Ƶ������+3bit��ʱ϶״̬+1bit�����ԣ�ֱͨ���м̣�
	if (g_runtime_inst_xvbase->base_watch_id >> 8)
	{
		book->id &= 0x7f000000;
		book->id |= (g_runtime_inst_xvbase->base_watch_id << 24) & ((uint32_t)USER_ID_BACKGROUND << 24);	// 20230609:��������Ҫ�Ǽ�Ϊȫ���飨�������вŻ�ת����CAN��
		book->id |= g_runtime_inst_xvbase->base_watch_id >> 8;
	}
}

void stack_set_watch(void)
{
	USER_BOOK *book;
	uint16_t index, next;
	uint32_t xcs;
	uint8_t  device_is_base = dev_is_base();
	USER_BOOK zzwpro_base_watch =
	{
		"XV TEST WATCH",
		0x0a100001,
		0x0003000a
	};

	if (interphone_mode != INTERPHONE_MODE_MPT_CONV)
	{
		if (device_is_base)
		{
			index = 0xffff;	// do not set response
			set_book_struct_with_base_set_id(&zzwpro_base_watch);
			book = &zzwpro_base_watch;
		}
		else // dev_is_base
		{
			if (is_enter_instruction_to_conv())
			{
				book = inst_conv;
				index = 0xffff;
				if (book->bind_ch & 0x80000000)
				{
					book->id &= ~((USER_ID_ATTR_CONVENTIONAL | USER_ID_ATTR_THROUGHOUT) << 24);
//					if ((book->bind_ch & 0x00000fff) <= 240)
					if ((book->bind_ch & ZZWPRO_GROUP_IS_DIFF_FREQ_MASK) == 0)
						book->id |= (USER_ID_ATTR_CONVENTIONAL | USER_ID_ATTR_THROUGHOUT) << 24;
					else
						book->id |= USER_ID_ATTR_CONVENTIONAL << 24;
				}
			}
			else
			{
				book = get_watch_at_user_book(1, 0xffff, &index);
				if (book)
				{
					if (id_can_watch(interphone_mode, book->id) == 0)
						book = 0;
				}
			}

			if (book == 0)
			{
				next = search_next_watching_group(0, KEY_CODE_KNOB_CLOCK);
				if (next != 0xffff)
				{
					if (next != 0xfffe)
						maintain_stack_parameter(PARA_OPERATE_WRITE, STACK_PARA_WATCH_ID, &next);
					book = get_books_item_content(next, 1);
					index = next;
				}
				else
				{
					vlog_v("stack","[stack_set_watch]NO item can be watched at group books");
				}
			}
		} // dev_is_base

		if (book)
		{
			stack_set_response(index);

			if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
			{
				xcs = book->bind_ch & ((interphone_mode == INTERPHONE_MODE_ZZWPRO_Q) ? 0x00070000 : 0x007f0000);	// 3bit(bind tx slot)+3bit(jump mode)+1bit(1-repeater or 0-direct group)
				// vlog_v("stack","switch to:id=0x%08x,chan=%d(R=%3.54f,T=%3.4f),vmode=%d,bind tx=%d",
				vlog_v("stack","switch to:id=0x%08x,chan=%d(tx%c=rx),jump=%d,bind tx=%d", book->id, book->bind_ch & 0x0000ffff,
						(book->bind_ch & ZZWPRO_GROUP_IS_DIFF_FREQ_MASK) ? '!' : '=', (xcs >> 16) & 0x07, (xcs >> 19) & 0x07);
				set_slot_detail_total((uint8_t)((xcs >> 16) & 0x07));
				stack_para_aux1 = book->id & 0x00ffffff;
				my_stack_id_watch = stack_para_aux1;
				stack_para_aux1 |= xcs << 8;
				set_xv_freq_is_different((book->bind_ch & ZZWPRO_GROUP_IS_DIFF_FREQ_MASK) ? 1 : 0);
				set_xv_work_mode((book->bind_ch & 0x003f0000) >> 16);
				set_dsp_mode_sync(1);
			}
			else
			{
				if (device_is_base)
				{
					set_dsp_mode_sync(IS_BASE_SYNC_MODE_FOLLOW_MOBILE());
					// 20220806: set pdt watch paras with zzw setup(by xvbase configer) ------ ����Э��ջֻ��Ҫ֪����ǰ��ֱͨ�����м�
					stack_para_aux1 = (book->id & 0x00ffffff) | ((book->bind_ch & ZZWPRO_GROUP_IS_DIFF_FREQ_MASK) ? 0x01000000 : 0x03000000);	// base_watch_id'bit6; copy base_watch_id at bind_ch's high 16bit
					vlog_v("stack","[base]switch to:id=0x%08x,chan=0x%08x", book->id, book->bind_ch);
				}
				else
				{
					stack_para_aux1 = book->id & 0x0fffffff;
					if (book->bind_ch & 0x80000000)
					{
						stack_para_aux1 |= 0x10000000;	// 20211122: highest byte: bit4:1-PQ(PDT down, Q up);
						if (auto_trace_nv25k & ZZWPRO_AUTOTRACE_PQ_FLAG_MASK)
							auto_trace_nv25k |= ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK;
					}
					else
					{
						auto_trace_nv25k &= ~ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK;
					}
					vlog_v("stack","switch to:id=0x%08x,chan=0x%08x(PQ=%s)",
						book->id, book->bind_ch, (auto_trace_nv25k & ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK) ? "En" : "Dis");
					if (stack_para_aux1 == 0)
						stack_para_aux1 = 1;
				}
			}

			stack_para_aux2 = book->bind_ch & 0x0000ffff;
//			stack_para_aux2 = (book->bind_ch & 0xffff0fff) | (p_stack_parameters->color_code & 0xf000);
			next = get_stack_scan_global_freq();
			if (next)
				vlog_v("stack","global scan chan=0x%04x", next);
			xcs = (CALL_STACK_PROCESS(STACK_CALL_TYPE_SWITCH_WATCH) == STACK_RETURN_TYPE_STATE) ? stack_call_buffer[9] : 0xffffffff;	// XCS

			index = (uint16_t)get_interphone_mode(stack_para_aux1);
			update_dsp_work_mode((uint8_t)index, 1, xcs);
//			set_vocoder_rate_agc_ns(interphone_mode, 3, get_vocoder_setup_content());	// pdt�����л���������ʱ��������������ò���ȷ�����⣬�����������غ���ʱ�ٴ�����һ������߳ɹ���

			if ((interphone_mode < INTERPHONE_MODE_PDT_CONV) && (get_stack_work_mode() & STACK_MODE_DIRECT_REPEATER))	// is_throughout_id(book->id)
			{
				set_time_gap_for_insert_slot_sync(6);
			}
			else
			{
				set_time_gap_for_insert_slot_sync(0);
				if (interphone_mode == INTERPHONE_MODE_PDT_CONV)	// use by get_zzwpro_nvq_q_chan() when send Q at pdt
				{
					set_xv_freq_is_different(1);
				}
				else if (interphone_mode >= INTERPHONE_MODE_ZZWPRO_Q)
				{
//					if (((book->bind_ch & 0x00000fff) != get_current_chan()) && IS_DSP_SAVE_POWER_ENABLE())
					if (IS_DSP_SAVE_POWER_ENABLE())
					{
						clr_heartbeat_rssi();
						set_delay_to_enter_sleep_external(60);
					}
					if (auto_trace_nv25k & ZZWPRO_AUTOTRACE_VQ_ENABLE_MASK)
					{
						set_nvq_sync_flag();
					}

					device_is_sync = 0;

					if (dev_have_base_feature())
					{
					}
					else // dev_have_base_feature
					{
						send_v_data_with_message_format_handle(SEND_V_MESSAGE_SEND_NOW | SEND_V_MESSAGE_ATTACH);
					} // dev_have_base_feature
				}
			}
		}
		else
		{
			index = 0xffff;	// set response all zero
		}
	}
	else
	{
		vlog_v("stack","mode: [A]MPT Conventional");
		dsp_work_mode_switch(INTERPHONE_MODE_MPT_CONV);
		config_pll_paras(INTERPHONE_MODE_MPT_CONV, 0, 0);
	}

	set_auto_switch_rf_power_enable();
}


static uint8_t iop_scan_channel_stt_flag = 0;	// 0-idle; 1-scanning group now; 2-scanning channel now
uint8_t is_enter_scanning_mode(void)
{
	return iop_scan_channel_stt_flag;
}

uint8_t is_high_authenticate_enable(void)
{
	return ((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) && (get_stack_work_mode() & STACK_MODE_HIGH_AUTHENTICATION)) ? 1 : 0;
}

#define STACK_MODE_TEST_SOMETHING_TIP_TOTAL	9
const uint8_t stack_mode_test_something_tip[STACK_MODE_TEST_SOMETHING_TIP_TOTAL][14] = {
	"��ֹԽ���л�", "����Խ���л�", "ȫ�ر�", "ɫ��ر�", "���ر�", "�رո߼���Ȩ", "�����߼���Ȩ", "��ֹ�鸽��", "�����鸽��"
};
uint8_t* stack_mode_test_something(uint8_t flag)	// 0-toggle area switch; 1-toggle avoidance mechanism
{
	uint32_t mode;
	uint8_t tip_index = 0xff;

	mode = get_stack_work_mode();
//	vlog_v("stack","mode=0x%08x", mode);
	if (flag == 0)
	{
		if (mode & STACK_MODE_HANDOVER_AREA)
		{
			mode &= ~STACK_MODE_HANDOVER_AREA;
			tip_index = 0;
		}
		else
		{
			mode |= STACK_MODE_HANDOVER_AREA;
			tip_index = 1;
		}
	}
	else if (flag == 1)
	{
		tip_index = (mode & STACK_MODE_AVOIDANCE_MECHANISM) >> STACK_MODE_AVOIDANCE_MECHANISM_SHIFT;
		mode &= ~STACK_MODE_AVOIDANCE_MECHANISM;
		if (tip_index == 0)
			tip_index = 1;
		else if (tip_index == 1)
			tip_index = 2;
		else
			tip_index = 0;
		mode |= (uint32_t)tip_index << STACK_MODE_AVOIDANCE_MECHANISM_SHIFT;
		tip_index += 2;
	}
	else if (flag == 2)
	{
		if (mode & STACK_MODE_HIGH_AUTHENTICATION)
		{
			mode &= ~STACK_MODE_HIGH_AUTHENTICATION;
			tip_index = 5;
		}
		else
		{
			mode |= STACK_MODE_HIGH_AUTHENTICATION;
			tip_index = 6;
		}
	}
	else if (flag == 3)
	{
		if (mode & STACK_MODE_GROUP_ATTACH)
		{
			mode &= ~STACK_MODE_GROUP_ATTACH;
			tip_index = 7;
		}
		else
		{
			mode |= STACK_MODE_GROUP_ATTACH;
			tip_index = 8;
		}
	}

	set_stack_work_mode(mode);
//	vlog_v("stack","mode to:0x%08x", mode);
	reset_stack_with_disable_int();
	if (tip_index < STACK_MODE_TEST_SOMETHING_TIP_TOTAL)
		return (uint8_t *)stack_mode_test_something_tip[tip_index];
	else
		return (uint8_t *)0;
}


const uint8_t iop_scan_channel_stt_tip[2][7][16] = {
	{"ֹͣɨƵ����", "����ɨƵ����", "ֹͣɨ�鹦��", "����ɨ�鹦��", "����ɨƵ��", "����ɨ����", "�����ڳ���ģʽ"},
	{"Stop scan chan", "Star scan chan", "Stop scan group", "Star scan group", "Scan chan now", "Scan group now", "CONV mode only"}
};

USER_BOOK* iop_scan_channel_next(uint8_t flag);
static uint32_t iop_scan_group_bind_ch = 0x0001100A, iop_scan_group_bind_id = 0x100001;	// bind slot1; color code=1; ch=10
void gen_iop_scan_tip(uint8_t *tip, uint32_t id, uint32_t bind_ch)
{
	uint16_t n;
	uint8_t tmp[8];

	n = id_to_number(interphone_mode, 0, id, 0, tip);
	if (n & 0x3fff)		// number
	{
		p_strncpy(tmp, &tip[4], 6);
		tmp[6] = 0;
		p_strcpy(&tip[3], tmp);
		p_strcpy(&tip[5], &tmp[3]);
	}
	n = p_strlen(tip);
	n += sprintf((char *)(tip + n), ".%1X.", (bind_ch >> 12) & 0x0F);
	sprintf((char *)(tip + n), "%03d", bind_ch & 0x0FFF);
}

void stack_set_watch_iop_scan_channel(USER_BOOK *book)
{
	uint8_t tmp, iop_scan_tip[22] = "C";

	tmp = (book->bind_ch >> 14) & 0x0C;	// zzw timeout: 10s(0x01)-bind slot1; 20s(0x02)-bind slot2; 30s(0x03)-bind nothing
	if (((book->bind_ch & 0x00000fff) > 240) && ((book->bind_ch & 0x00000fff) <= 640))
		tmp |= USER_ID_ATTR_CONVENTIONAL;							// conventional
	else
		tmp |= USER_ID_ATTR_CONVENTIONAL | USER_ID_ATTR_THROUGHOUT;	// conventional throughout
	stack_para_aux1 = (iop_scan_group_bind_id & 0x00ffffff) | ((uint32_t)tmp << 24);
	stack_para_aux2 = book->bind_ch & 0x0000ffff;
	gen_iop_scan_tip(&iop_scan_tip[1], stack_para_aux1, stack_para_aux2);
	vlog_v("stack","[iop scan channel]%s:%s", book->name, iop_scan_tip);
//	GL_LCD_TipDraw(0, iop_scan_tip, 0, 800);
	CALL_STACK_PROCESS(STACK_CALL_TYPE_SWITCH_WATCH);
}

void iop_scan_channel_handle(void)
{
	set_sync_call_stack_type(SYNC_CALL_STACK_IOP_SCAN_CHAN);
}

void stack_set_watch_iop_scan_group(USER_BOOK *book)
{
	uint8_t tmp, iop_scan_tip[22] = "G";
	uint32_t bind_ch;

	bind_ch = ((iop_scan_group_bind_ch & 0x00000fff) == 0) ? book->bind_ch : iop_scan_group_bind_ch;
	tmp = (bind_ch >> 14) & 0x0C;			// zzw timeout: 10s(0x01)-bind slot1; 20s(0x02)-bind slot2; 30s(0x03)-bind nothing
	if (((bind_ch & 0x00000fff) > 240) && ((bind_ch & 0x00000fff) <= 640))
		tmp |= USER_ID_ATTR_CONVENTIONAL;										// conventional
	else
		tmp |= USER_ID_ATTR_CONVENTIONAL | USER_ID_ATTR_THROUGHOUT;				// conventional throughout
	stack_para_aux1 = (book->id & 0x00ffffff) | ((uint32_t)tmp << 24);
	stack_para_aux2 = (book->bind_ch & 0x0000f000) | (bind_ch & 0x00000fff);	// use the user_book'color_code absolutely
	gen_iop_scan_tip(&iop_scan_tip[1], stack_para_aux1, stack_para_aux2);
	vlog_v("stack","[iop scan group]%s:%s", book->name, iop_scan_tip);
//	GL_LCD_TipDraw(0, iop_scan_tip, 0, 800);
	CALL_STACK_PROCESS(STACK_CALL_TYPE_SWITCH_WATCH);
}

void iop_scan_group_handle(void)
{
	set_sync_call_stack_type(SYNC_CALL_STACK_IOP_SCAN_GROUP);
}

uint8_t iop_scan_channel_stop(uint8_t timer_id)
{
	uint8_t ret;

	if (dev_is_base())
	{
		return 0xff;
	}
	else // dev_is_base
	{
		iop_scan_channel_stt_flag = 0;
		ret = timer_destroy(timer_id);
		return ret;
	} // dev_is_base
}

uint8_t* iop_scan_channel_stt(uint8_t flag)	// 0-group; 1-channel; else-stop quietly(but not reset to the watch group)
{
	static uint8_t iop_scan_channel_stt_timer_id = 0xff;

	if (dev_is_base())
	{
		iop_scan_channel_stt_flag = 0;
		return (uint8_t *)iop_scan_channel_stt_tip[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][6];
	}
	else // dev_is_base
	{
		if (interphone_mode <= INTERPHONE_MODE_PDT_CONV)
		{
			if (flag > 1)
			{
				if (iop_scan_channel_stt_flag)
					iop_scan_channel_stt_timer_id = iop_scan_channel_stop(iop_scan_channel_stt_timer_id);	// stop and play voice(not set watch)
			}
			else
			{
				if (iop_scan_channel_stt_flag == 0)		// 0-idle; 1-group; 2-channel
				{
					iop_scan_channel_stt_flag = (flag == 0) ? 1 : 2;
					iop_scan_group_bind_id = iop_scan_channel_next(0xff)->id;
					iop_scan_group_bind_ch = iop_scan_channel_next(1)->bind_ch;
					iop_scan_channel_stt_timer_id = timer_initial(0, 1000, (flag == 0) ? iop_scan_group_handle : iop_scan_channel_handle);
				}
				else
				{
					if (((flag == 0) && (iop_scan_channel_stt_flag == 1)) ||
						(flag && (iop_scan_channel_stt_flag == 2)))	// stop really
					{
						iop_scan_channel_stt_timer_id = iop_scan_channel_stop(iop_scan_channel_stt_timer_id);
						stack_set_watch();							// reset to watch the standby
					}
					else
					{
						return (uint8_t *)iop_scan_channel_stt_tip[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][(iop_scan_channel_stt_flag == 1) ? 5 : 4];
					}
				}
			}

			return (uint8_t *)iop_scan_channel_stt_tip[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][((iop_scan_channel_stt_flag == 0) ? 0 : 1) + ((flag == 0) ? 2 : 0)];
		}
		else
		{
			iop_scan_channel_stt_flag = 0;
			iop_scan_channel_stt_timer_id = timer_destroy(iop_scan_channel_stt_timer_id);
			return (uint8_t *)iop_scan_channel_stt_tip[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][6];
		}
	} // dev_is_base
}

uint32_t set_self_id(STATIC_PARAMETERS_TYPEDEF *dat, uint32_t id)
{
	uint32_t *p_id, ret;

	if (interphone_mode <= INTERPHONE_MODE_PDT_TRUNKING)
		p_id = &dat->stack_pdt.id;
	else if (interphone_mode <= INTERPHONE_MODE_MPT_CONV)
		p_id = &dat->stack_mpt.id;
	else
		p_id = &dat->stack_zzw.id;

	ret = *p_id;
	*p_id &= 0xff000000;
	*p_id |= id & 0x00ffffff;

	return ret;
}


char stack_info_code[2];	// 0/1: function code
char stack_compile_time[10];
void convert_stack_compile_data_format(char *new_str, char *ori_str)
{
	char n, day, tmp[8], month[12][4] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};
	uint32_t year;

	p_strncpy(tmp, ori_str, 3);
	tmp[3] = 0;
	for (n = 0; n < 12; n++)
	{
		if (p_strcmp(tmp, month[n]) == 0)
			break;
	}

	p_strncpy(tmp, ori_str + 4, 2);
	tmp[2] = 0;
	day = string2decimal((uint8_t *)tmp);

	p_strncpy(tmp, ori_str + 7, 4);
	tmp[4] = 0;
	year = string2decimal((uint8_t *)tmp);
	sprintf(new_str, "%d/%02d/%02d", year, n + 1, day);
}

void get_stack_information(void)
{
	char stack_compile_date[12];
	uint8_t stack_func_code[2], stack_ver_code[2], *stack_time;

	stack_func_code[0] = *((uint8_t *)((uint32_t)stack_call_buffer + 1));
	stack_func_code[1] = *((uint8_t *)((uint32_t)stack_call_buffer + 0));

	stack_ver_code[0] = *((uint8_t *)((uint32_t)stack_call_buffer + 3));
	stack_ver_code[1] = *((uint8_t *)((uint32_t)stack_call_buffer + 2));

	stack_time = (uint8_t *)((uint32_t)stack_call_buffer + 16);

	convert_stack_compile_data_format(stack_compile_date, (char *)((uint32_t)stack_call_buffer + 4));
	set_software_version(1, stack_ver_code[0], stack_ver_code[1], (uint8_t *)stack_compile_date, stack_time, stack_func_code);
}

void print_stack_version(void)
{
	vlog_v("stack","========%s========", "stack information");
	vlog_v("stack","  function code: %d%d%d%d", g_runtime_inst.device_information.stack_function.ver_major >> 4,
					g_runtime_inst.device_information.stack_function.ver_major & 0x0f,
					g_runtime_inst.device_information.stack_function.ver_minor >> 4,
					g_runtime_inst.device_information.stack_function.ver_minor & 0x0f);

	vlog_v("stack","  version: %d%d.%d%d", g_runtime_inst.device_information.stack_soft_ver.ver_major >> 4,
					g_runtime_inst.device_information.stack_soft_ver.ver_major & 0x0f,
					g_runtime_inst.device_information.stack_soft_ver.ver_minor >> 4,
					g_runtime_inst.device_information.stack_soft_ver.ver_minor & 0x0f);

	vlog_v("stack","  date/time: %04d-%02d-%02d %02d:%02d:%02d", 2000 + g_runtime_inst.device_information.stack_compile.rtc_year,
						g_runtime_inst.device_information.stack_compile.rtc_month,
						g_runtime_inst.device_information.stack_compile.rtc_day,
						g_runtime_inst.device_information.stack_compile.rtc_hour,
						g_runtime_inst.device_information.stack_compile.rtc_minute,
						g_runtime_inst.device_information.stack_compile.rtc_second);
}

uint8_t phone_is_busy_now(void)
{
	return (stack_state.vs & STACK_VS_CALLING) ? 1 : 0;
}

uint8_t phone_update_rssi_each_second(void)
{
	return ((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) && ((stack_state.cs & (STACK_CS_LOGIN_THE_BASE | STACK_CS_BACKGROUND_TSCC)) == STACK_CS_LOGIN_THE_BASE) &&
			((stack_state.vs & STACK_VS_OWN_CALLING) == 0)) ? 1 : 0;
//	return 0;
}

uint8_t phone_is_at_conventional(void)
{
	return ((stack_state.cs & (STACK_CS_CONVENTIONAL_TRUNK | STACK_CS_THROUGH_REPEAT)) == STACK_CS_CONVENTIONAL_TRUNK) ? 1 : 0;
}

uint16_t is_new_message_incoming(void)
{
	return new_msg_incoming;
}

void new_message_incoming_dec(void)
{
	if (new_msg_incoming)
		new_msg_incoming--;
}

uint8_t someone_talking_now(void)
{
	return (stack_state.vs & (STACK_VS_OWN_SPEAKING | STACK_VS_PLAY_VOICE)) ? 1 : 0;
}

uint8_t own_talking_now(void)
{
	return (stack_state.vs & STACK_VS_OWN_SPEAKING) ? 1 : 0;
}

uint8_t own_playing_voice_now(void)
{
	return (stack_state.vs & STACK_VS_PLAY_VOICE) ? 1 : 0;
}

uint8_t own_calling_now(void)
{
	return (stack_state.vs & STACK_VS_OWN_CALLING) ? 1 : 0;
}

uint16_t get_current_chan(void)
{
	uint16_t tmp;

	tmp = stack_state.chan;
//	if (get_mobile_rf_type() == MOBILE_RF_TYPE_DDS)
//		tmp &= 0x0fff;
//	else
//		tmp %= 1000;

	return tmp & 0x0fff;
}

uint16_t get_current_lai(void)
{
	return stack_state.lai;
}

uint8_t is_enter_dynamic_reconf(void)
{
	return (g_runtime_inst.air_config.air_dynamic_reconf.id & 0x00ffffff) ? 1 : 0;
}

uint8_t is_enter_instruction_to_conv(void)
{
	return (p_strcmp(inst_conv->name, inst_conv_name) == 0) ? 1 : 0;
}

USER_BOOK* set_instruction_to_conv_exec_flag(uint8_t flag)
{
	inst_conv->name[15] = flag;
	return inst_conv;
}

uint8_t get_instruction_to_conv_exec_flag(void)
{
	return inst_conv->name[15];
}

uint8_t is_group_calling(void)
{
	return (stack_state.vs & STACK_VS_GROUP_CALL) ? 1 : 0;
}

uint8_t is_crypto_calling(void)
{
	return (stack_state.vs & STACK_VS_ENCRYPT) ? 1 : 0;
}

uint8_t is_ambience_listening(void)
{
	return (stack_state.vs & STACK_VS_ENV_MONITOR) ? 1 : 0;
}

const uint8_t calling_type_tip[2][13][16] = {
	{"���������С�", "������������", "�����н����С�", "���㲥���С�", "���㲥������", "�����ܺ��С�", "�����ܵ�����", "�����������С�", "�������鷢�С�", "�����ݵ����С�", "�����㲥����", "�����㲥����", "OVCM����"},
	{"EMERGENCY[G]", "EMERGENCY[I]", "   CALLING    ", "BROADCAST[G]", "BROADCAST[I]", " ENCRYPT[G] ", " ENCRYPT[I] ", "  CALLING[I]  ", " RECEIVING[G] ", " RECEIVING[I] ", "EMG+BROAD[G]", "EMG+BROAD[I]", "OVCM CALLING"}
};
void get_calling_type_info(uint8_t *info, uint32_t id)
{
	uint8_t index_tip, is_individual_call = is_group_calling() ? 0 : 1;
	uint16_t flag;

	if (stack_state.vs & STACK_VS_EMERGENCY)
	{
		index_tip = (stack_state.vs & STACK_VS_BROADCAST) ? 10 : 0;
	}
	else if (stack_state.vs & STACK_VS_BROADCAST)
	{
		index_tip = (stack_state.vs & STACK_VS_EMERGENCY) ? 10 : 3;
	}
	else if (stack_state.vs & STACK_VS_OVCM)
	{
		index_tip = 12;
	}
	else if (stack_state.vs & STACK_VS_TOTAL_SYSTEM)
	{
		index_tip = 2;
		is_individual_call = 0;
	}
	else if (stack_state.vs & STACK_VS_ENCRYPT)
	{
		index_tip = 5;
	}
	else if (is_individual_call)
	{
		index_tip = 7;
		is_individual_call = 0;
	}
	else
	{
		index_tip = 0xff;
		flag = get_be_called_name(id & (~(USER_ID_INDIVIDUAL << 24)), info);
		if ((flag & 0x4000) && ((flag & 0x0fff) == 0))	// û�ҵ�����NP�����Ϲ淶
			p_strcpy(info, "");
	}

	if (index_tip != 0xff)
		p_strcpy(info, calling_type_tip[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][index_tip + is_individual_call]);
}

uint16_t set_call_type_byte2(uint32_t id)
{
	uint32_t ret;

	ret = (get_stack_work_mode() << 9) & 0x00000600;	// get the call prior
//	if (interphone_mode < INTERPHONE_MODE_ZZW_ADHOC)
	{
		if (id & ((uint32_t)USER_ID_EMERGENCY << 24))
			ret |= STACK_CALLING_EMERGENCY_CALL;
		if (id & ((uint32_t)USER_ID_BROADCAST << 24))
			ret |= STACK_CALLING_BROADCAST_GROUP;
	}

	return (uint16_t)(ret | ((sd_can_be_crypto() || is_enable_aes256_crypto()) ? STACK_CALLING_ENCRYPTED_CALL : 0));
}

uint16_t mpt_set_call_type_both(uint32_t id)
{
	uint32_t type;
	uint16_t ret;

	type = id & ((uint32_t)USER_ID_CALLTYPE_MASK << 24);
	if (type == ((uint32_t)USER_ID_EMERGENCY << 24))
		ret = STACK_MCALLING_EMERGENCY;
	else if (type == ((uint32_t)USER_ID_INCLUSIVE << 24))
		ret = STACK_MCALLING_INCLUSIVE;
	else
		ret = STACK_MCALLING_NORMAL;

	if (type == ((uint32_t)USER_ID_BROADCAST << 24))
		ret |= STACK_MCALLING_BROADCAST;
	else
		ret |= STACK_MCALLING_1NORMAL_0PRIOR;

	if (IS_OUTSIDE_CALL_ID(id))
		ret |= STACK_MCALLING_PABX;

	return ret;
}

uint8_t get_call_type_for_crypto(uint8_t mode, uint8_t is_own_calling)
{
	uint8_t ret;

	if ((is_own_calling == 0) || phone_is_busy_now())
		ret = is_group_calling() ? 0x03 : 0x02;
	else
		ret = *((uint8_t *)stack_call_buffer) ? 0x03 : 0x02;

	ret |= (mode < INTERPHONE_MODE_PDT_CONV) ? 0x00 : (mode == INTERPHONE_MODE_PDT_CONV) ? 0x10 : 0x20;
	return ret;
}

static uint16_t n_stack_data = 12;	// ctyep+code+byte+id1+id2 +(sdm...)
//uint8_t is_zzwpro_remote_ctrl_calling(void);
void ptt_active(uint32_t ptt_call_type)
{
	uint8_t msg_type, *ptr8, is_crypto;
	uint16_t n, *ptr16, tmp;
	uint32_t *ptr32, type, obj_id;
	USER_BOOK *book = (USER_BOOK *)0;
	USER_BOOK zzwpro_base_watch =
	{
		"XV TEST WATCH",
		0x0a100001,
		0x0003000a
	};


//	if (own_talking_now())
//		return;

	ptr32 = send_message_buffer;
	ptr16 = (uint16_t *)stack_call_buffer;

	type = *ptr32++;
	obj_id = *ptr32++;
	n = (uint16_t)(*ptr32);									// high 8bit: BYTES; low 8bit: CODE
	msg_type = (uint8_t)(*ptr32 >> 16);

	if ((type == CALL_PTT_ASYNC_MESSAGE) && (msg_type == MESSAGE_TYPE_LONG_MSG))
		send_message_buffer[0] = CALL_PTT_ASYNC_MESSAGE_2;
	else
		send_message_buffer[0] = CALL_PTT_ASYNC_NULL;

	if ((type == CALL_PTT_ASYNC_NULL) || (type == CALL_PTT_ASYNC_CALLING))	// talking
	{
		set_real_new_bt_to_own_calling(SET_BT_START_MIC);
		reset_mic_voice_paras();
		zzwpro_individual_call_ring(0);
		if (type == CALL_PTT_ASYNC_NULL)
		{
			if (phone_is_busy_now() && (is_ambience_listening() == 0))
			{
				if ((interphone_mode == INTERPHONE_MODE_ZZW_ADHOC) && (stack_state.id_be_called == 0))
					obj_id = get_zzw_watch_id();
				else
					obj_id = stack_state.id_be_called | (is_group_calling() ? 0 : (USER_ID_INDIVIDUAL << 24));	// stack should not use it, just for the sd_calling_setup when busy
			}
			else
			{
				if (dev_is_base())
				{
					set_book_struct_with_base_set_id(&zzwpro_base_watch);
					book = &zzwpro_base_watch;
				}
				else // dev_is_base
				{
					if (is_enter_instruction_to_conv())
					{
						book = inst_conv;
					}
					else
					{
						if ((interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) && (ptt_call_type == STACK_CALL_TYPE_PTT2_ACTIVE) &&
							(g_runtime_inst.air_config.air_dynamic_reconf2.id & 0x00ffffff))
						{
							book = &g_runtime_inst.air_config.air_dynamic_reconf2;
						}
						else if ((interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) && (ptt_call_type == STACK_CALL_TYPE_PTT3_ACTIVE) &&
							(g_runtime_inst.zzwpro_3ppt_id & 0x00ffffff))
						{
							zzwpro_base_watch.id = g_runtime_inst.zzwpro_3ppt_id;
							p_strcpy(zzwpro_base_watch.name, "ZZWPRO 3PTT");
							book = &zzwpro_base_watch;
						}
						else
						{
							book = get_watch_at_user_book(1, 0xffff, 0);
						}
					}
				}

				if (book == NULL)
					obj_id = 0;
				else
					obj_id = book->id;

				if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
				{
//					remote_ctrl_call_jump = is_zzwpro_remote_ctrl_calling();
					if ((remote_ctrl_call_jump & 0x7f) && ((remote_ctrl_call_jump & 0x80) == 0))
						obj_id = 0x00FFFECA;
				}
			}
		}

		if (USER_ID_IS_NONZERO(obj_id))
		{
//			if ((phone_is_busy_now() == 0) && (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING))
			if (phone_is_busy_now() == 0)
				insert_one_calling_record(1, 1, obj_id);	// save_one_calling_record
			if (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING)
			{
				*ptr16++ = mpt_set_call_type_both(obj_id);
			}
			else
			{
				if ((remote_ctrl_call_jump & 0x7f) && ((remote_ctrl_call_jump & 0x80) == 0))
				{
					*ptr16++ = STACK_CALLING_REMOTE_CTRL_CALL | ((uint16_t)(remote_ctrl_call_jump & 0x7f) << 8);
					remote_ctrl_call_jump |= 0x80;	// disable the remote ctrl call, but do not clear the content for draw the tip later
				}
				else
				{
					*ptr16 = (is_individual_id(interphone_mode, obj_id)) ? STACK_CALLING_VOICE_INDIVIDUAL : STACK_CALLING_VOICE_GROUP;
					*ptr16++ |= set_call_type_byte2(obj_id);
				}
			}
			*ptr16++ = IS_OUTSIDE_CALL_ID(obj_id) ? n : g_runtime_inst.runtime_paras.aes_key_index/*0*/;	// high 8bit: BYTES; low 8bit: CODE; 20220609:code=aes key index
		}
	}
	else if (type == CALL_PTT_ASYNC_MESSAGE)					// send message
	{
		if (USER_ID_IS_NONZERO(obj_id))
		{
			if (msg_type == MESSAGE_TYPE_STATUS_MSG)
			{
				if (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING)
				{
					*ptr16++ = STACK_MCALLING_STATE_CALL;
				}
				else
				{
					*ptr16 = is_individual_id(interphone_mode, obj_id) ? STACK_CALLING_STATE_CALL : (0x8000 | STACK_CALLING_STATE_CALL);
					if (n & SEND_STATUS_WITH_GPS)	// ͬʱ��GPS��״̬��Ϣ
					{
						n &= ~SEND_STATUS_WITH_GPS;
						stack_para_aux1 |= 0x80;
					}
					if (n & SEND_STATUS_CODE_10BIT)	// ���ػ����л�ģʽ����Ҫ�������ߣ�����7bit�Ҳ���GPS��
					{
						*ptr16 |= n & 0x0300;		// ����״̬�����뼯Ⱥ״̬���в�ͬ���տ���ʹ�ö����ݺ���ʵ�ֵģ�10bit=CTYPE���ֽڵĵ�2bit+CODE��8bit
						n &= 0x00ff;
					}
					ptr16++;
				}
			}
			else if (msg_type == MESSAGE_TYPE_DMR_EXT_FUNC)
			{
				*ptr16++ = STACK_CALLING_EXTENDED_CALL | ((n << 8) & 0xff00);	// set the extended operation code
			}
			else if (msg_type == MESSAGE_TYPE_SERVICE_REPORT)
			{
				*ptr16++ = STACK_CALLING_SERVICE_REPORT;
			}
			else
			{
				if (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING)
				{
					*ptr16++ = STACK_MCALLING_MSG;
				}
				else
				{
					if ((msg_type == MESSAGE_TYPE_USER_MSG) || (msg_type == MESSAGE_TYPE_BIN_MSG))
						*ptr16++ = is_individual_id(interphone_mode, obj_id) ? STACK_CALLING_MSG_INDIVIDUAL : STACK_CALLING_MSG_GROUP;
					else
						*ptr16++ = is_individual_id(interphone_mode, obj_id) ? STACK_CALLING_PKG_INDIVIDUAL : STACK_CALLING_PKG_GROUP;
				}
			}
			*ptr16++ = n;
		}
	}
	else if (type == CALL_PTT_ASYNC_MESSAGE_2)
	{
		*ptr16++ = is_individual_id(interphone_mode, obj_id) ? STACK_CALLING_MSG_INDIVIDUAL : STACK_CALLING_MSG_GROUP;
		*ptr16++ = n;
	}
	else
	{
		obj_id = 0;
	}

	vlog_v("stack","ptt:type=%d,id=0x%08x", type, obj_id);
	if (USER_ID_IS_NONZERO(obj_id))
	{
		ptr32 = (uint32_t *)ptr16--;										// ptr16: point to BYTE/CODE
		*ptr32++ = obj_id & 0x00ffffff;										// ID1: be called ID
		maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, ptr32);	// ID2: self ID
		*ptr32++ &= 0x00ffffff;												// point to sdm
		n_stack_data = (uint32_t)ptr32 - (uint32_t)stack_call_buffer;

		if ((type == CALL_PTT_ASYNC_MESSAGE) || (type == CALL_PTT_ASYNC_MESSAGE_2))
		{
			is_crypto = sd_data_encrypt(*(ptr32 - 2), *(ptr32 - 1), (*((uint8_t *)(ptr32 - 3)) == STACK_CALLING_MSG_INDIVIDUAL) ? 0 : 1,
				((n >> 8) & 0x00ff) | ((n << 2) & 0x0300), (uint8_t *)(send_message_buffer + 4));
			if (is_crypto == 0)
			{
				n = (((n >> 8) + 12) << 8) | (*ptr16 & 0x00ff);			// encrypt: data length must add 12(���ܺ���ʱ��ǰ12BΪ���ܲ�����0x04  0x08  KEYI��8B���ܲ����� 0  0)
				*((uint16_t *)((uint32_t)stack_call_buffer + 2)) = n;
				*((uint16_t *)stack_call_buffer) |= STACK_CALLING_ENCRYPTED_CALL;
			}
			else if (is_crypto != 0xff)									// aes256, return is the encrypted length
			{
				n = ((uint16_t)is_crypto << 8) | (*ptr16 & 0x00ff);
				*((uint16_t *)((uint32_t)stack_call_buffer + 2)) = n;
				*((uint16_t *)stack_call_buffer) |= STACK_CALLING_ENCRYPTED_CALL;
			}

			tmp = (msg_type == MESSAGE_TYPE_LONG_MSG) ? (sizeof(PACKET_DATA_HEADER) + 4) : ((n >> 8) & 0x00ff);	// long msg header+4(GAP)+4(dat address) or message content/pstn number
			memcpy(ptr32, send_message_buffer + 4, tmp);
			n_stack_data += tmp;

			if (type == CALL_PTT_ASYNC_MESSAGE_2)
			{
				type = ((*ptr16 << 2) & 0x0300) | (*ptr16 >> 8 & 0x00ff);
				type += 4;	// (really length of data for sending): add 4(GAP for timestamp)
				*ptr16 = (*ptr16 & 0x003f) | ((type & 0x0300) >> 2) | ((type << 8) & 0xff00);

				ptr8 = (uint8_t *)((uint32_t)(send_message_buffer + 4) + sizeof(PACKET_DATA_HEADER) + 4);		// fill the unicode to here
				type = ptr8[0] | ((uint32_t)ptr8[1] << 8) | ((uint32_t)ptr8[2] << 16) | ((uint32_t)ptr8[3] << 24);
				tmp = string2unicode((uint8_t *)type, (uint16_t *)((uint32_t)ptr32 + sizeof(PACKET_DATA_HEADER) + 4));
				n_stack_data += tmp * sizeof(uint16_t);
				type = CALL_PTT_ASYNC_MESSAGE;
			}
		}
		else
		{
//			if (sd_calling_setup(obj_id, *(ptr32 - 1), get_call_type_for_crypto(interphone_mode, 1),
//				((stack_state.vs & STACK_VS_OVCM) ? 0x80 : 0x00) | ((*((uint16_t *)stack_call_buffer) & STACK_CALLING_ENCRYPTED_CALL) ? 2 : 1)) == 0)
//				return;
			if (IS_OUTSIDE_CALL_ID(obj_id))
			{
				tmp = (n >> 8) & 0x00ff;
				memcpy(ptr32, send_message_buffer + 4, tmp);// message content/pstn number
				n_stack_data += tmp;
			}
		}
		vlog_v("stack","call ptt len=%d", n_stack_data);
		stack_return_process(CALL_STACK_PROCESS((type == CALL_PTT_ASYNC_MESSAGE) ? STACK_CALL_TYPE_PTT_ACTIVE : (ptt_call_type & ~SYNC_CALL_STACK_ALARM_PRESSED)));
	}
	else
	{
		if (phone_is_busy_now() == 0)
			send_tip_sound(TIP_TYPE_CALL_FAILED, 1);
	}
	printf("call ptt end");
}

void call_ptt_async(uint8_t type, uint32_t id, uint8_t msg_type, uint16_t msg_len, uint8_t *dat)
{
	uint8_t code, *ptr8;
	int n;
	uint32_t *p = send_message_buffer;
	PACKET_DATA_HEADER *p_packet_header;

//	vlog_v("stack","[async]type=%d,id=0x%08x[msg:type=%d,len=%d/%s]", type, id, msg_type, msg_len, (char *)dat);
	if (type == CALL_PTT_ASYNC_CALLING)
	{
		p[0] = CALL_PTT_ASYNC_CALLING;
		p[1] = id;
		if (IS_OUTSIDE_CALL_ID(id))								// call external phone
		{
			n = (int)string_to_bcd((uint8_t *)(send_message_buffer + 4), dat, 20);
//			p_strncpy(external_calling_number, dat, EXTERNAL_NUMBER_MAX_DISPLAY - 1);
//			external_calling_number[EXTERNAL_NUMBER_MAX_DISPLAY - 1] = 0;
			p[2] = (((uint16_t)n << 8) & 0xff00) | 2;			// high 8bit: BYTES; low 8bit: CODE
		}
	}
	else if ((type == CALL_PTT_ASYNC_MESSAGE) || (type == CALL_PTT_ASYNC_MESSAGE_DLY))
	{
		p[0] = type;
		p[1] = id;
		p[2] = (uint32_t)msg_type << 16;
		if (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING)
		{
			if (msg_type  == MESSAGE_TYPE_USER_MSG)				// user message
			{
				n = p_strlen(dat);
				if (n)
					p[2] |= (n < MAX_ONE_MPT_FRAME_MSG) ? ((uint16_t)n << 8) : (uint16_t)(MAX_ONE_MPT_FRAME_MSG << 8);
				else
					p[1] = 0;
				memcpy((void *)(send_message_buffer + 4), dat, n);
			}
			else if (msg_type == MESSAGE_TYPE_STATUS_MSG)		// 1: status message
				p[2] |= ((uint16_t)0 << 8) | *dat;				// code == status code; bytes == 0
			else
				p[1] = 0;
		}
		else
		{
			if ((msg_type == MESSAGE_TYPE_USER_MSG) || (msg_type == MESSAGE_TYPE_BIN_MSG) || (msg_type == MESSAGE_TYPE_LONG_MSG))	// unicode/bin message
			{
				if (msg_type == MESSAGE_TYPE_USER_MSG)
				{
					n = string2unicode(dat, (uint16_t *)(send_message_buffer + 4)) * 2;
				}
				else if (msg_type == MESSAGE_TYPE_BIN_MSG)
				{
					n = get_custom_message_max_length(interphone_mode);
					n = MIN(msg_len, n);
					memcpy(send_message_buffer + 4, dat, n);
				}
				else if (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING)
				{
					p_packet_header = (PACKET_DATA_HEADER *)(send_message_buffer + 4);
					p_packet_header->protocol_version = 0x02;
					p_packet_header->service_type = 0x01;	// long message
					p_packet_header->mfid = 0x08;
					p_packet_header->service_para = 0x01;	// unicode

					n = string2unicode(dat, 0) * 2;
					p_packet_header->data_length = n + sizeof(PACKET_DATA_HEADER);
					n = p_packet_header->data_length + 4;	// fill to stack(really length of data for sending)
					p_packet_header->data_length = ntohs(p_packet_header->data_length);
					ptr8 = (uint8_t *)((uint32_t)p_packet_header + sizeof(PACKET_DATA_HEADER) + 4);
					*ptr8++ = (uint8_t)((uint32_t)dat);
					*ptr8++ = (uint8_t)((uint32_t)dat >> 8);
					*ptr8++ = (uint8_t)((uint32_t)dat >> 16);
					*ptr8++ = (uint8_t)((uint32_t)dat >> 24);
				}
				else
				{
					n = 0;
				}

				if (n)
				{
					if (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING)
					{
						if (msg_type == MESSAGE_TYPE_USER_MSG)
							code = 7;		// unicode
						else if (msg_type == MESSAGE_TYPE_BIN_MSG)
							code = 0;		// bin
						else if (is_individual_id(interphone_mode, id))
							code = 15;		// trunking with respond
						else
							code = 14;		// trunking without respond
					}
					else
					{
						if (is_individual_id(interphone_mode, id))
							code = 13;		// conventional with apply
						else
							code = 12;		// conventional without apply
					}
					p[2] |= ((n << 8) & 0xff00) | (((n & 0x0300) >> 2) | (code & 0x3f));
				}
				else
				{
					p[1] = 0;
				}
			}
			else if ((msg_type == MESSAGE_TYPE_STATUS_MSG) || (msg_type == MESSAGE_TYPE_DMR_EXT_FUNC))	// 1: status message
			{
				if ((msg_type == MESSAGE_TYPE_STATUS_MSG) && (msg_len & SEND_STATUS_CODE_10BIT))
					p[2] |= msg_len;
				else
					p[2] |= ((uint16_t)0 << 8) | *dat;													// code == status code/op code; bytes == 0

				if (msg_len & SEND_STATUS_WITH_GPS)
					p[2] |= SEND_STATUS_WITH_GPS;
			}
			else if (msg_type == MESSAGE_TYPE_SERVICE_REPORT)
			{
//				p[2] |= ((uint16_t)0 << 8) | *dat;		// ʲô�����ô�����������������ӿڷ���ͱ������
			}
			else
			{
				p[0] = CALL_PTT_ASYNC_NULL;
				p[1] = 0;
			}
		}
	}
	else
	{
		p[0] = CALL_PTT_ASYNC_NULL;
		p[1] = 0;
	}
}

//void send_data_async(uint32_t id, uint8_t msg_type, uint8_t dat_len, uint8_t *dat)	// type: 12��������ȷ�϶����ݣ�13������ȷ�϶����ݣ�14����Ⱥ��ȷ�϶����ݣ�15����Ⱥ��ȷ�϶�����
void send_data_async(uint32_t id)
{
	uint32_t n, *p = send_message_buffer;
	uint8_t dat_len, dat[STACK_DUMP_DEFAULT_DATA_LEN];

//	if (interphone_mode <= INTERPHONE_MODE_PDT_TRUNKING)
	{
		dat[0] = USER_BIN_MESSAGE_PREFIX0;
		dat[1] = USER_BIN_MESSAGE_PREFIX1;
		for (dat_len = 2; dat_len < STACK_DUMP_DEFAULT_DATA_LEN; dat_len++)
			dat[dat_len] = dat_len;
		p[0] = CALL_PTT_ASYNC_MESSAGE;
		p[1] = id;	// 80020214: 16515087(FC000F) -> 284950543; 80020900: 16515073(FC0001); 32820900: 1048577(100001)
		n = (dat_len < MAX_ONE_PDT_FRAME_MSG) ? dat_len : MAX_ONE_PDT_FRAME_MSG;
		p[2] = (n << 8) | ((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) ? 14 : 12);
		memcpy(send_message_buffer + 4, dat, n);

		set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
		set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
	}
}

uint8_t send_bin_data_async(uint32_t id, uint8_t num, uint8_t *data)
{
	uint8_t len, dat[ZZWPRO_MSG_MAX_LENGTH];

	len = get_custom_message_max_length(interphone_mode);
	if (len)
		len -= (interphone_mode <= INTERPHONE_MODE_PDT_TRUNKING) ? 2 : 8;	// starter len idt ... ender crc_l crc_h + 2B(header of custom message)
	if (num && len && (num <= len))
	{
		dat[0] = USER_BIN_MESSAGE_PREFIX0;
		dat[1] = USER_BIN_MESSAGE_PREFIX1;
		memcpy(&dat[2], data, num);
		send_message_asynchronous(id, MESSAGE_TYPE_BIN_MSG, num + 2, dat);
		return num;
	}
	else
	{
		return 0;
	}
}

const uint8_t pdu_test_data[156] = {
	0x03, 0x10, 0x20, 0xFF, 0x10, 0xC5, 0x66, 0xA6, 0x55, 0xB5, 0xFF, 0x64, 0xB5,
	0x03, 0x10, 0x20, 0xFF, 0xF4, 0x72, 0x82, 0x82, 0xA5, 0x5A, 0xDC, 0x36, 0x17,
	0x03, 0x10, 0x20, 0xFF, 0x10, 0xC5, 0x66, 0xA6, 0x55, 0xB5, 0xFF, 0x64, 0xB5,
	0x03, 0x10, 0x20, 0xFF, 0xF4, 0x72, 0x82, 0x82, 0xA5, 0x5A, 0xDC, 0x36, 0x17,
	0x03, 0x10, 0x20, 0xFF, 0x10, 0xC5, 0x66, 0xA6, 0x55, 0xB5, 0xFF, 0x64, 0xB5,
	0x03, 0x90, 0x20, 0xFF, 0xF4, 0x72, 0x82, 0x82, 0xA5, 0x5A, 0xDC, 0xD2, 0x23,
	0x03, 0x10, 0x20, 0xFF, 0x08, 0xC5, 0x66, 0xA6, 0x55, 0xB5, 0xFF, 0xC0, 0x63,
	0x03, 0x10, 0x20, 0xFF, 0xF4, 0x72, 0x82, 0x82, 0xA5, 0x5A, 0xDC, 0x36, 0x17,
	0x03, 0x10, 0x20, 0xFF, 0x08, 0xC5, 0x66, 0xA6, 0x55, 0xB5, 0xFF, 0xC0, 0x63,
	0x03, 0x10, 0x20, 0xFF, 0xF4, 0x72, 0x82, 0x82, 0xA5, 0x5A, 0xDC, 0x36, 0x17,
	0x03, 0x10, 0x20, 0xFF, 0x08, 0xC5, 0x66, 0xA6, 0x55, 0xB5, 0xFF, 0xC0, 0x63,
	0x03, 0x90, 0x20, 0xFF, 0xF4, 0x72, 0x82, 0x82, 0xA5, 0x5A, 0xDC, 0xD2, 0x23,
};
void send_pdu_data_async(uint8_t *data, uint32_t len)
{
	send_message_buffer[0] = FREE_DATA_FLAG;
	send_message_buffer[1] = (len > 300) ? 300 : len;
	if (data == 0)
	{
		send_message_buffer[0] += (len ? 2 : 1);	// len: 0-send test data with fec clear; 1-withe ori fec
		send_message_buffer[1] = 156;
		memcpy(&send_message_buffer[2], (uint8_t *)pdu_test_data, send_message_buffer[1]);
	}
	else
	{
		memcpy(&send_message_buffer[2], data, send_message_buffer[1]);
	}
}

/*PDU: ST P0 P1...P11/P17*/
#define PDU_DATA_ONE_FRAME_LENGTH	10
uint8_t pdu_data_available(uint32_t *pdu_data)
{
	static uint8_t send_pdu_wait_a_slot = 0;
	uint16_t *pdu_data_index;
	uint8_t ret = 0;

	if ((send_message_buffer[0] & 0xfffffff0) == FREE_DATA_FLAG)
	{
		if (send_pdu_wait_a_slot == 0)
		{
			send_pdu_wait_a_slot = 1;
			pdu_data_index = (uint16_t *)(&send_message_buffer[1]) + 1;
			if (*pdu_data_index < (uint16_t)send_message_buffer[1])
			{
				if (send_message_buffer[0] == FREE_DATA_FLAG)
				{
					pdu_data[0] = 0x03000000;		// 0 0 0 ST
					memcpy(&pdu_data[1], (uint8_t *)(&send_message_buffer[2]) + *pdu_data_index, PDU_DATA_ONE_FRAME_LENGTH);	// P0-P9
					((uint16_t *)pdu_data)[7] = 0;	// P10-P11/P17, CRC
					*pdu_data_index += PDU_DATA_ONE_FRAME_LENGTH;
				}
				else
				{
					pdu_data[0] = 0x00000000;		// 0 0 0 0
					memcpy((uint8_t *)pdu_data + 3, (uint8_t *)(&send_message_buffer[2]) + *pdu_data_index, PDU_DATA_ONE_FRAME_LENGTH + 3);	// ST P0-P11
					if ((send_message_buffer[0] & 0x0000000f) == 1)
						((uint16_t *)pdu_data)[7] = 0;	// P10-P11/P17, clear CRC
					*pdu_data_index += PDU_DATA_ONE_FRAME_LENGTH + 3;
				}
				ret = 1;
			}
			else
			{
				send_message_buffer[0] = 0;
				send_message_buffer[1] = 0;
				send_pdu_wait_a_slot = 0;
				send_pdu_data_done();
			}
		}
		else
		{
			send_pdu_wait_a_slot = 0;
		}
	}

	return ret;
}

void send_pdu_data_done(void)
{
	vlog_v("stack","send pdu done!");
}

void push_pdu_data_to_app(uint8_t *data)
{
	if (dev_is_base())
	{
	}
	else // dev_is_base
	{
		vlog_v("stack","%02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X",
			data[3], data[4], data[5], data[6], data[7], data[8], data[9], data[10], data[11], data[12], data[13], data[14], data[15]);
		if (interphone_mode <= INTERPHONE_MODE_PDT_CONV)
		{
			if (get_stack_work_mode() & STACK_MODE_GPS_PULLUP)
				zzw_process_air_gps3u_data(&data[6]);
		}
		else if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
		{
			parse_xv_gps3u_data(data);
		}
	} // dev_is_base
}

void parse_xv_gps3u_data(uint8_t *data)		// V+��CHS VXS MID EMB TYPE BIT IDENT1 IDENT2 GPS3U(10B)
{
	STACK_XV_SIGNALING_STRUCT *xv_signaling = (STACK_XV_SIGNALING_STRUCT *)data;
	uint32_t id_be_called = 0, id_caller = 0;
	uint8_t type;

#ifndef STACK_USE_NEW_SIGNAL_FORMAT
//	if ((xv_signaling->vxs_type == ZZWPRO_STACK_SIG_VXS_TYPE_VH) || (xv_signaling->vxs_type == ZZWPRO_STACK_SIG_VXS_TYPE_VT))
	if (xv_signaling->vxs_type == ZZWPRO_STACK_SIG_VXS_TYPE_VH)
#else
//	if ((xv_signaling->chs_type == ZZWPRO_STACK_SIG_VXS_TYPE_VH) || (xv_signaling->chs_type == ZZWPRO_STACK_SIG_VXS_TYPE_VT))
	if (xv_signaling->chs_type == ZZWPRO_STACK_SIG_VXS_TYPE_VH)
#endif
	{
		memcpy(&id_be_called, xv_signaling->id_be_called, 3);
//		if (id_be_called == my_stack_id_watch)	// enable it shoule be NOT display distance of response group's calling(display current watch only)
		{
			if (xv_signaling->gps3u_fixed == ZZWPRO_STACK_SIG_GPS4U_FIXED)	// 20200224: parse 3u(ZZWPRO_STACK_SIG_GPS3U_FIXED)->4u(ZZWPRO_STACK_SIG_GPS4U_FIXED)
				type = 4;
			else if (xv_signaling->gps3u_fixed == ZZWPRO_STACK_SIG_GPS3U_FIXED)
				type = 3;
			else
				type = 0;

			if (type)
			{
				memcpy(&id_caller, xv_signaling->id_caller, 3);
				vlog_v("stack","%X->%X:%02X %02X %02X %02X %02X %02X %02X %02X", id_caller, id_be_called,
					xv_signaling->gps3u[0], xv_signaling->gps3u[1], xv_signaling->gps3u[2], xv_signaling->gps3u[3],
					xv_signaling->gps3u[4], xv_signaling->gps3u[5], xv_signaling->gps3u[6], xv_signaling->gps3u[7]);
				if (type == 4)
					zzw_process_air_gps4u_data(xv_signaling->gps3u);
				else
					zzw_process_air_gps3u_data(xv_signaling->gps3u);

#if defined FORWARDING_LOGIC_ENABLE && !defined FORWARDING_LOGIC_NO_GPS
				if ((zzw_distance != 0xffffffff) && (xv_signaling->chs_sig == 0))	// locked & mobile signal
					zzw_forward_level = get_forwarding_decision(calling_dev_position[0], calling_dev_position[1], calling_dev_position[2], calling_dev_position[3]);
				else
					zzw_forward_level = 0;
#endif
			}
		}
	}
#if defined FORWARDING_LOGIC_ENABLE && !defined FORWARDING_LOGIC_NO_GPS
	else
	{
		zzw_forward_level = 0;
	}
#endif
}

void set_output_stack_dump_data(uint8_t flag)	// 0-disable; 1-enable & to uart; 2-enable & to can; 3-enable & to sd; else-toggle and to uart
{
	if (flag == 0)									// disable
	{
		set_printf_redirection(UART_DEBUG_INDEX);
		dump_level = 0xff;
	}
	else if (flag == 1)								// to uart
	{
		set_printf_redirection(ITM_DEBUG_INDEX);
		dump_level = 0x01;
	}
	else if (flag == 2)								// to CAN
	{
//		dbg_data_to_can_set_type(0);
//		set_printf_redirection(UART_DEBUG_INDEX);
//		dump_level = 0x01;
	}
	else if (flag == 3)								// to SD
	{
		set_printf_redirection(UART_DEBUG_INDEX);
		dump_level = 0x03;
	}
	else if ((flag == UART_LINKTO_DSP_INDEX) || (flag == UART_LINKTO_SD_INDEX))
	{
		set_printf_redirection(flag);
		dump_level = 0xff;
	}
	else											// toggle, and to uart if enable
	{
		if (dump_level != 0xff)
		{
			set_printf_redirection(UART_DEBUG_INDEX);
			dump_level = 0xff;
		}
		else
		{
			set_printf_redirection(ITM_DEBUG_INDEX);
			dump_level = 0x01;
		}
	}
}

uint8_t get_dump_level(void)
{
	return dump_level;
}

extern uint8_t dqi_prev_slot;
extern uint8_t dsp_speech_pa_ctrl, speech_out_pa_close_delay, g_talk_tip_count, g_talk_tip_count_total, g_talk_tip_sound;
uint16_t stack_data_dump(char *s, uint8_t *data, uint32_t r0, uint16_t num, uint8_t flag)
{
	uint16_t n;
	uint8_t hour = 0, minute = 0, second = 0, *ptr_end = (uint8_t *)(s + 16 + num);

	strcpy(s, flag ? "DUMP" : "POST");
	((uint32_t *)s)[1] = r0;
	((uint32_t *)s)[2] = stack_para_aux1;
	((uint32_t *)s)[3] = stack_para_aux2;

	memcpy(s + 16, data, num);
	n = (uint16_t)get_timestamp_measure_format();
	*(ptr_end - 1) = (((n % 100) / 10) << 4) | (n % 10);
	if (r0 == STACK_CALL_TYPE_SLOT_SYNC)
	{
		*(ptr_end - 2) = get_rssi_value_rel(0);
		*(ptr_end - 3) = (auto_trace_q_vocoder << 4) | (interphone_mode & 0x0F);
		*((uint32_t *)(ptr_end - 8)) = g_runtime_inst.runtime_paras.stack_xmode;
	}
	else if ((r0 == STACK_RETURN_TYPE_STATE) || (r0 == STACK_CALL_TYPE_INQUIRY))
	{
		*(ptr_end - 2) = dsp_speech_pa_ctrl;
		*(ptr_end - 3) = g_talk_tip_sound;
		*(ptr_end - 4) = g_talk_tip_count;
	}

	n = 16 + num + 4 + 4;
	if (flag)
	{
		hour = dsp_save_power_status;
		((uint32_t *)s)[n / 4 - 2] = (slot_rising_edge_int_times << 16) | (slot_falling_edge_int_times & 0xffff);
		((uint32_t *)s)[n / 4 - 1] = (((uint32_t)hour << 24) | (((uint32_t)dqi_prev_slot << 16) & 0x00ff0000) | (num_of_frame_received & 0x0000ffff));
	}
	else
	{
//		RTC_GetTD(0, 0, 0, 0, &hour, &minute, &second);
		minute = dsp_save_power_counter9s % 100;
		second = dsp_save_power_counter % 100;
		((uint32_t *)s)[n / 4 - 2] = slot_middle_int_times << 16 | (((uint16_t)minute) << 8) | second;
		((uint32_t *)s)[n / 4 - 1] = 0x2e444e45;	// END.
	}

	return n;
}

#if 0	/* convert the stack data to ascii */
void get_call_type_string(unsigned int r0, char *str)
{
	switch (r0)
	{
	case STACK_CALL_TYPE_READ_VERSION:
		strcpy(str, "��ȡ�汾��Ϣ");
		break;
	case STACK_CALL_TYPE_INITIAL:
		strcpy(str, "Э��ջ��ʼ��");
		break;
	case STACK_CALL_TYPE_SET_PARAS:
		strcpy(str, "���û�������");
		break;
	case STACK_CALL_TYPE_SET_NETWORK:
		strcpy(str, "���ÿ����ŵ�");
		break;
	case STACK_CALL_TYPE_SET_RESPONSE:
		strcpy(str, "������Ӧ��");
		break;
	case STACK_CALL_TYPE_RESET:
		strcpy(str, "����λ");
		break;
	case STACK_CALL_TYPE_SLOT_SYNC:
		strcpy(str, "ʱ϶ͬ��");
		break;
	case STACK_CALL_TYPE_AIR_DATA:
		strcpy(str, "��������");
		break;
	case STACK_CALL_TYPE_MIC_VOICE:
		strcpy(str, "MIC��Ƶ");
		break;
	case STACK_CALL_TYPE_INQUIRY:
		strcpy(str, "�����ѯ");
		break;
	case STACK_CALL_TYPE_GPS_UPDATE:
		strcpy(str, "GPS���ݸ���");
		break;
	case STACK_CALL_TYPE_POWERDOWN:
		strcpy(str, "�ϵ�����");
		break;
	case STACK_CALL_TYPE_PTT_ACTIVE:
		strcpy(str, "PTT����");
		break;
	case STACK_CALL_TYPE_PTT2_ACTIVE:
		strcpy(str, "PTT2����");
		break;
	case STACK_CALL_TYPE_CANCEL_ACTIVE:
		strcpy(str, "CANCEL����");
		break;
	case STACK_CALL_TYPE_SWITCH_CTRL:
		strcpy(str, "�л������ŵ�");
		break;
	case STACK_CALL_TYPE_SWITCH_WATCH:
		strcpy(str, "�л�ֵ����");
		break;
	case STACK_RETURN_TYPE_VERSION:
		strcpy(str, "[����]�汾��Ϣ");
		break;
	case STACK_RETURN_TYPE_STATE:
		strcpy(str, "[����]��̨״̬");
		break;
	case STACK_RETURN_TYPE_SIGNALING:
		strcpy(str, "[����]�������");
		break;
	case STACK_RETURN_TYPE_VOICE:
		strcpy(str, "[����]SPEAK��Ƶ");
		break;
	case STACK_RETURN_TYPE_CALLING_STAGE:
		strcpy(str, "[����]���н���");
		break;
	case STACK_RETURN_TYPE_UPDATE_CTRL:
		strcpy(str, "[����]���¿����ŵ�");
		break;
	case STACK_RETURN_TYPE_POWER_DOWN:
		strcpy(str, "[����]�����ϵ�");
		break;
	default:
		sprintf(str, "%08d", r0);
		break;
	}
}
uint16_t stack_data_dump_to_sd(char *s, uint16_t max_length, uint32_t r0, uint8_t flag)		// flag: 0-before call stack; else-after
{
	uint16_t i, n_dump_to_sd = 0;
	char type[STACK_DUMP_DEFAULT_DATA_LEN];

	if (flag == 0)
	{
		if (r0 == STACK_CALL_TYPE_SLOT_SYNC)
		{
			RTC_GetTD(0, 0, 0, 0, (uint8_t *)s, (uint8_t *)(s + 1), (uint8_t *)(s + 2));
			s[0] = ((s[0] / 10) << 4) | ((s[0] % 10) & 0x0f);
			s[1] = ((s[1] / 10) << 4) | ((s[1] % 10) & 0x0f);
			s[2] = ((s[2] / 10) << 4) | ((s[2] % 10) & 0x0f);
			*((uint8_t *)(s + 3)) = '\r';
			*((uint8_t *)(s + 4)) = '\n';
			n_dump_to_sd = 5;
		}

		get_call_type_string(r0, type);
		n_dump_to_sd += sprintf(s + n_dump_to_sd, "[%04d/%04d/%04d][%04d]%s\t: R0=0x%08X,R1=0x%08X,R2=0x%08X: ",
			slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times, num_of_frame_received, type, r0, stack_para_aux1, stack_para_aux2);

		for (i = 0; i < STACK_DUMP_DEFAULT_DATA_LEN; i++)
			n_dump_to_sd += sprintf(s + n_dump_to_sd, "%02X ", (unsigned char)stack_call_buffer[i]);
		n_dump_to_sd += sprintf(s + n_dump_to_sd, "");
	}
	else
	{
		if (r0 == 0)
		{
			n_dump_to_sd += sprintf(s + n_dump_to_sd, "[%04d/%04d/%04d][����]NULL\t: R0=0x%08X,R1=0x%08X,R2=0x%08X",
				slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times, r0, stack_para_aux1, stack_para_aux2);
		}
		else
		{
			get_call_type_string(r0, type);
			n_dump_to_sd += sprintf(s + n_dump_to_sd, "[%04d/%04d/%04d]%s\t: R0=0x%08X,R1=0x%08X,R2=0x%08X: ",
				slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times, type, r0, stack_para_aux1, stack_para_aux2);
			for (i = 0; i < STACK_DUMP_DEFAULT_DATA_LEN; i++)
				n_dump_to_sd += sprintf(s + n_dump_to_sd, "%02X ", (unsigned char)stack_call_buffer[i]);
		}
		n_dump_to_sd += sprintf(s + n_dump_to_sd, "");

		if (n_dump_to_sd < max_length)
			memset(s + n_dump_to_sd, max_length - n_dump_to_sd, 0);
	}

	return n_dump_to_sd;
}
#endif	/* convert the stack data to ascii */


#ifdef PDT_DIRECT_SCAN_CHANNEL
extern uint8_t pdt_direct_scan_chan_offset;
uint8_t get_stack_rssi_lower(void)
{
	return p_stack_parameters->conts[14];
}
#endif

void dump_data_bypass_stack(uint8_t *data1, uint8_t *data2, uint32_t type, uint8_t len)
{
	uint16_t n;
	uint32_t stack_dump_buf[160];

	if (get_uart_redirection(UART_REAL_REMAP_VALUE) == ITM_DEBUG_INDEX)
	{
		n = stack_data_dump((char *)stack_dump_buf, data1, type, dump_number, 1);
		n += stack_data_dump((char *)stack_dump_buf + n, data2, type + 1, dump_number, 0);
		if (dev_is_base() && (dump_to_can & DUMP_TO_CAN_STACK_DUMP))
		{
			debug_info_redirect_can((uint8_t *)stack_dump_buf, n, 1);
		}
		else // dev_is_base
		{
#ifndef USE_DMA_TO_TRANSMIT_UART3
			put_data(UART_DEBUG_INDEX, (uint8_t *)stack_dump_buf, n);
#else
			debug_txdma_start((uint8_t *)stack_dump_buf, n);
#endif
		} // dev_is_base
	}
}

const uint16_t dump_data_length[3] = {56, 264, 136};
uint8_t set_dump_data_length(uint8_t num)
{
	if (num && (interphone_mode == INTERPHONE_MODE_ZZWPRO_V))
		dump_number = STACK_DUMP_LARGE_DATA_LEN;
	else
		dump_number = STACK_DUMP_DEFAULT_DATA_LEN;

	return dump_number + 24;	// 16 + 8
}

#ifdef DEBUG_DATA_FLOW_SIMPLE_INFO
void print_simple_data_flow(char *header)
{
	static uint32_t timestamp_save = 0;
	uint32_t timestamp_1ms = get_timestamp_measure();

	vlog_v("stack","%02d/%02d[%s]%03d/%03d", slot_rising_edge_int_times % 100, slot_falling_edge_int_times % 100, header,
			timestamp_1ms % 1000, get_measure_timer_difference(timestamp_1ms, timestamp_save));
	timestamp_save = timestamp_1ms;
}
//#else
//  #define print_simple_data_flow(header);
#endif

void clear_send_message_busy_flag(void)
{
	if (phone_is_busy_now() == 0)
	{
		send_message_flag_timer = timer_destroy(send_message_flag_timer);
		send_message_flag &= ~SEND_V_MESSAGE_BUSY;
	}
}

void start_send_message_busy_flag(void)
{
	timer_destroy(send_message_flag_timer);
	send_message_flag_timer = timer_initial(0, 2000, clear_send_message_busy_flag);
	send_message_flag |= SEND_V_MESSAGE_BUSY;
}



// bit8��Ҫ����忪��4Gģ�����ڽ�����
// bit7����վģʽ���յ���������������Э��ջֻ���һ��ʱ϶�����Դ˱�־��ȥ����
// bit6������ģ��GPS���ڽ�����
// bit5���������GPS���ڽ�����
// bit4���ȴ���λ���������������λ����ȡ��
#define HEARTBEAT_STATE_MASK_CLOSE_PANNEL_WIFI			0x0400
#define HEARTBEAT_STATE_MASK_OPEN_PANNEL_WIFI			0x0200
#define HEARTBEAT_STATE_MASK_OPEN_PANNEL_4G				0x0100
#define HEARTBEAT_STATE_MASK_STACK_SET_SYNC_FAIL		0x0080
#define HEARTBEAT_STATE_MASK_RESET_MODULE_GPS			0x0040
#define HEARTBEAT_STATE_MASK_RESET_PANNEL_GPS			0x0020
#define HEARTBEAT_STATE_MASK_WAIT_FAIL_COUNTER_RESET	0x0010
#define HEARTBEAT_STATE_MASK_FAIL_COUNTER				0x000f
  #define HEARTBEAT_STATE_MASK_FAIL_COUNTER_AND_FLAG	(HEARTBEAT_STATE_MASK_WAIT_FAIL_COUNTER_RESET | HEARTBEAT_STATE_MASK_FAIL_COUNTER)
uint16_t heartbeat_fail_counter = 0;
void repower_gps_module_handle(void)
{
	heartbeat_fail_counter &= ~HEARTBEAT_STATE_MASK_RESET_MODULE_GPS;
	assessories_gps_set_power(1);
}

void request_to_reset_gps(void)
{
	if ((heartbeat_fail_counter & HEARTBEAT_STATE_MASK_RESET_MODULE_GPS) == 0)
	{
		heartbeat_fail_counter |= HEARTBEAT_STATE_MASK_RESET_MODULE_GPS;
		assessories_gps_set_power(0);
		if (timer_initial(1, 3000, repower_gps_module_handle) == 0xff)
			repower_gps_module_handle();
	}
}


// operate pannel 4g
void clear_request_open_panel_4g_handle(void)
{
	heartbeat_fail_counter &= ~HEARTBEAT_STATE_MASK_OPEN_PANNEL_4G;
	request_panel_do_something(REQUEST_PANEL_OPEN_4G_CLEAR);
}

void request_to_open_panel_4g(void)
{
	if ((heartbeat_fail_counter & HEARTBEAT_STATE_MASK_OPEN_PANNEL_4G) == 0)
	{
		heartbeat_fail_counter |= HEARTBEAT_STATE_MASK_OPEN_PANNEL_4G;
		request_panel_do_something(REQUEST_PANEL_OPEN_4G);
		if (timer_initial(1, 3200, clear_request_open_panel_4g_handle) == 0xff)
			clear_request_open_panel_4g_handle();
	}
}

// operate pannel WiFi
void clear_request_open_panel_wifi_handle(void)
{
	heartbeat_fail_counter &= ~HEARTBEAT_STATE_MASK_OPEN_PANNEL_WIFI;
	request_panel_do_something(REQUEST_PANEL_OPEN_WIFI_CLEAR);
}

void request_to_open_panel_wifi(void)
{
	if ((heartbeat_fail_counter & HEARTBEAT_STATE_MASK_OPEN_PANNEL_WIFI) == 0)
	{
		heartbeat_fail_counter |= HEARTBEAT_STATE_MASK_OPEN_PANNEL_WIFI;
		request_panel_do_something(REQUEST_PANEL_OPEN_WIFI);
		if (timer_initial(1, 3200, clear_request_open_panel_wifi_handle) == 0xff)
			clear_request_open_panel_wifi_handle();
	}
}

void clear_request_close_panel_wifi_handle(void)
{
	heartbeat_fail_counter &= ~HEARTBEAT_STATE_MASK_CLOSE_PANNEL_WIFI;
	request_panel_do_something(REQUEST_PANEL_CLOSE_WIFI_CLEAR);
}

void request_to_close_panel_wifi(void)
{
	if ((heartbeat_fail_counter & HEARTBEAT_STATE_MASK_CLOSE_PANNEL_WIFI) == 0)
	{
		heartbeat_fail_counter |= HEARTBEAT_STATE_MASK_CLOSE_PANNEL_WIFI;
		request_panel_do_something(REQUEST_PANEL_CLOSE_WIFI);
		if (timer_initial(1, 3200, clear_request_close_panel_wifi_handle) == 0xff)
			clear_request_close_panel_wifi_handle();
	}
}

// operate pannel gps
void repower_panel_gps_module_handle(void)
{
	heartbeat_fail_counter &= ~HEARTBEAT_STATE_MASK_RESET_PANNEL_GPS;
	request_panel_do_something(REQUEST_PANEL_RESET_GPS_CLEAR);
}

void request_to_reset_panel_gps(void)
{
	if ((heartbeat_fail_counter & HEARTBEAT_STATE_MASK_RESET_PANNEL_GPS) == 0)
	{
		heartbeat_fail_counter |= HEARTBEAT_STATE_MASK_RESET_PANNEL_GPS;
		request_panel_do_something(REQUEST_PANEL_RESET_GPS);
		if (timer_initial(1, 3200, repower_panel_gps_module_handle) == 0xff)
			repower_panel_gps_module_handle();
	}
}

void clear_heartbeat_fail_handle(void)
{
	heartbeat_fail_counter &= ~HEARTBEAT_STATE_MASK_FAIL_COUNTER_AND_FLAG;
}

void clear_heartbeat_fail_counter(void)
{
	if ((heartbeat_fail_counter & HEARTBEAT_STATE_MASK_WAIT_FAIL_COUNTER_RESET) == 0)
	{
		heartbeat_fail_counter |= HEARTBEAT_STATE_MASK_WAIT_FAIL_COUNTER_RESET;
		if (timer_initial(1, 5000, clear_heartbeat_fail_handle) == 0xff)
			clear_heartbeat_fail_handle();
	}
}


#define PROBE_NEIGHBOUR_WAITING_SLOT		5
#define PROBE_NEIGHBOUR_REPEAT_TIMES		3
#define PROBE_NEIGHBOUT_PROBE_ME			0x80000000
#define PROBE_NEIGHBOUT_RECEIVE_RET			0x40000000
#define PROBE_NEIGHBOUT_TIMEOUT				0x20000000
static uint16_t probe_new_chan = 0;
char probe_style_string[3][5] = {"RSSI", "CTRL", "GPS"};

void get_gps_info_to_probe(uint8_t *save)
{
	V_OBJ_REPORT_GPS_PROBE probe_gps;

	probe_gps.gps_active = is_gps_alive() ? 1 : 0;
	probe_gps.gps_pps = is_gps_pps_ok() ? 1 : 0;
	probe_gps.gps_locked_rmc = is_gps_rmc_locked() ? 1 : 0;
	probe_gps.gps_locked_gga = is_gps_gga_locked();
	probe_gps.gps_satellite = get_satellite_number();
	probe_gps.slot = zzwpro_marked_slot;

	probe_gps.gps_minute = rmc_data.utc_minute;
	probe_gps.gps_second = rmc_data.utc_second;
	probe_gps.slot_fail_counter = heartbeat_fail_counter & HEARTBEAT_STATE_MASK_FAIL_COUNTER;
	memcpy(save, &probe_gps, sizeof(uint32_t));
}

void convert_gps_probe_to_gps_report(uint8_t *gps_probe_src, uint8_t *gps_probe_obj, V_ReportProbeGPS *gps_rep, uint32_t id)
{
	V_OBJ_REPORT_GPS_PROBE gps_src, gps_obj;
	V_ReportProbeGPS gps_report;

	memcpy(&gps_src, gps_probe_src, sizeof(V_OBJ_REPORT_GPS_PROBE));
	memcpy(&gps_obj, gps_probe_obj, sizeof(V_OBJ_REPORT_GPS_PROBE));

	memset(&gps_report, 0, sizeof(V_ReportProbeGPS));
	gps_report.obj_minute = gps_obj.gps_minute;
	gps_report.obj_second = gps_obj.gps_second;
	gps_report.src_minute = gps_src.gps_minute;
	gps_report.src_second = gps_src.gps_second;

	gps_report.probe_addr = (id & 0x00ffffff) | ((uint32_t)gps_src.slot_fail_counter << 28) | ((uint32_t)gps_obj.slot_fail_counter << 24);

	gps_report.obj_active = gps_obj.gps_active;
	gps_report.obj_pps = gps_obj.gps_pps;
	gps_report.obj_locked_rmc = gps_obj.gps_locked_rmc;
	gps_report.obj_locked_gga = gps_obj.gps_locked_gga;
	gps_report.obj_satellite = gps_obj.gps_satellite;
	gps_report.obj_slot = gps_obj.slot;

	gps_report.src_active = gps_src.gps_active;
	gps_report.src_pps = gps_src.gps_pps;
	gps_report.src_locked_rmc = gps_src.gps_locked_rmc;
	gps_report.src_locked_gga = gps_src.gps_locked_gga;
	gps_report.src_satellite = gps_src.gps_satellite;
	gps_report.src_slot = gps_src.slot;

	memcpy(gps_rep, &gps_report, sizeof(V_ReportProbeGPS));
	clear_heartbeat_fail_counter();
}

#ifdef DEBUG_GPS_SYNC_PPS
extern uint32_t timestamp_gps_2pps;
#endif
void probe_return_result_to_host(uint32_t result)				// result: PROBE_NEIGHBOUT_RECEIVE_RET or PROBE_NEIGHBOUT_TIMEOUT
{
	uint32_t my_id;

	V_ReportProbeGPS gps_report;

	if (dev_is_base() && delay_to_reply_tm[0])
	{
		if (delay_to_reply_tm[0] != 0xffffffff)
		{
			if (g_neighbour_opcode == PROBE_OP_CODE_GPS_RET)
			{
				convert_gps_probe_to_gps_report(g_neighbour_info, &g_neighbour_info[4], &gps_report, g_neighbour_id & 0x00ffffff);
				LBUS_ModuleReplyTM_Probe(g_neighbour_id & 0x00ffffff, (uint8_t *)&gps_report, g_neighbour_opcode);
			}
			else
			{
				LBUS_ModuleReplyTM_Probe(g_neighbour_id & 0x00ffffff, g_neighbour_info, g_neighbour_opcode);
			}
		}
		goto probe_remote_complete;
	}
	else // dev_is_base
	{
		maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, (void *)&my_id);
		if ((manual_probe_id & 0x00ffffff) != (my_id & 0x00ffffff))
		{
			g_neighbour_id = (g_neighbour_id & 0x00ffffff) | result;	// set the receive/timeout flag for avoiding to repeat sending
			send_v_data_with_message_format_handle(SEND_V_MESSAGE_SEND_NOW | SEND_V_MESSAGE_PROBE);
		}
		else
		{
			print_probe_remote_result((result & PROBE_NEIGHBOUT_TIMEOUT) ? 0 : g_neighbour_opcode, g_neighbour_info);
probe_remote_complete:
			memset(delay_to_reply_tm, 0, 12);
			g_neighbour_id = 0;
		}
	} // dev_is_base

	if (result == PROBE_NEIGHBOUT_TIMEOUT)
		vlog_v("stack","\t[%d][%s]Prob timeout", zzwpro_marked_slot, (char *)probe_style_string[g_neighbour_opcode - PROBE_OP_CODE_RSSI_RET]);
}
uint32_t inquiry_checking(uint32_t aux1, uint32_t ori_ret)
{
	WIRELESS_PROBE_TYPEDEF probe;
	V_OBJ_REPORT_GPS_PROBE *probe_gps;
	uint8_t timeout;
	uint8_t carr_time, wifi_pwr;
	uint32_t carr_paras;

	if (g_runtime_inst.runtime_paras.project_mode_permit.no_forwarding || is_high_rp_protect())
	{
		return STACK_RETURN_TYPE_NOFORWARDING;
	}
	else if (g_neighbour_id && (aux1 == 0))
	{
		timeout = (uint8_t)(g_neighbour_id >> 24);

		if (((timeout < PROBE_NEIGHBOUR_REPEAT_TIMES * PROBE_NEIGHBOUR_WAITING_SLOT) && ((timeout % PROBE_NEIGHBOUR_WAITING_SLOT) == 0)) ||
			(timeout & (PROBE_NEIGHBOUT_PROBE_ME >> 24)))
		{
probe_send_signaling:
			memset(&probe, 0, sizeof(WIRELESS_PROBE_TYPEDEF));
			probe.ctask = ZZWPRO_BASE_AIR_CMD_GET_NEIGHBOUR_INFO;
			probe.op_code = g_neighbour_opcode;
			probe.op_para = g_neighbour_oppara;
			probe.obj_addr = (g_neighbour_id & 0x00ffffff) | ((uint32_t)USER_ID_INDIVIDUAL << 24);
			maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, (void *)&probe.src_addr);
			probe.src_addr &= ((uint32_t)USER_ID_INDIVIDUAL << 24) | 0x00ffffff;
			if (timeout & (PROBE_NEIGHBOUT_PROBE_ME >> 24))
			{
				probe.op_code += PROBE_OP_CODE_RSSI_RET;					// reply the probe
				if (probe.op_code == PROBE_OP_CODE_RSSI_RET)
				{
					probe.rssi_rcv = get_rssi_value_rel(0xfe);
					probe.dqi_rcv = dqi_prev_slot;
					probe.op_para = get_rssi_value_rel(0xff);
					probe.reserved2 = (uint8_t)get_real_battery_voltage();	// use low 1B to trans battery(one decimal place)
					vlog_v("stack","\t[%d/%d][%s]Ret prob:rssi=%d,dqi=%d,noise=%d,bat=%d.%d", zzwpro_marked_slot, timeout,
						(char *)probe_style_string[probe.op_code - PROBE_OP_CODE_RSSI_RET], -130 + probe.rssi_rcv, probe.dqi_rcv, probe.op_para, (uint8_t)probe.reserved2 / 10, (uint8_t)probe.reserved2 % 10);
				}
				else if (probe.op_code == PROBE_OP_CODE_CTRL_RET)
				{
					if (probe.op_para == PROBE_OP_RESET_MODULE)
					{
						delay_to_reset_module(1, 2000);
					}
					else if (probe.op_para == PROBE_OP_RESET_GPS)
					{
						request_to_reset_gps();
					}
					else if (probe.op_para == PROBE_OP_RESET_PANEL_GPS)
					{
						request_to_reset_panel_gps();
					}
					else if (probe.op_para == PROBE_OP_OPEN_4G)		// ֪ͨ��忪��4Gģ��
					{
						request_to_open_panel_4g();
					}
					else if ((probe.op_para == PROBE_OP_OPEN_WIFI) || (probe.op_para == PROBE_OP_CLOSE_WIFI))	// ���ز���WiFiģ��
					{
						maintain_setup_parameter(PARA_OPERATE_WRITE, WIFI_POWER_PARAS_POS, (probe.op_para == PROBE_OP_OPEN_WIFI) ? 1 : 0);
						wifi_pwr = maintain_setup_parameter(PARA_OPERATE_READ, WIFI_POWER_PARAS_POS, 0);
						wifi_power_onoff(wifi_pwr);
						if (dev_is_base())
						{
							if (probe.op_para == PROBE_OP_OPEN_WIFI)
								request_to_open_panel_wifi();
							else
								request_to_close_panel_wifi();
						}
						save_user_data_to_flash(1);
						vlog_v("stack","remote %s wifi", wifi_pwr ? "Open" : "Close");
					}
					else if ((probe.op_para >= PROBE_OP_SWITCH_TO_Q) && (probe.op_para <= PROBE_OP_SWITCH_TO_V25K))
					{
						delay_to_switch_workmode(probe.op_para - 1, 2000);
					}
					else if ((probe.op_para >= PROBE_OP_SEND_KEY_0) && (probe.op_para <= PROBE_OP_SEND_KEY_9))
					{
						// do nothing, done at receive in order to improve the response speed
					}
					else if (probe.op_para == PROBE_OP_TRACE_GPS)
					{
						g_runtime_inst_xvbase->fpga_write_gcr &= ~FPGA_REG_GCR_60MS_AUTO_SEL_MASK;
						g_runtime_inst_xvbase->fpga_write_gcr &= ~FPGA_REG_GCR_60MS_SEL_MASK;
						xvbase_force_check_and_60ms_source();
					}
					else if (probe.op_para == PROBE_OP_TRACE_BASE)
					{
						g_runtime_inst_xvbase->fpga_write_gcr &= ~FPGA_REG_GCR_60MS_AUTO_SEL_MASK;
						g_runtime_inst_xvbase->fpga_write_gcr |= FPGA_REG_GCR_60MS_SEL_MASK;
						xvbase_force_check_and_60ms_source();
					}
					else if (probe.op_para == PROBE_OP_AUTOTRACE)
					{
						g_runtime_inst_xvbase->fpga_write_gcr |= FPGA_REG_GCR_60MS_AUTO_SEL_MASK;
						xvbase_force_check_and_60ms_source();
					}
					else
					{
						if (probe.op_para & PROBE_OP_TX_CARR_MASK_8BIT)
						{
							carr_time = probe.op_para & (~PROBE_OP_TX_CARR_MASK_8BIT);
							if ((carr_time >= PROBE_OP_TX_CARR_MIN)  && (carr_time <= PROBE_OP_TX_CARR_MAX))
							{
								if (get_auto_send_carr_times() == 0)
								{
									if (timer_initial(0, 1000, auto_start_send_carr) != 0xff)
									{
										probe.reserved2 = ((uint16_t)g_neighbour_info[5] << 8) | g_neighbour_info[4];
										probe.rssi_rcv = g_neighbour_info[6];
										vlog_v("stack","Set:T=%d,F=%3.4f,P=%d", carr_time, (float)probe.reserved2 / 80, probe.rssi_rcv);
										set_auto_send_carr_times(PROBE_OP_TX_CARR_MASK_16BIT | carr_time);
										set_auto_send_carr_freq_power(probe.reserved2, probe.rssi_rcv);
										probe.op_para &= PROBE_OP_TX_CARR_MASK_8BIT;	// return low7bit=0 to notify the operation is succ
									}
								}
								else
								{
									probe.op_para &= PROBE_OP_TX_CARR_MASK_8BIT;		// already start(by previous probe frame)
								}
							}
						}
					}
					vlog_v("stack","[%d/%d][%s]Ret prob:0x%02x,op_para_ret=%02x", zzwpro_marked_slot, timeout, (char *)probe_style_string[probe.op_code - PROBE_OP_CODE_RSSI_RET], g_neighbour_opcode, probe.op_para);
				}
				else if (probe.op_code == PROBE_OP_CODE_GPS_RET)
				{
					get_gps_info_to_probe(&probe.rssi_rcv);
					probe_gps = (V_OBJ_REPORT_GPS_PROBE *)(&probe.rssi_rcv);
					vlog_v("stack","[%d/%d][%s]Ret prob:min=%d,sec=%d,act=%d,pps=%d,rmc=%d,gga=%d,sat=%d,slot=%d,fail=%d", zzwpro_marked_slot, timeout, (char *)probe_style_string[probe.op_code - PROBE_OP_CODE_RSSI_RET],
						probe_gps->gps_minute, probe_gps->gps_second, probe_gps->gps_active, probe_gps->gps_pps,
						probe_gps->gps_locked_rmc, probe_gps->gps_locked_gga, probe_gps->gps_satellite, probe_gps->slot, probe_gps->slot_fail_counter);
				}

				g_neighbour_id = 0;
			}
			else																// send the probe frame only once
			{
				if ((probe.op_code == PROBE_OP_CODE_CTRL) && (probe.op_para & PROBE_OP_TX_CARR_MASK_8BIT))
				{
					carr_paras = get_auto_send_carr_freq_power();
					probe.reserved2 = (uint16_t)carr_paras;
					probe.rssi_rcv = (uint8_t)(carr_paras >> 16);
					vlog_v("stack","Get:F=%d,P=%d,op=%02x", probe.reserved2, probe.rssi_rcv, probe.op_para);
				}

				if (probe_new_chan)
					config_pll_paras(interphone_mode, 0, probe_new_chan);

				g_neighbour_id += 0x01000000;
#if DEBUG_GPS_SYNC_PPS > 2
				vlog_v("stack","[%d/%d][%s:%d]Send prob:%08x->%08x(%d)", zzwpro_marked_slot, timeout, (char *)probe_style_string[probe.op_code], probe.op_para,
					probe.src_addr, probe.obj_addr, get_measure_timer_difference(get_timestamp_measure(), timestamp_gps_2pps));
#else
				vlog_v("stack","[%d/%d][%s:%d]Send prob:%08x->%08x", zzwpro_marked_slot, timeout, (char *)probe_style_string[probe.op_code], probe.op_para,
					probe.src_addr, probe.obj_addr);
#endif
			}
			probe.crc = (uint16_t)CRC_Check((uint8_t *)&probe, sizeof(WIRELESS_PROBE_TYPEDEF) - sizeof(uint16_t));
#ifdef EMB_3B_WHEN_NOT_SELECT_CID
			if ((ZZWPRO_BASE_IS_CID_MODE() == 0) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_V25K))
#else
			if (interphone_mode == INTERPHONE_MODE_ZZWPRO_V25K)
#endif
			{
				memcpy(stack_call_buffer, &probe, 3);
				memcpy((void *)((uint32_t)stack_call_buffer + SIGNALING_XV_STACK_EMBEDDED), (void *)((uint32_t)&probe + 3), sizeof(WIRELESS_PROBE_TYPEDEF) - 3);
			}
			else
			{
				memcpy(stack_call_buffer, &probe, sizeof(WIRELESS_PROBE_TYPEDEF));
			}
			return STACK_RETURN_TYPE_SIGNALING;
		}
		else
		{
			if (probe_new_chan)
			{
				probe_new_chan = 0;
				config_pll_paras(interphone_mode, 0, get_current_chan());
				timeout = PROBE_NEIGHBOUR_WAITING_SLOT;											// jump back and send immediately
				g_neighbour_id = (g_neighbour_id & 0x00ffffff) | ((uint32_t)PROBE_NEIGHBOUR_WAITING_SLOT << 24);
				goto probe_send_signaling;
			}

			if (timeout < PROBE_NEIGHBOUR_REPEAT_TIMES * PROBE_NEIGHBOUR_WAITING_SLOT)
			{
				timeout++;
				g_neighbour_id = (g_neighbour_id & 0x00ffffff) | ((uint32_t)timeout << 24);
			}
			else
			{
				if ((timeout & ((PROBE_NEIGHBOUT_TIMEOUT | PROBE_NEIGHBOUT_RECEIVE_RET) >> 24)) == 0)
				{
					g_neighbour_opcode += PROBE_OP_CODE_RSSI_RET;
					memset(g_neighbour_info, 0, sizeof(g_neighbour_info));
					probe_return_result_to_host(PROBE_NEIGHBOUT_TIMEOUT);
				}
			}
			return ori_ret;
		}
	}
	else
	{
//		return ((ori_ret == STACK_RETURN_TYPE_NULL) && f_is_diff_tr_mode_at_voice_channel()) ? STACK_RETURN_TYPE_SIGNALING : ori_ret;
		return ori_ret;
	}
}

void process_neighbout_probe_frame(void)
{
	WIRELESS_PROBE_TYPEDEF probe;
	V_OBJ_REPORT_GPS_PROBE probe_gps_src, probe_gps_obj;
	uint32_t my_id;
	uint16_t crc;
	uint8_t  key_info_to_bt[4];

#ifdef EMB_3B_WHEN_NOT_SELECT_CID
	if ((ZZWPRO_BASE_IS_CID_MODE() == 0) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_V25K))
#else
	if (interphone_mode == INTERPHONE_MODE_ZZWPRO_V25K)
#endif
	{
		memcpy(&probe, stack_call_buffer, 3);
		memcpy((void *)((uint32_t)&probe + 3), (void *)((uint32_t)stack_call_buffer + SIGNALING_XV_STACK_EMBEDDED), sizeof(WIRELESS_PROBE_TYPEDEF) - 3);
	}
	else
	{
		memcpy(&probe, stack_call_buffer, sizeof(WIRELESS_PROBE_TYPEDEF));
	}
	crc = (uint16_t)CRC_Check((uint8_t *)&probe, sizeof(WIRELESS_PROBE_TYPEDEF) - sizeof(uint16_t));

	if (crc == probe.crc)
	{
		maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, (void *)&my_id);
		my_id &= ((uint32_t)USER_ID_INDIVIDUAL << 24) | 0x00ffffff;
//		if ((probe.obj_addr == my_id) || ((probe.op_code == PROBE_OP_CODE_RSSI) && ((probe.obj_addr & 0x00ffffff) == 0x00ffffff)))
		if (probe.obj_addr == my_id)
		{
			if ((probe.op_code == PROBE_OP_CODE_RSSI) || (probe.op_code == PROBE_OP_CODE_CTRL) || (probe.op_code == PROBE_OP_CODE_GPS))
			{
				g_neighbour_id = (probe.src_addr & 0x00ffffff) | PROBE_NEIGHBOUT_PROBE_ME;
				g_neighbour_opcode = probe.op_code;
				g_neighbour_oppara = probe.op_para;
				vlog_v("stack","[%d][%s]Prob me:%08x->%08x", zzwpro_marked_slot, (char *)probe_style_string[probe.op_code], probe.src_addr, probe.obj_addr);
				if (probe.op_code == PROBE_OP_CODE_CTRL)
				{
					if (probe.op_para & PROBE_OP_TX_CARR_MASK_8BIT)
					{
						g_neighbour_info[4] = (uint8_t)probe.reserved2;
						g_neighbour_info[5] = (uint8_t)(probe.reserved2 >> 8);
						g_neighbour_info[6] = probe.rssi_rcv;
					}
					else if ((probe.op_para >= PROBE_OP_SEND_KEY_0) && (probe.op_para <= PROBE_OP_SEND_KEY_9))
					{
						if (uart2_use_as_dataport())
						{
							key_info_to_bt[0] = 1;
							key_info_to_bt[1] = 4;
							key_info_to_bt[2] = probe.op_para - PROBE_OP_SEND_KEY_0;
							key_info_to_bt[3] = 0;
							send_bt_frame_to_host(key_info_to_bt, 4, TSC_CTASK_KEY_OPERATE, 0, 0);
						}
					}
				}
			}
			else if (((probe.op_code == PROBE_OP_CODE_RSSI_RET) || (probe.op_code == PROBE_OP_CODE_CTRL_RET) || (probe.op_code == PROBE_OP_CODE_GPS_RET)) &&
				(probe.src_addr == (((uint32_t)USER_ID_INDIVIDUAL << 24) | (g_neighbour_id & 0x00ffffff))))
			{
				if (probe.op_code == PROBE_OP_CODE_RSSI_RET)
				{
					g_neighbour_opcode = PROBE_OP_CODE_RSSI_RET;
					g_neighbour_info[0] = probe.rssi_rcv;
					g_neighbour_info[1] = probe.dqi_rcv;
					g_neighbour_info[2] = get_rssi_value_rel(0xfe);
					g_neighbour_info[3] = dqi_prev_slot;
					g_neighbour_info[4] = (uint8_t)probe.reserved2;
					g_neighbour_info[5] = (uint8_t)get_real_battery_voltage();	// (uint8_t)(probe.reserved2 >> 8)Ϊ�Զ˷��ص�ֵ��Ŀǰδ�ã���صĵ�ص�ѹ�Ա㴫�ع�������
					g_neighbour_info[6] = probe.op_para;
					g_neighbour_info[7] = get_rssi_value_rel(0xff);	// own battery not return to g_neighbour_info(inc g_neighbour_info's capacity if wanted)
					vlog_v("stack","[%d][%s]Prob ret:%08x->%08x,rssi_rcv=%d,dqi_rcv=%d,rssi_ret=%d,dqi_ret=%d,noise_rcv=%d,bat_rcv=%d.%d,noise_ret=%d",
						zzwpro_marked_slot, (char *)probe_style_string[probe.op_code - PROBE_OP_CODE_RSSI_RET], probe.src_addr, probe.obj_addr,
						-130 + g_neighbour_info[0], g_neighbour_info[1],
						-130 + g_neighbour_info[2], g_neighbour_info[3],
						-130 + g_neighbour_info[6], /*((uint16_t)g_neighbour_info[5] << 8) | g_neighbour_info[4]*/g_neighbour_info[4] / 10, g_neighbour_info[4] % 10,
						-130 + g_neighbour_info[7]);
				}
				else if (probe.op_code == PROBE_OP_CODE_CTRL_RET)
				{
					g_neighbour_opcode = PROBE_OP_CODE_CTRL_RET;
					g_neighbour_info[0] = probe.op_para;
					vlog_v("stack","[%d][%s]Prob ret:%08x->%08x,para=0x%02x",
						zzwpro_marked_slot, (char *)probe_style_string[probe.op_code - PROBE_OP_CODE_RSSI_RET], probe.src_addr, probe.obj_addr, probe.op_para);
				}
				else
				{
					g_neighbour_opcode = PROBE_OP_CODE_GPS_RET;
					memcpy(&g_neighbour_info[4], &probe.rssi_rcv, sizeof(uint32_t));
					get_gps_info_to_probe(g_neighbour_info);
					memcpy(&probe_gps_src, g_neighbour_info, sizeof(V_OBJ_REPORT_GPS_PROBE));
					memcpy(&probe_gps_obj, &g_neighbour_info[4], sizeof(V_OBJ_REPORT_GPS_PROBE));
					vlog_v("stack","[%d][%s]Prob ret:min=%d/%d,sec=%d/%d,act=%d/%d,pps=%d/%d,rmc=%d/%d,gga=%d/%d,sat=%d/%d,slot=%d/%d,fail=%d/%d",
						zzwpro_marked_slot, (char *)probe_style_string[probe.op_code - PROBE_OP_CODE_RSSI_RET],
						probe_gps_obj.gps_minute, probe_gps_src.gps_minute,
						probe_gps_obj.gps_second, probe_gps_src.gps_second,
						probe_gps_obj.gps_active, probe_gps_src.gps_active,
						probe_gps_obj.gps_pps, probe_gps_src.gps_pps,
						probe_gps_obj.gps_locked_rmc, probe_gps_src.gps_locked_rmc,
						probe_gps_obj.gps_locked_gga, probe_gps_src.gps_locked_gga,
						probe_gps_obj.gps_satellite, probe_gps_src.gps_satellite,
						probe_gps_obj.slot, probe_gps_src.slot,
						probe_gps_obj.slot_fail_counter, probe_gps_src.slot_fail_counter);
				}

				probe_return_result_to_host(PROBE_NEIGHBOUT_RECEIVE_RET);
			}
			else
			{
				vlog_v("stack","[%d]IS Prob?op=%02x:%08x->%08x", zzwpro_marked_slot, probe.op_code, probe.src_addr, probe.obj_addr);
			}
		}
		else
		{
			if ((probe.op_code == PROBE_OP_CODE_RSSI) || (probe.op_code == PROBE_OP_CODE_CTRL) || (probe.op_code == PROBE_OP_CODE_GPS))
			{
				vlog_v("stack","[%d][%s:%d]Prob another:%08x->%08x", zzwpro_marked_slot, (char *)probe_style_string[probe.op_code], probe.op_para, probe.src_addr, probe.obj_addr);
			}
			else if (probe.op_code == PROBE_OP_CODE_RSSI_RET)
			{
				vlog_v("stack","[%d][%s:%d]Prob another ret:%08x->%08x,rssi=%d,dqi=%d", zzwpro_marked_slot, (char *)probe_style_string[probe.op_code], probe.op_para, probe.src_addr, probe.obj_addr, -130 + probe.rssi_rcv, probe.dqi_rcv);
			}
			else if ((probe.op_code == PROBE_OP_CODE_CTRL_RET) || (probe.op_code == PROBE_OP_CODE_GPS_RET))
			{
				vlog_v("stack","[%d][%s:%d]Prob another ret:%08x->%08x", zzwpro_marked_slot, (char *)probe_style_string[probe.op_code], probe.op_para, probe.src_addr, probe.obj_addr);
			}
		}
	}
}

uint8_t remap_pcm_direct(uint8_t check_voice)	// return: 0-internal; 1-external
{
	if (uart2_use_as_bt())
	{
		if (uart2_use_as_voiceport())
			return get_bt_link_state() ? 1 : 0;						// ����ֻ�������Ͼ�ӳ��Ϊ����
		else
			return 0;
	}
	else
	{
		return (uart2_use_as_voiceport() && check_voice) ? 1 : 0;	// ֻʹ��PCM�ͱ����յ�PCM���ɿ�PTT���ж��Ƿ��յ�PCM��
	}
}

const uint16_t stack_call_paras_len[23] = {
	0,		// 0  STACK_CALL_TYPE_READ_VERSION
	0,		// 1   STACK_CALL_TYPE_INITIAL
	62,		// 2  STACK_CALL_TYPE_SET_PARAS
	264,	// 3  STACK_CALL_TYPE_SET_NETWORK
	256,	// 4  STACK_CALL_TYPE_SET_RESPONSE
	0,		// 5
	0,		// 6
	0,		// 7
	0,		// 8  STACK_CALL_TYPE_RESET
	16,		// 9  STACK_CALL_TYPE_SLOT_SYNC
	36,		// 10 STACK_CALL_TYPE_AIR_DATA
	36,		// 11 STACK_CALL_TYPE_MIC_VOICE
	0,		// 12 STACK_CALL_TYPE_INQUIRY
	14,		// 13 STACK_CALL_TYPE_GPS_UPDATE NEMA(13)+battery level
	0,		// 14 STACK_CALL_TYPE_POWERDOWN
	0xffff,	// 15 STACK_CALL_TYPE_PTT_ACTIVE �趯̬����
	0xffff,	// 16 STACK_CALL_TYPE_PTT2_ACTIVE �趯̬����
	0,		// 17 STACK_CALL_TYPE_CANCEL_ACTIVE
	0,		// 18 STACK_CALL_TYPE_SWITCH_CTRL
	0,		// 19 STACK_CALL_TYPE_SWITCH_WATCH
	36,		// 20 STACK_CALL_TYPE_AUTH_ANSWER ???
	20,		// 21 STACK_CALL_TYPE_SEND_PDU_DATA
	0xffff,	// 22 STACK_CALL_TYPE_PTT3_ACTIVE �趯̬����
};

uint16_t get_stack_paras_length(uint32_t call_type)
{
	uint16_t ret = (stack_call_paras_len[call_type] > 1024) ? n_stack_data : stack_call_paras_len[call_type];

	n_stack_data = 12;
	return ret;
}

uint32_t CALL_STACK_PROCESS(uint32_t type)
{
	uint32_t n = 0, ret, bak_aux1;
	uint16_t data_num;
	uint8_t  device_is_base = dev_is_base();

#ifndef STACK_CALL_TYPE_PTT3_ACTIVE
	if ((type == STACK_CALL_TYPE_PTT_ACTIVE) || (type == STACK_CALL_TYPE_PTT2_ACTIVE))
#else
	if ((type == STACK_CALL_TYPE_PTT_ACTIVE) || (type == STACK_CALL_TYPE_PTT2_ACTIVE) || (type == STACK_CALL_TYPE_PTT3_ACTIVE))
#endif
	{
		if (stack_para_aux1 & 0x03)	// press
		{
			start_send_message_busy_flag();
			if (uart2_use_as_voiceport() && (is_rcv_bt_voice_frame() == 0))
				set_dsp_pcm_speech_inout(0);

#ifndef STACK_CALL_TYPE_PTT3_ACTIVE
			if ((interphone_mode == INTERPHONE_MODE_ZZWPRO_N) && ((stack_para_aux1 & 0x40) == 0))
				stack_para_aux1 |= 0x80;					// BS.7��1��N��0��V; BS.6��1��Q
#endif
		}
		else
		{
			clear_ptt_voice_flag();
			if (uart2_use_as_voiceport())
				set_dsp_pcm_speech_inout(1);
		}

		set_delay_to_enter_sleep_external(3);				// PTT�����ɶ���ʱ3s��������ն˺��У���peroidic_refresh()������
	}
	else if (type == STACK_CALL_TYPE_AIR_DATA)
	{
		if (((stack_para_aux2 & ZZWPRO_STACK_SIG_PDN_TYPE_CAN) == 0) && (interphone_mode != INTERPHONE_MODE_PDT_TRUNKING) &&
/*			(uart2_use_as_dataport() && (ZZWPRO_FRAME_IS_VOICE(stack_call_buffer) == 0)))*/
			uart2_use_as_dataport())
			send_bt_frame_to_host((uint8_t *)stack_call_buffer, BT_DMA_DATA_DLEN, TSC_CTASK_SEND_SIGNALING, 0, 0);
	}

	if (get_stack_signaling_monitor())					// signaling monitor open
	{
		ret = p_stack_call_interface(type, &stack_para_aux1, &stack_para_aux2, (uint32_t)stack_call_buffer, get_stack_paras_length(type & (~R0_SECOND_NETWORK_WHEN_DOUBLE)));
		if (ret == STACK_RETURN_TYPE_SIN_MONITOR)
		{
			stack_dump_buffer[0] = ret;
			stack_dump_buffer[1] = stack_para_aux1;
			stack_dump_buffer[2] = stack_para_aux2;
			memcpy((void *)(&stack_dump_buffer[3]), (void *)stack_call_buffer, 44);
			n = 56;	// 4 + 4 + 4 + 44
		}
	}
	else
	{
		bak_aux1 = stack_para_aux1;
		if (dump_level == 1)		// to uart
		{
/*			if (type == 2)		// set base
				data_num = 56;	// output: 16+56+4+4=80
			else if (type == 3)	// set ctrl table
				data_num = 264;	// output: 16+264+4+4=288
			else if (type == 4)	// set response
				data_num = 136;	// output: 16+136+4+4=160
			else
				data_num = STACK_DUMP_DEFAULT_DATA_LEN;	// output: 16+40+4+4=64
*/
			if ((type >= STACK_CALL_TYPE_SET_PARAS) && (type <= STACK_CALL_TYPE_SET_RESPONSE))
				data_num = dump_data_length[type - 2];
			else
				data_num = dump_number;	// STACK_DUMP_DEFAULT_DATA_LEN;

			n = stack_data_dump((char *)stack_dump_buffer, (uint8_t *)stack_call_buffer, type, data_num, 1);
			ret = p_stack_call_interface(type, &stack_para_aux1, &stack_para_aux2, (uint32_t)stack_call_buffer, get_stack_paras_length(type & (~R0_SECOND_NETWORK_WHEN_DOUBLE)));
			if (type == STACK_CALL_TYPE_INQUIRY)
				ret = inquiry_checking(bak_aux1, ret);	// bak_aux1: b0=0�����߻��������=1������CAN���
#ifdef PDT_DIRECT_SCAN_CHANNEL
			if ((ret == STACK_RETURN_TYPE_STATE) && (interphone_mode < INTERPHONE_MODE_PDT_CONV))
				((uint16_t *)stack_call_buffer)[1] += pdt_direct_scan_chan_offset;
#endif
			n += stack_data_dump((char *)stack_dump_buffer + n, (uint8_t *)stack_call_buffer, ret, data_num, 0);
		}
//		else if (dump_level == 3)	// to sd
//		{
//		}
		else
		{
//			printf("call %d,aux1=%08x,aux2=%08x,l=%d", type, stack_para_aux1, stack_para_aux2, get_stack_paras_length(type & (~R0_SECOND_NETWORK_WHEN_DOUBLE)));
			ret = p_stack_call_interface(type, &stack_para_aux1, &stack_para_aux2, (uint32_t)stack_call_buffer, get_stack_paras_length(type & (~R0_SECOND_NETWORK_WHEN_DOUBLE)));
/*			if (type == STACK_CALL_TYPE_SET_RESPONSE)
			{	// ��������Ϊ0ʱ��������һ��Э��ջ�����أ����500-600us������256ʱ���900-1000us��Ҳ������256B����Ϣ�����Ҫ500us��
				ret = p_stack_call_interface(type, &stack_para_aux1, &stack_para_aux2, (uint32_t)stack_call_buffer, get_stack_paras_length(type & (~R0_SECOND_NETWORK_WHEN_DOUBLE)));
				ret = p_stack_call_interface(type, &stack_para_aux1, &stack_para_aux2, (uint32_t)stack_call_buffer, 0);
				ret = p_stack_call_interface(type, &stack_para_aux1, &stack_para_aux2, (uint32_t)stack_call_buffer, 0);
				ret = p_stack_call_interface(type, &stack_para_aux1, &stack_para_aux2, (uint32_t)stack_call_buffer, 512);
				ret = p_stack_call_interface(type, &stack_para_aux1, &stack_para_aux2, (uint32_t)stack_call_buffer, 512);
				ret = p_stack_call_interface(type, &stack_para_aux1, &stack_para_aux2, (uint32_t)stack_call_buffer, 1024);
				ret = p_stack_call_interface(type, &stack_para_aux1, &stack_para_aux2, (uint32_t)stack_call_buffer, 1024);
			}*/
			if (type == STACK_CALL_TYPE_INQUIRY)
				ret = inquiry_checking(bak_aux1, ret);
#ifdef PDT_DIRECT_SCAN_CHANNEL
			if ((ret == STACK_RETURN_TYPE_STATE) && (interphone_mode < INTERPHONE_MODE_PDT_CONV))
				((uint16_t *)stack_call_buffer)[1] += pdt_direct_scan_chan_offset;
#endif
		}
	}

	if (n && (get_uart_redirection(UART_REAL_REMAP_VALUE) == ITM_DEBUG_INDEX))
	{
		if (device_is_base && (dump_to_can & DUMP_TO_CAN_STACK_DUMP))
		{
			debug_info_redirect_can((uint8_t *)stack_dump_buffer, n, 1);
		}
		else // dev_is_base
		{
#ifndef USE_DMA_TO_TRANSMIT_UART3
			put_data(UART_DEBUG_INDEX, (uint8_t *)stack_dump_buffer, n);
#else
			debug_txdma_start((uint8_t *)stack_dump_buffer, n);
#endif
		} // dev_is_base
	}

	return ret;
}
#if 0
void dump_stack_to_sd(void)
{
	if ((stack_dump_end_the_slot & 0xf000) == 0xf000)
	{
//		dsp_interrupt_config(0x01);
		if (dump_level == 3)
			test_write_one_block((uint8_t *)stack_dump_buffer);
		stack_dump_end_the_slot = 0;
//		dsp_interrupt_config(0x11);
	}
}
#endif
uint16_t get_the_loggin_channel_index(uint16_t lai, uint16_t chan)
{
	uint16_t i, ctrl_num;
	uint32_t ch_cmp, ch_read, cmp_mask;

//	cmp_mask = (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) ? 0xffff : 0x0fff;
	cmp_mask = 0x0fff;
	ch_cmp = chan & cmp_mask;

	ctrl_num = get_ctrl_table_items(0, 0xfffe, 0);
	for (i = 0; i < ctrl_num; i++)
	{
		get_ctrl_table_items(0, i, &ch_read);
		if ((ch_read & cmp_mask) == ch_cmp)
			return i;
	}
	return 0xffff;
}

uint32_t is_individual_ring_call_incoming(void)
{
	return individual_ring_calling_id;
}

uint8_t get_individual_ring_call_reason(void)
{
	return ring_call_reason;
}

uint8_t is_individual_ring_call_fail(void)
{
	return ((stack_stage.step & STACK_STEP_CALLING_FAILED) == STACK_STEP_CALLING_FAILED) ? 1 : 0;
}

uint8_t is_individual_ring_call_return(void)
{
	uint32_t cmp;

	cmp = (stack_state.id_be_called & 0x00ffffff) | (USER_ID_INDIVIDUAL << 24) | (1 << 24);
	return (cmp == (individual_ring_calling_id & (USER_ID_CMP_MASK | (1 << 24)))) ? 1 : 0;
//	return ((stack_state.vs & STACK_VS_OWN_CALLING) && ((stack_state.vs & STACK_VS_GROUP_CALL) == 0) &&
//		(stack_state.id_be_called == (individual_ring_calling_id & 0x00ffffff))) ? 1 : 0;
}

uint8_t individual_ring_call_tip_info(uint8_t *line1, uint8_t *line2)
{
	if (individual_ring_calling_id)
	{
		if (individual_ring_calling_id & ((uint32_t)USER_ID_BACKGROUND << 24))
		{
			p_strcpy(line1, get_dmr_extended_label(maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0,
				(individual_ring_calling_id & (USER_ID_EMERGENCY << 24)) ? DMR_EXTENDED_FUNC_E_ALARM : DMR_EXTENDED_FUNC_CALL_ALERT));
		}
		else
		{
			p_strcpy(line1, call_fail_reason[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][ring_call_reason]);
		}
		get_be_called_name(individual_ring_calling_id, line2);
		return 1;
	}
	else
	{
		return 0;
	}
}

void individual_ring_call_tip_clear(void)
{
	individual_ring_calling_id = 0;
}

//////////////////// �����ݿտ�дƵ ////////////////////
const uint8_t air_iop_tip[8][16] = {
	"дƵ�ɹ�",
	"дƵ������",
	"дƵ�鲻����",
	"�����ŵ�����",
	"�ŵ���������",
	"�����ŵ���ƥ��",
	"ָ���ŵ�������",
	"���ݸ�ʽδ֧��",
	};

uint8_t iop_mod_group(uint8_t attr, uint32_t id, uint8_t *name)	// attr: 1-add; 2-del; 3-replace; else-reserved
{
	uint8_t ret = 7, i;

//	vlog_v("stack","attr=%02d, id=0x%08X, name=%s", attr, id, name);
	if (attr == 1)		// add
	{
		ret = 1;		// full
		for (i = 0; i < MAX_AIR_CONF_BOOK; i++)
		{
			if ((g_runtime_inst.air_config.air_conf_book[i].id & 0x00ffffff) == 0)
			{
				p_strcpy(g_runtime_inst.air_config.air_conf_book[i].name, name);
				g_runtime_inst.air_config.air_conf_book[i].id = id;
				g_runtime_inst.air_config.air_conf_book[i].bind_ch = 0;
				ret = 0;	// succ
				switch_watch_flag = DYNAMIC_CONFIG_FLAG | ((id & ((uint32_t)USER_ID_BACKGROUND << 24)) >> 16) | (i + 1);		// for GUI auto switch
				break;
			}
		}
	}
	else if (attr == 2)	// del
	{
		ret = 2;		// not match
		for (i = 0; i < MAX_AIR_CONF_BOOK; i++)
		{
			if ((g_runtime_inst.air_config.air_conf_book[i].id & USER_ID_CMP_MASK) == (id & USER_ID_CMP_MASK))
			{
				g_runtime_inst.air_config.air_conf_book[i].id = 0;
				ret = 0;
				switch_watch_flag = DYNAMIC_CONFIG_FLAG | DYNAMIC_CONFIG_DEL_GRP | ((uint32_t)i << 8);	// for GUI auto switch
//				break;	// do no break: search all air books to del
			}
		}

		if (ret == 0)
			switch_watch_flag |= arrangement_air_user_books();
	}
	else if (attr == 0)		// reserved(modify the name of air config group)
	{
		if (name[0] == 0)	// delete all air config
			clear_dynamic_conf_data();
		else
			p_strcpy(get_air_books_config_name(), name);
		ret = 0;	// succ
	}

	return ret;
}

uint8_t iop_mod_control(uint8_t attr, uint8_t number, uint32_t *ctrl)
{
	uint16_t i;
	uint8_t ret = 7, j, num_operate = 0;

	if (attr == 1)		// add
	{
		i = get_ctrl_table_items(0, 0xfffe, 0);
		if (i + number >= CTRL_TABLE_TOTAL_ITEM)
		{
			ret = 4;
		}
		else
		{
			for (j = 0; j < number; j++)
			{
				for (i = 0; i < MAX_AIR_CONF_CTRL; i++)
				{
					if ((g_runtime_inst.air_config.air_conf_ctrl[i] & 0x00000fff) == 0)
					{
						g_runtime_inst.air_config.air_conf_ctrl[i] = ctrl[j];
						num_operate++;
						break;
					}
				}
			}
			if (num_operate >= number)
			{
				ret = 0;
			}
			else if (num_operate == 0)
				ret = 3;
			else
				ret = 4;
		}
	}
	else if (attr == 2)	// del
	{
		for (j = 0; j < number; j++)
		{
			for (i = 0; i < MAX_AIR_CONF_CTRL; i++)
			{
				if ((g_runtime_inst.air_config.air_conf_ctrl[i] & 0x00000fff) == (ctrl[j] & 0x00000fff))
				{
					g_runtime_inst.air_config.air_conf_ctrl[i] = 0;
					num_operate++;
					break;
				}
			}
		}
		if (num_operate >= number)
			ret = 0;
		else
			ret = 5;

		arrangement_air_ctrl_table();
	}

	timer_initial(1, 2000, reset_stack_with_disable_int);
	return ret;
}

#ifdef CUSTOM_SHORT_MESSAGE

uint8_t air_mod_group(uint8_t flag, uint8_t *msg_data)
{
	uint8_t *data, i, ret, index = 0, char_num = 0, name[16];
	uint32_t id;

	data = msg_data + 1;				// jump over the group type
	while ((char_num < 11) && ((data[index] != 0xa1) && (data[index + 1] != 0xd1)))	// ��, finish
	{
		name[index] = data[index];		// copy the content
		if (data[index] >= 0x7f)		// chinese, copy 2 bytes
		{
			index++;
			name[index] = data[index];
		}
		index++;						// index of content inc
		char_num++;						// number of char inc
	}
	name[index] = 0;

	while (char_num < 11)				// jump over the remainder content, and index point to the user number
	{
		index++;
		char_num++;
		if (data[index] >= 0x7f)
			index++;
	}

	i = (msg_data[0] == '2') ? USER_ID_BACKGROUND : ((msg_data[0] == '1') ? 0 : USER_ID_ATTR_WATCHING);
	id = number_to_id(INTERPHONE_MODE_PDT_TRUNKING, i, &data[index]);
	ret = iop_mod_group((flag == '0') ? 1 : 2, id, name);
	return ret;
}

uint8_t air_mod_control(uint8_t flag, uint8_t *msg_data)
{
	uint8_t i, j, number = 0, ret;
	uint32_t ctrl[MAX_AIR_CONF_CTRL];

	for (i = 0, j = 0; i < MAX_AIR_CONF_CTRL; i++)
	{
		ctrl[i] = (msg_data[j] - '0') * 100 + (msg_data[j + 1] - '0') * 10 + (msg_data[j + 2] - '0');
		j += 3;
		if (ctrl[i])
			number++;
	}

	ret = iop_mod_control((flag == '1') ? 1 : 2, number, ctrl);
	return ret;
}

uint8_t air_assign_control(uint8_t flag, uint8_t *msg_data)
{
	uint8_t ret;
	uint16_t i, ctrl_channel;
	uint32_t ctrl_rd;

	ctrl_channel = (msg_data[0] - '0') * 100 + (msg_data[1] - '0') * 10 + (msg_data[2] - '0');

	if (flag == '2')				// assign
	{
		ret = 6;
		for (i = 0; i < CTRL_TABLE_TOTAL_ITEM; i++)
		{
			get_ctrl_table_items(0, i, &ctrl_rd);
			ctrl_rd &= 0x00000fff;
			if (ctrl_rd == ctrl_channel)
			{
				ret = 0;
				break;
			}
			else if (ctrl_rd == 0)	// end already
				break;
		}
		if (ret == 0)
			setup_scanning_mode(0, SCANNING_MODE_ASSIGNED, i);
	}
	else							// short hunt
	{
		ret = 0;
		setup_scanning_mode(0, SCANNING_MODE_SHORT_HUNT, 0);
	}

	return ret;
}

uint8_t parse_message_for_air_iop(uint8_t *msg)	// return: 0xff-normal message
{
	uint8_t ret = 0xff;

	if (p_strncmp(msg, "�ʡ�", 4) == 0)
	{
		if ((msg[4] == '0') || (msg[4] == '8'))			// add/del a group
		{
			ret = air_mod_group(msg[4], &msg[5]);
		}
		else if ((msg[4] == '1') || (msg[4] == '9'))	// add/del control
		{
			ret = air_mod_control(msg[4], &msg[5]);
		}
		else if ((msg[4] == '2') || (msg[4] == 'A'))	// assign a control
		{
			ret = air_assign_control(msg[4], &msg[5]);
		}
		else if ((msg[4] == '3') || (msg[4] == 'B'))	// open/close GPS
		{
			maintain_setup_parameter(PARA_OPERATE_WRITE, GPS_POWER_PARAS_POS, (msg[4] == '3') ? 1 : 0);
			maintain_setup_parameter(PARA_OPERATE_WRITE, GPS_TYPE_PARAS_POS, (msg[5] - '0') & 0x03);
			gps_power_onoff(ret);						// set absolutely
			set_gps_bar_icon();
			update_gps_work_mode();
			ret = 0;
		}
		else if (msg[4] == '4')							// app config dynamic password
		{
			string_to_bcd(&msg[11], &msg[5], 6);
			verify_app_password(&msg[11]);
			ret = 0;
		}
	}

	return ret;
}

#endif	// CUSTOM_SHORT_MESSAGE

uint8_t verify_app_password(uint8_t *pwd)
{
//	vlog_v("stack","pwd=%02x %02x %02x, sav=%02x %02x %02x", pwd[0], pwd[1], pwd[2],
//		g_runtime_inst.runtime_paras.reserved8[0], g_runtime_inst.runtime_paras.reserved8[1], g_runtime_inst.runtime_paras.reserved8[2]);
	if (pwd == 0)	// self-lock
	{
		if ((g_runtime_inst.runtime_paras.reserved8[0] != 0x00) || (g_runtime_inst.runtime_paras.reserved8[1] != 0x00) || (g_runtime_inst.runtime_paras.reserved8[2] != 0x00))
			goto dynamic_stun_by_app;
	}
	else
	{
		if ((g_runtime_inst.runtime_paras.reserved8[0] != pwd[0]) || (g_runtime_inst.runtime_paras.reserved8[1] != pwd[1]) || (g_runtime_inst.runtime_paras.reserved8[2] != pwd[2]))
		{
			memcpy(g_runtime_inst.runtime_paras.reserved8, pwd, 3);
			save_user_data_to_flash(0);
		}

		if ((g_runtime_inst.runtime_paras.reserved8[0] == 0x00) && (g_runtime_inst.runtime_paras.reserved8[1] == 0x00) && (g_runtime_inst.runtime_paras.reserved8[2] == 0x00))
		{
			if (switch_watch_flag == DYNAMIC_APP_STUN)
			{
				switch_watch_flag = DYNAMIC_APP_REVIVED;
//				vlog_v("stack","unlock device by sys!");
			}
		}
		else
		{
dynamic_stun_by_app:
			if ((switch_watch_flag != DYNAMIC_REMOTE_KILL) && (switch_watch_flag != DYNAMIC_REMOTE_STUN))
			{
				switch_watch_flag = DYNAMIC_APP_STUN;
//				vlog_v("stack","lock device!");
			}
		}
	}

	return 1;
}

uint8_t check_app_password(uint8_t *pwd)
{
//	vlog_v("stack","pwd=%02x %02x %02x, sav=%02x %02x %02x", pwd[0], pwd[1], pwd[2],
//		g_runtime_inst.runtime_paras.reserved8[0], g_runtime_inst.runtime_paras.reserved8[1], g_runtime_inst.runtime_paras.reserved8[2]);
	if ((pwd[0] == g_runtime_inst.runtime_paras.reserved8[0]) && (pwd[1] == g_runtime_inst.runtime_paras.reserved8[1]) && (pwd[2] == g_runtime_inst.runtime_paras.reserved8[2]))
	{
//		memset(g_runtime_inst.runtime_paras.reserved8, 0x00, 3);
		if (switch_watch_flag == DYNAMIC_APP_STUN)
			switch_watch_flag = DYNAMIC_APP_REVIVED;
//		vlog_v("stack","unlock device!");
		return 1;
	}
	else
	{
//		vlog_v("stack","pwd not match!");
		return 0;
	}
}

void save_neighbour_base_info(uint32_t *chan)
{
	uint16_t i, *ptr16;

	ptr16 = g_runtime_inst.neighbour_base_info[GET_DIGITAL_ANALOG_MODE_INDEX() - 1];
	for (i = 0; i < NEIGHBOUT_BASE_NUMBER; i++)
		ptr16[i] = (uint16_t)chan[i];
}

// PDT���ּ�Ⱥͨ��ϵͳ�����淶-���нӿں��п��Ʋ㣨��׼�棩.doc
uint8_t get_call_fail_reason(uint32_t step, uint32_t ackw)
{
	uint8_t ret, ack_used = (uint8_t)(ackw & 0xff);

	if (step & STACK_STEP_CALLING_FAILED)
	{
		if (step & STACK_STEP_CALLING_CANCEL)
			ret = 3;
		else if (step & STACK_STEP_RETRANS_TIMEOUT)
			ret = 1;
		else if (step & STACK_STEP_TIMEOUT)
			ret = 2;
		else if (step & STACK_STEP_ACK_BY_BASE_BECALLED)
		{
			switch (ack_used)
			{
				case 0x14:		// ���оܾ�
					ret = 5;
					break;
				case 0x20:		// ϵͳ����֧��
					ret = 6;
					break;
				case 0x21:		// ����δ����Ȩ
					ret = 7;
					break;
				case 0x24:		// ����δ�Ǽ�
					ret = 8;
					break;
				case 0x25:		// �������߲��ɴ�
					ret = 9;
					break;
				case 0x27:		// ϵͳ���ض��ܾ�ҵ��/��ʱ
					ret = 10;
					break;
				case 0x2a:		// �ǼǾܾ�
					ret = 11;
					break;
				case 0x2b:		// �ǼǷ���
					ret = 12;
					break;
				case 0x2d:		// ����δ�Ǽ�
					ret = 13;
					break;
				case 0x2e:		// ����æ
					ret = 14;
					break;
				case 0x26:		// ������ת��
					ret = 15;
					break;
				case 0x2c:		// IP����ʧ��
					ret = 16;
					break;
				case 0x11:		// ��·ҵ�����֧��
					ret = 17;
					break;
				case 0x13:		// �豸æ�ܾ�
					ret = 18;
					break;
				default:
					ret = 4;
					break;
			}
		}
		else
			ret = 4;
	}
	else
	{
		ret = 0;
	}

	return ret;
}

void send_fail_tip_and_sound(uint8_t flag)		// 0-system event; 0xff-ptt time timeout;
{
	uint8_t tmp, lang_type, reason;

	lang_type = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0;
	if (flag == 0)
	{
		reason = get_call_fail_reason(stack_stage.step, stack_stage.ackw);
fail_tip_and_sound_handle:
		tmp = is_ptt_pressed();
		send_tip_sound(TIP_TYPE_CALL_FAILED, tmp ? 1000000 : 1);
		GL_LCD_TipDraw(0, (uint8_t *)call_fail_reason[lang_type][reason], 0, tmp ? 1000000 : 1000);
	}
	else if (flag < CALL_FAIL_REASON_TOTAL)
	{
		reason = (interphone_mode != INTERPHONE_MODE_ZZW_ADHOC) ? flag : zzw_fail_reason;
		goto fail_tip_and_sound_handle;
	}
}

void reset_fail_tip_and_sound(void)
{
	send_tip_sound(0, 1);			// force to stop the tip(if playing)
}

#if (TEST_STACK_BACKGROUND_TSCC > 1)
uint16_t test_back_tscc = 0;
void set_back_tscc_start(uint8_t slot, uint16_t chan)
{
	stack_back_tscc.vtype.flag = 0;			// invalid data when send to stack
	stack_back_tscc.vtype.slot = slot + 1;
	stack_back_tscc.cr.chan = chan;
	stack_back_tscc.cr.rssi = 0;
	stack_back_tscc.tscc_fsm = 0;
	if (current_stack_slot == dsp_edge_slot)
		stack_back_tscc.slot = (stack_back_tscc.vtype.slot == 1) ? 1 : 0;
	else
		stack_back_tscc.slot = (stack_back_tscc.vtype.slot == 1) ? 0 : 1;
}

//void set_back_tscc_stop(void)
//{
//	stack_back_tscc.tscc_fsm = 0xff;
//}
#endif

uint16_t cal_back_rssi_value_avg(uint8_t reset);
void stack_background_tscc_handle(BACK_TSCC_STRUCT *p_background_tscc)
{
//	p_background_tscc->vtype.slot &= 0x03;
	if (p_background_tscc->vtype.flag == 1)					// update the chan
	{
		cal_back_rssi_value_avg(1);
		stack_back_tscc.vtype.flag = 0;						// invalid data when send to stack
		if ((p_background_tscc->vtype.slot == 1) || (p_background_tscc->vtype.slot == 2))	// start or restart the scanning
		{
			stack_back_tscc.vtype.slot = p_background_tscc->vtype.slot;
			stack_back_tscc.cr.chan = p_background_tscc->cr.chan;
			stack_back_tscc.cr.rssi = 0;
			stack_back_tscc.tscc_fsm = 0;
			if (current_stack_slot == dsp_edge_slot)		// stack_slot is synchronous with dsp_slot(compare with dsp_edge_slot)
				stack_back_tscc.slot = (stack_back_tscc.vtype.slot == 1) ? 1 : 0;	// adjust freq at the another slot
			else
				stack_back_tscc.slot = (stack_back_tscc.vtype.slot == 1) ? 0 : 1;
#ifdef TEST_STACK_BACKGROUND_TSCC
			vlog_v("stack","[%05d %05d %05d]start:idle slot=%d(stack=%d dsp=%d),chan=%d", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
				stack_back_tscc.slot, current_stack_slot, dsp_edge_slot, p_background_tscc->cr.chan);
#endif
		}
		else
		{
#ifdef TEST_STACK_BACKGROUND_TSCC
			vlog_v("stack","[%05d %05d %05d]slot(%d) invalid,stop now(fsm=%d)", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
			p_background_tscc->vtype.slot - 1, stack_back_tscc.tscc_fsm);
#endif
			stack_back_tscc.tscc_fsm = 0xff;
		}
	}
	else if (p_background_tscc->vtype.flag == 2)			// rf power control
	{
		p_background_tscc->cr.chan &= 0x00ff;
#ifdef TEST_STACK_BACKGROUND_TSCC
		vlog_v("stack","set rf power: %d", p_background_tscc->cr.chan);
#endif
		if (p_background_tscc->cr.chan == 1)				// increase 3dB
			setup_rf_power_state(1);
		else if (p_background_tscc->cr.chan == 2)			// decrease 3dB
			setup_rf_power_state(0);
		else if (p_background_tscc->cr.chan == 3)			// max power
			setup_rf_power_state(1);
		else if (p_background_tscc->cr.chan == 4)			// min power
			setup_rf_power_state(0);
	}
//	else if (p_background_tscc->vtype.flag == 3)			// message answer(use by moto)
//	{
//	}
}

#define COMBINE_CHAN_AND_SLOTS(chan)	((uint16_t)((chan) & 0x0fff) | ((uint16_t)stack_back_tscc.slot << 14) | ((uint16_t)dsp_edge_slot << 13) | ((uint16_t)current_stack_slot << 12))
void stack_background_tscc_data_proc(void)
{
	int16_t rssi;

	if (stack_back_tscc.tscc_fsm < 0xfe)
	{
		if (stack_back_tscc.slot == dsp_edge_slot)
		{
			rssi = cal_back_rssi_value_avg(0);
			if ((rssi & 0x8000) == 0)
			{
#ifdef TEST_STACK_BACKGROUND_TSCC
				vlog_v("stack","\t[%05d %05d %05d](%d)calc avg end", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
					stack_back_tscc.tscc_fsm);
#endif
				stack_back_tscc.cr.rssi = (uint8_t)rssi;
				stack_back_tscc.tscc_fsm = 0xff;
			}
			else
			{
				rssi &= 0x00ff;
			}
#ifdef TEST_STACK_BACKGROUND_TSCC
			vlog_v("stack","\t[%05d %05d %05d](%d)get rssi=%d,stack=%d,dsp=%d", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
				stack_back_tscc.tscc_fsm, rssi, current_stack_slot, dsp_edge_slot);
#endif
			stack_call_buffer[8] = stack_back_tscc.tscc_fsm;	// for debug only
		}
		else
		{
			rssi = get_rssi_value_rel_imme(0);
#ifdef TEST_STACK_BACKGROUND_TSCC
			vlog_v("stack","\t[%05d %05d %05d](%d)get idle=%d,stack=%d,dsp=%d", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
				stack_back_tscc.tscc_fsm, rssi, current_stack_slot, dsp_edge_slot);
#endif
			stack_call_buffer[8] = 0x000000FF;	// for debug only
		}

		stack_call_buffer[8] |= ((uint16_t)rssi << 8) | (uint32_t)COMBINE_CHAN_AND_SLOTS(stack_back_tscc.cr.chan) << 16;			// for debug only
		if (stack_back_tscc.tscc_fsm > BACK_TSCC_ONCE_CHECK * 4)	// dsp slot middle int should be occur multiple
		{
#ifdef TEST_STACK_BACKGROUND_TSCC
			vlog_v("stack","[%05d %05d %05d]tscc end:fsm=%d,chan=%d,rssi=%d,stack=%ddsp=%d",
				slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
				stack_back_tscc.tscc_fsm, stack_back_tscc.cr.chan, -130 + stack_back_tscc.cr.rssi, current_stack_slot, dsp_edge_slot);
#endif
			stack_back_tscc.tscc_fsm = 0xff;
			stack_back_tscc.vtype.flag = 1;
			cal_back_rssi_value_avg(1);
		}
		memcpy(stack_call_buffer, &stack_back_tscc, sizeof(BACK_TSCC_STRUCT));
	}
	else
	{
		rssi = get_rssi_value_rel(0);
		stack_call_buffer[0] = 0;
		stack_call_buffer[8] = 0x0000FFFF | ((uint32_t)stack_back_tscc.tscc_fsm << 16) | ((uint32_t)rssi << 24);	// for debug only
	}
}

void stack_background_tscc_switch_chan(void)
{
	if (stack_back_tscc.tscc_fsm != 0xff)
	{
		stack_call_buffer[7] = 0x000000FF | ((uint32_t)stack_back_tscc.tscc_fsm << 8);					// for debug only
		if (stack_back_tscc.slot == dsp_edge_slot)
		{
			config_pll_paras(interphone_mode, 1, stack_back_tscc.cr.chan);
#ifdef TEST_STACK_BACKGROUND_TSCC
			vlog_v("stack","\t[%05d %05d %05d](%d)switch to %d,slot:stack=%d dsp=%d", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
				stack_back_tscc.tscc_fsm, stack_back_tscc.cr.chan, current_stack_slot, dsp_edge_slot);
#endif
			stack_call_buffer[7] |= (uint32_t)COMBINE_CHAN_AND_SLOTS(stack_back_tscc.cr.chan) << 16;	// for debug only
		}
		else
		{
			stack_back_tscc.tscc_fsm++;
#ifdef TEST_STACK_BACKGROUND_TSCC
			vlog_v("stack","\t[%05d %05d %05d](fsm->%d)to normal,slot:stack=%d dsp=%d", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
				stack_back_tscc.tscc_fsm, current_stack_slot, dsp_edge_slot);
#endif
			stack_call_buffer[7] |= (uint32_t)COMBINE_CHAN_AND_SLOTS(0) << 16;	// for debug only
		}
	}
	else
	{
		stack_call_buffer[7] = 0xFFFFFFFF;					// for debug only
	}
}

void custom_packet_process(uint8_t code, uint8_t bytes, uint32_t id_caller, uint32_t id_called, uint32_t *data)
{
	uint8_t str1[12], str2[12];

	id_to_number(interphone_mode, 0, id_caller, 0, str1);
	id_to_number(interphone_mode, 0, id_called, 0, str2);
	vlog_v("stack","[USR DAT:%02d.%02d,%s->%s]%08X %08X %08X %08X", code, bytes, str1, str2,
		data[0], data[1], data[2], data[3]);
}

const uint8_t decrypt_message_fail_tip[2][2][48] = {
		{"[���ܶ���]�������ܿ��Խ��н���", "[���ܶ���]���ܽ���"},
		{"[Encrypted msg] Insert crypto   card first", "[Encrypted msg] Can NOT decrypt"}
};

uint32_t get_intruction_to_conv_chan(uint8_t mode, uint32_t tx, uint32_t rx)
{
	float f_tx, f_rx;
	uint32_t chan;

	f_tx = ((tx >> 13) & 0x3ff) + (float)(tx & 0x1fff) * 0.000125f;
	f_rx = ((rx >> 13) & 0x3ff) + (float)(rx & 0x1fff) * 0.000125f;

//	chan = rx_freq_to_chan(f_rx);
	chan = rx_freq_to_chan(f_rx + freq_base - (float)g_static_ptr->stack_mpt.freq_base_admit / 80);		// 20221011: ʹ�ó���Ļ�վ�������ŵ���(��Ϊ��Ⱥ�ͳ����Ƶ���ܲ�ͬ)
	chan = (chan & 0x00000fff) | ((tx == rx) ? 0 : ZZWPRO_GROUP_IS_DIFF_FREQ_MASK);
	vlog_v("stack","\ti2c_tx=%3.4f,i2c_rx=%3.4f(conv active ch=%d)", f_tx, f_rx, chan);

	return chan;
}

uint8_t get_custom_message_max_length(uint8_t work_mode)
{
	if (interphone_mode <= INTERPHONE_MODE_PDT_TRUNKING)
		return MAX_ONE_PDT_FRAME_MSG;
	else if (interphone_mode == INTERPHONE_MODE_ZZWPRO_V)
		return ZZWPRO_MSG_MAX_LEN_V;
	else if (interphone_mode == INTERPHONE_MODE_ZZWPRO_N)
		return ZZWPRO_MSG_MAX_LEN_N;
	else if (interphone_mode == INTERPHONE_MODE_ZZWPRO_V25K)
		return ZZWPRO_MSG_MAX_LEN_V25K;
	else
		return 0;
}

// V25K:[27895/55766/27895][3319][����]��������    : R0=0x0000000A,R1=0x00000046,R2=0x81000000: 11 30 CA 00 12 04 01 00 10 CA FE FF 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 00 00 00 00 01 00 00 00 1C 04 00 46
// N: 	[3505/7011/3506][0019][����]��������    : R0=0x0000000A,R1=0x00000045,R2=0x81000000: 		31 30 CA FE 12 04 01 00 10 CA FE FF 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 00 00 00 00 00 01 00 00 54 81 00 79
// V75K:[0193/0386/0193][0006][����]��������    : R0=0x0000000A,R1=0x0000002B,R2=0x80000000: 		11 30 CA FE 12 03 01 00 10 CA FE FF 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 55 00 00 00 00 00 84
uint8_t signaling_is_synchronous(uint32_t *singnal)
{
	return (ZZWPRO_FRAME_IS_CTRL_SINGNAL(singnal) && ZZWPRO_FRAME_SINGNAL_TYPE_IS_SYNC(singnal) && ZZWPRO_FRAME_IS_BASE_FRAME(singnal)) ? 1 : 0;
//	return (ZZWPRO_FRAME_IS_CTRL_SINGNAL(singnal) && ZZWPRO_FRAME_SINGNAL_TYPE_IS_SYNC(singnal) && ZZWPRO_FRAME_IS_BASE_FRAME(singnal) &&
//		((singnal[2] & 0xffffff00) == 0xfffeca00)) ? 1 : 0;		// caller id == 0xfffeca
}

uint8_t packet_data_to_v_message(uint8_t *src, uint8_t *obj, uint8_t type, uint8_t len)
{
	uint8_t n;
	uint32_t crc;

	if (len)
	{
		obj[0] = V_WIRELESS_MANAGER_STARTER;
		if ((type == V_IDT_USER_MESSAGE) || (type == V_REPLY_IDT_DEFINE))
		{
			n = MIN(ZZWPRO_MSG_MAX_LENGTH - (V_WIRELESS_DATA_EXPENSE + 1), len);// dec starter len idt ... ender crc_l crc_h(+1 is src data not include idt)
			obj[1] = V_WIRELESS_DATA_EXPENSE + 1 + n;
			obj[2] = type;														// insert the idt
			memcpy(&obj[3], src, n);
		}
		else
		{
			n = MIN(ZZWPRO_MSG_MAX_LENGTH - V_WIRELESS_DATA_EXPENSE, len);		// dec starter len ... ender crc_l crc_h
			obj[1] = V_WIRELESS_DATA_EXPENSE + n;
			memcpy(&obj[2], src, n);
			n--;																// dec the idt(because it is include at src data)
		}
#ifdef V_WIRELESS_NOTUSE_ENDER
		crc = (uint16_t)CRC_Check(obj, obj[1] - V_WIRELESS_DATA_OFFSET);
		obj[3 + n] = (uint8_t)crc;
		obj[4 + n] = (uint8_t)(crc >> 8);
#else
		obj[3 + n] = V_WIRELESS_MANAGER_ENDER;
		crc = (uint16_t)CRC_Check(obj, obj[1] - V_WIRELESS_DATA_OFFSET);
		obj[4 + n] = (uint8_t)crc;
		obj[5 + n] = (uint8_t)(crc >> 8);
#endif
	}
	else
	{
		obj[1] = 0;
	}

	return obj[1];
}

void gen_v_attach_info(V_AttachGoup *attach)
{
	USER_BOOK *book;

	book = get_watch_at_user_book(1, 0xffff, 0);
	attach->idt = V_IDT_ATTACH_GROUP;
	attach->option = (2 << 4) | (1 << 0);
	attach->reserved = 0;
	if (g_runtime_inst.air_config.air_dynamic_reconf.id & 0x00ffffff)
		attach->id1 = g_runtime_inst.air_config.air_dynamic_reconf.id & 0x00ffffff;
	else
		attach->id1 = book->id & 0x00ffffff;

	if (g_runtime_inst.air_config.air_dynamic_reconf2.id & 0x00ffffff)
		attach->id2 = g_runtime_inst.air_config.air_dynamic_reconf2.id & 0x00ffffff;
	else
		attach->id2 = book->id & 0x00ffffff;
}

void gen_v_probe_info(V_ReportProbeInfo *probe)
{
	V_ReportProbeGPS *probe_gps;

	if (g_neighbour_opcode == PROBE_OP_CODE_GPS_RET)
	{
		probe_gps = (V_ReportProbeGPS *)probe;
		convert_gps_probe_to_gps_report(g_neighbour_info, &g_neighbour_info[4], probe_gps, g_neighbour_id & 0x00ffffff);
		probe_gps->idt = V_RIDT_PROBE_NEIGHBOUR_GPS;
	}
	else
	{
		if (g_neighbour_opcode == PROBE_OP_CODE_RSSI_RET)
			probe->idt = V_RIDT_PROBE_NEIGHBOUR;
		else
			probe->idt = V_RIDT_PROBE_NEIGHBOUR_CTRL;

		probe->probe_addr = (g_neighbour_id & 0x00ffffff) | ((uint32_t)g_neighbour_info[7] << 24);
		probe->rssi_rcv = g_neighbour_info[0];
		probe->dqi_rcv = g_neighbour_info[1];
		probe->rssi_ret = g_neighbour_info[2];
		probe->dqi_ret = g_neighbour_info[3];
		probe->reserved[0] = g_neighbour_info[4];	// �Զ˵�ѹ
		probe->reserved[1] = g_neighbour_info[5];	// ���˵�ѹ
		probe->reserved[2] = g_neighbour_info[6];	// �Զ˵���
	}
}

void manual_probe_v_neighbour(uint32_t obj_id, uint32_t src_id, uint16_t new_chan, uint8_t op_code, uint8_t op_para)
{
	g_neighbour_id = obj_id & 0x00ffffff;
	g_neighbour_opcode = op_code;
	g_neighbour_oppara = op_para;
	if (new_chan)
		probe_new_chan = new_chan;

	if (src_id != 0xffffffff)								// 0xffffffff: tm probe(reply from can)
	{
		if (src_id)											// send to manual dispatch(uart command)
		{
			manual_probe_id = src_id & 0x00ffffff;
		}
		else
		{
			delay_to_reply_tm[0] = 0xffffffff;				// do NOT send anything(uart command, but unuseful(mobile have NOT delay_to_reply_tm))
		}
	}
}

//extern uint16_t int_stat;
void gen_v_base_info(V_ReportBaseInfo *base, uint8_t on_off)		// on_off: 0-power on, 1-power off
{
	uint8_t dev_sn = get_real_esn_second_sector();

	base->idt = V_IDT_BASE_INFO;
	base->rssi = get_rssi_value_rel(0xfe);
	base->speed = rmc_data.speed;
	base->power.pwr = get_battery_power_level();
	base->power.base = ((dev_sn == VICTEL_ZZWPRO_BASE_SN) || DEV_IS_805_SERIES(dev_sn))  ? 1 : 0;
//	base->power.base = dev_is_base() ? 1 : 0;
	base->power.on_off = on_off ? 1 : 0;
	base->power.east_west = (rmc_data.gps_state & 0x02) ? 1 : 0;
	base->power.south_north = (rmc_data.gps_state & 0x04) ? 1 : 0;
	base->power.gps_valid = rmc_data.gps_state & 0x01;
	base->lat = calling_dev_position[1];
	base->lon = calling_dev_position[0];
//	vlog_v("stack","[%d]p_rssi=%d", slot_rising_edge_int_times, base->rssi);
}

void gen_v_ext_info(V_ReportExtInfo *ext)
{
	float rx, tx;

	ext->idt = V_IDT_EXT_INFO;
	ext->type = dev_have_base_feature() ? 0 : (dev_is_vehicle() ? 2 : 1);
	ext->fp = 0;
	ext->rp = 0;
	ext->mode.pdt_dmr = interphone_mode_is_dmr() ? 0 : 1;
	ext->mode.power = IS_DSP_SAVE_POWER_ENABLE() ? 1 : 0;
	ext->mode.reserved = 0;
	ext->mode.mode = interphone_mode;
	ext->noise = get_rssi_value_rel(0xff);
	ext->height = gps_height;

	if (dev_is_base())
	{
		ext->f_rx = get_xvbase_rx_freq();
		ext->f_tx = get_xvbase_tx_freq();
		ext->can_id = g_runtime_inst_xvbase->stack_xv.reserved32[0];
	}
	else // dev_is_base
	{
		chan_to_freq(interphone_mode, &tx, &rx, get_current_chan());
		ext->f_rx = rx * 80;
		ext->f_tx = tx * 80;
		ext->can_id = 0;
	} // dev_is_base

//	vlog_v("stack","[%d]p_noise=%d", slot_rising_edge_int_times, ext->noise);
}

void gen_reply_v_message(V_ReplyFrame *rep, uint32_t caller, uint32_t called, uint16_t ret_val, uint8_t type)
{
	rep->idt = V_REPLY_IDT_DEFINE;
	rep->ridt = type;
	rep->ret = ret_val;
	rep->id_src = caller;
	rep->id_rep = called;
}

void gen_v_single_call_ring(V_SingleCallRing *ring)	// 80020210:16515083; 80020202:16515075
{
	V_ReportBaseInfo base;

	ring->idt = V_IDT_SINGLE_CALL_RING;
	ring->reserved[0] = (uint8_t)(single_call_ring_id >> 24);
	ring->reserved[0] = GET_STATUS_CODE_7bit(ring->reserved[0]);
	ring->ring_time = 5;
	ring->ring_type = single_call_ring_type;

	gen_v_base_info(&base, 0);
	memcpy(&ring->reserved[1], &base.power, 9);
//	ring->reserved[1] = 0;
//	ring->reply_addr = 0;
//	ring->reserved2 = 0;
}

void send_single_call_ring_frame(uint8_t is_individual, uint32_t id, uint8_t op_type, uint8_t content)
{
	if (id && ((op_type == MISC_OP_TYPE_RING) || (op_type == MISC_OP_TYPE_STATUS)))
	{
		single_call_ring_type = op_type;
		single_call_ring_id = (id & 0x00ffffff) | ((uint32_t)FIXED_STATUS_CODE_WITH_8bit(content) << 24);
		if (is_individual)
			single_call_ring_id |= (uint32_t)USER_ID_INDIVIDUAL << 24;
		else
			single_call_ring_id &= ~((uint32_t)USER_ID_INDIVIDUAL << 24);
		send_v_data_with_message_format_handle(SEND_V_MESSAGE_SEND_NOW | SEND_V_MESSAGE_SINGLE_CALL);
	}
}

#ifdef DEBUG_ACTIVE_REPORT_DEFINE
void print_act_report_check_debug_info(uint8_t line, uint8_t flag, uint32_t dist)
{
	uint8_t debug_info[40] = "";

	if (dev_havenot_lcd())
	{
		if (flag == 0)
			vlog_v("stack","[%d]Chk:id=%08x,syn=%02x,conf=%02x", prev_report_time, g_runtime_inst.act_report_conf.id, device_is_sync, *((uint16_t *)&g_runtime_inst.act_report_conf.config));
		else if (flag == 1)
			vlog_v("stack","[%d]Pwr:%d->%d", prev_report_time, g_runtime_inst.act_report_conf.reserved, get_battery_power_level());
		else if (flag == 2)
			vlog_v("stack","[%d]Dist:conf=%d,time match", prev_report_time, g_runtime_inst.act_report_conf.config.dist);
		else if (flag == 3)
			vlog_v("stack","[%d]Dist:conf=%d,time=%d,dist=%d", prev_report_time, g_runtime_inst.act_report_conf.config.dist, prev_report_time, dist);
		else if (flag == 10)
			vlog_v("stack","Send %s msg", dist ? "offline" : "online");
		else if (flag == 11)
			vlog_v("stack","Send %s msg", dist ? "locked" : "unlock");
		else if (flag == 12)
			vlog_v("stack","Move=%d", dist);
		else if (flag == 13)
			vlog_v("stack","Attach=%08x", dist);

		if (flag & 0x80)
			vlog_v("stack","==== Misc=%02x ====", flag);
	}
	else
	{
		if (flag == 0)
			sprintf((char *)debug_info, "%d Chk:%6X %02x %02x", prev_report_time, g_runtime_inst.act_report_conf.id & 0x00ffffff, device_is_sync, *((uint16_t *)&g_runtime_inst.act_report_conf.config));
		else if (flag == 1)
			sprintf((char *)debug_info, "%d Pwr:%d->%d", prev_report_time, g_runtime_inst.act_report_conf.reserved, get_battery_power_level());
		else if (flag == 2)
			sprintf((char *)debug_info, "%d Dst:%d,T=match", prev_report_time, g_runtime_inst.act_report_conf.config.dist);
		else if (flag == 3)
			sprintf((char *)debug_info, "%d Dst:%d,D=%d", prev_report_time, g_runtime_inst.act_report_conf.config.dist, dist);
		else if (flag == 10)
			sprintf((char *)debug_info, dist ? "Online" : "Offline");
		else if (flag == 11)
			sprintf((char *)debug_info, dist ? "Locked" : "Unlock");
		else if (flag == 12)
			sprintf((char *)debug_info, "Move=%d", dist);
		else if (flag == 13)
			sprintf((char *)debug_info, "Att=%08x", dist);

		if (debug_info[0])
			GL_LCDPrintString(0, line ? (LCD_ACTIVE_HEIGHT + 24) : (LCD_ACTIVE_HEIGHT + 1), debug_info, front_ground_color, back_ground_color, 2, FONT_NONTRANSPARENT);

		if (flag & 0x80)
		{
			sprintf((char *)debug_info, "Misc=%02x", flag);
			GL_LCDPrintString(0, LCD_PIXEL_HEIGHT - 10, debug_info, front_ground_color, back_ground_color, 2, FONT_NONTRANSPARENT);
		}
	}
}
#else
  #define print_act_report_check_debug_info(a, b, c)
#endif

uint32_t send_v_data_is_busy_now(void)					// 0-free
{
	return (device_is_sync && ((send_message_flag & SEND_V_MESSAGE_BUSY) == 0)) ? 0 : (0x80000000 | ((uint32_t)device_is_sync << 24) | send_message_flag);
}

void send_v_data_with_message_format(void)
{
	uint32_t my_id, send_id, buff[(ZZWPRO_MSG_MAX_LENGTH + 3) / sizeof(uint32_t)], id_to_fill_ack = 0xffffffff;
	uint8_t offset = 0, proc_flag = 0, on_off = 0;

#ifdef DEBUG_ACTIVE_REPORT_DEFINE
	vlog_v("stack","[%d]V[%04X]:%06x/%s", slot_rising_edge_int_times, send_message_flag, g_runtime_inst.act_report_conf.id, device_is_sync ? "Syn" : "NotSyn");
#endif
	poll_v_message_timer = 0xff;
	if (device_is_sync && (send_message_flag & ~(SEND_V_MESSAGE_BUSY | SEND_V_MESSAGE_SEND_NOW)) && ((send_message_flag & SEND_V_MESSAGE_BUSY) == 0))
	{
		send_id = g_runtime_inst.act_report_conf.id;
		if (send_message_flag & SEND_V_MESSAGE_REPLY_RING)		// ����̨��������Ӧ֡�ǻظ�������ID�������������ϱ�������������ʱ��ID
		{
			send_message_flag &= ~SEND_V_MESSAGE_REPLY_RING;
			on_off = V_IDT_SINGLE_CALL_RING;
			if ((dispatch_reply_ret_id >> 28) == MISC_OP_TYPE_MOD_GRP_RESP)
				id_to_fill_ack = g_runtime_inst_xvbase->response_group[XVBASE_MAX_RESPONSE_GROUP - 1];
			goto v_send_reply_proc;
		}
		else if (send_message_flag & SEND_V_MESSAGE_REPLY_MSG)	// �����ݵ��������̨��������Ӧ֡�ǻظ�������ID�������������ϱ�������������ʱ��ID
		{
			send_message_flag &= ~SEND_V_MESSAGE_REPLY_MSG;
			on_off = V_RIDT_USER_MESSAGE;
v_send_reply_proc:
			maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, &my_id);
			send_id = (dispatch_reply_ret_id & 0x00ffffff) | ((uint32_t)USER_ID_INDIVIDUAL << 24);
			if (id_to_fill_ack == 0xffffffff)
				id_to_fill_ack = send_id;
			gen_reply_v_message((V_ReplyFrame *)((uint32_t)buff + offset), id_to_fill_ack, my_id, (uint8_t)(dispatch_reply_ret_id >> 24), on_off);
			offset += sizeof(V_ReplyFrame);
		}
		else if (send_message_flag & SEND_V_MESSAGE_PROBE)
		{
			send_message_flag &= ~SEND_V_MESSAGE_PROBE;
			send_id = (manual_probe_id & 0x00ffffff) | ((uint32_t)USER_ID_INDIVIDUAL << 24);
			vlog_v("stack","\t[%d]Rep prob:%08x(%02x)", zzwpro_marked_slot, g_neighbour_id, g_neighbour_opcode);
			gen_v_probe_info((V_ReportProbeInfo *)((uint32_t)buff + offset));
			g_neighbour_id = 0;
			offset += sizeof(V_ReportProbeInfo);
		}
		else if (send_message_flag & SEND_V_MESSAGE_REPLY_ACT)	// individual config active report
		{
			send_message_flag &= ~SEND_V_MESSAGE_REPLY_ACT;
			on_off = V_RIDT_ACT_REPORT_CONFIG;
			goto v_send_reply_proc;
		}
		else if (send_message_flag & SEND_V_MESSAGE_REPLY_DYN)	// dynamic config
		{
			send_message_flag &= ~SEND_V_MESSAGE_REPLY_DYN;
			on_off = V_RIDT_AIR_CONFIG;
			goto v_send_reply_proc;
		}
		else if (send_message_flag & SEND_V_MESSAGE_SINGLE_CALL)
		{
			send_message_flag &= ~SEND_V_MESSAGE_SINGLE_CALL;
			send_id = single_call_ring_id & USER_ID_CMP_MASK;
			gen_v_single_call_ring((V_SingleCallRing *)((uint32_t)buff + offset));
			offset += sizeof(V_SingleCallRing);
		}
		else if (send_message_flag & SEND_V_MESSAGE_MSG)
		{
			send_message_flag &= ~SEND_V_MESSAGE_MSG;
			if (send_message_buffer[0] == CALL_PTT_ASYNC_MESSAGE_DLY)
			{
				send_message_buffer[0] = CALL_PTT_ASYNC_MESSAGE;
				set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
				set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
			}
			else
			{
				send_message_buffer[0] = CALL_PTT_ASYNC_NULL;
			}
		}
		else if (send_message_flag & SEND_V_MESSAGE_DISPATCH_POLL)
		{
			send_message_flag &= ~SEND_V_MESSAGE_DISPATCH_POLL;
			send_id = (dispatch_reply_ret_id & 0x00ffffff) | ((uint32_t)USER_ID_INDIVIDUAL << 24);
			for (on_off = 0; on_off < 3; on_off++)
			{
				if (v_dispatch_poll.idt_poll[on_off])
				{
					if (v_dispatch_poll.idt_poll[on_off] == V_IDT_BASE_INFO)
					{
						if ((offset + sizeof(V_ReportBaseInfo)) <= ZZWPRO_MSG_PAYLOAD)
						{
							gen_v_base_info((V_ReportBaseInfo *)((uint32_t)buff + offset), 0);
							offset += sizeof(V_ReportBaseInfo);										// 12B
						}
						else
						{
							on_off = 0xff;
						}
					}
					else if (v_dispatch_poll.idt_poll[on_off] == V_IDT_EXT_INFO)
					{
						if ((offset + sizeof(V_ReportExtInfo)) <= ZZWPRO_MSG_PAYLOAD)
						{
							gen_v_ext_info((V_ReportExtInfo *)((uint32_t)buff + offset));
							offset += sizeof(V_ReportExtInfo);										// 12B
						}
						else
						{
							on_off = 0xff;
						}
					}
					else if (v_dispatch_poll.idt_poll[on_off] == V_IDT_ATTACH_GROUP)
					{
						if ((offset + sizeof(V_AttachGoup)) <= ZZWPRO_MSG_PAYLOAD)
						{
							gen_v_attach_info((V_AttachGoup *)((uint32_t)buff + offset));
							offset += sizeof(V_AttachGoup);										// 12B
						}
						else
						{
							on_off = 0xff;
						}
					}
				}
			}
		}
		else
		{
			if (send_message_flag & SEND_V_MESSAGE_ONLINE)
			{
//				send_message_flag &= ~SEND_V_MESSAGE_ONLINE;
				send_message_flag--;
				on_off = 0;
//				vlog_v("stack","[%d]online", slot_rising_edge_int_times);
v_send_online_proc:
				if ((proc_flag & SEND_V_MESSAGE_BASE) == 0)
				{
					proc_flag |= SEND_V_MESSAGE_BASE;
					gen_v_base_info((V_ReportBaseInfo *)((uint32_t)buff + offset), on_off);
					offset += sizeof(V_ReportBaseInfo);										// 12B
				}
				if ((proc_flag & SEND_V_MESSAGE_EXT) == 0)
				{
					proc_flag |= SEND_V_MESSAGE_EXT;
					gen_v_ext_info((V_ReportExtInfo *)((uint32_t)buff + offset));
					offset += sizeof(V_ReportExtInfo);										// 20B
				}
			}

			if (send_message_flag & SEND_V_MESSAGE_OFFLINE)
			{
				send_message_flag &= ~SEND_V_MESSAGE_OFFLINE;
				on_off = 1;
//				vlog_v("stack","[%d]offline", slot_rising_edge_int_times);
				goto v_send_online_proc;
			}

			if (send_message_flag & SEND_V_MESSAGE_ATTACH)
			{
				if (offset + sizeof(V_AttachGoup) <= ZZWPRO_MSG_PAYLOAD)
				{
					send_message_flag &= ~SEND_V_MESSAGE_ATTACH;
//					vlog_v("stack","[%d]attach", slot_rising_edge_int_times);
					gen_v_attach_info((V_AttachGoup *)((uint32_t)buff + offset));
					offset += sizeof(V_AttachGoup);											// 12B
				}
			}

			if (send_message_flag & SEND_V_MESSAGE_BASE)
			{
				if ((proc_flag & SEND_V_MESSAGE_BASE) == 0)
				{
					if (offset + sizeof(V_ReportBaseInfo) <= ZZWPRO_MSG_PAYLOAD)
					{
						send_message_flag &= ~SEND_V_MESSAGE_BASE;
						proc_flag |= SEND_V_MESSAGE_BASE;
						gen_v_base_info((V_ReportBaseInfo *)((uint32_t)buff + offset), 0);
						offset += sizeof(V_ReportBaseInfo);
					}
				}
				else
				{
					send_message_flag &= ~SEND_V_MESSAGE_BASE;
				}
			}

			if (send_message_flag & SEND_V_MESSAGE_EXT)
			{
				if ((proc_flag & SEND_V_MESSAGE_EXT) == 0)
				{
					if (offset + sizeof(V_ReportExtInfo) <= ZZWPRO_MSG_PAYLOAD)
					{
						send_message_flag &= ~SEND_V_MESSAGE_EXT;
						proc_flag |= SEND_V_MESSAGE_EXT;
						gen_v_ext_info((V_ReportExtInfo *)((uint32_t)buff + offset));
						offset += sizeof(V_ReportExtInfo);
					}
				}
				else
				{
					send_message_flag &= ~SEND_V_MESSAGE_EXT;
				}
			}

		}

		if (offset)
		{
#ifdef DEBUG_ACTIVE_REPORT_DEFINE
			vlog_v("stack","[%d]V[%04X]:%06x/n=%d", slot_rising_edge_int_times, send_message_flag, g_runtime_inst.act_report_conf.id, offset);
#endif
			send_message_asynchronous(send_id, V_IDT_BASE_INFO, offset, (uint8_t *)buff);	// msg_type: bit7 must be 0, and must not MESSAGE_TYPE_USER_MSG&MESSAGE_TYPE_BIN_MSG
			print_act_report_check_debug_info(2, 0x80 | (uint8_t)proc_flag, on_off);
		}
	}

	if (send_message_flag & ~(SEND_V_MESSAGE_BUSY | SEND_V_MESSAGE_SEND_NOW))		// ��֡δ��װ��ȫ����Ҫ�ظ����ϱ������ݣ���ȷ����֡����һ���ں��ʵ�ʱ���������δ�ܴ����Ļظ����ϱ�
	{
		if (poll_v_message_timer == 0xff)
			poll_v_message_timer = timer_initial(1, 720, send_v_data_with_message_format);
	}
}

void send_v_data_with_message_format_handle(uint32_t flag)
{
	if ((flag & (SEND_V_MESSAGE_MSG | SEND_V_MESSAGE_REPLY_MSG | SEND_V_MESSAGE_REPLY_RING | SEND_V_MESSAGE_PROBE | SEND_V_MESSAGE_DISPATCH_POLL | SEND_V_MESSAGE_SINGLE_CALL)) || g_runtime_inst.act_report_conf.id)
		send_message_flag |= flag & ~(SEND_V_MESSAGE_SEND_NOW | SEND_V_MESSAGE_BUSY);

	if ((send_message_flag & ~(SEND_V_MESSAGE_SEND_NOW | SEND_V_MESSAGE_BUSY)) && (flag & SEND_V_MESSAGE_SEND_NOW))
	{
		if (poll_v_message_timer == 0xff)
			send_v_data_with_message_format();
	}
}

void send_v_dispatch_poll_handle(void)
{
#ifdef DEBUG_DSP_SAVE_POWER
	vlog_v("stack","[%d]send=%06x", slot_rising_edge_int_times, send_message_flag);
#endif
	send_v_data_with_message_format_handle(SEND_V_MESSAGE_SEND_NOW | SEND_V_MESSAGE_DISPATCH_POLL);
	v_dispatch_poll.poll_counter = 0xff;
}

const uint8_t ring_type[MISC_OP_TOTAL_TYPE][8] = {"Ring", "Stun", "Kill", "Revive", "Status", "G_resp"};

// STARTER(0x88) LEN IDT1 DATA1-DATAn ENDER CRC16_l CRC16_h
uint8_t parse_v_message(uint8_t *msg, uint16_t *msg_num, uint32_t caller, uint32_t called)
{
	uint8_t ret = 0xfe, offset = V_WIRELESS_DATA_OFFSET, end_pos;
	uint16_t crc;
	uint32_t v_msg[(ZZWPRO_MSG_MAX_LENGTH + 3) / sizeof(uint32_t)];
	V_ActReportConfig *p_act_rep = (V_ActReportConfig *)v_msg;
	uint32_t set_id;
	V_AttachGoup *p_attach = (V_AttachGoup *)v_msg;
	V_ReplyFrame *rep = (V_ReplyFrame *)(&v_msg[6]);
	V_ReportProbeInfo *p_probe = (V_ReportProbeInfo *)v_msg;
	V_SingleCallRing *p_ring = (V_SingleCallRing *)v_msg;

#ifdef V_WIRELESS_NOTUSE_ENDER
	end_pos = msg[1] - 2;	// point to the first byte of crc
	crc = ((uint16_t)msg[end_pos + 1] << 8) | msg[end_pos];
	if ((msg[0] == V_WIRELESS_MANAGER_STARTER) && (msg[1] > 5))
#else
	end_pos = msg[1] - 3;	// ender's position
	crc = ((uint16_t)msg[end_pos + 2] << 8) | msg[end_pos + 1];
	if ((msg[0] == V_WIRELESS_MANAGER_STARTER) && (msg[end_pos] == V_WIRELESS_MANAGER_ENDER) && (msg[1] > 6))
#endif
//	if (msg[0] == V_WIRELESS_MANAGER_STARTER)
	{
		if (crc == (uint16_t)CRC_Check(msg, msg[1] - V_WIRELESS_DATA_OFFSET))
//		if (1)
		{
			dispatch_reply_ret_id = caller & 0x00ffffff;
			while (1)
			{
				switch (msg[offset])
				{
					case V_IDT_USER_MESSAGE:
						msg[end_pos] = 0;			// finish the message content
						end_pos = end_pos + 1 - 3;	// len of string: add the EOF(1B), and dec the header(3B)
						memcpy(v_msg, &msg[offset + 1], end_pos);
						memcpy(msg, v_msg, end_pos);
						*msg_num = end_pos;
						if (called & ((uint32_t)USER_ID_INDIVIDUAL << 24))	// individual message
						{
							dispatch_reply_ret_id |= (uint32_t)0 << 24;				// ret val
							send_v_data_with_message_format_handle(SEND_V_MESSAGE_REPLY_MSG);
						}
						offset = 0xff;
						ret = 0;
						break;

					case V_IDT_DYNAMIC_CONFIG:
						if (dev_is_base())
						{
						}
						else // dev_is_base
						{
							memcpy(p_attach, &msg[offset], sizeof(V_AttachGoup));
							if ((p_attach->option & 0x0f) == 1)					// id1 is ptt id
								set_id = p_attach->id1 & 0x00ffffff;
							else if (((p_attach->option >> 4) & 0x0f) == 1)	// id2 is ptt id
								set_id = p_attach->id2 & 0x00ffffff;
							else
								set_id = 0xffffffff;							// do not set ptt id
							if ((set_id != 0xffffffff) && ((g_runtime_inst.air_config.air_dynamic_reconf.id & 0x00ffffff) != set_id))
							{
								g_runtime_inst.air_config.air_dynamic_reconf.id = 0;		// for get the current watch at user_book
								if (set_id)
								{
									g_runtime_inst.air_config.air_dynamic_reconf.bind_ch = get_watch_at_user_book(1, 0xffff, 0)->bind_ch;
									g_runtime_inst.air_config.air_dynamic_reconf.id = (USER_ID_ATTR_IS_ZZW | USER_ID_EXT_ATTR_IS_ZZWPRO) | set_id;
									switch_watch_flag = DYNAMIC_CONFIG_RECONF;
								}
								else
								{
									set_switch_group_without_confirm(0, 1);
									switch_watch_flag = DYNAMIC_CONFIG_CANCEL;
								}
							}

							if ((p_attach->option & 0x0f) == 2)					// id1 is 2ptt id
								g_runtime_inst.air_config.air_dynamic_reconf2.id = (p_attach->id1 & USER_ID_CMP_MASK) | ((uint32_t)(USER_ID_ATTR_IS_ZZW | USER_ID_EXT_ATTR_IS_ZZWPRO) << 24);
							else if (((p_attach->option >> 4) & 0x0f) == 2)	// id2 is 2ptt id
								g_runtime_inst.air_config.air_dynamic_reconf2.id = (p_attach->id2 & USER_ID_CMP_MASK) | ((uint32_t)(USER_ID_ATTR_IS_ZZW | USER_ID_EXT_ATTR_IS_ZZWPRO) << 24);

							if (called & ((uint32_t)USER_ID_INDIVIDUAL << 24))	// individual message
							{
								dispatch_reply_ret_id |= (uint32_t)0 << 24;				// ret val
								send_v_data_with_message_format_handle(SEND_V_MESSAGE_REPLY_DYN);
							}
						} // dev_is_base
						offset += sizeof(V_AttachGoup);
						break;

					case V_IDT_REPORT_STATUS:
						send_v_data_with_message_format_handle(SEND_V_MESSAGE_BASE | SEND_V_MESSAGE_EXT);
//						offset += sizeof(V_ReportBaseInfo);
						offset = 0xff;
						break;

					case V_IDT_ACT_REPORT_CONFIG:
						memcpy(p_act_rep, &msg[offset], sizeof(V_ActReportConfig));
						if (p_act_rep->config.ig == 0)
						{
							p_act_rep->id &= USER_ID_CMP_MASK;
							p_act_rep->id |= (uint32_t)USER_ID_INDIVIDUAL << 24;
							maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, &rep->id_rep);
							if (p_act_rep->id == (rep->id_rep & USER_ID_CMP_MASK))
							{
								g_runtime_inst.act_report_conf.id = caller;
								dispatch_reply_ret_id |= (uint32_t)0 << 24;
								send_v_data_with_message_format_handle(SEND_V_MESSAGE_REPLY_ACT);
								prev_report_time = 0;
								memcpy(prev_pos_save, &rmc_data, 9);
							}
							else
							{
								rep->id_rep = 0;
							}
						}
						else if (p_act_rep->config.ig == 2)
						{
							g_runtime_inst.act_report_conf.id = caller;
							rep->id_rep = g_runtime_inst.act_report_conf.id;
						}
//						else if ((p_act_rep->config.ig == 1) || (p_act_rep->config.ig == 3))
						else
						{
							p_act_rep->id &= 0x00ffffff;
							rep->id_rep = get_watch_at_user_book(1, 0xffff, 0)->id & 0x00ffffff;
							if (((p_act_rep->config.ig == 1) && (p_act_rep->id == rep->id_rep)) ||
								((p_act_rep->config.ig == 3) && (p_act_rep->id != rep->id_rep)))
								g_runtime_inst.act_report_conf.id = caller;
							else
								rep->id_rep = 0;
						}

						if (rep->id_rep)						// 0: change nothing
						{
							if (p_act_rep->config.opt_dist)
							{
								g_runtime_inst.act_report_conf.config.dist = p_act_rep->config.dist;
								g_runtime_inst.act_report_conf.time_val = p_act_rep->time_val;
								g_runtime_inst.act_report_conf.dist_val = p_act_rep->dist_val;
								if (((g_runtime_inst.act_report_conf.config.dist != 1) && (g_runtime_inst.act_report_conf.dist_val == 0)) ||
									((g_runtime_inst.act_report_conf.config.dist != 2) && (g_runtime_inst.act_report_conf.time_val == 0)))
								{
									g_runtime_inst.act_report_conf.config.dist = 0;
									dispatch_reply_ret_id |= 1 << 24;
								}

							}
							if (p_act_rep->config.opt_pwr)
							{
								g_runtime_inst.act_report_conf.config.power = p_act_rep->config.power;
							}
							if (p_act_rep->config.opt_rp)
							{
								g_runtime_inst.act_report_conf.config.rp = p_act_rep->config.rp;
								g_runtime_inst.act_report_conf.rp_val = p_act_rep->rp_val;
							}

							if ((*((uint16_t *)&g_runtime_inst.act_report_conf.config) & ACT_REPORT_CONFIG_MASK) == 0)
								g_runtime_inst.act_report_conf.id = 0;
							if (dev_have_base_feature())
								save_user_data_to_flash(0);
						}
						offset += sizeof(V_ActReportConfig);
						break;

/*					case V_IDT_BASE_INFO:
						send_v_data_with_message_format_handle(SEND_V_MESSAGE_BASE);
						offset += sizeof(V_ReportBaseInfo);
						break;

					case V_IDT_EXT_INFO:
						send_v_data_with_message_format_handle(SEND_V_MESSAGE_EXT);
						offset += sizeof(V_ReportExtInfo);
						break;
*/
					case V_IDT_DISPATCH_POLL:
#ifdef DEBUG_DSP_SAVE_POWER
						vlog_v("stack","[%d]poll=%d.%d/%06x", slot_rising_edge_int_times, v_dispatch_poll.interval, v_dispatch_poll.peroid, send_message_flag);
#endif
						if (v_dispatch_poll.poll_counter != 0xff)
							timer_destroy(v_dispatch_poll.poll_counter);
						memcpy(&v_dispatch_poll, &msg[offset], sizeof(V_DispatchPoll));
						v_dispatch_poll.poll_counter = 0xff;
						if (dev_have_base_feature() && (v_dispatch_poll.ig == 2))
						{
							// base, and operation is all mobile, do nothing
						}
						else
						{
							if (v_dispatch_poll.interval == 0)	// respond immediately
							{
								send_v_dispatch_poll_handle();
							}
							else
							{
								maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, &rep->id_rep);
								rep->id_rep = ((rep->id_rep & 0x00ffffff) % v_dispatch_poll.interval) * (v_dispatch_poll.peroid * 180);
								v_dispatch_poll.poll_counter = timer_initial(1, rep->id_rep, send_v_dispatch_poll_handle);
#ifdef DEBUG_DSP_SAVE_POWER
								vlog_v("stack","\tnew=%06x,time=%d", send_message_flag, rep->id_rep);
#endif
							}
						}
						offset = 0xff;
						break;

					case V_REPLY_IDT_DEFINE:
						memcpy(rep, &msg[offset], sizeof(V_ReplyFrame) - sizeof(uint32_t));
						maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, &rep->id_rep);
						rep->id_rep &= USER_ID_CMP_MASK;
						if (((rep->ridt == V_RIDT_USER_MESSAGE) || (rep->ridt == V_IDT_SINGLE_CALL_RING)) && (rep->id_src == rep->id_rep))
						{
							GL_LCD_TipDraw(0, (uint8_t *)call_succ_reason[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][(rep->ridt == V_RIDT_USER_MESSAGE) ? 0 : 3], 0, 1000);
						}
//						offset += sizeof(V_ReplyFrame);
						offset = 0xff;	// 20220824��REPLY�����ν�������Ϊ�������߹���ʱ���Ȼᳬ��V_ReplyFrame������ν���ʱ�Ὣ���������ݵ����µ��ֶν��н���
						break;
					case V_IDT_MANAGER_FRAME:
//						if (dev_is_base())
						if (dev_have_base_feature())
						{
  #ifdef V_WIRELESS_NOTUSE_ENDER
							AIR_TMtoModule_Handle(dispatch_reply_ret_id, &msg[offset + 1], msg[1] - 5);	// jump over idt
  #else
							AIR_TMtoModule_Handle(dispatch_reply_ret_id, &msg[offset + 1], msg[1] - 6);	// jump over idt
  #endif
						}
						offset = 0xff;
						break;
					case V_IDT_PROBE_NEIGHBOUR:
					case V_IDT_PROBE_NEIGHBOUR_CTRL:
					case V_IDT_PROBE_NEIGHBOUR_GPS:
						if ((called & ((uint32_t)USER_ID_INDIVIDUAL << 24)) && 		// individual V/N message
							((interphone_mode == INTERPHONE_MODE_ZZWPRO_V) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_V25K) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_N)))
						{
							memcpy(p_probe, &msg[offset], sizeof(V_ReportProbeInfo));
//							g_neighbour_id = p_probe->probe_addr & 0x00ffffff;
//							g_neighbour_opcode = (msg[offset] == V_IDT_PROBE_NEIGHBOUR) ? PROBE_OP_CODE_RSSI :
//								((msg[offset] == V_IDT_PROBE_NEIGHBOUR_CTRL) ? PROBE_OP_CODE_CTRL : PROBE_OP_CODE_GPS);
//							g_neighbour_oppara = p_probe->rssi_rcv;				// p_probe->rssi_rcv: para of V_IDT_PROBE_NEIGHBOUR_CTRL
							manual_probe_v_neighbour(p_probe->probe_addr & 0x00ffffff, dispatch_reply_ret_id, 0,
								(msg[offset] == V_IDT_PROBE_NEIGHBOUR) ? PROBE_OP_CODE_RSSI :
								((msg[offset] == V_IDT_PROBE_NEIGHBOUR_CTRL) ? PROBE_OP_CODE_CTRL : PROBE_OP_CODE_GPS), p_probe->rssi_rcv);
						}
						offset = 0xff;
						break;

					case V_IDT_SINGLE_CALL_RING:
						memcpy(p_ring, &msg[offset], sizeof(V_SingleCallRing));
						if (p_ring->ring_type < MISC_OP_TOTAL_TYPE)
						{
							if (p_ring->ring_type == MISC_OP_TYPE_RING)
							{
								zzwpro_individual_call_ring(((p_ring->ring_time & 0x0f) == 0) ? 5 : (p_ring->ring_time & 0x0f));
							}
							else if (p_ring->ring_type == MISC_OP_TYPE_STATUS)
							{
								vlog_v("stack","\tStatus=%d", p_ring->reserved[0]);
							}
							else if (p_ring->ring_type == MISC_OP_TYPE_MOD_GRP_RESP)
							{
//								vlog_v("stack","\tResponse: 0x%08x->0x%08x,0x%08x->0x%08x",
//									g_runtime_inst_xvbase->response_group[XVBASE_MAX_RESPONSE_GROUP - 1], p_ring->reply_addr,
//									g_runtime_inst_xvbase->response_group[XVBASE_MAX_RESPONSE_GROUP - 2], p_ring->reserved2);
//								g_runtime_inst_xvbase->response_group[XVBASE_MAX_RESPONSE_GROUP - 1] = p_ring->reply_addr;
//								g_runtime_inst_xvbase->response_group[XVBASE_MAX_RESPONSE_GROUP - 2] = p_ring->reserved2;
								vlog_v("stack","\t[AIR]Response:0x%08x->0x%08x", g_runtime_inst_xvbase->response_group[XVBASE_MAX_RESPONSE_GROUP - 1], p_ring->reply_addr);
								g_runtime_inst_xvbase->response_group[XVBASE_MAX_RESPONSE_GROUP - 1] = p_ring->reply_addr;
								delay_to_reset_module(1, 2000);
							}
							else
							{
								if ((p_ring->ring_type == MISC_OP_TYPE_STUN) && (p_ring->reserved[0] == MISC_OP_MASK_BYTE_STUN))
									g_runtime_inst.runtime_paras.misc_runtime_config.remote_stun = 1;
								else if ((p_ring->ring_type == MISC_OP_TYPE_KILL) && (p_ring->reserved[0] == MISC_OP_MASK_BYTE_KILL))
									g_runtime_inst.runtime_paras.misc_runtime_config.remote_kill = 1;
								else if ((p_ring->ring_type == MISC_OP_TYPE_REVIVE) && (p_ring->reserved[0] == MISC_OP_MASK_BYTE_REVIVE))
									g_runtime_inst.runtime_paras.misc_runtime_config.remote_stun = 0;
								else
									p_ring->ring_type = 0xff;	// set it fail
								set_device_remote_operation_status(p_ring->ring_type);
							}
							if (called & ((uint32_t)USER_ID_INDIVIDUAL << 24))						// individual message
							{
								dispatch_reply_ret_id |= ((uint32_t)p_ring->ring_type << 28) | 0;	// ret val(h4bit=type, l4bit=result:0=OK)
								send_v_data_with_message_format_handle(SEND_V_MESSAGE_REPLY_RING);
							}
						}
						offset = 0xff;
						break;

					default:
						offset = 0xff;
						break;
				}

				if (offset >= end_pos)
					break;
			}

			send_v_data_with_message_format_handle(SEND_V_MESSAGE_SEND_NOW);
		}
		else
		{
			vlog_v("stack","CRC fail!want=%04x", (uint16_t)CRC_Check(msg, msg[1] - V_WIRELESS_DATA_OFFSET));
		}
	}

	return ret;
}

void act_report_check_vmode(uint16_t period)
{
	uint8_t proc_flag = 0;
	uint32_t dist;
	GPS_STRUCT_TYPEDEF prev_pos;

	print_act_report_check_debug_info(0, 0, 0);
//	if (g_runtime_inst.act_report_conf.id)
	if (g_runtime_inst.act_report_conf.id && device_is_sync)
	{
		prev_report_time += period;
		if (g_runtime_inst.act_report_conf.config.power)
		{
			if (g_runtime_inst.act_report_conf.reserved > get_battery_power_level())
			{
				g_runtime_inst.act_report_conf.reserved = get_battery_power_level();
				print_act_report_check_debug_info(1, 1, 0);
				send_v_data_with_message_format_handle(SEND_V_MESSAGE_BASE);
				proc_flag = V_IDT_BASE_INFO;
			}
		}

		if ((proc_flag == 0) && g_runtime_inst.act_report_conf.config.dist)
		{
			if (g_runtime_inst.act_report_conf.config.dist != 2)	// ==2: report with distance only, so it must compare time
			{
				if (prev_report_time >= (uint32_t)g_runtime_inst.act_report_conf.time_val * 3000)
				{
					if ((g_runtime_inst.act_report_conf.config.dist == 1) || (g_runtime_inst.act_report_conf.config.dist == 3))
					{
						prev_report_time = 0;
						print_act_report_check_debug_info(1, 2, 0);
						send_v_data_with_message_format_handle(SEND_V_MESSAGE_BASE);
						proc_flag = V_IDT_BASE_INFO;
					}
					else if (g_runtime_inst.act_report_conf.config.dist == 4)
					{
						proc_flag = 0x0f;			// time meet the requirement
					}
				}
			}

			if (((proc_flag == 0) || (proc_flag == 0x0f)) && (g_runtime_inst.act_report_conf.config.dist != 1))
			{
//#ifdef DEBUG_ACTIVE_REPORT_DEFINE
//				vlog_v("stack","prev:%02x %02x %02x %02x %02x %02x %02x %02x %02x",
//					prev_pos_save[0], prev_pos_save[1], prev_pos_save[2],
//					prev_pos_save[3], prev_pos_save[4], prev_pos_save[5],
//					prev_pos_save[6], prev_pos_save[7], prev_pos_save[8]);
//				vlog_v("stack","own:%02x %02x %02x %02x %02x %02x %02x %02x %02x",
//					((uint8_t *)&rmc_data)[0], ((uint8_t *)&rmc_data)[1], ((uint8_t *)&rmc_data)[2],
//					((uint8_t *)&rmc_data)[3], ((uint8_t *)&rmc_data)[4], ((uint8_t *)&rmc_data)[5],
//					((uint8_t *)&rmc_data)[6], ((uint8_t *)&rmc_data)[7], ((uint8_t *)&rmc_data)[8]);
//#endif
				memcpy(&prev_pos, prev_pos_save, 9);
				if (((prev_pos.gps_state & GPS_DATA_VALID_FLAG) == 0) && (rmc_data.gps_state & GPS_DATA_VALID_FLAG))
				{
					memcpy(prev_pos_save, &rmc_data, 9);
				}
				else
				{
					prev_pos.gps_state |= GPS_DATA_IS_FULL;
					dist = cal_2points_distance(&rmc_data, &prev_pos, 0);
					if ((dist != 0xffffffff) && (dist >= g_runtime_inst.act_report_conf.dist_val * 10))
					{
						if ((g_runtime_inst.act_report_conf.config.dist == 2) || (g_runtime_inst.act_report_conf.config.dist == 3) || ((g_runtime_inst.act_report_conf.config.dist == 4) && proc_flag))
						{
							memcpy(prev_pos_save, &rmc_data, 9);
							if (g_runtime_inst.act_report_conf.config.dist == 4)
								prev_report_time = 0;
							print_act_report_check_debug_info(1, 3, dist);
							send_v_data_with_message_format_handle(SEND_V_MESSAGE_BASE);
							proc_flag = V_IDT_BASE_INFO;
						}
					}
					else
					{
						print_act_report_check_debug_info(1, 12, (dist == 0xffffffff) ? 65535 : dist);
					}
				}
			}
		}

		if (proc_flag == V_IDT_BASE_INFO)
		{
			send_v_data_with_message_format_handle(SEND_V_MESSAGE_SEND_NOW);
//#ifdef DEBUG_ACTIVE_REPORT_DEFINE
//			vlog_v("stack","Act send[%x]->%08x", V_IDT_BASE_INFO, g_runtime_inst.act_report_conf.id);
//#endif
		}
	}
}

/*
const uint8_t u_neng_air_config_stage_data[] = {
	0x04, 0x00, 0x0D, 0x1B, 0x3A, 0x7C, 0xD4, 0x00, 0x01, 0xFD, 0xFF, 0x00, 0x02, 0x02, 0x00, 0x17, 0x08, 0x07, 0x10, 0x02, 0xC8, 0x00, 0x0A, 0x70, 0x00, 0x01, 0xD4, 0x1A, 0x93, 0x00, 0x00, 0xFF, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
void test_u_neng_air_config(void)
{
	memcpy(stack_message_buffer, u_neng_air_config_stage_data, 56);
	message_process_handle(1);
}
*/
extern GUI_INTERACTIVE_TYPEDEF *g_gui_interactive;
extern uint32_t timestamp_10ms;
void print_receive_bin_msg(uint8_t num, uint8_t *data, uint32_t caller, uint32_t be_called);
void message_process_handle(uint8_t flag)
{
	uint8_t msg_type, code, undecrypt_msg = 0, long_msg_flag = 7;	// long_msg_flag: 0xff-long message OK; code: b3~b0: 0-bin;1-��̨ID;2-BCD;3-ISO7;4-ISO8;5-NEMA;6-IP��ַ;7-UNICODE;12-������ȷ��;13-������ȷ��;14-��Ⱥ��ȷ��;15-��Ⱥ��ȷ��; b7~b6: bytes�ĸ�2b; ״̬����ʱ��7b״̬��
	uint8_t trunk_msg_content[MAX_NORMAL_MESSAGE_LENGTH];
	uint16_t bytes, ctype, ctype2, pkg_ret, bin_msg_num;			// bytes: SDM�����ֽ���/PSTN�����ֽ����ĵ�8b������Ϊ0
	MESSAGE_STRUCT *msg;
	uint32_t msg_time, msg_id_caller, msg_id_called;
	MESSAGE_INDEX_TYPEDEF msg_index;

	if (flag == 0)
	{
		memcpy(stack_message_buffer, stack_call_buffer, STACK_CALL_BUFFER_LENGTH);
	}
	else if (stack_message_buffer[0])
	{
		msg_index.msg_is_new = 0;
		ctype = stack_message_buffer[0] & 0x00ff;
		ctype2 = stack_message_buffer[0] & 0xff00;
		code = (uint8_t)(stack_message_buffer[0] >> 16);
		bytes = ((uint16_t)(code & 0xC0) << 2) | (uint16_t)(stack_message_buffer[0] >> 24);

		msg_time = compress_rtc_to_word();
		msg_id_caller = stack_message_buffer[2] | ((uint32_t)USER_ID_INDIVIDUAL << 24);
		if ((ctype == STACK_CALLING_MSG_INDIVIDUAL) || ((ctype == STACK_CALLING_STATE_CALL) && ((ctype2 & STACK_CALLING_STATE_CALL_GROUP) == 0)))
			msg_id_called = stack_message_buffer[1] | ((uint32_t)USER_ID_INDIVIDUAL << 24);
		else
			msg_id_called = stack_message_buffer[1];

		vlog_v("stack","[%c]message[0x%x->0x%x] code=%d,byte=%d", (msg_id_called & ((uint32_t)USER_ID_INDIVIDUAL << 24)) ? 'I' : 'G',
			msg_id_caller, msg_id_called, code, bytes);

		if (ctype == STACK_CALLING_STATE_CALL)
		{
			code &= 0x7F;
			gen_status_info_to_user(code, (uint8_t *)stack_message_buffer + 12);
			msg_type = MESSAGE_TYPE_STATUS_MSG;
			code = 7;	// just for save & display this status message
			goto process_msg_content;
		}
		else
		{
			code &= 0x0F;							// ZZWPRO receive bin message: code == 0
			if ((code >= 12) && (code <= 15))		// 12/13: conventional message without/with apply; 14/15: trunking packet data without/with apply
			{
				msg_type = MESSAGE_TYPE_LONG_MSG;
				pkg_ret = packet_data_process((uint32_t)stack_message_buffer + 12, (uint8_t *)stack_message_buffer + 12);
				if (pkg_ret == 0xff)				// long message OK
				{
					code = 0;						// means that unicode->gb2312 done already
				}
				else if (pkg_ret == 0xfe)			// custom data
				{
//					custom_packet_process(code, bytes, msg_id_caller, msg_id_called, &stack_message_buffer[3]);
					pkg_ret = bytes;				// number of bin message(add 2B header)
					print_receive_bin_msg(pkg_ret - 2, (uint8_t *)stack_message_buffer + 12 + 2, msg_id_caller, msg_id_called);	// -2&+2: jump over the prefix0&prefix1
					code = 0xfe;				// do not draw tip
				}
				else if ((pkg_ret == 7) && ((code == 12) || (code == 13)))	// convertional message and not support
				{
					code = 7;					// look this message as user message
					goto set_user_message_type_number;
				}
				else							// otap
				{
					code = 0xff;				// draw the tip
					long_msg_flag = (uint8_t)pkg_ret;
				}
			}
			else								// trunking short message
			{
				if (ctype2 & STACK_CALLING_ENCRYPTED_CALL)			// trunking encrypted short message
				{
					msg_type = MESSAGE_TYPE_USER_MSG_ENCRYPT;
					pkg_ret = bytes - 12;		// AES256 have no any additional data; SD(encrypt: data length must add 12)
decrypt_user_message:
					long_msg_flag = sd_data_decrypt(msg_id_called, msg_id_caller, (ctype == STACK_CALLING_MSG_INDIVIDUAL) ? 0 : 1,
						pkg_ret, (uint8_t *)stack_message_buffer + 12);
					if (long_msg_flag == 0xff)
					{
						p_strcpy((void *)((uint32_t)stack_message_buffer + 12),
							decrypt_message_fail_tip[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][(long_msg_flag == 1) ? 0 : 1]);
						pkg_ret = p_strlen((uint8_t *)((uint32_t)stack_message_buffer + 12));
						code = 0;
						undecrypt_msg = 1;
					}
				}
				else
				{
set_user_message_type_number:
					msg_type = (code == 7) ? MESSAGE_TYPE_USER_MSG : MESSAGE_TYPE_BIN_MSG;
					pkg_ret = bytes;
					if ((interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) && (code == 0))
					{
						code = parse_v_message((uint8_t *)((uint32_t)stack_message_buffer + 12), &bin_msg_num, msg_id_caller, msg_id_called);
						if (dev_is_base())
						{
							LBUS_ModuleReplyTM_Frame(msg_id_caller, (uint8_t *)((uint32_t)stack_message_buffer + 12),  pkg_ret);
						}
						else
						{
							if (uart2_use_as_dataport())
								send_xv_wireless_frame_to_host((uint8_t *)((uint32_t)stack_message_buffer + 12),  pkg_ret, msg_id_caller, msg_id_called);
						}
					}
					else if ((interphone_mode < INTERPHONE_MODE_PDT_TRUNKING) && (code == 7))	// 20220415: DMR encrypted message
					{
						if (ctype2 & STACK_CALLING_ENCRYPTED_CALL)
							goto decrypt_user_message;
//						else																	// look this message as user message(no encrypt)
//							;
					}
				}
			}

			if ((code == 7) || (code == 0))		// message
			{
				if (code == 7)					// 7:unicode; 0:bin
				{
					pkg_ret = unicode2string((uint16_t *)((uint32_t)stack_message_buffer + 12), trunk_msg_content, pkg_ret / 2, MAX_NORMAL_MESSAGE_LENGTH - 1);
//					trunk_msg_content[pkg_ret] = 0;
					p_strcpy((uint8_t *)stack_message_buffer + 12, trunk_msg_content);
				}
process_msg_content:
				msg = (MESSAGE_STRUCT *)stack_message_buffer;
				*((uint32_t *)(&msg->msg_time)) = msg_time;
				msg->id_caller = msg_id_caller;
				msg->id_called = msg_id_called;
				msg->msg_misc.msg_type = msg_type;
				msg->msg_valid_flag = MESSAGE_INIT_FLAG;

				if (code == 7)
				{
#ifdef CUSTOM_SHORT_MESSAGE
					if (parse_message_for_air_iop(msg->msg_content) == 0xff)	// not the custom message
#endif
					msg_index.msg_is_new = 1;
				}
				else if (pkg_ret == 0xff)		// long message done
				{
					msg_index.msg_is_new = 1;
				}
				else							// bin message
				{
					if ((*((uint8_t *)stack_message_buffer + 12) == USER_BIN_MESSAGE_PREFIX0) &&
							(*((uint8_t *)stack_message_buffer + 12 + 1) == USER_BIN_MESSAGE_PREFIX1))	// bin message
					{
						pkg_ret = bin_msg_num - 1;		// number of zzw's bin message; -1 is dec the EOF of the string
						print_receive_bin_msg(pkg_ret - 2, (uint8_t *)stack_message_buffer + 12 + 2, msg->id_caller, msg->id_called);	// -2&+2: jump over the prefix0&prefix1
					}
					else
					{
						if ((interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) || undecrypt_msg)	// code of zzw-message is 0
						{
							msg_index.msg_is_new = 1;
						}
						else
						{
							vlog_v("stack","[MSG/bin]%08x->%08x,n=%d:", msg->id_caller, msg->id_called, pkg_ret);
							for (ctype = 0; ctype < pkg_ret; ctype++)
								vlog_v("stack","%02x ", *((uint8_t *)stack_message_buffer + 12 + ctype));
							vlog_v("stack","");
						}
					}
				}
			}
		}

		if (msg_index.msg_is_new || (code == 0xff))
		{
			if (!(maintain_setup_parameter(PARA_OPERATE_READ, TIP_CONFIG_PARA_POS, 0) & TIP_CONFIG_MASK_MSG))
				send_tip_sound(TIP_TYPE_CALL_SUCCESSFUL, 1);
		}

		if (msg_index.msg_is_new)
		{
			pkg_ret = save_one_user_message(1, &msg_index, (void *)stack_message_buffer);
			if (pkg_ret != 0xffff)
			{
				send_status_to_bt(2, 0);	// only mobile
				if (new_msg_incoming < MAX_MESSAGE_NUMBER)
					new_msg_incoming++;
				g_gui_interactive->dev_misc_notify.new_msg = 1;
			}
		}
		else if (code == 0xff)
		{
			GL_LCD_TipDraw(0, (uint8_t *)air_iop_tip[long_msg_flag & 0x07], 0, 1000);
		}
		stack_message_buffer[0] = 0;
	}
}


#ifdef DEBUG_RCV_RSSI_IS_255
extern uint8_t replace_voice_flag, rssi_voice;
extern ZZWPRO_STATUS_STRUCT zzwpro_status[];
#endif
void send_status_to_bt(uint8_t can_uart, uint8_t reset_rmt_gps)	// bit0-can; bit1-uart
{
//	static uint32_t timestamp_10ms_status_bak = 0;

	BT_DMA_DATA_TYPEDEF status_to_can_buf;
	STATUS_TO_BT_TYPEDEF status_to_bt_buf;
	uint8_t send_to_host = 0;
#ifdef DEBUG_REAL_BT
	char calling_str[40];
#endif

	if (dev_is_base())
	{
		if (send_to_can_is_valid() && (can_uart & 0x01))
		{
			memcpy(status_to_can_buf.data, &stack_state, sizeof(STACK_STATE_STRUCT));
			memcpy(status_to_can_buf.data + sizeof(STACK_STATE_STRUCT), &stack_stage, sizeof(STACK_STAGE_STRUCT));
			stack_stage.ackw &= ~0x80000000;
			send_bt_frame_to_host_from_can((uint8_t *)&status_to_can_buf,  sizeof(STACK_STATE_STRUCT) + sizeof(STACK_STAGE_STRUCT), TSC_CTASK_DEV_STATUS, 0, 0);
		}
	}
	else // dev_is_base
	{
		send_to_host = uart2_use_as_dataport() && (can_uart & 0x02);
//		if (send_to_host)	// must process always
		{
			status_to_bt_buf.watch_index = get_watch_id_index();
			status_to_bt_buf.msg_total = g_runtime_inst.msg_table.message_total;
			status_to_bt_buf.msg_addr = g_runtime_inst.msg_table.message_total ? GET_MESSAGE_SLOT_REAL_ADDR(g_runtime_inst.msg_table.msg_index[g_runtime_inst.msg_table.message_total - 1].msg_addr) : 0;
			status_to_bt_buf.dev_mode = interphone_mode;
			if (reset_rmt_gps && (dev_is_fixed_gps() == 0))
				rmt_gps_state &= ~GPS_DATA_VALID_FLAG;
			status_to_bt_buf.rmt_gps_state = (rmt_gps_state & 0x0f) | (rmc_data.gps_state << 4);
			status_to_bt_buf.rssi = get_rssi_value_real();
			memcpy(status_to_bt_buf.rmt_gps_pos, &calling_dev_position[2], sizeof(float) * 2);
			memcpy(status_to_bt_buf.own_gps_pos, &calling_dev_position[0], sizeof(float) * 2);
			status_to_bt_buf.own_speed = rmc_data.speed;
			status_to_bt_buf.rcv_rssi = get_signal_value_discontinuous();
#ifdef DEBUG_RCV_RSSI_IS_255
			vlog_v("stack","\trcv=%d(%d:%d %d %d %d %d %d)", status_to_bt_buf.rcv_rssi, replace_voice_flag,
				zzwpro_status[0].rssi, zzwpro_status[1].rssi, zzwpro_status[2].rssi, zzwpro_status[3].rssi, zzwpro_status[4].rssi, zzwpro_status[5].rssi);
#endif
			memcpy(&status_to_bt_buf.stk_state, &stack_state, sizeof(STACK_STATE_STRUCT));
			memcpy(&status_to_bt_buf.stk_stage, &stack_stage, sizeof(STACK_STAGE_STRUCT));
			stack_stage.ackw &= ~0x80000000;

			g_gui_interactive->dev_status.dev_sta_wr = (g_gui_interactive->dev_status.dev_sta_wr + 1) & (DEV_STA_MAX_FRAME - 1);
			memcpy(&g_gui_interactive->dev_status.dev_sta[g_gui_interactive->dev_status.dev_sta_wr], &status_to_bt_buf, sizeof(STATUS_TO_BT_TYPEDEF));
			g_gui_interactive->dev_misc_notify.crypto = crypto_is_enable() ? 1 : 0;
			g_gui_interactive->dev_misc_notify.stack_op_slot = get_dsp_demod_zzw_slot();
//			vlog_v("stack","bat=%d/%d", g_gui_interactive->dev_misc_notify2.batt_lv, get_battery_power_level());
			g_gui_interactive->dev_misc_notify2.batt_lv = get_battery_power_level();
/*
			vlog_v("stack","(M:%d/%d)sta(r=%d,w=%d):msg=%02d,gps=%02x,rssi=%d,chan=%03d,cs=%08x,vs=%08x,dist=%s",
				timestamp_10ms, timestamp_10ms - timestamp_10ms_status_bak, g_gui_interactive->dev_status.dev_sta_rd, g_gui_interactive->dev_status.dev_sta_wr,
				g_gui_interactive->dev_status.dev_sta[g_gui_interactive->dev_status.dev_sta_wr].msg_total,
				g_gui_interactive->dev_status.dev_sta[g_gui_interactive->dev_status.dev_sta_wr].rmt_gps_state,
				g_gui_interactive->dev_status.dev_sta[g_gui_interactive->dev_status.dev_sta_wr].rssi,
				g_gui_interactive->dev_status.dev_sta[g_gui_interactive->dev_status.dev_sta_wr].stk_state.chan,
				g_gui_interactive->dev_status.dev_sta[g_gui_interactive->dev_status.dev_sta_wr].stk_state.cs,
				g_gui_interactive->dev_status.dev_sta[g_gui_interactive->dev_status.dev_sta_wr].stk_state.vs,
				g_gui_interactive->relative_distance);
			timestamp_10ms_status_bak = timestamp_10ms;
*/
			if (send_to_host)
			{
//				vlog_v("stack","Send status to uart2");
				send_bt_frame_to_host((uint8_t *)&status_to_bt_buf, BT_DMA_DATA_DLEN, TSC_CTASK_DEV_STATUS, 0, 0);
			}

#ifdef DEBUG_REAL_BT
			if (status_to_bt_buf.stk_state.vs & STACK_VS_CALLING)
			{
				p_strcpy(calling_str, (status_to_bt_buf.stk_state.vs & STACK_VS_GROUP_CALL) ? "grp call" : "ind call");
				if (status_to_bt_buf.stk_state.vs & STACK_VS_PLAY_VOICE)
					p_strcat(calling_str, "/play voice");
			}
			else
			{
				p_strcpy(calling_str, "idle");
			}
			vlog_v("stack","[STATUS:  %02d]%08x->%08x, GPS=0x%02X(remote:lon=%3.6f, lat=%3.6f, own:lon=%3.6f, lat=%3.6f), own speed=%d(%s)",
				status_to_bt_buf.dev_mode, status_to_bt_buf.stk_state.id_caller, status_to_bt_buf.stk_state.id_be_called, status_to_bt_buf.rmt_gps_state,
				status_to_bt_buf.rmt_gps_pos[0], status_to_bt_buf.rmt_gps_pos[1], status_to_bt_buf.own_gps_pos[0], status_to_bt_buf.own_gps_pos[1], status_to_bt_buf.own_speed, calling_str);
#endif
		}
	} // dev_is_base
}

void send_vocoder_to_speaker_at_slot_middle(void);
void set_real_name(uint8_t *real_name);
void vocoder_switch_speed(uint8_t speed);
#if defined VAD_KEY_USE_AS_STICK_PTT || defined PTT_KEY_USE_AS_STICK
void clear_ptt_stick_status(void);
#endif
uint8_t *stack_print_ret_info(uint16_t code);
//void stack_return_process(uint32_t ret) __attribute((section(".ARM.__at_0x8030000")));
void stack_return_process(uint32_t ret)
{
	uint32_t aux1, aux2, tmp, stack_cs_prev, stack_vs_prev;
	uint16_t ctype, ctype2, *ptr16;
	char call_type_string[MAX_USER_NAME_LEN * sizeof(uint16_t)];
	uint8_t *ptr8;
//	uint32_t timestamp1ms;
    static uint32_t count_timeslot_for_m4 = 0;
    static uint32_t aux1_pre = 0;
    static uint32_t aux2_pre = 0;

    uint8_t *pinfo = stack_print_ret_info(ret);

    if(pinfo)
    {
        vlog_i("stack","%3d:%s",ret,pinfo);
    }

	switch (ret)
	{
		case STACK_RETURN_TYPE_VERSION:						// �汾��Ϣ: NULL NULL		FUNC VER DATE TIME
			get_stack_information();
			print_stack_version();
			break;
		case STACK_RETURN_TYPE_VOICE:						// SPEAKER��Ƶ: NULL NULL	0 0 0 V0 V1 ���� V32
			send_vocoder_to_speaker_at_slot_middle();
			break;
		case STACK_RETURN_TYPE_SIGNALING:					// �������: NULL NULL		0 0 0 V0 V1 ���� V32
		case STACK_RETURN_TYPE_V_SIGNALING:
		case STACK_RETURN_TYPE_RC_SIGNALING:
		case STACK_RETURN_TYPE_RC_END:
//			if (ret == STACK_RETURN_TYPE_SIGNALING)
				ret = DSP_CMD_FRAME_INFO;
//			else if (ret == STACK_RETURN_TYPE_RC_SIGNALING)
//				ret = DSP_CMD_FRAME_RC;
//			else if (ret == STACK_RETURN_TYPE_RC_END)
//				ret = DSP_CMD_FRAME_RC_CANCEL;
//			else
//				ret = DSP_CMD_FRAME_VOICE;
			send_data_sync_to_slot_middle(ret, stack_para_aux2);
			tx_icon_delay_clean = 2;
			if (is_ambience_listening() == 0)
			{
				led_play(BAR_TYPE_TR_ARROW_UP);
			}
			break;

		case STACK_RETURN_TYPE_STATE:						// ��̨״̬: CS VS	LAI CHAN ID1 ID2 ID3 CTIMER
			current_stack_slot = (stack_para_aux1 >> STACK_CS_SLOT_FLAG_OFFSET) & 0x01;
			link_status_flag = (stack_para_aux1 >> STACK_CS_NET_LINK_STAT_OFFSET) & 0x01;
			ptr16 = (uint16_t *)((uint32_t)stack_call_buffer + GPS_UPLOAD_TIMER_FLAG_OFFSET);
			g_runtime_inst.runtime_paras.stack_conts_gps[0] = (uint8_t)ptr16[0];
			g_runtime_inst.runtime_paras.stack_conts_gps[1] = (uint8_t)ptr16[1];
			if (*ptr16++)
				link_status_flag |= 0x02;
			if (*ptr16)
				link_status_flag |= 0x04;

			aux1 = (stack_para_aux1 ^ stack_state.cs) &
					(STACK_CS_PDT_DMR | STACK_CS_CONVENTIONAL_TRUNK | STACK_CS_THROUGH_REPEAT | STACK_CS_REMOTE_STUN |
					STACK_CS_REMOTE_KILL | STACK_CS_SHORT_SEARCHING | STACK_CS_LONG_SEARCHING | STACK_CS_LOGIN_THE_BASE |
					STACK_CS_SLOT_SYNC | STACK_CS_SYSCODE_MATCH | STACK_CS_LOGIN_REQUESTED | STACK_CS_BACKGROUND_TSCC);
			aux2 = stack_para_aux2 ^ stack_state.vs;

			memcpy(&stack_state.id_be_called, stack_call_buffer + 1, sizeof(STACK_STATE_STRUCT) - 12);	// subtract (cs+vs+lai+chan)
            if(stack_para_aux1 == 0x01000006 || 0x01080006==stack_para_aux1)
            {
                count_timeslot_for_m4++;
                if(count_timeslot_for_m4 % 200 == 0)
                {
                    vlog_v("stack","[%d]aux1=%08x,aux2=%08x,total=%u", slot_rising_edge_int_times, stack_para_aux1, stack_para_aux2,count_timeslot_for_m4);
                }
            }
            else
            {
                vlog_v("stack","[%d]aux1=%08x,aux2=%08x", slot_rising_edge_int_times, stack_para_aux1, stack_para_aux2);
            }
            //only change print
            // if(aux1_pre != stack_para_aux1 || aux2_pre != stack_para_aux2)
            // {
            //     vlog_v("stack","[%d]aux1=%08x->%08x,aux2=%08x->%08x", slot_rising_edge_int_times, aux1_pre,stack_para_aux1, aux2_pre,stack_para_aux2);
            //     aux1_pre = stack_para_aux1;
            //     aux2_pre = stack_para_aux2;
            // }


			if (aux1 || aux2)
			{
				stack_cs_prev = stack_state.cs;
				stack_vs_prev = stack_state.vs;
				stack_state.cs = stack_para_aux1;
				stack_state.vs = stack_para_aux2;
				if (aux2)
					send_status_to_bt(3, ((aux2 & STACK_VS_CALLING) && ((stack_state.vs & STACK_VS_CALLING) == 0)) ? 1 : 0);	// force send
				if (aux1)
				{
					ctype2 = (uint16_t)get_interphone_mode(stack_state.cs);
					if ((uint8_t)ctype2 != interphone_mode)	// ָ��� or ȫ��ɨ��
					{
						ctype = (uint16_t)get_interphone_mode(inst_conv->id >> 23);
						if ((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) && (ctype2 == ctype) && (ctype2 <= INTERPHONE_MODE_PDT_CONV) && (stack_cs_prev & STACK_CS_LOGIN_THE_BASE) &&
							((stack_state.cs & (STACK_CS_SHORT_SEARCHING | STACK_CS_LONG_SEARCHING | STACK_CS_LOGIN_THE_BASE | STACK_CS_SYSCODE_MATCH | STACK_CS_LOGIN_REQUESTED)) == 0))
						{
							vlog_v("stack","[inst_2_conv]mode:%d->%d,grp0=0x%08x(%d)", interphone_mode, ctype2, inst_conv->id, ctype);
							update_dsp_work_mode((uint8_t)ctype2, 1, 0);
							set_instruction_to_conv_exec_flag(0x00);
							p_strcpy(inst_conv->name, inst_conv_name);
							inst_conv->bind_ch = (stack_call_buffer[0] << 16) | (stack_call_buffer[0] >> 16);
							if ((inst_conv->bind_ch & 0x00000fff) > 800)	// error, so use the abs frequency
							{
//								inst_conv->bind_ch &= 0xfffff000;
//								inst_conv->bind_ch |= get_intruction_to_conv_chan(ctype2, stack_call_buffer[6], stack_call_buffer[7]) & 0x00000fff;
								inst_conv->bind_ch = get_intruction_to_conv_chan(ctype2, stack_call_buffer[6], stack_call_buffer[7]) | 0x80000000;	// 0x80000000:��������Ҫ�����ŵ�������ֱͨ�򳣹�����
							}
							else
							{
								inst_conv->bind_ch &= ~0x80000000;	// ��������Ҫ�����ŵ�������ֱͨ�򳣹�����
							}
//							maintain_setup_parameter(PARA_OPERATE_WRITE, WORK_MODE_PARAS_POS, (uint8_t)ctype2);
							maintain_setup_parameter(PARA_OPERATE_WRITE, WORK_MODE_PARAS_POS, (uint8_t)INTERPHONE_MODE_PDT_CONV);
							system_power_down(2);
						}
						else
						{
							update_dsp_work_mode((uint8_t)ctype2, 0, 0);
						}
					}
					else
					{
						if ((interphone_mode < INTERPHONE_MODE_ZZW_ADHOC) || ((aux1 & (STACK_CS_SLOT_SYNC | STACK_CS_SLOT_FLAG | STACK_CS_LOGIN_THE_BASE)) == 0))
							vlog_v("stack","cs[0x%x](0x%x->0x%x)", aux1, stack_cs_prev, stack_state.cs);
					}

					if (dev_have_base_feature())
					{
						if (GET_STACK_XCS(stack_call_buffer) & STACK_XCS_SLOT_FAIL)		// ����չ����״̬��XCS.b7=1��ʾ��GPSͬ���Ļ�վ�յ���������վ��������
						{
							if ((heartbeat_fail_counter & HEARTBEAT_STATE_MASK_STACK_SET_SYNC_FAIL) == 0)
							{
								heartbeat_fail_counter |= HEARTBEAT_STATE_MASK_STACK_SET_SYNC_FAIL;
								vlog_v("stack","XCS,NOT reset GPS");
								if ((heartbeat_fail_counter & HEARTBEAT_STATE_MASK_FAIL_COUNTER) < HEARTBEAT_STATE_MASK_FAIL_COUNTER)	// use low 4bit only(only 4bit to report to dispatch)
									heartbeat_fail_counter++;

							}
						}
						else
						{
							heartbeat_fail_counter &= ~HEARTBEAT_STATE_MASK_STACK_SET_SYNC_FAIL;										// ��������������0��������������ֻ�ܿ���һ�Σ�����ֻ�ǼӸ�����
						}
					}
					else // dev_have_base_feature
					{
					} // dev_have_base_feature
				}

				if (aux2)
				{
					if (aux2 != STACK_VS_CALLING)
						vlog_v("stack","[%d]vs[0x%x](0x%x->0x%x)", slot_rising_edge_int_times, aux2, stack_vs_prev, stack_state.vs);
				}

				if (stack_state.vs & STACK_VS_ENV_MONITOR)
				{
					stack_state.vs &= ~STACK_VS_CALLING;
					aux2 &= ~STACK_VS_CALLING;
				}

				if (aux1 & (STACK_CS_REMOTE_STUN | STACK_CS_REMOTE_KILL))
				{
					tmp = get_stack_work_mode();
					if(stack_state.cs & STACK_CS_REMOTE_KILL)
					{
//						GL_LCD_TipDraw(0, (uint8_t *)"ң�гɹ�", 0, 1000);	// ����Э��ջһֱ����ң�λ�ң��״̬������aux1��aux2û�б仯���ظ�����ң�λ�ң��ʱ�������˴�
						if ((tmp & STACK_MODE_SET_KILL) == 0)
						{
							set_stack_work_mode(tmp | STACK_MODE_SET_KILL);
							set_device_remote_operation_status(MISC_OP_TYPE_KILL);
//							save_user_data_to_flash(0);
//							save_user_data_to_flash(0);
						}
					}
					else if (stack_state.cs & STACK_CS_REMOTE_STUN)
					{
//						GL_LCD_TipDraw(0, (uint8_t *)"ң�γɹ�", 0, 1000);
						if ((tmp & STACK_MODE_SET_STUN) == 0)
						{
							set_stack_work_mode(tmp | STACK_MODE_SET_STUN);
							set_device_remote_operation_status(MISC_OP_TYPE_STUN);
//							save_user_data_to_flash(0);
//							save_user_data_to_flash(0);
						}
					}
					else if ((stack_state.cs & STACK_CS_REMOTE_STUN) == 0)
					{
//						GL_LCD_TipDraw(0, (uint8_t *)"����ɹ�", 0, 1000);
						if ((tmp & STACK_MODE_SET_STUN) == STACK_MODE_SET_STUN)
						{
							set_stack_work_mode(tmp & (~STACK_MODE_SET_STUN));
							set_device_remote_operation_status(MISC_OP_TYPE_REVIVE);
//							save_user_data_to_flash(0);
//							save_user_data_to_flash(0);
//							nvic_system_reset;	// NVIC_SystemReset();
						}
					}
				}

				if (aux1 & (STACK_CS_SHORT_SEARCHING | STACK_CS_LONG_SEARCHING))
				{
					if (stack_state.cs & (STACK_CS_SHORT_SEARCHING | STACK_CS_LONG_SEARCHING))
					{
						if (stack_state.cs & STACK_CS_SHORT_SEARCHING)
						{
							search_the_base = 0xff;
							vlog_v("stack","short hunt...");
						}
						else
						{
							search_the_base = 0xfe;
							vlog_v("stack","long hunt...");
						}
					}
					else
					{
						search_the_base = 0;
						// vlog_v("stack","stop hunt");
					}
				}

				if (aux1 & STACK_CS_LOGIN_THE_BASE)
				{
					if ((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) || (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING))
					{
						if (stack_state.cs & STACK_CS_LOGIN_THE_BASE)
						{
							loggin_the_base = 0xff;
							*((uint32_t *)&stack_state.lai) = stack_call_buffer[0];
							ctype2 = get_the_loggin_channel_index(stack_state.lai, stack_state.chan);
							// MPT: lai is 16bit=sys code; chan has no color code
							// PDT: lai is 12bit=sys code >> 2; chan has 4bit color code
							maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_PRIOR_CTRL_CHAN, &ctype);
							if ((ctype2 != 0xffff) && (ctype != ctype2))
							{
								if (maintain_stack_parameter(PARA_OPERATE_WRITE, STACK_PARA_PRIOR_CTRL_CHAN, &ctype2))
									save_user_data_to_flash(1);
							}
							vlog_v("stack","[LOG IN]base id=0x%x, chan=%d", stack_state.lai, stack_state.chan & 0x0fff);
							verify_app_password(0);
						}
						else
						{
							loggin_the_base = 0;
							vlog_v("stack","[LOG OUT]the base!");
						}
					}
					else if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
					{
						// 20220424:������Э��ջV2.64��Э��ջ�����˳�ʡ��ģʽ����λ�����ص�̨״̬֡�Ĵ���R1.b7�����������ŵ�ֻʣһ��ʱ��Э��ջ��ʡ�����λ=1������ֹͣʡ���߼�
//						if (stack_state.cs & STACK_CS_LOGIN_THE_BASE)
//							temp_to_set_dsp_save_power(0, 0, 0);
//						else
//							dsp_save_power_init(1);

						// 20220519:Э��ջ��ͨ��ʱҲ��λ����ʱ�ر�ʡ��ᵼ��ͨ����������ά������״̬����Ϊ��λ����Ҫ����ʡ��ʱ��֪ͨDSP���߼�����ʡ�磬ʵ��DSPά��������
						stack_force_set_savepower((stack_state.cs & STACK_CS_LOGIN_THE_BASE) ? 1 : 0);
					}
				}

				if (aux2 & STACK_VS_CALLING)
				{
					if (stack_state.vs & STACK_VS_CALLING)
					{
						vocoder_switch_speed(is_crypto_calling() ? 1 : 0);

						start_send_message_busy_flag();
						// �����ڴ˵���: ����ʱ������PTT�����ɿ���Ȼ��������̨����������release��
//						sd_calling_setup(stack_state.id_be_called, stack_state.id_speaker,
//							((stack_state.vs & STACK_VS_OVCM) ? 0x80 : 0x00) | get_call_type_for_crypto(interphone_mode, 1), (stack_state.vs & STACK_VS_OWN_CALLING) ? 1 : 0);
						if ((stack_state.vs & STACK_VS_OWN_CALLING) == 0)
						{
							set_real_new_bt_to_own_calling(SET_BT_START_PLAY);
							if (is_group_calling() == 0)
								tmp = (stack_state.id_caller & 0x00ffffff) | (USER_ID_INDIVIDUAL << 24);
							else
								tmp = stack_state.id_be_called & 0x00ffffff;
							if (stack_state.vs & STACK_VS_EMERGENCY)
								tmp |= USER_ID_EMERGENCY << 24;
							else if (stack_state.vs & STACK_VS_BROADCAST)
								tmp |= USER_ID_BROADCAST << 24;
							insert_one_calling_record(0, 1, tmp);	// save_one_calling_record
						}
						else
						{
//							set_real_new_bt_to_own_calling(SET_BT_START_MIC);
#ifdef READ_CODEWORD_DELAY_BY_LC_NUM
							if (is_speech_vad_enable())
							{
								delay_to_read_vocoder = get_stack_lc_time();
								if (delay_to_read_vocoder)
//									delay_to_read_vocoder = (delay_to_read_vocoder - 1) * GET_ZZWPRO_TR_STATUS_MAX_SLOT();
									delay_to_read_vocoder *= GET_ZZWPRO_TR_STATUS_MAX_SLOT();
							}
  #ifdef DEBUG_VOICE_SEQUENCE
							vlog_v("stack","[%d]CALL,dly=%d", slot_rising_edge_int_times, delay_to_read_vocoder);
  #endif
#endif
							if ((interphone_mode < INTERPHONE_MODE_ZZW_ADHOC) && !(maintain_setup_parameter(PARA_OPERATE_READ, TIP_CONFIG_PARA_POS, 0) & TIP_CONFIG_MASK_CALL_ESTABLISH))
							{
								if (stack_state.vs & STACK_VS_EMERGENCY)
									send_tip_sound(TIP_TYPE_EMERGENCY_CALL, 1);
								else
									send_tip_sound(TIP_TYPE_CALL_SUCCESSFUL, 1);
							}
						}

						if (send_message_buffer[0] == CALL_PTT_ASYNC_MESSAGE_2)
						{
							if (is_ptt_key_pressed() == 0)
							{
								set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
								set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
							}
							else
							{
								send_message_buffer[0] = CALL_PTT_ASYNC_NULL;
							}
						}

						if (iop_scan_channel_stt_flag)
						{
							set_iop_scan_flag(iop_scan_channel_stt_flag);
							iop_scan_channel_stt(2);
						}
					}
					else
					{
						set_real_new_bt_to_own_calling(SET_BT_FAKE_STOP);
						clear_ptt_voice_flag();
						clear_send_message_busy_flag();
						// һ�������ֹͣ������ֹͣ����ʱ���Ѿ�release�ˣ������ٴε�����Ϊ�˷�ֹ�쳣�������֤�˳�ͨ��ʱ�϶�release���ܿ�
						sd_calling_release(0xff);	// release at stop speak or play voice already, do it again to ensure sd card quit the setup
						if (!(maintain_setup_parameter(PARA_OPERATE_READ, TIP_CONFIG_PARA_POS, 0) & TIP_CONFIG_MASK_CALL_FINISH))
							send_tip_sound(TIP_TYPE_CALL_FINISH, 1);
//						vlog_v("stack","free now...");

						tmp = set_iop_scan_flag(0xff);
						if (tmp)
						{
							iop_scan_channel_stt(tmp - 1);
							set_iop_scan_flag(0);
						}
						clear_zzw_last_speaker_data();
#if defined VAD_KEY_USE_AS_STICK_PTT || defined PTT_KEY_USE_AS_STICK
						clear_ptt_stick_status();
#endif
						last_call_timestamp = timestamp_1s;
					}
					set_delay_to_enter_sleep_external(0xff);	// 0xff: replace by rf_save_power.keep
				}

				if (interphone_mode != INTERPHONE_MODE_MPT_TRUNKING)
				{
					if (aux2 & STACK_VS_PLAY_VOICE)
					{
						if (stack_state.vs & STACK_VS_PLAY_VOICE)
						{
							set_dev_gate(IS_TERMINAL_GATE, 1);

/*							20220810: ��������Ҫ����callsetup���ں�����������Ƶ����ͬ��֡�н���
							if (is_group_calling() || ((stack_state.vs & STACK_VS_OWN_CALLING) == 0))	// 20220809:������Ǳ�������ĺ��вŵ��ã�����ᱻ�ظ���������
							{
								vlog_v("stack","\tPlay:%08x->%08x", stack_state.id_speaker, stack_state.id_be_called);
								sd_calling_setup(stack_state.id_be_called, stack_state.id_speaker,
									((stack_state.vs & STACK_VS_OVCM) ? 0x80 : 0x00) | get_call_type_for_crypto(interphone_mode, 0), 0);
							}
*/
							dsp_speech_out_pa_ctrl(1, SPEECH_PA_CTRL_OUT_VOICE);
						}
						else
						{
							set_dev_gate(IS_TERMINAL_GATE, 0);

							dsp_speech_out_pa_ctrl(0, SPEECH_PA_CTRL_OUT_VOICE);
							if (!(maintain_setup_parameter(PARA_OPERATE_READ, TIP_CONFIG_PARA_POS, 0) & TIP_CONFIG_MASK_TALK_FINISH))
								send_tip_sound(TIP_TYPE_PTT_RELEASED, 1);
							sd_calling_release(0);
#ifdef BASE_ENABLE_VOCODER_AUTO_TRACK
							runtime_to_switch_dsp_mod(ZZWPRO_VOCODER_STACK_RESET);
#endif
						}
					}

					if (aux2 & STACK_VS_OWN_SPEAKING)
					{
						if (stack_state.vs & STACK_VS_OWN_SPEAKING)
						{
							tmp = ((is_group_calling() == 0) && ((stack_state.vs & STACK_VS_OWN_CALLING) == 0)) ? stack_state.id_caller : stack_state.id_be_called;	// �����ػ�ʱ������IDҪʹ�÷�������˵�ID
#ifdef DEBUG_MINISTERIAL_CRYPTO
							vlog_v("stack","\tSpeak:%08x->%08x", stack_state.id_speaker, tmp);
#endif
							sd_calling_setup(tmp/*stack_state.id_be_called*/, stack_state.id_speaker,
								((stack_state.vs & STACK_VS_OVCM) ? 0x80 : 0x00) | get_call_type_for_crypto(interphone_mode, 1), 1);
							send_voice_to_module();
#ifdef DEBUG_VOICE_SEQUENCE
							vlog_v("stack","[%d]SPK act", slot_rising_edge_int_times);
#endif
						}
						else
						{
							send_voice_to_module_stop();
							sd_calling_release(1);
#ifdef DEBUG_VOICE_SEQUENCE
							vlog_v("stack","[%d]SPK end", slot_rising_edge_int_times);
#endif
						}
					}
				}
				else
				{
					if (aux2 & STACK_VS_ANALOG_CALL_MODE)						// stack set this bit absolutely when calling build
					{
						if (stack_state.vs & STACK_VS_ANALOG_CALL_MODE)
						{
							set_dsp_analog_mskon_squelch(SIGNALING_TONE_OUTPUT_POS, 1);
							dsp_speech_out_pa_ctrl(1, SPEECH_PA_CTRL_OUT_VOICE);
						}
						else													// stack clear this bit when calling release
						{
							set_dsp_analog_mskon_squelch(SIGNALING_TONE_OUTPUT_POS, 0);
							dsp_speech_out_pa_ctrl(0, SPEECH_PA_CTRL_OUT_VOICE);
							if (!(maintain_setup_parameter(PARA_OPERATE_READ, TIP_CONFIG_PARA_POS, 0) & TIP_CONFIG_MASK_TALK_FINISH))
								send_tip_sound(TIP_TYPE_PTT_RELEASED, 1);
#ifdef BASE_ENABLE_VOCODER_AUTO_TRACK
							runtime_to_switch_dsp_mod(ZZWPRO_VOCODER_STACK_RESET);
#endif
						}
					}

					if (aux2 & STACK_VS_OWN_SPEAKING)
					{
						if (stack_state.vs & STACK_VS_OWN_SPEAKING)
						{
							dsp_speech_out_pa_ctrl(0, SPEECH_PA_CTRL_OUT_VOICE);// close out_pa when own speaking
							send_voice_to_module();
						}
						else
						{
							send_voice_to_module_stop();
							dsp_speech_out_pa_ctrl(1, SPEECH_PA_CTRL_OUT_VOICE);// open out_pa for playing voice(if present) when own speaking finish
						}
					}
				}

				call_type_string[0] = 0;
				if (aux2 & STACK_VS_ENV_MONITOR)
				{
					if (stack_state.vs & STACK_VS_ENV_MONITOR)
						p_strcat(call_type_string, "ENV/");
				}
				if (aux2 & STACK_VS_EMERGENCY)
				{
					if (stack_state.vs & STACK_VS_EMERGENCY)
						p_strcat(call_type_string, "E/");
				}
				if (aux2 & STACK_VS_BROADCAST)
				{
					if (stack_state.vs & STACK_VS_BROADCAST)
						p_strcat(call_type_string, "B/");
				}
				if (p_strlen(call_type_string))
					vlog_v("stack","%s calling", call_type_string);
			}

			if ((stack_state.chan != (uint16_t)(stack_call_buffer[0] >> 16)) || get_xv_freq_is_change())
			{
//				vlog_v("stack","[set chan]%d->%d", stack_state.chan, (uint16_t)(stack_call_buffer[0] >> 16));
				*((uint32_t *)&stack_state.lai) = stack_call_buffer[0];
				if (((stack_state.chan & 0x0fff) != 0x0fff) || (is_enter_instruction_to_conv() == 0))
				{
					nvq_tx_freq_index = 0xffff;	// 0xffff:��ʾ�ص��غ�Ƶ�����Ѿ���Ƶ�ɹ�
 					stack_set_ctrl_channel(0);
				}
			}
			else
			{
				stack_state.lai = stack_call_buffer[0] & 0xffff;
			}
			break;

		case STACK_RETURN_TYPE_NULL:						// ����ɨ��Ƶ��: MODE	GRP0		VTYPE CR0 CR1����.CR7
//			ctype2 = (uint16_t)get_interphone_mode(stack_para_aux2 >> 23);
//			if ((uint8_t)ctype2 != interphone_mode)			// ָ���
//				vlog_v("stack","[inst2conv]mode(%d->%d):GRP0=%08x", interphone_mode, ctype2, stack_para_aux2);
			if (is_enter_instruction_to_conv() == 0)
				inst_conv->id = stack_para_aux2;
			if (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING)
			{
#if (TEST_STACK_BACKGROUND_TSCC > 1)
				if (test_back_tscc)
				{
					set_back_tscc_start(test_back_tscc & 0x8000 ? 1 : 0, test_back_tscc & 0x7fff);
					test_back_tscc = 0;
				}
				else
#endif
				{
					stack_background_tscc_handle((BACK_TSCC_STRUCT *)stack_call_buffer);
				}
			}
			break;

		case STACK_RETURN_TYPE_UPDATE_CTRL:						// ���¿����ŵ�: PD		CH0 CH1 ����CH63
			save_neighbour_base_info(stack_call_buffer);
		case STACK_RETURN_TYPE_POWER_DOWN:
			system_power_down(0xff);
			break;

		case STACK_RETURN_TYPE_PDU_DATA:
			push_pdu_data_to_app((uint8_t *)stack_call_buffer);
			break;

		default:
			break;
	}
}

#define NEW_STACK_PTT_DEFINE_PTT		0x40
#define NEW_STACK_PTT_DEFINE_3PTT		0x00/*20220507: ��PTT����ʱR1.6���¶���Ϊ��=1��PTT/PTT2���У�=0��PTT3����*/
void stack_call_sync_to_slot(void)
{
	static uint8_t ambience_listening_end = 0;
	uint8_t code;

	if (dev_is_base())
	{
		sync_call_stack_bak = 0;
		if (sync_call_stack)
		{
			if (sync_call_stack & SYNC_CALL_STACK_PTT_PRESSED)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_PTT_PRESSED;
#ifndef STACK_CALL_TYPE_PTT3_ACTIVE
				stack_para_aux1 = 1 | NEW_STACK_PTT_DEFINE_PTT;
#else
				stack_para_aux1 = 1;
#endif
				ptt_active(STACK_CALL_TYPE_PTT_ACTIVE);
			}
			else if (sync_call_stack & SYNC_CALL_STACK_PTT_RELEASED)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_PTT_RELEASED;
				stack_para_aux1 = 0;
				CALL_STACK_PROCESS(STACK_CALL_TYPE_PTT_ACTIVE);
			}
			else if (sync_call_stack & SYNC_CALL_STACK_SWITCH_WATCH)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_SWITCH_WATCH;
				stack_set_watch();
			}
		}
	}
	else // dev_is_base
	{
		if (sync_call_stack_bak)
		{
			if (stack_state.cs & (STACK_CS_SHORT_SEARCHING | STACK_CS_LONG_SEARCHING))		// reset
			{
				sync_call_stack_bak = 0;
				ambience_listening_end = 0;
			}
			else if (ambience_listening_end == 0)
			{
				if ((stack_state.cs & (STACK_CS_SLOT_SYNC | STACK_CS_SYSCODE_MATCH)) == 0)	// waiting ambience listening end
					ambience_listening_end = 1;
			}
			else
			{
				if (((stack_state.cs & (STACK_CS_LOGIN_THE_BASE | STACK_CS_SLOT_SYNC | STACK_CS_SYSCODE_MATCH)) ==
					(STACK_CS_LOGIN_THE_BASE | STACK_CS_SLOT_SYNC | STACK_CS_SYSCODE_MATCH)) &&
					((sync_call_stack & (SYNC_CALL_STACK_CANCEL_PRESSED | SYNC_CALL_STACK_CANCEL_RELEASED)) == 0))		// ready to transmit
				{
					sync_call_stack |= sync_call_stack_bak;
					sync_call_stack_bak = 0;
					ambience_listening_end = 0;
//					vlog_v("stack","to trans:%08x", sync_call_stack);
				}
//				else
//				{
//					vlog_v("stack","wait cs:%08x(%08x)", stack_state.cs, sync_call_stack);
//				}
			}
		}

		if (sync_call_stack)
		{
			if (sync_call_stack & SYNC_CALL_STACK_GPS_UPDATE)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_GPS_UPDATE;
				memcpy(stack_call_buffer, &rmc_data, 13);	// sizeof(GPS_STRUCT_TYPEDEF)
				*((uint8_t *)stack_call_buffer + 13) = get_battery_power_level();
				CALL_STACK_PROCESS(STACK_CALL_TYPE_GPS_UPDATE);
			}
			else if (sync_call_stack & SYNC_CALL_STACK_SWITCH_CTRL)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_SWITCH_CTRL;
				if ((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) || (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING))
					stack_set_ctrl_channel(1);
			}
			else if (sync_call_stack & SYNC_CALL_STACK_SWITCH_WATCH)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_SWITCH_WATCH;
				stack_set_watch();
			}
			else if (sync_call_stack & SYNC_CALL_STACK_POWER_DOWN)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_POWER_DOWN;
				stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_POWERDOWN));
			}
//			else if (sync_call_stack & SYNC_CALL_STACK_SWITCH_FREQ)
//			{
//				sync_call_stack &= ~SYNC_CALL_STACK_SWITCH_FREQ;
//				stack_set_ctrl_channel(0);
//			}
			else if (sync_call_stack & SYNC_CALL_STACK_CANCEL_PRESSED)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_CANCEL_PRESSED;
				stack_para_aux1 = 4;
				CALL_STACK_PROCESS(STACK_CALL_TYPE_CANCEL_ACTIVE);
			}
			else if (sync_call_stack & SYNC_CALL_STACK_CANCEL_RELEASED)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_CANCEL_RELEASED;
				stack_para_aux1 = 0;
				CALL_STACK_PROCESS(STACK_CALL_TYPE_CANCEL_ACTIVE);
			}
#ifndef STACK_CALL_TYPE_PTT3_ACTIVE
			else if (sync_call_stack & (SYNC_CALL_STACK_PTT_PRESSED | SYNC_CALL_STACK_PTT3_PRESSED))
			{
  #ifdef DEBUG_VOICE_SEQUENCE
				vlog_v("stack","[%d]PTT act", slot_rising_edge_int_times);
  #endif
				stack_para_aux1 = 1 | ((sync_call_stack & SYNC_CALL_STACK_PTT3_PRESSED) ? NEW_STACK_PTT_DEFINE_3PTT : NEW_STACK_PTT_DEFINE_PTT);
				sync_call_stack &= ~(SYNC_CALL_STACK_PTT_PRESSED | SYNC_CALL_STACK_PTT3_PRESSED);
				ptt_active(STACK_CALL_TYPE_PTT_ACTIVE);
			}
#else
			else if (sync_call_stack & SYNC_CALL_STACK_PTT_PRESSED)
			{
  #ifdef DEBUG_VOICE_SEQUENCE
				vlog_v("stack","[%d]PTT act", slot_rising_edge_int_times);
  #endif
				stack_para_aux1 = 1;
				sync_call_stack &= ~SYNC_CALL_STACK_PTT_PRESSED;
				ptt_active(STACK_CALL_TYPE_PTT_ACTIVE);
			}
			else if (sync_call_stack & SYNC_CALL_STACK_PTT3_PRESSED)
			{
  #ifdef DEBUG_VOICE_SEQUENCE
				vlog_v("stack","[%d]3PTT act", slot_rising_edge_int_times);
  #endif
				stack_para_aux1 = 1;
				sync_call_stack &= ~SYNC_CALL_STACK_PTT3_PRESSED;
				ptt_active(STACK_CALL_TYPE_PTT3_ACTIVE);
			}
#endif
			else if (sync_call_stack & SYNC_CALL_STACK_ALARM_PRESSED)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_ALARM_PRESSED;
				code = (uint8_t)maintain_stack_parameter(PARA_OPERATE_READ, STACK_EMERGENCY_CALL_ID, send_message_buffer + 1);
				if (interphone_mode_is_dmr_conv())
				{
					code = get_dmr_extended_opcode(DMR_EXTENDED_FUNC_E_ALARM);
					send_message_asynchronous(send_message_buffer[1], MESSAGE_TYPE_DMR_EXT_FUNC, 0, &code);
				}
				else
				{
					if (code)	// send status code to emergency id
					{
						if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
							send_single_call_ring_frame(is_individual_id(interphone_mode, send_message_buffer[1]), send_message_buffer[1], MISC_OP_TYPE_STATUS, code);
						else
							send_status_msg_auto(send_message_buffer[1], (get_log_in_out_paras(2) ? SEND_STATUS_WITH_GPS : 0) | (uint32_t)code);
					}
					else
					{
#ifndef STACK_CALL_TYPE_PTT3_ACTIVE
						stack_para_aux1 = 1 | NEW_STACK_PTT_DEFINE_PTT;
#else
						stack_para_aux1 = 1;
#endif
						send_message_buffer[0] = CALL_PTT_ASYNC_CALLING;
						send_message_buffer[1] |= (uint32_t)USER_ID_EMERGENCY << 24;
						send_message_buffer[2] = 0;
						ptt_active(STACK_CALL_TYPE_PTT_ACTIVE);
					}
				}
			}
			else if (sync_call_stack & SYNC_CALL_STACK_PTT_RELEASED)
			{
#ifdef DEBUG_VOICE_SEQUENCE
				vlog_v("stack","[%d]PTT rel", slot_rising_edge_int_times);
#endif
				sync_call_stack &= ~SYNC_CALL_STACK_PTT_RELEASED;
				stack_para_aux1 = 0;
				CALL_STACK_PROCESS(STACK_CALL_TYPE_PTT_ACTIVE);
			}
			else if (sync_call_stack & SYNC_CALL_STACK_PTT2_PRESSED)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_PTT2_PRESSED;
#ifndef STACK_CALL_TYPE_PTT3_ACTIVE
				stack_para_aux1 = 2 | NEW_STACK_PTT_DEFINE_PTT;
#else
				stack_para_aux1 = 2;
#endif
				ptt_active(STACK_CALL_TYPE_PTT2_ACTIVE);
			}
			else if (sync_call_stack & SYNC_CALL_STACK_PTT2_RELEASED)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_PTT2_RELEASED;
				stack_para_aux1 = 0;
				CALL_STACK_PROCESS(STACK_CALL_TYPE_PTT2_ACTIVE);
			}
#ifdef STACK_CALL_TYPE_PTT3_ACTIVE
			else if (sync_call_stack & SYNC_CALL_STACK_PTT3_RELEASED)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_PTT3_RELEASED;
				stack_para_aux1 = 0;
				CALL_STACK_PROCESS(STACK_CALL_TYPE_PTT3_ACTIVE);
			}
#endif
			else if (sync_call_stack & SYNC_CALL_STACK_IOP_SCAN_GROUP)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_IOP_SCAN_GROUP;
				stack_set_watch_iop_scan_group(iop_scan_channel_next(0));
			}
			else if (sync_call_stack & SYNC_CALL_STACK_IOP_SCAN_CHAN)
			{
				sync_call_stack &= ~SYNC_CALL_STACK_IOP_SCAN_CHAN;
				stack_set_watch_iop_scan_channel(iop_scan_channel_next(0));
			}
		}
	} // dev_is_base
}


#ifdef TEST_STACK_CALLING
void test_stack_call(void)
{
	uint8_t *ptr8;
	uint16_t *ptr16;
	uint32_t *ptr32;

	ptr8 = (uint8_t *)stack_call_buffer;
	*ptr8++ = 0x11;
	*ptr8++ = 0x22;
	ptr16 = (uint16_t *)ptr8;
	*ptr16++ = 0x3344;
	*ptr16++ = 0x5566;
	*ptr16++ = 0x7788;
	ptr32 = (uint32_t *)ptr16;
	*ptr32++ = 0x99aabbcc;
	*ptr32++ = 0xddeeff00;
	*ptr32++ = 0x11223344;
	ptr16 = (uint16_t *)ptr32;
	*ptr16++ = 0x5566;
	*ptr16++ = 0x7788;

	stack_para_aux1 = 0x1234;
	stack_para_aux2 = 0x5678abcd;
	CALL_STACK_PROCESS(0xef);
}
#endif

/*
��ʼ��Э��ջ������stack_initial��ʱ��
��1����վ��p_stack_parametersָ��g_runtime_inst_xvbase->stack_xv��
��2���նˣ�����������վģʽ����p_stack_parametersָ��g_static_ptr->stack_pdt��
��3�������豸��g_runtime_inst_xvbase��ָ��runtime_inst_xvbase_4KB.xvbase_1028������еĻ�����
��4�����ݴ�������g_runtime_inst_xvbase������Чʱ��801�Ὣg_static_ptr->stack_pdt���ݸ������������豸��g_runtime_inst_xvbase���㣻
	������ж�����豸�Ǵ�ת�����豸��������վ����������վģʽ���նˣ�������conts�����Ƿ���12�����ϸ�0���еĻ���g_static_ptr->stack_zzwpro.conts���ݸ�������

xvbase��д����ģʽʱ����������g_runtime_inst_xvbase->stack_mode��
��1����վ��ȡg_runtime_inst_xvbase->stack_modeʹ�ã�
��2���նˣ�����������վģʽ����ȡg_runtime_inst.runtime_paras.stack_mode[GET_STACK_MODE_INDEX()]ʹ�ã�

xvbase��д�������ʱ����������g_runtime_inst_xvbase->stack_xv.conts��
��1����վ��������վģʽ���նˣ�ȡp_stack_parameters->conts��ʵ�ʾ���g_runtime_inst_xvbase->stack_xv.conts��ʹ�ã�
��2���նˣ�û������վģʽ������������g_static_ptr->stack_zzwpro��Ч��ȡg_static_ptr->stack_zzwpro.contsʹ�ã�
	����ȡp_stack_parameters->contsʹ��
*/
#ifdef FORWARDING_LOGIC_ENABLE
void init_forwarding_table(void);
#endif
void stack_initial(void)
{
	uint8_t i, tmp[24] = "                    ";
#ifdef TEST_STACK_CALLING
	test_stack_call();
#endif
	if (dump_level != 0xff)
	{
		for (i = 0; i < 40; i++)
			put_data(UART_DEBUG_INDEX, tmp, p_strlen(tmp));
	}
	vlog_v("stack","Calling stack program...");
	CALL_STACK_PROCESS(STACK_CALL_TYPE_INITIAL);
	stack_set_base_parameters();
	stack_set_network_parameters();
//	CALL_STACK_PROCESS(STACK_CALL_TYPE_RESET);
	sys_delay(50);	// for output all stack data if enable at the first(and use dma send)
	stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_READ_VERSION));

	memset(&stack_state, 0, sizeof(STACK_STATE_STRUCT));
	memset(&stack_stage, 0, sizeof(STACK_STAGE_STRUCT));
	memset(&stack_back_tscc, 0, sizeof(BACK_TSCC_STRUCT));
	memset(prev_pos_save, 0, 9);
	memset(&v_dispatch_poll, 0, sizeof(V_DispatchPoll));
	v_dispatch_poll.poll_counter = 0xff;
#ifdef FORWARDING_LOGIC_ENABLE
	init_forwarding_table();
#endif
	g_runtime_inst_xvbase->stack_xv.reserved32[1] |= (uint32_t)USER_ID_INDIVIDUAL << 24;	// 0x10107ee1; // 328100
	if ((g_runtime_inst.act_report_conf.id & 0x00ffffff) == 0)
		g_runtime_inst.act_report_conf.id = get_log_in_out_paras(0) ? get_log_in_out_id() : 0;
	g_runtime_inst.act_report_conf.reserved = 3;	// active report battery when level is low than 3
	stack_back_tscc.tscc_fsm = 0xff;

	loggin_the_base = 0;

//	set_module_init_done_flag(MODULE_INIT_DONE_STACK);	// set it after init vocoder
	clear_zzw_last_speaker_data();
}

void reset_stack_with_disable_int(void)
{
	dsp_interrupt_config((0 << 4) | 3);
	save_user_data_to_flash(0xf0);
	stack_initial();
	stack_set_watch();
	dsp_interrupt_config((3 << 4) | 3);
//	set_sync_call_stack_type(SYNC_CALL_STACK_SWITCH_CTRL);
}

void re_search_ctrl_channel(void)
{
	if ((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) || (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING))
	{
		GL_LCD_TipDraw(0, maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? (uint8_t *)"Searching..." : (uint8_t *)"���������ŵ�...", 0, 1000);
		reset_stack_with_disable_int();
	}
}

GROUP_CONTROL_LIST* ctrl_table_has_extend_page(GROUP_CONTROL_LIST *ctrl_list)
{
	uint8_t n;
	uint32_t flag[2];
	GROUP_CONTROL_LIST *ret;

	ret = (GROUP_CONTROL_LIST *)0;
	if ((ctrl_list->ctrl_table[CTRL_TABLE_TOTAL_ITEM - 2] == CTRL_TABLE_TOTAL_ITEM_EXT1) &&
		((ctrl_list->ctrl_table[CTRL_TABLE_TOTAL_ITEM - 1] & 0xFF00FFFF) == CTRL_TABLE_TOTAL_ITEM_EXT2))
	{
		n = (ctrl_list->ctrl_table[CTRL_TABLE_TOTAL_ITEM - 1] >> 16) & 0xff;
		if (n < MAX_GROUPS_OF_CTRL)
		{
			ret = &(g_group_books->grp_ctrl_list[n]);
			memcpy(flag, &ret->name[8], 8);
			if (!((ret->name[0] == 0) && (((flag[1] >> 16) & 0xff) == GET_CTRL_GROUP_INDEX(ctrl_list)) &&
				(flag[0] == CTRL_TABLE_TOTAL_ITEM_EXT1) &&
				((flag[1] & 0xFF00FFFF) == CTRL_TABLE_TOTAL_ITEM_EXT2)))
				ret = (GROUP_CONTROL_LIST *)0;
		}
	}

	return ret;
}

uint16_t get_ctrl_table_items(uint8_t *ctrl_table_name, uint16_t n_get, uint32_t *ctrl_table)	// n_get: <MAX:get one item; =0xffff:get all; =0xfffe:get none(just get total&page_num); =0xfffx(x=0-3):get this page; else bit7=1:assigned
{
	uint16_t num[CTRL_TABLE_TOTAL_PAGE], num_air_conf, num_ext, max_num_at_page;
	GROUP_CONTROL_LIST *ctrl_list[CTRL_TABLE_TOTAL_PAGE + 1];

	ctrl_list[0] = get_ctrl_table_list();
	if (ctrl_list[0] == 0)
	{
		if (ctrl_table_name)
			ctrl_table_name[0] = 0;

		if (n_get == 0xffff)
		{
			memset(ctrl_table, 0, CTRL_TABLE_TOTAL_ITEM_MAX * sizeof(uint32_t));
		}
		else if (n_get < CTRL_TABLE_TOTAL_ITEM_MAX)
		{
			ctrl_table[0] = 0;
		}
		else if ((n_get >= 0xfff0) && (n_get < 0xfff0 + CTRL_TABLE_TOTAL_PAGE))
		{
			memset(ctrl_table, 0, CTRL_TABLE_TOTAL_ITEM * sizeof(uint32_t));
		}
		else if (n_get == 0xfffe)
		{
			if (ctrl_table)
				memcpy(ctrl_table, num, sizeof(num));
		}
		else
		{
			ctrl_table[0] = n_get & 0x7fff;
		}

		return 0;
	}

	if (ctrl_table_name)
		p_strcpy(ctrl_table_name, ctrl_list[0]->name);

	num_ext = 0;
	memset(num, 0, sizeof(num));
	while (1)
	{
		ctrl_list[num_ext + 1] = num_ext < (CTRL_TABLE_TOTAL_PAGE - 1) ? ctrl_table_has_extend_page(ctrl_list[num_ext]) : (GROUP_CONTROL_LIST *)0;
		max_num_at_page = ctrl_list[num_ext + 1] ? (CTRL_TABLE_TOTAL_ITEM - 2) : CTRL_TABLE_TOTAL_ITEM;
		for (; num[num_ext] < max_num_at_page; num[num_ext]++)
		{
			if ((ctrl_list[num_ext]->ctrl_table[num[num_ext]] & 0x00000fff) == 0)
				break;
		}

		if ((num[num_ext] >= max_num_at_page) && ctrl_list[num_ext + 1])
			num_ext++;
		else
			break;
	}

	num_air_conf = (uint8_t)get_air_ctrl_table_count();

	if (n_get == 0xffff)
	{
		for (num_ext = 0, max_num_at_page = 0; num[num_ext] && (num_ext < CTRL_TABLE_TOTAL_PAGE); num_ext++)
		{
			memcpy(ctrl_table + max_num_at_page, ctrl_list[num_ext]->ctrl_table, num[num_ext] * sizeof(uint32_t));
			max_num_at_page += num[num_ext];
		}
		num_air_conf = ((max_num_at_page + num_air_conf) <= CTRL_TABLE_TOTAL_ITEM_MAX) ? num_air_conf : (CTRL_TABLE_TOTAL_ITEM_MAX - max_num_at_page);
		memcpy(ctrl_table + max_num_at_page, g_runtime_inst.air_config.air_conf_ctrl, num_air_conf * sizeof(uint32_t));
		memset(ctrl_table + max_num_at_page + num_air_conf, 0, (CTRL_TABLE_TOTAL_ITEM_MAX - max_num_at_page - num_air_conf) * sizeof(uint32_t));
	}
	else if (n_get < CTRL_TABLE_TOTAL_ITEM_MAX)
	{
		ctrl_table[0] = 0;
		for (num_ext = 0, max_num_at_page = 0; num[num_ext] && (num_ext < CTRL_TABLE_TOTAL_PAGE); num_ext++)
		{
			if ((n_get >= max_num_at_page) && (n_get < max_num_at_page + num[num_ext]))
				ctrl_table[0] = ctrl_list[num_ext]->ctrl_table[n_get - max_num_at_page];
			max_num_at_page += num[num_ext];
		}
		num_air_conf = ((max_num_at_page + num_air_conf) <= CTRL_TABLE_TOTAL_ITEM_MAX) ? num_air_conf : (CTRL_TABLE_TOTAL_ITEM_MAX - max_num_at_page);
		if ((n_get >= max_num_at_page) && (n_get < max_num_at_page + num_air_conf))
			ctrl_table[0] = g_runtime_inst.air_config.air_conf_ctrl[n_get - max_num_at_page];
	}
	else if ((n_get >= 0xfff0) && (n_get < 0xfff0 + CTRL_TABLE_TOTAL_PAGE))
	{
		num_ext = n_get & 0x07;
		if (num[num_ext])
			memcpy(ctrl_table, ctrl_list[num_ext]->ctrl_table, num[num_ext] * sizeof(uint32_t));
		memset(ctrl_table + num[num_ext], 0, (CTRL_TABLE_TOTAL_ITEM - num[num_ext]) * sizeof(uint32_t));
		max_num_at_page = num[num_ext];		// for return the chan number of this page
		num_air_conf = 0;
	}
	else
	{
		for (num_ext = 0, max_num_at_page = 0; num[num_ext] && (num_ext < CTRL_TABLE_TOTAL_PAGE); num_ext++)
			max_num_at_page += num[num_ext];
		num_air_conf = ((max_num_at_page + num_air_conf) <= CTRL_TABLE_TOTAL_ITEM_MAX) ? num_air_conf : (CTRL_TABLE_TOTAL_ITEM_MAX - max_num_at_page);
		if (n_get == 0xfffe)
		{
			if (ctrl_table)
				memcpy(ctrl_table, num, sizeof(num));
		}
		else
		{
			ctrl_table[0] = n_get & 0x7fff;
		}
	}

	return max_num_at_page + num_air_conf;
}

uint8_t *get_stack_conts_address(void)
{
	if (dev_is_base())
	{
		return (uint8_t *)p_stack_parameters->conts;			// base: set p_stack_parameters at set_xvbase_stack(&g_static_ptr->stack_pdt), p_stack_parameters->conts point to g_runtime_inst_xvbase->stack_xv.conts really
	}
	else if (dev_have_base_feature())
	{
		return (uint8_t *)g_runtime_inst_xvbase->stack_xv.conts;
	}
	else // dev_is_base
	{
		if ((interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) && (g_static_ptr->stack_zzwpro.para_valid_flag == USER_FLASH_SAVE_FLAG))
			return (uint8_t *)g_static_ptr->stack_zzwpro.conts;
		else
			return (uint8_t *)p_stack_parameters->conts;
	} // dev_is_base
}

uint16_t maintain_stack_parameter(uint8_t op, uint8_t type, void *content)	// op==0:read; else:write
{
	uint16_t ret, tmp;

	ret = 0;
	switch (type)
	{
		case STACK_PARA_ESN:
			if (op == 0)
				ret = get_esn_from_flash((uint32_t)content);
			break;
		case STACK_PARA_ID:
			if (op == 0)
			{
				ret = 4;
				if ((interphone_mode >= INTERPHONE_MODE_ZZW_ADHOC) && (dev_is_base() == 0))	// ��վҪʹ��дƵ���ã�g_runtime_inst_xvbase->stack_xv�������ڻ�վp_stack_parameters�Ѿ���ָ������
					*((uint32_t *)content) = p_zzw_parameters->id | ((uint32_t)USER_ID_INDIVIDUAL << 24);
				else
					*((uint32_t *)content) = p_stack_parameters->id | ((uint32_t)USER_ID_INDIVIDUAL << 24);
			}
//			*((uint32_t *)content) = 0x108CCED1;	// just for testing zhejiang code, id=57720177
			break;
		case STACK_PARA_CONST:
			if (op == 0)
			{
				ret = CONTS_LENGTH;
				memcpy(content, get_stack_conts_address(), ret);
			}
			break;
		case STACK_PARA_CTIME:
			if (op == 0)
			{
				ret = 2;
				*((uint16_t *)content) = p_stack_parameters->ctime;
			}
			break;
		case STACK_PARA_NID:
			if (op == 0)
			{
				ret = 2;
				*((uint16_t *)content) = p_stack_parameters->nid;
			}
			break;
		case STACK_PARA_NIDM:
			if (op == 0)
			{
				ret = 2;
				*((uint16_t *)content) = p_stack_parameters->nidm;
			}
			break;
		case STACK_PARA_SCANNING:
			if (op == 0)
			{
				ret = 4;
				*((uint16_t *)content) = p_stack_parameters->chl;
				*((uint16_t *)content + 1) = p_stack_parameters->chh;
			}
			break;
		case STACK_PARA_WATCH_ID:
			tmp = get_books_item_count() + get_air_books_item_count();
			if (op)
			{
				if (*((uint16_t *)content) < tmp)
				{
					ret = 2;
					g_runtime_inst.runtime_paras.grp0[GET_VARIOUS_MODE_INDEX()] = *((uint16_t *)content);
				}
				else
				{
					g_runtime_inst.runtime_paras.grp0[GET_VARIOUS_MODE_INDEX()] = 0;
				}
			}
			else
			{
				*((uint16_t *)content) = g_runtime_inst.runtime_paras.grp0[GET_VARIOUS_MODE_INDEX()];
				if (*((uint16_t *)content) >= tmp)
				{
					*((uint16_t *)content) = 0;
					g_runtime_inst.runtime_paras.grp0[GET_VARIOUS_MODE_INDEX()] = 0;	// re-write the index
				}
				else
				{
					ret = 2;
				}
			}
			break;
		case STACK_PARA_CTRL_INDEX:
//			ret = get_ctrl_table_items(0, 0xfffe, 0);
			tmp = GET_DIGITAL_ANALOG_MODE_INDEX() - 1;
			if (op)
			{
//				if ((*((uint16_t *)content) < ret) ||
//					(maintain_setup_parameter(PARA_OPERATE_READ, PROJECT_APP_PERMIT, 0) || get_test_mode_flag()))
				{
					ret = 2;
					g_runtime_inst.runtime_paras.ctrl_index[tmp] = *((uint16_t *)content);
				}
//				else
//				{
//					ret = 0;
//					g_runtime_inst.runtime_paras.ctrl_index[tmp] = 0;
//				}
			}
			else
			{
//				if ((g_runtime_inst.runtime_paras.ctrl_index[tmp] < ret) ||
//					(maintain_setup_parameter(PARA_OPERATE_READ, PROJECT_APP_PERMIT, 0) || get_test_mode_flag()))
				{
					ret = 2;
					*((uint16_t *)content) = g_runtime_inst.runtime_paras.ctrl_index[tmp];
				}
//				else
//				{
//					ret = 0;
//					*((uint16_t *)content) = 0;
//				}
			}
			break;
		case STACK_PARA_COLOR_CODE:
			if (op == 0)
			{
				ret = 2;
//				*((uint16_t *)content) = (p_stack_parameters->color_code >> 12) & 0x000f;
				*((uint16_t *)content) = p_stack_parameters->color_code;
			}
			break;
		case STACK_PARA_RCODE_MODE:
			if (op == 0)
			{
				ret = 2;
				*((uint16_t *)content) = p_stack_parameters->rcode;
			}
			break;
		case STACK_PARA_MPT1343_FIN:
		case STACK_PARA_MPT1343_FGN:
			if (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING)
			{
				if (op == 0)
				{
					ret = 2;
					if (type == STACK_PARA_MPT1343_FIN)
						*((uint16_t *)content) = p_stack_parameters->province_1343fin;
					else
						*((uint16_t *)content) = p_stack_parameters->ministry_1343fgn;
				}
			}
			else
			{
				if (op == 0)
					*((uint16_t *)content) = 0;
			}
			break;
		case STACK_PARA_PRIOR_CTRL_CHAN:
			tmp = get_ctrl_table_items(0, 0xfffe, 0);
			if (op)
			{
				if (*((uint16_t *)content) < tmp)
				{
					ret = 2;
					g_runtime_inst.runtime_paras.priro_ctrl[GET_DIGITAL_ANALOG_MODE_INDEX() - 1] = *((uint16_t *)content);
				}
			}
			else
			{
				*((uint16_t *)content) = g_runtime_inst.runtime_paras.priro_ctrl[GET_DIGITAL_ANALOG_MODE_INDEX() - 1];
				if (*((uint16_t *)content) < tmp)
					ret = 2;
				else
					*((uint16_t *)content) = 0;
			}
			break;
		case STACK_EMERGENCY_CALL_ID:
			if (op == 0)
			{
//				ret = 4;
				ret = (uint8_t)(p_stack_parameters->stack_emergency_id >> 24);
				ret = GET_STATUS_CODE_7bit(ret);
				*((uint32_t *)content) = p_stack_parameters->stack_emergency_id & USER_ID_CMP_MASK;
			}
			break;
		case STACK_PROVINCE_AREA_CODE:
		case STACK_MINISTRY_AREA_CODE:
			if (interphone_mode == INTERPHONE_MODE_PDT_TRUNKING)
			{
				if (op == 0)
				{
					ret = 2;
					if (type == STACK_PROVINCE_AREA_CODE)
						*((uint16_t *)content) = p_stack_parameters->province_1343fin;
					else
						*((uint16_t *)content) = p_stack_parameters->ministry_1343fgn;
//					if ((*((uint16_t *)content) < 328) || (*((uint16_t *)content) > 806))
//						*((uint16_t *)content) = 328;
				}
			}
			else
			{
				if (op == 0)
					*((uint16_t *)content) = 0;
			}
			break;
		case STACK_ZZW_2PTT_CALL_ID:
			if (op == 0)
			{
				ret = 4;
				*((uint32_t *)content) = p_zzw_parameters->id_2ptt;
			}
			break;
		case STACK_ZZW_ADHOC_WIN:
			if (op == 0)
			{
				ret = 1;
				*((uint8_t *)content) = p_zzw_parameters->adhoc_win;
			}
			break;
		case STACK_ZZW_ADHOC_RCMAX:
			if (op == 0)
			{
				ret = 1;
				*((uint8_t *)content) = p_zzw_parameters->rc_max;
			}
			break;

		default:
			break;
	}

	return ret;
}

uint32_t get_stack_work_mode(void)
{
	if (dev_is_base())
	{
		return g_runtime_inst_xvbase->stack_mode;
	}
	else // dev_is_base
	{
		return g_runtime_inst.runtime_paras.stack_mode[GET_STACK_MODE_INDEX()];
	} // dev_is_base
}

void set_stack_work_mode(uint32_t mode)
{
	if (dev_is_base())
	{
		if (mode == 0)
		{
			g_runtime_inst_xvbase->stack_mode = STACK_MODE_SET_PDT_DMR | STACK_MODE_GPS_PULLUP | STACK_MODE_PTT_AUTH | STACK_MODE_PATCS |
				STACK_MODE_SLOT2_VOICE_TX | STACK_MODE_SLOT2_VOICE_RX |
				STACK_MODE_GROUPTALK_PERMIT | STACK_MODE_INDIVITALK_PERMIT | STACK_MODE_MESSAGE_TX_PERMIT |
				STACK_MODE_STATE_TX_PERMIT | STACK_MODE_PBXTALK_PERMIT | STACK_MODE_EMERGTALK_PERMIT | STACK_MODE_GROUP_ATTACH;
		}
		else
		{
			g_runtime_inst_xvbase->stack_mode = mode;
		}
	}
	else // dev_is_base
	{
		if (mode == 0)
		{
			g_runtime_inst.runtime_paras.stack_mode[0] = STACK_MODE_SET_PDT_DMR | STACK_MODE_GPS_PULLUP | STACK_MODE_PTT_AUTH | STACK_MODE_PATCS |
				STACK_MODE_SLOT2_VOICE_TX | STACK_MODE_SLOT2_VOICE_RX |
				STACK_MODE_GROUPTALK_PERMIT | STACK_MODE_INDIVITALK_PERMIT | STACK_MODE_MESSAGE_TX_PERMIT |
				STACK_MODE_STATE_TX_PERMIT | STACK_MODE_PBXTALK_PERMIT | STACK_MODE_EMERGTALK_PERMIT | STACK_MODE_GROUP_ATTACH;
			g_runtime_inst.runtime_paras.stack_mode[1] = g_runtime_inst.runtime_paras.stack_mode[0];
			g_runtime_inst.runtime_paras.stack_mode[2] = STACK_MODE_GPS_PULLUP | STACK_MODE_PTT_AUTH | STACK_MODE_PATCS | STACK_MODE_GROUPTALK_PERMIT |
				STACK_MODE_INDIVITALK_PERMIT | STACK_MODE_MESSAGE_TX_PERMIT | STACK_MODE_STATE_TX_PERMIT |
				STACK_MODE_EMERGTALK_PERMIT | STACK_MODE_CPS_CODE_MODE | STACK_MODE_MPT_INTERPHONE;
		}
		else
		{
			g_runtime_inst.runtime_paras.stack_mode[GET_STACK_MODE_INDEX()] = mode;
		}
	} // dev_is_base
}

void set_stack_signaling_monitor(uint8_t flag)
{
	uint8_t flag_int = flag;
	uint32_t mode;

	if (interphone_mode != INTERPHONE_MODE_MPT_CONV)
	{
		mode = get_stack_work_mode();
stack_signaling_monitor_reset:
		if (flag_int == 0)
		{
			set_stack_work_mode(mode & (~STACK_MODE_SIGNAL_MONITOR));
			set_output_stack_dump_data(0);
		}
		else if (flag_int == 1)
		{
			set_output_stack_dump_data(1);
			set_stack_work_mode(mode | STACK_MODE_SIGNAL_MONITOR);
		}
		else
		{
			flag_int = (mode & STACK_MODE_SIGNAL_MONITOR) ? 0 : 1;
			goto stack_signaling_monitor_reset;
		}
		reset_stack_with_disable_int();
	}
}

uint8_t get_stack_signaling_monitor(void)
{
	return (get_stack_work_mode() & STACK_MODE_SIGNAL_MONITOR) ? 1 : 0;
}

uint16_t get_conv_base_freq(void)				// �����׼Ƶ��
{
	if (g_static_ptr->stack_mpt.freq_base_admit && (g_static_ptr->stack_mpt.freq_base_admit != 0xffff) &&
			((uint16_t)g_static_ptr->stack_mpt.freq_gap_admit != 0xffff))
	{
		return g_static_ptr->stack_mpt.freq_base_admit;
	}
	else
	{
		return 0;
	}
}

uint16_t get_admit_base_freq(void)				// ���̻�׼Ƶ��
{
	if (p_stack_parameters->freq_base_admit && (p_stack_parameters->freq_base_admit != 0xffff) &&
			((uint16_t)p_stack_parameters->freq_gap_admit != 0xffff))
	{
		return p_stack_parameters->freq_base_admit;
	}
	else
	{
		return 0;
	}
}

void set_freq_cal_paras(uint8_t flag)		// flag: 0-normal; else-for admittest reinit
{
	uint16_t base;
	int16_t gap;

	if (flag)
	{
		if (get_admit_base_freq())
		{
			base = p_stack_parameters->freq_base_admit;
			gap = p_stack_parameters->freq_gap_admit;
		}
		else
		{
			goto get_normal_base_freq;
		}
	}
	else
	{
get_normal_base_freq:
		if (dev_is_base())
		{
			goto set_trunk_base_freq;
		}
		else // dev_is_base
		{
			if (((interphone_mode <= INTERPHONE_MODE_PDT_CONV) || (interphone_mode >= INTERPHONE_MODE_ZZW_ADHOC)) && get_conv_base_freq())
			{
				base = g_static_ptr->stack_mpt.freq_base_admit;
				gap = g_static_ptr->stack_mpt.freq_gap_admit;
			}
			else
			{
set_trunk_base_freq:
				base = p_stack_parameters->freq_base_para;
				gap = p_stack_parameters->freq_gap_para;
			}
		} // dev_is_base
	}
	freq_base = (float)base / 80;
	freq_step = (float)p_stack_parameters->freq_step_para / 80;
	freq_gap = (float)gap / 80;

	chan_start_index = base + 1 * p_stack_parameters->freq_step_para;
	chan_end_index = base + 800 * p_stack_parameters->freq_step_para;
}

float get_freq_base(void)
{
	return freq_base;
}

void set_freq_base(float base)
{
	freq_base = base;
}


/*
	�ƶ��ն˵�Ƶ�ʼ��㷽����

	======== PDT��׼ ========
	F = 358.0 + chan * 0.0125������chanΪ�ŵ��ţ���λΪMHz
	��1�����1<=chan<=240�����շ�ͬƵ�ҵ���F��
	��2�����241<=chan<=640������Ƶ��=F����Ƶ��=F-10.0

	======== MPT��׼ ========
	1. CPS
	F = 358.0 + chan * 0.0125������chanΪ�ŵ��ţ�ͬһϵͳ��ֻ��ȡ������ż����һ�㶼��ȡ����������λΪMHz
	��1�����chan<=240��Ŀǰû�ҵ���׼����ʱ�������շ�ͬƵ�ҵ���F��
	��2�����chan>=241������Ƶ��=F����Ƶ��=F-10.0

	2. 1343
	F = 361.0 + chan * 0.025������chanΪ�ŵ��ţ���λΪMHz
	��1����Ƶ��=F����Ƶ��=F-10.0

uint8_t chan_to_freq_old(uint8_t work_mode, float *tx, float *rx, uint16_t ch)		// ms: tx at low frequency; rx at high
{
	uint16_t ret = 1;

	if ((get_mobile_rf_type() == MOBILE_RF_TYPE_DDS) && freq_is_pdt_mode())
	{
		*rx = freq_base + ch * freq_step;
		if (work_mode != INTERPHONE_MODE_MPT_TRUNKING)
		{
			if (ch <= 240)							// same frequency of rx & tx
			{
				*tx = *rx;
			}
			else if ((ch >= 241) && (ch <= 800))	// double frequency I/II
			{
				*tx = *rx + freq_gap;
			}
			else
			{
				*tx = *rx;
				ret = 0;
			}
		}
		else
		{
			if ((get_stack_work_mode() & STACK_MODE_CPS_CODE_MODE) == STACK_MODE_CPS_CODE_MODE)
			{
				if (ch >= 241)
				{
					*tx = *rx + freq_gap;
				}
				else
				{
					*tx = *rx;
					ret = 0;
				}
			}
			else
			{
				if (ch > 0)
				{
					*tx = *rx + freq_gap;
				}
				else
				{
					*tx = *rx;
					ret = 0;
				}
			}
		}
	}
	else
	{
		*rx = freq_base + ch * freq_step;
		*tx = *rx + freq_gap;
	}

	return ret;
}
*/

uint8_t chan_to_freq(uint8_t work_mode, float *tx, float *rx, uint16_t ch)		// ms: tx at low frequency; rx at high
{
	uint16_t ret = 1;
																				// set base=350, ch=2
	*rx = freq_base + ch * freq_step;											// result: 350.025
//	*rx = (uint16_t)((freq_base + ch * freq_step) * 80) / 80;					// result: 350
//	*rx = (uint16_t)((freq_base + ch * freq_step) * 80) * 0.0125;				// result: 350.025
//	*rx = (uint16_t)((freq_base + ch * freq_step) / 0.0125) / 80;				// result: 350
//	*rx = (uint16_t)((freq_base + ch * freq_step) / 0.0125) * 0.0125;			// result: 350.0125
	switch (work_mode)
	{
		case INTERPHONE_MODE_PDT_CONV:
		case INTERPHONE_MODE_PDT_TRUNKING:							// 20210624: ��Ⱥ�²��������ŵ���
			*tx = *rx + (dev_is_force_throughout() ? 0 : freq_gap);	// 20220914: ����KS˫��ģ�飬�����غ�����Ƶ�£�����Ҫ����ͬƵ����
			break;

/*		case INTERPHONE_MODE_PDT_TRUNKING:
			if (ch <= 240)							// same frequency of rx & tx
			{
				*tx = *rx;
			}
			else if (ch <= 800)						// double frequency I/II
			{
				*tx = *rx + freq_gap;
			}
			else
			{
				*tx = *rx;
				ret = 0;
			}
			break;
*/
		case INTERPHONE_MODE_MPT_TRUNKING:
			if ((get_stack_work_mode() & STACK_MODE_CPS_CODE_MODE) == STACK_MODE_CPS_CODE_MODE)
			{
				if (ch >= 241)
				{
					*tx = *rx + freq_gap;
				}
				else
				{
					*tx = *rx;
					ret = 0;
				}
			}
			else
			{
				if (ch > 0)
				{
					*tx = *rx + freq_gap;
				}
				else
				{
					*tx = *rx;
					ret = 0;
				}
			}
			break;

		default:
			if ((work_mode > INTERPHONE_MODE_ZZW_ADHOC) && get_xv_freq_is_different())
				*tx = *rx + freq_gap;
			else
				*tx = *rx;
			break;
	}

	return ret;
}

uint16_t rx_freq_to_chan(float f_rx)
{
	uint32_t mhz_int;

	if (get_mobile_rf_type() == MOBILE_RF_TYPE_DDS)
	{
		mhz_int = (uint32_t)(f_rx * 80);
		if ((mhz_int >= chan_start_index) && (mhz_int <= chan_end_index))
			mhz_int = mhz_int - chan_start_index + 1;
		else
			mhz_int = 0;
	}
	else
	{
		if (f_rx < freq_base)
			mhz_int = 0;
		else
			mhz_int = (uint32_t)(f_rx * (1 / freq_step) - freq_base * (1 / freq_step));
	}

//	vlog_v("stack","f_rx=%3.4f, f_rx/step=%d, base/step=%d (%d %d %d %d)", f_rx,
//		(uint32_t)(f_rx / freq_step), (uint32_t)(freq_base / freq_step),
//		(uint32_t)(1 / freq_step), (uint32_t)(f_rx * (1 / freq_step)),
//		(uint32_t)((f_rx - freq_base) * (1 / freq_step)), mhz_int);

//		result: f_rx=352.9875, f_rx/step=28238, base/step=28000 (80 28239 238 239)
	return (uint16_t)mhz_int;
}

void get_stack_dmode_ctype(uint8_t *dmode, uint8_t *ctype)	// dmode: 1-pdt 0-dmr; ctype: 1-patcs 0-oacsu
{
	uint32_t mode = get_stack_work_mode();

	if (mode & STACK_MODE_SET_PDT_DMR)
		*dmode = 1;
	else
		*dmode = 0;

	if (mode & STACK_MODE_PATCS)			// not zero is patcs
		*ctype = 1;
	else
		*ctype = 0;
}

void set_stack_dmode_ctype(uint8_t dmode, uint8_t ctype)	// dmode: 1-pdt 0-dmr; ctype: 1-patcs 0-oacsu
{
	uint32_t mode = get_stack_work_mode();

	if (dmode)
		mode |= STACK_MODE_SET_PDT_DMR;
	else
		mode &= ~STACK_MODE_SET_PDT_DMR;

	if (ctype)
		mode |= STACK_MODE_PATCS;
	else
		mode &= ~STACK_MODE_PATCS;

	set_stack_work_mode(mode);
}

uint8_t get_stack_cps1343_mode(void)
{
	return (get_stack_work_mode() & STACK_MODE_CPS_CODE_MODE) ? 1 : 0;	// 1-CPS; 0-1343
}

void set_stack_cps1343_mode(uint8_t mode)						// !0-cps; 0-1343
{
	if (mode)
		set_stack_work_mode(get_stack_work_mode() | STACK_MODE_CPS_CODE_MODE);
	else
		set_stack_work_mode(get_stack_work_mode() & (~STACK_MODE_CPS_CODE_MODE));
}

uint8_t get_mpt_rcode_paras(uint16_t *fleet_boundary, uint8_t *cpsl, uint8_t *cpsm, uint8_t *cpss)	// return: 1-cps; 0-1343
{
	uint8_t ret;

	ret = (p_stack_parameters->rcode & 0x8000) ? 0xff : 0;
	if (ret)
	{
		*fleet_boundary = 0;
		*cpss = (uint8_t)(p_stack_parameters->rcode & 0x000f);
		*cpsm = (uint8_t)((p_stack_parameters->rcode >> 4) & 0x000f);
		*cpsl = (uint8_t)((p_stack_parameters->rcode >> 8) & 0x000f);
	}
	else
	{
		*fleet_boundary = p_stack_parameters->rcode & 0x1fff;
		*cpss = 0;
		*cpsm = 0;
		*cpsl = 0;
	}
	return ret;
}

uint8_t get_stack_single_ptt_time(void)
{
	return get_stack_conts_address()[7];
}

uint8_t get_stack_lc_time(void)
{
	return get_stack_conts_address()[19];
}

uint8_t set_system_throughout_authentication(uint8_t through, uint8_t authen)	// bit7: 1-set,0-no change; bit0: 0-throughout/no auth, 1-repeat/auth
{
	uint32_t mode_cmp, mode = get_stack_work_mode();

	mode_cmp = mode;
	if (through & 0x80)
	{
		if (through & 0x01)
			mode |= STACK_MODE_DIRECT_REPEATER;
		else
			mode &= ~STACK_MODE_DIRECT_REPEATER;
	}
	if (authen & 0x80)
	{
		if (authen & 0x01)
			mode |= STACK_MODE_HIGH_AUTHENTICATION;
		else
			mode &= ~STACK_MODE_HIGH_AUTHENTICATION;
	}

	if (mode != mode_cmp)
	{
		set_stack_work_mode(mode);
		reset_stack_with_disable_int();
	}

	return ((mode & STACK_MODE_DIRECT_REPEATER) ? 1 : 0) | ((mode & STACK_MODE_HIGH_AUTHENTICATION) ? 2 : 0);
}

void set_system_pdt_dmr_mode(uint8_t index)	// index: 0-pdt; 1-dmr
{
	uint8_t dmode, ctype;

	get_stack_dmode_ctype(&dmode, &ctype);
	vlog_v("stack","sys=%d,set=%d", dmode, index);
	set_stack_dmode_ctype(index ? 0 : 1, ctype);
	if (dmode == index)
		reset_stack_with_disable_int();
}

void check_and_adjust38p4(void);
uint32_t incoming_pcm_voice_old = 0, incoming_pcm_voice_num = 0;
extern uint8_t slot60ms_counter/*, slot_timer_flag_check*/;
extern uint16_t reg_mod_gain_dc[];
void mpt_conv_voice_delay(void)
{
	static uint8_t slot20ms_count = 0, mpt_conv_voice_delay_times = 0;

	slot_middle_int_times++;

	speech_pa_ctrl_process();

	average_power_check_adc(0);
	average_power_adc(1);

	conv_report_rssi_handle();

	if (is_mpt_conv_receiving_voice())
	{
		incoming_pcm_voice_num++;
//		vlog_v("stack","[%d]receiving: %d", slot_middle_int_times, incoming_pcm_voice_num);
	}

	if (++slot20ms_count > 3)
	{
		slot20ms_count = 0;
		send_status_to_bt(2, 0);
		if (++slot60ms_counter > SLOT_TIMER_CHECK_TIME / 60)
		{
			slot60ms_counter = 0;
//			slot_timer_flag_check = 1;
		}

		check_and_adjust38p4();
	}

	if (own_talking_now())
	{
		led_play(BAR_TYPE_TR_ARROW_UP);
	}
	else if (someone_talking_now())
	{
		led_play(BAR_TYPE_TR_ARROW_DOWN);
	}
	else
	{
		led_play(BAR_TYPE_TR_ARROW_CLEAR);
	}

	if (stack_state.vs & STACK_VS_CALLING)							// phone busy
	{
		if (stack_state.vs & STACK_VS_OWN_CALLING)					// self-calling
		{
			if ((stack_state.vs & STACK_VS_OWN_SPEAKING) == 0)		// speaking stop (PTT released already)
			{
				if (mpt_conv_voice_delay_times++ > 10)
				{
					mpt_conv_voice_delay_times = 0;
					stack_state.vs = 0;
				}
			}
			else
			{
				dsp_analog_ptt_ctrl_with_hp();
			}
		}
		else
		{
			if (incoming_pcm_voice_old < incoming_pcm_voice_num)	// someone else still talking
			{
				incoming_pcm_voice_old = incoming_pcm_voice_num;
				mpt_conv_voice_delay_times = 0;
				dsp_speech_out_pa_ctrl(1, SPEECH_PA_CTRL_OUT_VOICE);// maybe close if PTT pressed
			}
			else
			{
				if (mpt_conv_voice_delay_times > 1)
				{
//					vlog_v("stack","[%d]stop talking: %d(old=%d, dly=%d)", slot_middle_int_times, incoming_pcm_voice_num, incoming_pcm_voice_old, mpt_conv_voice_delay_times);
					mpt_conv_voice_delay_times = 0;
					stack_state.vs = 0;
					dsp_speech_out_pa_ctrl(0, SPEECH_PA_CTRL_OUT_VOICE);
				}
				else
				{
					mpt_conv_voice_delay_times++;
//					vlog_v("stack","[%d]stop talking delay: %d(old=%d, dly=%d)", slot_middle_int_times, incoming_pcm_voice_num, incoming_pcm_voice_old, mpt_conv_voice_delay_times);
				}
			}
		}
	}
	else														// phone idle
	{
		set_dsp_analog_ptt_on_off(0);
		if (incoming_pcm_voice_old + 1 < incoming_pcm_voice_num)// someone else talking longer than 20ms
		{
//			vlog_v("stack","[%d]start talking: %d(old=%d)", slot_middle_int_times, incoming_pcm_voice_num, incoming_pcm_voice_old);
			incoming_pcm_voice_old = incoming_pcm_voice_num;
			stack_state.vs = STACK_VS_CALLING | STACK_VS_PLAY_VOICE;
			dsp_speech_out_pa_ctrl(1, SPEECH_PA_CTRL_OUT_VOICE);
		}
	}
}

void mpt_conv_calling_reset(void)
{
	incoming_pcm_voice_old = incoming_pcm_voice_num;
	stack_state.vs = 0;
}


uint8_t mpt_trunk_is_someone_talking(void)
{
	if (incoming_pcm_voice_old < incoming_pcm_voice_num)	// ��վ��ѡ�����󣬻�һֱ�������˽���״̬(��ָʾ�ƻ᳤��)
	{
		incoming_pcm_voice_old = incoming_pcm_voice_num;
		return 1;
	}
	else
	{
		return 0;
	}
}



//////////////////////////////////////////////////////////////////////////////////
////////////////////////////////// ������Э��ջ //////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////
/*
uint8_t zzw_dsp_receive_data(void)
{
	static uint32_t num_of_frame_received_old = 0;
	static uint32_t slots_to_free_hold = 0;

	if (num_of_frame_received > num_of_frame_received_old)
	{
		num_of_frame_received_old = num_of_frame_received;
		slots_to_free_hold = 0;
		return 1;
	}
	else
	{
//		vlog_v("stack","[%d]%d.%d(%d)", slot_rising_edge_int_times, num_of_frame_received, num_of_frame_received_old, slots_to_free_hold);
		if (slots_to_free_hold++ >= STACK_ZZW_VOICE_DELAY_SLOT)
		{
//			vlog_v("stack","voice stop");
			slots_to_free_hold = 0;
			return 0;
		}
		else
		{
			return 1;
		}
	}
}
*/

#define ZZW_PHONE_BUSY						0x80000000
#define ZZW_PHONE_END_CALLING				0x40000000
#define ZZW_PHONE_BUSY_TO_BE_CALLED(vs)		((((vs & (STACK_VS_CALLING | STACK_VS_PLAY_VOICE)) == (STACK_VS_CALLING | STACK_VS_PLAY_VOICE)) ||\
											((vs & ZZW_PHONE_BUSY) == ZZW_PHONE_BUSY)))

#define ZZW_STOP_SENDING_VOICE(vs,stage)	vs &= ~STACK_VS_OWN_SPEAKING;\
											stage = PTT_STAGE_FINISH;\
											protect_timer_slot = MAX_PROTECTED_TIMER_SLOT


#define MAX_CALLING_TIME_LIMITED			60
#define INT_CALL_SUCC_PROTECT_TIME			40
#define MAX_PROTECTED_TIMER_SLOT			(2 + (p_zzw_parameters->zzw_proteced_paras & 0x0F))

#define PTT_STAGE_IDLE						0
#define PTT_STAGE_PRESSED					1
#define PTT_STAGE_SEND_NEXT					2
#define PTT_STAGE_WAIT_FOR_SEND_VOICE		3
#define PTT_STAGE_SEND_VOICE				4
#define PTT_STAGE_FINISH					5
#define PTT_STAGE_RETRY_TO_CALL				6
#define INTCALL_STAGE_PRESSED				11
#define INTCALL_STAGE_SUCCESSFUL			12
#define PTT_STAGE_WAITING_LAST_INT_R		13
#define PTT_STAGE_FORCE_FINISH				14
#define PTT_STAGE_SEND_INTSUCC				15

#define ZZW_IS_CALL_ME(id)					(((my_stack_vs & STACK_VS_GROUP_CALL) == 0) && (id == stack_state.id_caller))
#define ZZW_IS_CALL_MY_WATCH(id)			((my_stack_vs & STACK_VS_GROUP_CALL) && (id == my_stack_id_watch))

#ifdef USE_MAIN_EMBEDDED_Q_MODE

static uint32_t my_stack_vs = STACK_VS_GROUP_CALL;
static uint16_t countdown_timer = 0;
static uint16_t my_stack_chan = 0;
// zzw_global_mode: USER_ID_BACKGROUND:1-global monitor; USER_ID_BROADCAST:1-allow individual call; USER_ID_EMERGENCY-allow play voice when id do not exist at books
// 					USER_ID_ATTR_CONVENTIONAL: allow send when not sync
static uint8_t ptt_stage = PTT_STAGE_IDLE;
static uint8_t wait_for_gps4u_lc = 0;
static uint8_t protect_timer_slot = 0;
static uint8_t received_bad_lc_times = 0, unexpected_lauchint_time = 0;

#endif


uint32_t get_zzw_watch_id(void)
{
	return my_stack_id_watch;
}

uint8_t get_zzw_sync_flag(void)
{
	return device_is_sync;
}

uint32_t get_zzw_distance(uint8_t *str)
{
	if (str)
		p_strcpy(str, zzw_distance_string);
//		p_strcpy(str, "Dist:E.N.12��");

//	zzw_distance = 12;
	return zzw_distance;
}

#define GET_ZZWPRO_RX_FREQ_FROM_TX(tx_freq)	(tx_freq - (get_xv_freq_is_different() ? (int16_t)(freq_gap * 80) : 0))
uint32_t get_zzwpro_nvq_q_chan(uint16_t q_chan, uint8_t real_freq)	// real_freq:1-f_int(freq*80),0-chan; return: high 2B: tx; low 2B: rx
{
	uint16_t tx_freq, rx_freq;
	float f_tx, f_rx;

//	rx_freq_to_chan();
//	chan_to_freq();

#ifdef NVQ_UNIFY_JUMP_FREQ
	if (q_chan < CHAN_REAL_FREQ_DIVIDER)
	{
		chan_to_freq(interphone_mode, &f_tx, &f_rx, q_chan);
		tx_freq = (uint16_t)(f_tx * 80);
		rx_freq = (uint16_t)(f_rx * 80);
	}
	else
	{
		tx_freq = q_chan;
		rx_freq = GET_ZZWPRO_RX_FREQ_FROM_TX(tx_freq);	// chan_to_freq(): *tx = *rx + freq_gap;
	}
#else
	tx_freq = dynamic_q_tx_freq[q_chan & 0x07];
	rx_freq = GET_ZZWPRO_RX_FREQ_FROM_TX(tx_freq);		// chan_to_freq(): *tx = *rx + freq_gap;
#endif

	return real_freq ? (((uint32_t)tx_freq << 16) | rx_freq) : rx_freq_to_chan((float)rx_freq / 80);
}

uint16_t zzwpro_tx_freq_to_chan(uint16_t tx_freq)
{
	return rx_freq_to_chan((float)GET_ZZWPRO_RX_FREQ_FROM_TX(tx_freq) / 80);
}

void set_zzwpro_nvq_q_chan(uint16_t *q_chan)
{
	if ((((uint32_t *)dynamic_q_tx_freq)[0] != ((uint32_t *)q_chan)[0]) || (((uint32_t *)dynamic_q_tx_freq)[1] != ((uint32_t *)q_chan)[1]) ||
		(((uint32_t *)dynamic_q_tx_freq)[2] != ((uint32_t *)q_chan)[2]) || (((uint32_t *)dynamic_q_tx_freq)[3] != ((uint32_t *)q_chan)[3]))
		nvq_tx_freq_index = 0xfffe;	// ����һ�������ڵ�Ƶ�ʣ��Ա��´η���ʱ����ʽ��Ƶ
	memcpy(dynamic_q_tx_freq, q_chan, ZZWPRO_Q_TX_TOTAL_CHAN * sizeof(uint16_t));
}

void set_pdt_conv_q_chan(uint16_t *q_chan)
{
	if ((((uint32_t *)dynamic_q_tx_freq)[0] != ((uint32_t *)q_chan)[0]) || (((uint32_t *)dynamic_q_tx_freq)[1] != ((uint32_t *)q_chan)[1]))	// 20211127:4 chan at pdt now
	{
		nvq_tx_freq_index = 0xfffe;	// ����һ�������ڵ�Ƶ�ʣ��Ա��´η���ʱ����ʽ��Ƶ
		memcpy(dynamic_q_tx_freq, q_chan, ZZWPRO_Q_TX_TOTAL_CHAN * sizeof(uint16_t));
		memcpy(g_runtime_inst_xvbase->dynamic_q_tx_freq, dynamic_q_tx_freq, 4 * sizeof(uint16_t));
		vlog_v("stack","update pdt Q:%04X %04X %04X %04X", dynamic_q_tx_freq[0], dynamic_q_tx_freq[1], dynamic_q_tx_freq[2], dynamic_q_tx_freq[3]);
	}
}

void print_zzwpro_nvq_q_chan(void)
{
	vlog_v("stack","\tQ:%3.4f/%3.4f/%3.4f/%3.4f/%3.4f/%3.4f/%3.4f/%3.4f", (float)dynamic_q_tx_freq[0] / 80, (float)dynamic_q_tx_freq[1] / 80, (float)dynamic_q_tx_freq[2] / 80,
		(float)dynamic_q_tx_freq[3] / 80, (float)dynamic_q_tx_freq[4] / 80, (float)dynamic_q_tx_freq[5] / 80, (float)dynamic_q_tx_freq[6] / 80, (float)dynamic_q_tx_freq[7] / 80);
}

uint16_t PDT_Crc_Csbk(uint8_t inBuf[], uint8_t PDT_DMR, uint16_t inLen);
unsigned int CRC_Check(unsigned char * buf, unsigned char len)
{
	int i,j;
	unsigned int ch,crc=0;
	for( i=0;i<len;i++)
	{
		ch = (unsigned char)buf[i];
		ch=(ch<<8);
		for(j=0;j<8;j++)
		{
			if(((ch^crc) & 0x8000)!=0)
            		{
            			crc^=0x6815;
			}
			ch=(ch<<1);
			crc=(crc<<1);
		}
	}
	return crc;
}

PDU_GPS3U zzw_speaker_position;
void gps_to_gps3u(GPS_STRUCT_TYPEDEF *gps, PDU_GPS3U *gps3u, uint8_t encrypt)
{
	uint8_t i, *ptr8;

	gps3u->encrypt = encrypt ? 1 : 0;
	gps3u->east_west = (gps->gps_state & LONGITUDE_EAST_FLAG) ? 1 : 0;
	// ��λ����д�����ݽṹʱ���Ƚ��б��������ã�ʹ��bit���ݷ����ڵ�bitλ��
	gps3u->lat_degree = convert_bit_stream(2, ((gps->gps_state & LATITUDE_NORTH_FLAG) ? (90 + gps->lat_degree) : (90 - gps->lat_degree)) % 4);
	gps3u->lat_integer = convert_bit_stream(6, (gps->lat_integer & 0x3f));
	gps3u->lat_decimal = convert_bit_stream(10, (gps->lat_decimal / 10) & 0x3ff);
	gps3u->lon_degree = convert_bit_stream(3, gps->lon_degree % 8);
	gps3u->lon_integer = convert_bit_stream(6, gps->lon_integer & 0x3f);
	gps3u->lon_decimal = convert_bit_stream(10, (gps->lon_decimal / 10) & 0x3ff);
	gps3u->invalid = (gps->gps_state & GPS_DATA_VALID_FLAG) ? 0 : 1;
	// �ٴν�ÿ���ֽ����ݵ��ú󣬵�bit���ݷ��õ���bitλ�ã���λ���ݵĵ�bitҲ�����õ���bitλ�ã������Ӹ�λ���������ʱ�ͱ����˵�λ�ȳ�
	for (i = 0, ptr8 = (uint8_t *)gps3u; i < 5; i++, ptr8++)
		*ptr8 = (uint8_t)convert_bit_stream(8, (uint32_t)(*ptr8));
}

void gps3u_to_gps(PDU_GPS3U *gps3u, GPS_STRUCT_TYPEDEF *gps)
{
	uint8_t i, *ptr8;

	memset(gps, 0, sizeof(GPS_STRUCT_TYPEDEF));
	// �ֽ����ݵ���
	for (i = 0, ptr8 = (uint8_t *)gps3u; i < 5; i++, ptr8++)
		*ptr8 = (uint8_t)convert_bit_stream(8, (uint32_t)(*ptr8));
//	if (gps3u->encrypt == 0)
	{
		gps->lat_degree = convert_bit_stream(2, (uint32_t)gps3u->lat_degree);
		gps->lat_integer = convert_bit_stream(6, (uint32_t)gps3u->lat_integer);
		gps->lat_decimal = convert_bit_stream(10, (uint32_t)gps3u->lat_decimal) * 10;
		gps->lon_degree = convert_bit_stream(3, (uint32_t)gps3u->lon_degree);
		gps->lon_integer = convert_bit_stream(6, (uint32_t)gps3u->lon_integer);
		gps->lon_decimal = convert_bit_stream(10, (uint32_t)gps3u->lon_decimal) * 10;

		gps->gps_state = gps3u->invalid ? 0 : GPS_DATA_VALID_FLAG;
		gps->gps_state |= gps3u->east_west ? LONGITUDE_EAST_FLAG : 0;
		gps->gps_state |= LATITUDE_NORTH_FLAG;	// default is north
	}
}

PDU_GPS4U zzw_speaker_position_4u;
void gps4u_to_gps(PDU_GPS4U *gps4u, GPS_STRUCT_TYPEDEF *gps)
{
	uint8_t i, *ptr8;

	memset(gps, 0, sizeof(GPS_STRUCT_TYPEDEF));
	// �ֽ����ݵ���
	for (i = 0, ptr8 = (uint8_t *)gps4u; i < 6; i++, ptr8++)
		*ptr8 = (uint8_t)convert_bit_stream(8, (uint32_t)(*ptr8));
//	if (gps4u->encrypt == 0)
	{
		gps->lat_degree = convert_bit_stream(3, (uint32_t)gps4u->lat_degree);
		gps->lat_integer = convert_bit_stream(6, (uint32_t)gps4u->lat_integer);
		gps->lat_decimal = convert_bit_stream(10, (uint32_t)gps4u->lat_decimal) * 10;
		gps->lon_degree = convert_bit_stream(4, (uint32_t)gps4u->lon_degree);
		gps->lon_integer = convert_bit_stream(6, (uint32_t)gps4u->lon_integer);
		gps->lon_decimal = convert_bit_stream(10, (uint32_t)gps4u->lon_decimal) * 10;

		gps->gps_state = gps4u->invalid ? 0 : GPS_DATA_VALID_FLAG;
		gps->gps_state |= gps4u->east_west ? LONGITUDE_EAST_FLAG : 0;
		gps->gps_state |= LATITUDE_NORTH_FLAG;	// default is north
		gps->gps_state |= GPS_DATA_IS_VICTEL4U;
	}
}

void zzw_process_air_gps4u_data(uint8_t *dat)
{
	memcpy(&zzw_speaker_position_4u, dat, sizeof(PDU_GPS4U));
	zzw_speaker_position_4u.id_middle = 4;
	cal_zzw_distance(0);
	vlog_v("stack","\t[%s]%d", zzw_distance_string, zzw_distance);
}

void printf_zzw_info(char *prehead, uint8_t *buff)
{
	vlog_v("stack","[%02d-%02d-%02d]%s[%d]: %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x",
		rmc_data.utc_hour, rmc_data.utc_minute, rmc_data.utc_second, prehead, slot_rising_edge_int_times,
		buff[0], buff[1], buff[2], buff[3], buff[4], buff[5], buff[6], buff[7],
		buff[8], buff[9], buff[10], buff[11], buff[12]);
}

void printf_zzw_id(uint32_t call_type, uint32_t id1, uint32_t id2)	// call_type: 0-individual; else-group
{
	vlog_v("stack","\t[%c]%06X->%06X", call_type ? 'G' : 'I', id1 & 0x00ffffff, id2 & 0x00ffffff);
}

void printf_gps_info(char *prehead, GPS_STRUCT_TYPEDEF *gps, uint8_t flag)	// flag: 0-use gps; 1/2:use float
{
	if (flag == 0)
	{
		vlog_v("stack","\t%s[%c]%c%d��%d.%04d',%c%d��%d.%04d'", prehead,
			(gps->gps_state & GPS_DATA_VALID_FLAG) ? 'L' : 'U',
			(gps->gps_state & LONGITUDE_EAST_FLAG) ? 'E' : 'W', gps->lon_degree, gps->lon_integer, gps->lon_decimal,
			(gps->gps_state & LATITUDE_NORTH_FLAG) ? 'N' : 'S', gps->lat_degree, gps->lat_integer, gps->lat_decimal);
	}
	else
	{
//		vlog_v("stack","\t%s[%c]%c%3.6f��%c%3.6f��", prehead,
//			(gps->gps_state & GPS_DATA_VALID_FLAG) ? 'L' : 'U',
//			(gps->gps_state & LONGITUDE_EAST_FLAG) ? 'E' : 'W', calling_dev_position[((flag - 1) & 0x01) * 2],
//			(gps->gps_state & LATITUDE_NORTH_FLAG) ? 'N' : 'S', calling_dev_position[((flag - 1) & 0x01) * 2 + 1]);
		vlog_v("stack","\t%s[%c]%c%3.6f��%c%3.6f��", prehead,
			(gps->gps_state & GPS_DATA_VALID_FLAG) ? 'L' : 'U',
			(gps->gps_state & LONGITUDE_EAST_FLAG) ? 'E' : 'W', CAL_POS_FLOAT(gps->lon_degree, gps->lon_integer, gps->lon_decimal),
			(gps->gps_state & LATITUDE_NORTH_FLAG) ? 'N' : 'S', CAL_POS_FLOAT(gps->lat_degree, gps->lat_integer, gps->lat_decimal));
	}
}

extern const uint8_t distance_pattern[2][8][7];
void cal_zzw_distance(uint8_t reset)
{
	GPS_STRUCT_TYPEDEF speaker_pos;
	uint8_t lang_type;

	if (reset == 0)
	{
		if (zzw_speaker_position_4u.id_middle == 4)
			gps4u_to_gps(&zzw_speaker_position_4u, &speaker_pos);
		else
			gps3u_to_gps(&zzw_speaker_position, &speaker_pos);
		zzw_distance = cal_2points_distance(&rmc_data, &speaker_pos, zzw_distance_string);
		printf_gps_info("RMT:", &speaker_pos, 2);
		printf_gps_info("OWN:", &rmc_data, 1);
	}
	else
	{
		lang_type = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0;
		if (zzw_speaker_position_4u.id_middle == 4)
			zzw_speaker_position_4u.invalid = 1;	// invalidate previous position of the speaker
		else
			zzw_speaker_position.invalid = 1;		// invalidate previous position of the speaker
		p_strcpy(zzw_distance_string, distance_pattern[lang_type][4]);
		p_strcat(zzw_distance_string, distance_pattern[lang_type][5]);
		zzw_distance = 0xffffffff;
	}
}

void zzw_process_air_gps3u_data(uint8_t *dat)
{
	memcpy(&zzw_speaker_position, dat, sizeof(PDU_GPS3U));
	zzw_speaker_position_4u.id_middle = 3;
	cal_zzw_distance(0);
	vlog_v("stack","\t[%s]%d", zzw_distance_string, zzw_distance);
}

#define ZZW_POLL_PERIODS_TIME	3600
static uint16_t zzw_gps_poll_second_slot = 0xffff;		// 0xffff: polling is forbidden
uint16_t zzw_gps_poll_periods_time = 0xffff;
void zzw_gps_poll_init(uint8_t flag)				// 0-disable; else-init and enable(if the paras OK)
{
	zzw_gps_poll_second_slot = 0xffff;

	if (flag && (interphone_mode == INTERPHONE_MODE_ZZW_ADHOC) &&
		p_zzw_parameters->gps_poll.time_gap && p_zzw_parameters->gps_poll.dev_total_num)
	{
		if (p_zzw_parameters->gps_poll.dev_sn < p_zzw_parameters->gps_poll.dev_total_num)
		{
			zzw_gps_poll_periods_time = p_zzw_parameters->gps_poll.time_gap * p_zzw_parameters->gps_poll.dev_total_num;
			if (zzw_gps_poll_periods_time < ZZW_POLL_PERIODS_TIME)
			{
				zzw_gps_poll_second_slot = p_zzw_parameters->gps_poll.dev_sn * p_zzw_parameters->gps_poll.time_gap;
				vlog_v("stack","\t[ZZW POLL]enable!period=%d,slot=%d,sn=%d", zzw_gps_poll_periods_time, zzw_gps_poll_second_slot, p_zzw_parameters->gps_poll.dev_sn);
			}
			else
			{
				vlog_v("stack","\t[ZZW POLL]period(%d) out of range(%d)", zzw_gps_poll_periods_time, ZZW_POLL_PERIODS_TIME);
			}
		}
		else
		{
			vlog_v("stack","\t[ZZW POLL]sn(%d) out of range(%d)",
				p_zzw_parameters->gps_poll.dev_sn, p_zzw_parameters->gps_poll.dev_total_num);
		}
	}
	else
	{
		vlog_v("stack","\t[ZZW POLL]function forbidden");
	}
}

#define ZZWPRO_GPS_REPORT_MAX		(254 * 50)
#define GPS_WINDOW_DEV_TOTAL		g_runtime_inst_xvbase->gps_window.dev_total
#define GPS_WINDOW_DEV_SN			g_runtime_inst_xvbase->gps_window.dev_sn
#define GPS_WINDOW_DEV_SLOT			g_runtime_inst_xvbase->gps_window.slot

uint32_t zzwpro_gps_poll_init(void)
{
	uint32_t ret;

	zzw_gps_poll_periods_time = GPS_WINDOW_DEV_TOTAL / 50;			// 9s/180ms=50
	if (GPS_WINDOW_DEV_TOTAL % 50)
		zzw_gps_poll_periods_time++;								// number of gps window(unit is 9s)
//	ret = ((uint32_t)zzw_gps_poll_periods_time << 24) & 0xff000000;	// 20220725: trans to stack even if forbidden
	ret = ((uint32_t)GPS_WINDOW_DEV_TOTAL << 24) & 0xff000000;		// 20230225: trans the ori value to stack and use the minimal 9s period to generate the GPS start window

	if ((interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) && GPS_WINDOW_DEV_TOTAL && (GPS_WINDOW_DEV_TOTAL <= ZZWPRO_GPS_REPORT_MAX) &&
		(GPS_WINDOW_DEV_SN < GPS_WINDOW_DEV_TOTAL))					// 20230330: ��������ʱ϶�� && (GPS_WINDOW_DEV_SLOT < 6)����ʱ϶�Ŵ���6ʱЭ��ջ��ѡ���κο���ʱ϶����GPS
	{
		zzw_gps_poll_periods_time *= 50 * 180 / 1000;				// time of window(unit is s)
		vlog_v("stack","\t[ZZWPRO GPS]enable!period=%ds,slot=%d,sn=%d(total=%d)",
			zzw_gps_poll_periods_time, GPS_WINDOW_DEV_SLOT, GPS_WINDOW_DEV_SN, GPS_WINDOW_DEV_TOTAL);
	}
	else
	{
		vlog_v("stack","\t[ZZWPRO GPS]function disable");
		zzw_gps_poll_periods_time = 0xffff;
	}

	return ret | (((uint32_t)GPS_WINDOW_DEV_SLOT << 16) & 0x00ff0000) | (GPS_WINDOW_DEV_SN & 0xffff);
}

const uint8_t remote_ctrl_jump_tip[2][5][12] =
		{{" ��������", "��������:1", "��������:2", "��������:3", "��������:4"},
		 {"Para error", "Set jump:1", "Set jump:2", "Set jump:3", "Set jump:4"}
		};
void draw_zzwpro_remote_ctrl_calling_tip(void)
{
	GL_LCD_TipDraw(0, (uint8_t *)remote_ctrl_jump_tip[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][remote_ctrl_call_jump & 0x7f], 0, 1000);
	remote_ctrl_call_jump = 0;
}

uint8_t* set_zzwpro_remote_ctrl_calling(uint8_t jump_num)	// jump_num: 01��1����·��02��2����·��03��3����·��04��6����·
{
//	if ((p_zzw_parameters->zzw_meeting_paras & 0x80) && ((jump_num >= 1) && (jump_num <= 4)))
	if (g_static_ptr->stack_zzwpro.misc_config_zzwpro.remote_config_enable && ((jump_num >= 1) && (jump_num <= 4)))
	{
		remote_ctrl_call_jump = jump_num;
		set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
		set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
		timer_initial(1, 1000, draw_zzwpro_remote_ctrl_calling_tip);
	}
	else
	{
		remote_ctrl_call_jump = 0;
	}

	return (uint8_t *)remote_ctrl_jump_tip[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][remote_ctrl_call_jump];
}

#ifdef USE_MAIN_EMBEDDED_Q_MODE
uint32_t stack_return_state(uint32_t buff, uint16_t chan)
{
	static uint32_t pre_timestamp = 0;

	((uint16_t *)buff)[0] = 0;									// lai
	((uint16_t *)buff)[1] = chan;								// chan
	((uint32_t *)buff)[1] = stack_state.id_be_called;			// ID1: id be called
	((uint32_t *)buff)[2] = stack_state.id_caller;				// ID2: id of caller
	((uint32_t *)buff)[3] = stack_state.id_speaker;				// ID3: id of speaker
	if (pre_timestamp != timestamp_1s)
	{
		pre_timestamp = timestamp_1s;
		if ((my_stack_vs & (STACK_VS_CALLING | STACK_VS_PLAY_VOICE)) == (STACK_VS_CALLING | STACK_VS_PLAY_VOICE))
		{
			countdown_timer++;
		}
		else
		{
			if (countdown_timer)
				countdown_timer--;
		}
	}
	((uint32_t *)buff)[4] = (uint32_t)countdown_timer;			// CTIMER
	((uint32_t *)buff)[5] = my_stack_id_watch;
	((uint32_t *)buff)[6] = ((uint32_t)ptt_stage) | ((uint32_t)received_bad_lc_times << 8) | ((uint32_t)unexpected_lauchint_time << 16);
	((uint32_t *)buff)[7] = 0;
	((uint32_t *)buff)[8] = 0;
	((uint32_t *)buff)[9] = 0;

	return STACK_RETURN_TYPE_STATE;
}

uint32_t stack_call_interface_zzwpro_q(uint32_t type, uint32_t *aux1, uint32_t *aux2, uint32_t buff)
{
	uint32_t ret = STACK_RETURN_TYPE_NULL_REALLY;

	switch (type)
	{
		case STACK_CALL_TYPE_READ_VERSION:
			set_stack_version_same_as_main();
			break;

		case STACK_CALL_TYPE_SWITCH_WATCH:
			my_stack_id_watch = *aux1;
			my_stack_id_watch &= USER_ID_CMP_MASK;
			my_stack_chan = (uint16_t)*aux2;

			ptt_stage = PTT_STAGE_IDLE;
			my_stack_vs = STACK_VS_GROUP_CALL;

			maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, &(stack_state.id_caller));
			stack_state.id_caller &= USER_ID_CMP_MASK;
			stack_state.id_speaker = 0;
			vlog_v("stack","[Q]watch id=0x%08x, chan:%d->%d", my_stack_id_watch, get_current_chan(), my_stack_chan & 0x0fff);
			break;

		case STACK_CALL_TYPE_AIR_DATA:
			if ((my_stack_vs & STACK_VS_CALLING) == 0)
			{
				my_stack_vs |= STACK_VS_CALLING | STACK_VS_PLAY_VOICE;
				countdown_timer = 0;
				wait_for_gps4u_lc = 0;
				received_bad_lc_times = 0;
			}

			if (my_stack_vs & STACK_VS_PLAY_VOICE)
			{
				wait_for_gps4u_lc++;				// receive air data;
				ret = STACK_RETURN_TYPE_VOICE;
			}
			break;

//		case STACK_CALL_TYPE_INQUIRY:
//			break;

		case STACK_CALL_TYPE_MIC_VOICE:
			if (my_stack_vs & STACK_VS_OWN_SPEAKING)
				ret = STACK_RETURN_TYPE_SIGNALING;
			break;

		case STACK_CALL_TYPE_PTT_ACTIVE:
		case STACK_CALL_TYPE_PTT2_ACTIVE:
			if (*aux1)						// pressed
			{
				if ((my_stack_vs & STACK_VS_CALLING) == 0)	// free
				{
//					ptt_stage = PTT_STAGE_PRESSED;
					stack_state.id_speaker = stack_state.id_caller;
					stack_state.id_be_called = ((uint32_t *)buff)[1];
					my_stack_vs |= STACK_VS_CALLING | STACK_VS_OWN_CALLING | STACK_VS_OWN_SPEAKING;
					countdown_timer = get_stack_single_ptt_time();
				}
			}
			else
			{
				if ((my_stack_vs & (STACK_VS_OWN_CALLING | STACK_VS_OWN_SPEAKING)) == (STACK_VS_OWN_CALLING | STACK_VS_OWN_SPEAKING))
				{
					my_stack_vs = STACK_VS_GROUP_CALL;
//					ptt_stage = PTT_STAGE_IDLE;
					vlog_v("stack","[%d]Calling end at %d", slot_rising_edge_int_times, zzw_remap_chan(ZZW_TRUNKING_REMAP_FREQ_CURR_F1));
				}
			}
			break;

		case STACK_CALL_TYPE_SLOT_SYNC:
			if (dev_is_base())
			{
				if ((is_ptt_pressed() == 0) && (my_stack_vs & STACK_VS_OWN_SPEAKING))
					goto zzwq_unexpected_calling;
				else
					goto zzwq_unexpected_calling_reset;
			}
			else // dev_is_base
			{
				if (((is_ptt_pressed() == 0) && (my_stack_vs & STACK_VS_OWN_SPEAKING)) && (uart2_use_as_dataport() == 0))
				{
zzwq_unexpected_calling:
					if (unexpected_lauchint_time++ > 4)
					{
						vlog_v("stack","[%d]Calling force end %d(ptt:%d,vs:0x%08X)", unexpected_lauchint_time, slot_rising_edge_int_times, ptt_stage, my_stack_vs);
						ZZW_STOP_SENDING_VOICE(my_stack_vs, ptt_stage);
						unexpected_lauchint_time = 0;
					}
				}
				else
				{
zzwq_unexpected_calling_reset:
					unexpected_lauchint_time = 0;
				}
			} // dev_is_base

			if (my_stack_vs & STACK_VS_OWN_CALLING)
			{
				if (countdown_timer == 0)
				{
					my_stack_vs = STACK_VS_GROUP_CALL;
//					ptt_stage = PTT_STAGE_IDLE;
				}
			}
			else if (my_stack_vs & STACK_VS_PLAY_VOICE)
			{
				if (wait_for_gps4u_lc == 0)
				{
					if (received_bad_lc_times++ > 2)		// 3 slots do not receive air data
					{
						my_stack_vs = STACK_VS_GROUP_CALL;
						goto q_reset_air_data_counter;
					}
				}
				else
				{
q_reset_air_data_counter:
					wait_for_gps4u_lc = 0;
					received_bad_lc_times = 0;
				}
			}
			break;

		default:
			break;
	}

	if (ret == STACK_RETURN_TYPE_NULL_REALLY)
	{
		*aux1 = 0;
		*aux2 = my_stack_vs;
		ret = stack_return_state(buff, my_stack_chan);
	}

	return ret;
}
#endif // USE_MAIN_EMBEDDED_Q_MODE

#if 0

#define MCU_FAULT_SPI_SAVE_ADDRESS		FLASH_SPI_FLASH_ADDRESS_START
#define MCU_FAULT_SPI_SAVE_MAX_LENGTH	FLASH_SECTOR_SIZE
#define MCU_FAULT_SPI_SAVE_TIMES		4
#define MCU_FAULT_SPI_SAVE_LENGTH		(MCU_FAULT_SPI_SAVE_MAX_LENGTH / MCU_FAULT_SPI_SAVE_TIMES)
// �쳣����ʱ���ں˽�R0~R3��R12��Return address��PSR��LR�Ĵ���������ջ������Return address��
// Ϊ�����쳣ǰPC��Ҫִ�е���һ��ָ���ַ������ڶ�ջ�з����������ּ�Ϊ����λ�á�
// ��ʱ�������Ҫ�ڷ����ģʽ�µ��ԣ���Ϊ�����ǳ����ܷ�һ����ų���HardFault_Handler��
void hard_fault_handler_save_to_spi(uint8_t *info, uint32_t len)
{
	uint8_t *ptr, i, j;
	uint32_t mcu_fault_debug[MCU_FAULT_SPI_SAVE_MAX_LENGTH / sizeof(uint32_t)];

	GET_RUNTIME_PARAS(mcu_fault_debug, REMAP_SPI_TO_INTERNAL(MCU_FAULT_SPI_SAVE_ADDRESS), MCU_FAULT_SPI_SAVE_MAX_LENGTH);
	for (ptr = (uint8_t *)mcu_fault_debug, i = 0; i < MCU_FAULT_SPI_SAVE_TIMES; i++, ptr += MCU_FAULT_SPI_SAVE_LENGTH)
	{
		if ((ptr[0] == 0xff) && (ptr[1] == 0xff) && (ptr[2] == 0xff) && (ptr[3] == 0xff))
			break;
	}
	if (i >= MCU_FAULT_SPI_SAVE_TIMES)
	{
		i = MCU_FAULT_SPI_SAVE_TIMES - 1;
		for (ptr = (uint8_t *)mcu_fault_debug, j = 0; j < i; j++, ptr += MCU_FAULT_SPI_SAVE_LENGTH)
			memcpy(ptr, ptr + MCU_FAULT_SPI_SAVE_LENGTH, MCU_FAULT_SPI_SAVE_LENGTH);
	}
	ptr = (uint8_t *)mcu_fault_debug + i * MCU_FAULT_SPI_SAVE_LENGTH;
	memset(ptr, 0, MCU_FAULT_SPI_SAVE_LENGTH);
	memcpy(ptr, info, (len < MCU_FAULT_SPI_SAVE_LENGTH) ? len : MCU_FAULT_SPI_SAVE_LENGTH);

	SAV_RUNTIME_PARAS_ERASE(mcu_fault_debug, REMAP_SPI_TO_INTERNAL(MCU_FAULT_SPI_SAVE_ADDRESS), MCU_FAULT_SPI_SAVE_MAX_LENGTH, 1);
	printf ("save to spi(%dB) successfully!", len);
//	save_user_data_to_flash(0);	// ���������������������Ϊ����ȷ����ʱ�Ĳ����Ƿ��ѱ��ƻ������Բ�����ֱ������
}
void hard_fault_handler_save_to_temp(uint8_t flag)	// flag: 0-reset; 0xff-save to spi
{
	uint8_t year, month, day, weekday;
	uint8_t hour, minute, second;

	if (flag == 0)
	{
		memset(stack_call_buffer, 0, sizeof(stack_call_buffer));
		RTC_GetTD(&year, &month, &day, &weekday, &hour, &minute, &second);
		stack_call_buffer[0] = sprintf((char *)(&stack_call_buffer[2]), "\r\n====[HardFault_Handler: 20%02d-%02d-%02d(%01d) %02d:%02d:%02d]====",
			year, month, day, weekday, hour, minute, second);
	}
	else if (flag == 0xff)
	{
		hard_fault_handler_save_to_spi((uint8_t *)(stack_call_buffer + 2), stack_call_buffer[0]);
	}
}

void hard_fault_handler_c(uint32_t * hardfault_args)
{
	uint32_t stacked_r0;
	uint32_t stacked_r1;
	uint32_t stacked_r2;
	uint32_t stacked_r3;
	uint32_t stacked_r12;
	uint32_t stacked_lr;
	uint32_t stacked_pc;
	uint32_t stacked_psr;

	uint32_t n;
	uint8_t year, month, day, weekday;
	uint8_t hour, minute, second;
	uint8_t ptr[MCU_FAULT_SPI_SAVE_LENGTH];

	stacked_r0 = ((uint32_t) hardfault_args[0]);
	stacked_r1 = ((uint32_t) hardfault_args[1]);
	stacked_r2 = ((uint32_t) hardfault_args[2]);
	stacked_r3 = ((uint32_t) hardfault_args[3]);
	stacked_r12 = ((uint32_t) hardfault_args[4]);
	stacked_lr = ((uint32_t) hardfault_args[5]);
	stacked_pc = ((uint32_t) hardfault_args[6]);
	stacked_psr = ((uint32_t) hardfault_args[7]);

	RTC_GetTD(&year, &month, &day, &weekday, &hour, &minute, &second);

	n = sprintf((char *)ptr, "====[Hard fault handler(HEX): 20%02d-%02d-%02d(%01d) %02d:%02d:%02d]====",
		year, month, day, weekday, hour, minute, second);
	n += sprintf((char *)ptr + n, "SP = %08X(TOP=%08X, USED=%d)",
		(uint32_t)hardfault_args, *((uint32_t *)0x08060000), *((uint32_t *)0x08060000) - (uint32_t)hardfault_args);
	n += sprintf((char *)ptr + n, "R0 = %08X", stacked_r0);
	n += sprintf((char *)ptr + n, "R1 = %08X", stacked_r1);
	n += sprintf((char *)ptr + n, "R2 = %08X", stacked_r2);
	n += sprintf((char *)ptr + n, "R3 = %08X", stacked_r3);
	n += sprintf((char *)ptr + n, "R12 = %08X", stacked_r12);
	n += sprintf((char *)ptr + n, "LR [R14] = %08X", stacked_lr);	// [subroutine call return address]
	n += sprintf((char *)ptr + n, "PC [R15] = %08X", stacked_pc);	// [program counter]
	n += sprintf((char *)ptr + n, "PSR = %08X", stacked_psr);

	n += sprintf((char *)ptr + n, "=====================================================");
	n += sprintf((char *)ptr + n, "CFSR = %08X", *((volatile uint32_t *)0xE000ED28));	// [Configurable Fault Status Register]
	n += sprintf((char *)ptr + n, "\tCFSR_MFSR=%02X", *((volatile unsigned char *)0xE000ED28));	// [Memory-management Fault Status Register]
	n += sprintf((char *)ptr + n, "\tCFSR_BFSR=%02X", *((volatile unsigned char *)0xE000ED29));	// [Bus Fault Status Register]
	n += sprintf((char *)ptr + n, "\tCFSR_UFSR=%02X", *((volatile unsigned char *)0xE000ED2A));	// [Usage Fault Status Register]

	n += sprintf((char *)ptr + n, "HFSR = %08X", *((volatile uint32_t *)0xE000ED2C));	// [HardFault Status Register]

	n += sprintf((char *)ptr + n, "DFSR = %08X", *((volatile uint32_t *)0xE000ED30));

	n += sprintf((char *)ptr + n, "MMAR = %08X", *((volatile uint32_t *)0xE000ED34));	// [MemManage Fault Address register]

	n += sprintf((char *)ptr + n, "BFAR = %08X", *((volatile uint32_t *)0xE000ED38));	// [Bus Fault Address Register]

	n += sprintf((char *)ptr + n, "AFSR = %08X", *((volatile uint32_t *)0xE000ED3C));

	n += sprintf((char *)ptr + n, "SCB_SHCSR = %08X", SCB->SHCSR);

	n += sprintf((char *)ptr + n, "stack buffer:");
	for (stacked_r2 = 0, stacked_r0 = 0; stacked_r0 < 4; stacked_r0++)
	{
		for (stacked_r1 = 0; stacked_r1 < 16; stacked_r1++)
			n += sprintf((char *)ptr + n, "%02X ", ((uint8_t *)stack_call_buffer)[stacked_r2++]);
		n += sprintf((char *)ptr + n, "");
	}
	n += sprintf((char *)ptr + n, "");

	vlog_v("stack","%s", ptr);
	hard_fault_handler_save_to_spi(ptr, n);

	nvic_system_reset();	// NVIC_SystemReset();
	while (1)
		;
}


#ifdef SET_WATCHDOG_ENABLE

#define IWATCH_DOG_MIN_PERIOD	8
void iwatchdog_init(uint16_t idog_ms)	// test result(469): set with 2000ms, watchdog reset after 1800ms really
{
	uint16_t idog;

	if (idog_ms > IWATCH_DOG_MIN_PERIOD)
	{
		crm_periph_clock_enable(CRM_PWC_PERIPH_CLOCK, TRUE);
		/* disable register write protection */
		wdt_register_write_enable(TRUE);
		/* set the wdt divider value */
		wdt_divider_set(WDT_CLK_DIV_256);	// 32K/256=125Hz,8ms per clock

		/* set reload value
		 timeout = reload_value * (divider / lick_freq )(s)

		 lick_freq	  = 40000 Hz(WDT��������LICKʱ��(�ڲ�RCʱ��)��������ΧΪ30kHz~60kHz֮��)
		 divider	  = 4(WDT_CLK_DIV_4)
		 reload_value = 3000

		 timeout = 3000 * (4 / 40000 ) = 0.3s = 300ms
		*/
		idog = idog_ms / IWATCH_DOG_MIN_PERIOD;	// 12bit,max=4095(4095*8ms=32s)
		idog = (idog > 0x0fff) ? 0xfff : (idog - 1);
		wdt_reload_value_set(idog);

		/* reload wdt counter */
		wdt_counter_reload();

		/* enable wdt */
		wdt_enable();

		sys_delay(10);
		vlog_v("stack","\twatchdog set to %d(%dms)", idog, idog * IWATCH_DOG_MIN_PERIOD);
	}
}

void iwatchdog_handle_put_info(void)
{
	uint32_t n;
	uint8_t year, month, day, weekday;
	uint8_t hour, minute, second;
	uint8_t ptr[200];

	RTC_GetTD(&year, &month, &day, &weekday, &hour, &minute, &second);

	n = sprintf((char *)ptr, "====[iwatchdog reset handler: 20%02d-%02d-%02d(%01d) %02d:%02d:%02d]====",
		year, month, day, weekday, hour, minute, second);

	vlog_v("stack","%s", ptr);
	hard_fault_handler_save_to_spi(ptr, n);
}

uint8_t iwatchdog_check(void)
{
	uint8_t ret = 0;

	/* Check if the system has resumed from IWDG reset */
	if(crm_flag_get(CRM_WDT_RESET_FLAG) != RESET)
	{
		/* reset from wdt */
		crm_flag_clear(CRM_WDT_RESET_FLAG);
		/* IWDGRST flag set */
		iwatchdog_handle_put_info();
		ret = 2;
	}
	else
	{
		/* IWDGRST flag is not set */
		vlog_v("stack","\t[iwatchdog] flag not set");
	}

	return ret;
}

void iwatchdog_handle(void)
{
    /* reload wdt counter */
    wdt_counter_reload();
}

  #ifdef TEST_WATCHDOG_FUNC
void test_iwatchdog(void)
{
	uint32_t time_check1s = timestamp_1s, time_check40ms = timestamp_kb_scan, time_hard1ms = get_timestamp_measure();

	while (1)
	{
		vlog_v("stack","1s=%d,40ms=%d,hard1ms=%d",
			(timestamp_1s - time_check1s) * TIMESTAMP_POLL_PERIOD,
			(timestamp_kb_scan - time_check40ms) * KEYBOARD_POLL_PERIOD,
			get_measure_timer_difference(get_timestamp_measure(), time_hard1ms) * TIMESTAMP_MEASURE_PERIOD);

			sys_delay(200);
	}
}
  #endif

#endif // SET_WATCHDOG_ENABLE


#ifdef SET_WRITE_SPI_DATA_DEBUG_INFO
void change_spi_flash_put_info(uint8_t *info, uint32_t len)
{
	uint32_t n, length;
	uint8_t year, month, day, weekday;
	uint8_t hour, minute, second;
	uint8_t ptr[MCU_FAULT_SPI_SAVE_LENGTH];

	RTC_GetTD(&year, &month, &day, &weekday, &hour, &minute, &second);

	n = sprintf((char *)ptr, "====[write factory spi: 20%02d-%02d-%02d(%01d) %02d:%02d:%02d]====",
		year, month, day, weekday, hour, minute, second);	// attention: this string must less than 84(MCU_FAULT_SPI_SAVE_LENGTH-WRITE_SPI_DATA_DEBUG_INFO_LEN)
	length = (len + n > MCU_FAULT_SPI_SAVE_LENGTH) ? (MCU_FAULT_SPI_SAVE_LENGTH - n) : len;
	memcpy(ptr + n, info, length);
	vlog_v("stack","\tchange spi save total=%d(real len=%d)", n + length, len);
	hard_fault_handler_save_to_spi(ptr, n + length);
}
#endif

#endif
