/**
  ******************************************************************************
  *                Copyright (c) 2013, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    3-October-2013
  * @brief   This file provides
  *            - DSP function interface
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include "stddef.h"
#include "victel_digital_board.h"
#include "global_define.h"
#include "dsp_reg_define.h"
#include "dsp_reg_hal_define.h"
#include "regDefine.h"
#include "victel_digital_keyboard.h"
#include "victel_digital_usart.h"
#include "victel_digital_xvmode.h"
#include "victel_digital_local_can.h"
#include "victel_digital_spi_flash.h"
#include "victel_digital_spi_arm.h"
#include "stack_config.h"
#include "dsp_slp_4620ms.h"
#include "tip_lib.h"
#include "DVSI.h"
#include "vlog.h"


#define DSP_WRITE_REG_ADDR32				REG_WRITE_BASE_ADDR
#define DSP_READ_REG_ADDR32					REG_READ_BASE_ADDR
#define DSP_CMD_REG_ADDR32					REG_CMD_BASE_ADDR

#define ALG_ANALOG_DEMOD_STATE_MSK_DETECT	0x0001
#define ALG_ANALOG_DEMOD_STATE_MSK_DEMOD	0x0002
#define ALG_ANALOG_DEMOD_STATE_CTCSS_DETECT	0x0004
#define ALG_ANALOG_DEMOD_STATE_CTCSS_TAIL	0x0008
#define ALG_ANALOG_DEMOD_STATE_DCS_DETECT	0x0010
#define ALG_ANALOG_DEMOD_STATE_DCS_TAIL		0x0020
#define ALG_ANALOG_DEMOD_STATE_CARRIER		0x0040
#define ALG_ANALOG_DEMOD_STATE_SQUELCH		0x0080


/*
#define SIGNAL_LEVEL5						-80
#define SIGNAL_LEVEL4						-90
#define SIGNAL_LEVEL3						-100
#define SIGNAL_LEVEL2						-105
#define SIGNAL_LEVEL1						-110
#define SIGNAL_LEVEL0						-130
*/
#define SIGNAL_LEVEL5						-85
#define SIGNAL_LEVEL4						-90
#define SIGNAL_LEVEL3						-95
#define SIGNAL_LEVEL2						-100
#define SIGNAL_LEVEL1						-105
#define SIGNAL_LEVEL0						-130

#define UART_CMD_DATA_BUF_LEN				256

#define SPEECH_IN_REG_GAIN					0
#define	SPEECH_OUT_REG_GAIN					1

#define	SPEECH_USE_DEFAULT					0
#define	SPEECH_LEFT_CHANNEL					1
#define	SPEECH_RIGHT_CHANNEL				2
#define	SPEECH_COMBINE_CHANNEL				3

typedef struct
{
	// ע��: ��ֻ������һ���Ĵ�����ĳЩλʱ�����뱣������λ�����ݲ���
	uint16_t reg_vocoder_mode;				// ����������ģʽ bit0=0:����2400; bit0=1: ����2200��(DSP64: bit1=0:AMBE; bit1=1:NVOC)
	uint16_t reg_speech_mode;				// ����ģʽ��Ŀǰ��ʱ����Ϊֻ��)
//	uint16_t reg_speech_in_gain[2];			// ���Ѿ�����ʹ���ˡ�reg_speech_in_gain[0](������):��˷���������/reg_speech_in_gain[1](������):������������(δʹ��)
//	uint16_t reg_speech_in_gain_shift[2];	// ���Ѿ�����ʹ���ˡ�������������λ�ƣ�Ŀǰ��ʱ����Ϊֻ��)
//	uint16_t reg_speech_out_gain[2];		// ���Ѿ�����ʹ���ˡ�0-31,0Ϊ������31Ϊ�������reg_speech_out_gain[0](������):����������/reg_speech_out_gain[1](������):��������(δʹ��)
//	uint16_t reg_speech_out_gain_shift[2];	// ���Ѿ�����ʹ���ˡ���������λ�ƣ�Ŀǰ��ʱ����Ϊֻ��)
//	uint16_t reg_da_pcm_gain_shift;			// ���Ѿ�����ʹ���ˡ���ʾ����������λ�ƣ�Ŀǰ��ʱ����Ϊֻ��)
	uint16_t reg_hardware_mode;				// Ӳ������ģʽ��Ŀǰ��ʱ����Ϊֻ��)
	uint16_t reg_mod_gain[2];				// channel gain modulation, [0]:left; [1]:right��Ŀǰ��ʱ����Ϊֻ��)
	uint16_t reg_rdata_loc;					// ���Ź���ʱ�����ã�Ŀǰ��ʱ����Ϊֻ��)
	uint16_t reg_analog_in_gain;			// ģ���������棨Ŀǰ��ʱ����Ϊֻ��)(ģ��Ƶƫ)
	uint16_t reg_analog_in_gain_shift;		// ģ����������λ�ƣ�Ŀǰ��ʱ����Ϊֻ��)
	uint16_t reg_analog_out_gain;			// ģ��������棨Ŀǰ��ʱ����Ϊֻ��)
	uint16_t reg_analog_out_gain_shift;		// ģ���������λ�ƣ�Ŀǰ��ʱ����Ϊֻ��)
	uint16_t reg_analog_sub_level;			// ��������С�����֣�Ŀǰ��ʱ����Ϊֻ��)
	uint16_t reg_analog_msk_level;			// MSK���ȣ�Ŀǰ��ʱ����Ϊֻ��) freq level Ƶƫ
	uint16_t reg_analog_mode;				// ģ��Ӧ��ģʽ: bit6-4:000-�������͹رգ�001-����ģ��������010-��������������
	uint16_t reg_analog_audio;				// ģ����ƵӦ��ģʽ: bit1=1: ��300Hz��ͨ�˲�����=0: �ر�300Hz��ͨ�˲�����
											//				  bit2=1:ʹ��3K�ŵ��˲�����=0:ʹ��2.55K�ŵ��˲���
											//				  bit3=1:��Ԥ���أ�=0:��Ԥ����
											//				  bit4=1:��ѹ�أ�=0:�ر�ѹ��
											//				  bit5=1:����Ƶ��Ƶ��=0:�ر���Ƶ��Ƶ
	uint16_t reg_analog_audio_threshold;	// ģ����Ƶ�޷����ȣ�Ŀǰ��ʱ����Ϊֻ��)
	uint16_t reg_sync_offset;				// ͬ���źŹ̶�ƫ�ƣ�Ŀǰ��ʱ����Ϊֻ��)
	uint16_t reg_resync_offset;				// �л��ŵ�������ͬ��ʱ϶��
	uint16_t reg_digital_mode;
	uint16_t reg_vocoder_config;
}DSP_REG_TYPEDEF;

#define CALL_IS_IDLE			0
#define CALL_BY_LOCAL			1
#define CALL_BY_LINK			2
#define CALL_FALLOW_DISABLE		3
#define CALL_MAX_TIMESLOT		27
typedef struct
{
	uint8_t call_direct			: 2;
	uint8_t call_timeslot		: 5;	// downcount
	uint8_t reserved			: 1;
}SET_DSP_30MS_SOURCE_TYPEDEF;

#define DSP_VOCODER_INTERFACE_BYTE_ALIGNED						10
#define SET_VOCODER_PAYLOAD_LENGTH(payload, len, one_frame_len)	payload = len / (DSP_VOCODER_INTERFACE_BYTE_ALIGNED / sizeof(uint16_t)) * one_frame_len

const int8_t tip_voice_gain_factor[VOICE_GAIN_LEVEL] = {-6, -5, -4, -2, 0, 2, 4, 5, 6, 7};	// 20240418: 0-29, dsp default is 12 20240814
const int8_t voice_gain_factor[VOICE_GAIN_LEVEL] = {-6, -1, 5, 11, 15, 21, 24, 26, 27, 29};
const int8_t mic_gain_factor[MICROPHONE_GAIN_LEVEL] = {0, 20, 50, 70, 95};					// 20240418: 0-95, dsp default is 6
const uint16_t ext_mic_amp_table[8] = {0, 1, 2, 3, 4, 5, 6, 7};

const uint16_t test_info_data[18] = {0x0000, 0x00a4, 0xa5a5, 0xa6a6, 0xa7a7, 0xa8a8, 0xa9a9, 0xaa02,
					0x9d5d, 0x7f77, 0xfd75, 0x73e0, 0xaaab, 0xabac, 0xacad, 0xadae, 0xaeaf, 0xafb0};
const uint16_t test_voice_data[18] = {0x0000, 0x0032, 0x93df, 0xb3b7, 0xc8f1, 0xa7a1, 0x3382, 0xdfb3,
					0xb7f7, 0xd5dd, 0x57df, 0xd798, 0xf1a7, 0xb533, 0x82df, 0xb3b7, 0x98f1, 0xa3e1};


#pragma pack(4)
const uint8_t test_o_153[216] = {
	0x00,0x00,0x00,0xFF,0x83,0xDF,0x17,0x32,0x09,0x4E,0xD1,0xE7,0xCD,0x8A,0x91,0xC6,0xD7,0xF7,0xD5,0xDD,0x57,
	0xDF,0xD5,0xC4,0xC4,0x40,0x21,0x18,0x4E,0x55,0x86,0xF4,0xDC,0x8A,0x15,0xA7,//====0

	0x00,0x00,0x00,0xEC,0x92,0xDF,0x93,0x53,0x30,0x18,0xCA,0x34,0xBF,0xA2,0xC7,0x59,0x6E,0x21,0xE1,0xE1,0x40,
	0x59,0x57,0x8F,0xBA,0x0D,0x6D,0xD8,0x2D,0x7D,0x54,0x0A,0x57,0x97,0x70,0x39,//====1

	0x00,0x00,0x00,0xD2,0x7A,0xEA,0x24,0x33,0x85,0xED,0x9A,0x1D,0xE1,0xFF,0x07,0xBE,0x2E,0x61,0xE1,0xE1,0xE1,
	0xD7,0x0E,0x64,0x12,0x9D,0xA3,0xCF,0x9B,0x15,0x23,0x8D,0xAB,0x89,0x88,0x80,//====2

	0x00,0x00,0x00,0x42,0x30,0x9C,0xAB,0x0D,0xE9,0xB9,0x14,0x2B,0x4F,0xD9,0x25,0xBF,0x2E,0x60,0xC0,0xC2,0x71,
	0xE7,0x06,0xA6,0x60,0x31,0x94,0x69,0x7F,0x45,0x8E,0xB2,0xCF,0x1F,0x74,0x1A,//====3

	0x00,0x00,0x00,0xDB,0xB0,0x5A,0xFA,0xA8,0x14,0xAF,0x2E,0xE0,0x73,0xA4,0xF5,0xD4,0x4E,0x40,0xF2,0x72,0xE2,
	0xD0,0x38,0x67,0x0B,0xDB,0x34,0x3B,0xC3,0xFE,0x0F,0x7C,0x5C,0xC8,0x25,0x3B,//====4

	0x00,0x00,0x00,0x47,0x9F,0x36,0x2A,0x47,0x1B,0x57,0x13,0x11,0x00,0x84,0x61,0x39,0x5E,0x00,0x00,0x00,0x00,
	0x0E,0x66,0x1B,0xD3,0x72,0x28,0x56,0x9F,0xB2,0x4B,0x7E,0x4D,0x4C,0xC0,0x63};//====5


#pragma pack(4)
const uint8_t test_1031[216] = {
	0x00,0x00,0x00,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xa7,0xf7,0xd5,0xdd,0x57,0xdf,0xdc,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a,
	0x00,0x00,0x00,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xa1,0x30,0x60,0x60,0x50,0x59,0x1c,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a,
	0x00,0x00,0x00,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xa1,0x70,0x60,0xf0,0xf0,0xc7,0x4c,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a,
	0x00,0x00,0x00,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xa1,0x70,0xc0,0xc1,0x20,0xf7,0x4c,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a,
	0x00,0x00,0x00,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xa1,0x50,0xf1,0xb1,0x21,0x10,0x7c,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a,
	0x00,0x00,0x00,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xa1,0x10,0x00,0x00,0x00,0x0e,0x2c,0xc4,0x58,0x20,0x0a,0xce,0xa8,0xfe,0x83,0xac,0xc4,0x58,0x20,0x0a
};

//__align(2) const uint8_t signal_valid_base[72] = {
//	0x00,0x00,0x00,0x0F,0x2C,0x9A,0x5E,0x3A,0x90,0x78,0x50,0xF6,0x98,0xCC,0x31,0x84,0xCD,0x5D,0x7F,0x77,0xFD,0x75,0x7A,0xC9,0xA6,0x09,0x28,0x42,0xB8,0xC6,0x21,0x83,0xC2,0x8E,0xA6,0x73,
//	0x00,0x00,0x00,0x14,0x6D,0x02,0xC4,0x0F,0xA8,0x3F,0x78,0x4F,0x50,0x5C,0xE0,0x84,0x6D,0x5D,0x7F,0x77,0xFD,0x75,0x7E,0x31,0x90,0x03,0xC0,0x07,0xA1,0x0F,0x22,0x9F,0x01,0x3C,0x01,0x79};

const uint16_t xv_test_1031[45] = {
	0xFEE2,0x1212,0x1210,0x0000,0x0000,0xFEE2,0x1212,0x1210,0x0000,0x0000,0xFEE2,0x1212,0x1210,0x0000,0x0000,
	0xFEE2,0x1212,0x1210,0x0000,0x0000,0xFEE2,0x1212,0x1210,0x0000,0x0000,0xFEE2,0x1212,0x1210,0x0000,0x0000,
	0xFEE2,0x1212,0x1210,0x0000,0x0000,0xFEE2,0x1212,0x1210,0x0000,0x0000,0xFEE2,0x1212,0x1210,0x0000,0x0000
};

extern uint8_t g_print_adc;
extern uint8_t interphone_mode;
extern uint32_t stack_para_aux1, stack_para_aux2;
extern uint32_t timestamp_1s, timestamp_kb_scan;
//extern uint32_t stack_cs, stack_vs;
extern uint8_t  device_is_sync, pre_device_sync;
extern uint32_t stack_call_buffer[];
extern uint32_t stack_message_buffer[];
extern FACTORY_PARAS_TYPEDEF *g_factory_inst;
extern RUNTIME_PARAMETERS_TYPEDEF g_runtime_inst;
extern RUNTIME_PARAMETERS_XVBASE_TYPEDEF *g_runtime_inst_xvbase;
extern uint32_t incoming_pcm_voice_old, incoming_pcm_voice_num;
extern STATIC_PARAMETERS_TYPEDEF *g_static_ptr;
extern const char reportrssi_string[];
extern GPS_STRUCT_TYPEDEF rmc_data;
extern uint32_t long_message_tmp_save[];
extern uint32_t last_call_timestamp;
extern uint32_t send_message_flag;

extern uint16_t zzw_gps_poll_periods_time;

#ifdef FORWARDING_LOGIC_ENABLE
extern uint8_t zzw_forward_level, zzw_forward_downcount;
#endif

extern MCU_INF_TYPEDEF *g_mcu_inf_struct;
extern uint32_t (*p_stack_call_interface)(uint32_t type, uint32_t *aux1, uint32_t *aux2, uint32_t buff_addr, uint16_t buff_len);

P_FUN_VOID_U16 dsp_send_cmd, dsp_enter_save_power;
P_FUN_U16_U16_U16PTR dsp_reg_read, dsp_reg_write;

DSP_REG_TYPEDEF *g_dsp_reg;
#ifdef DSP_NEW_FREQ_INTERFACE
DSP_2P_TUNE_TYPEDEF *g_rf2p_tune_reg;
#endif

int16_t g_rssi_regulator_ex = 0;

uint8_t dqi_prev_slot = 0, dqi_prev_slot_ex = 0;

int16_t rssi_value = SIGNAL_LEVEL5, rssi_value_ex = SIGNAL_LEVEL5;
int16_t rssi_prev_slot = SIGNAL_LEVEL5, rssi_prev_slot_noise = SIGNAL_LEVEL0, rssi_curr_slot = SIGNAL_LEVEL5;
int16_t rssi_prev_slot_ex = SIGNAL_LEVEL5, rssi_prev_slot_noise_ex = SIGNAL_LEVEL0, rssi_curr_slot_ex = SIGNAL_LEVEL5;

int16_t rssi_sync = SIGNAL_LEVEL0, rssi_sync_max = SIGNAL_LEVEL0, rssi_sync_ex = SIGNAL_LEVEL0, rssi_sync_max_ex = SIGNAL_LEVEL0;
uint8_t  rssi_rel_base = 0, rssi_rel_base_max = 0, rssi_voice = 0;

uint16_t auto_trace_nv25k = 0, dsp_save_power_counter9s = 0, dsp_save_power_counter = 0, gps_minute_second = 1;
uint16_t int_stat, dsp_demod_state[REG_READ_RSSI_DQI_DEMOD_REAL_LEN] = {0, 0}, dsp_demod_state_ex[REG_READ_RSSI_DQI_DEMOD_REAL_LEN] = {0, 0}, device_desync_counter = 0;
uint8_t slot60ms_counter = 0, /*slot_timer_flag_check = 0, */rssi_level = 5, tx_icon_delay_clean = 0;	// tx_icon_delay_clean just for mpt trunking
uint8_t dsp_edge_slot, sent_dsp_slot, dsp_save_power_status = 0, sync_to_middle_exit_save_power = 0;
uint8_t auto_trace_q_vocoder = 0, delay_to_sync = 0xff, num_of_sync_9s = 0, g_freq_offset = 0, xcs_sync_mask = STACK_XCS_MOBILE_BASE_GPS_SYNC;
uint8_t super_frame_count = 0, dsp_speech_pa_ctrl = 0, speech_out_pa_close_delay = 0, g_talk_tip_count = 0, g_talk_tip_count_total = 0, g_talk_tip_sound = 0;	// g_talk_tip_sound: 0-no tip to talk; else: tip type

#ifdef SUPPORT_EXTEND_FUNCTION
static uint8_t time_gap_for_insert_slot_sync = 0;
static uint32_t prev_slot_edge_timestamp = 0;
#endif

VOCODER_TRANS_TYPEDEF trans_vocoder_type = {0, 0, 0, 0, VOCODER_TYPE_Q_SELP1200, 0, 0};
static SET_DSP_30MS_SOURCE_TYPEDEF op_dsp_30ms = {0, 0, 0};
static uint8_t  dsp_work_mode = 0, ex_dsp_work_mode = 0;	// bit2-0:dsp_mode_ex(0�Զ�1����2��Ⱥ3��վ4Ƶ��); bit6-3:work_mode_ex; bit7:1-manual
static uint16_t dsp_rx_int_ex = 0;

#if defined FORWARDING_LOGIC_ENABLE && defined FORWARDING_LOGIC_NO_GPS
uint8_t forwarding_forbidden_slot = 0;
const uint8_t forwarding_slot_defined[ZZWPRO_TR_STATUS_MAX_SLOT] = {1 << 0, 1 << 2, 1 << 4, 1 << 1, 1 << 3, 1 << 5};	// forwarding seq: 1->3->5->2->4->6
#endif

static uint8_t zzw_xv_frame_to_dsp_length = REG_WRITE_REG_PACKET_IN_ADHOC_LEN;
static uint8_t vocoder_data_to_dsp_length = REG_VOCODER_DATA_IN_LENGTH, vocoder_data_fr_dsp_length = REG_VOCODER_DATA_OUT_LENGTH;
static uint8_t zzwpro_vocoder_one_frame_to_dsp = 6, zzwpro_vocoder_one_frame_fr_dsp = 6, zzwpro_vocoder_offset_of_one_frame = 6;

static uint8_t  send_inner_vocoder = 0;	// 0-mic; 3-internal vocoder; 1-1031; else-o.153(replace by 1031 now);

static uint16_t dsp_mix_flag = 0;
static SAVE_POWER_CONFIG_TYPEDEF rf_save_power;

static uint16_t g_talk_tip_times = 0;
static uint16_t vocoder_data[MAX(REG_READ_REG_VOCODER_DATA_OUT_LEN, REG_WRITE_REG_VOCODER_DATA_IN_LEN)];
static uint32_t g_talk_tip_address, dsp_runtime_misc_flag = 0;
uint32_t num_of_frame_received = 0;
uint32_t slot_rising_edge_int_times = 0, slot_falling_edge_int_times = 0, slot_middle_int_times = 0;
uint32_t zzwpro_xcs = 0;

uint8_t sub_ctcss_enable = 0, replace_voice_flag = 0;	// replace_voice_flag: 0-normal; else-play local vocoder
uint8_t zzwpro_marked_slot = 0, zzwpro_send_slot = 0, stack_tx_slot_downcounter = 0, pdt_report_rssi_cnt = 0, pdt_report_rssi_time = 34/*unit is 10/23/30ms*/;
#ifdef READ_CODEWORD_DELAY_BY_LC_NUM
uint8_t delay_to_read_vocoder = 0;
#endif
#ifdef SHOW_SOLT_DETAIL_WITH_VOICE
uint8_t receive_voice_counter[ZZWPRO_TR_STATUS_MAX_SLOT] = {0, 0, 0, 0, 0, 0};
#endif
#if defined NV_AUTO_TRACE_VOICE_AT_QMODE && !defined NVQ_SENDING_SWITCH_TX_ONLY
uint8_t nvq_send_q_downcount = 0;
#endif
uint8_t dsp_incompatible_reg_len = REG_READ_RSSI_DQI_DEMOD_REAL_LEN;
uint8_t dsp_primary_mode = 0/*0-ģ��,1-DMR/PDT,2-Q,3-N,4-V,5-V25K*/, dsp_secondary_mode = REG_DEMOD_ENABLE_DMR_SHIFT/*DMR/PDT*/, dsp_primary_mode_ex = REG_DEMOD_ENABLE_MRG_SHIFT/*Q*/;
uint16_t nvq_tx_freq_index = 0xffff, conv_tx_freq_index = 0xffff, check_ext_mic_amp = 0;
ZZWPRO_STATUS_STRUCT zzwpro_status[ZZWPRO_TR_STATUS_MAX_SLOT];
ZZWPRO_STATUS_STRUCT zzwpro_status_ex[ZZWPRO_TR_STATUS_MAX_SLOT];

ZZWPRO_FRAME_STATISTIC_TYPEDEF zzwpro_frame[ZZWPRO_TR_STATUS_MAX_SLOT];


#ifdef CHEAT_STACK_WHEN_CRC_FAIL
uint16_t save_zzwpro_q_pre_embedded;
#endif

#ifdef DEBUG_GPS_SYNC_PPS
uint32_t timestamp_gps_2pps;
#endif

#define GET_SAVE_TX_STATUS_SLOT(mode)	(mode > INTERPHONE_MODE_ZZW_ADHOC) ? ((zzwpro_marked_slot < (ZZWPRO_TR_STATUS_MAX_SLOT - 1)) ? (zzwpro_marked_slot + 1) : 0) : ((dsp_edge_slot + 1) & 0x01)
#define GET_SAVE_RX_STATUS_SLOT(mode)	(mode > INTERPHONE_MODE_ZZW_ADHOC) ? (zzwpro_marked_slot ? (zzwpro_marked_slot - 1) : (ZZWPRO_TR_STATUS_MAX_SLOT - 1)) : ((dsp_edge_slot + 1) & 0x01)

#define DSP_SAVE_POWER_SLOT_COUNTER9S				300

#define REG_VOCODER_MODE_DEFAULT_VALUE				0
#define REG_SPEECH_MODE_DEFAULT_VALUE				((2 << REG_SPEECH_MODE_FRAME_SHIFT) | REG_SPEECH_MODE_SE_MASK)
#define REG_SPEECH_OUT_GAIN_SHIFT_DEFAULT_VALUE		3
#define REG_SPEECH_DA_PCM_GAIN_SHIFT_DEFAULT_VALUE	3
#define	REG_HARDWARE_MODE_DEFAULT_VALUE_PDT			0							/* RX not invert, rf is low power */
#define	REG_HARDWARE_MODE_DEFAULT_VALUE_ZZW			REG_HARDWARE_MODE_INV_MASK	/* RX invert, rf is low power */
#define	REG_MOD_GAIN_LB_DEFAULT_VALUE				119							/* TX not invert */
#define	REG_MOD_GAIN_LB_DEFAULT_VALUE_2P			10000 /*3400�������壬û�е����Ŀ��Ƶ��Ʒ���λ���Զ������λ��ʾ����λ*/
#define	REG_MOD_GAIN_HB_DEFAULT_VALUE				0
#define	REG_MOD_GAIN_HB_DEFAULT_VALUE_2P			8000 /*6000����VCO��û�е����Ŀ��Ƶ��Ʒ���λ���Զ������λ��ʾ����λ*/
#define	REG_RDATA_LOC_DEFAULT_VALUE					30
#define	REG_ANALOG_IN_GAIN_DEFAULT_VALUE			27000/*18000*/
#define	REG_ANALOG_IN_GAIN_SHIFT_DEFAULT_VALUE		2/*4*/
#define	REG_ANALOG_OUT_GAIN_DEFAULT_VALUE			18252
#define	REG_ANALOG_OUT_GAIN_SHIFT_DEFAULT_VALUE		0
#define	REG_ANALOG_SUB_LEVEL_DEFAULT_VALUE			2400/*ori=250; 20210707:F4 change to 2400*/
#define	REG_ANALOG_MSK_LEVEL_DEFAULT_VALUE			2083
#define REG_ANALOG_MODE_DEFAULT_VALUE				0	/* bit6-4: 000-�����رգ�001-CTCSS/PL��010-DCS/DPL */
#define REG_ANALOG_AUDIO_DEFAULT_VALUE				10
#define REG_ANALOG_AUDIO_THRESHOLD_DEFAULT_VALUE	11067
#define REG_SYNC_OFFSET_DEFAULT_VALUE_PDT			72/*134->118->72*/
#define REG_SYNC_OFFSET_DEFAULT_VALUE_ZZW			216
#define REG_RESYNC_NUM_DEFAULT_VALUE				0/*100*/

#define REG_DIGITAL_MODE_VALUE						REG_DIGITAL_MODE_FORMAT_MASK

#define DSP_RUNTIME_MISC_SLOT_ADJUST_TIMEOUT		0x00000007
#define DSP_RUNTIME_MISC_CCM_VOCODER_NOFEC			0x00000008
#define DSP_RUNTIME_MISC_SWITCH_MOD					0x00000010
#define DSP_RUNTIME_MISC_INQUIRE_DONE				0x00000020
//#define DSP_RUNTIME_MISC_DELAY_CHECK_NOISE			0x00000040/* �յ��տ��������ʱ1��ʱ϶�ٶ�ȡ���룬������������������ʱ϶�ĵ��� */
#define DSP_RUNTIME_MISC_REPORT_DOWNLINK_RSSI		0x00000080
#define DSP_RUNTIME_MISC_SYNC_WITH_BASE				0x00000100
#define DSP_RUNTIME_MISC_PTT_PRESSED				0x00000200
#if defined VAD_KEY_USE_AS_STICK_PTT || defined PTT_KEY_USE_AS_STICK
#define DSP_RUNTIME_MISC_PTT_DETECTED				0x00000400
#endif
#define DSP_RUNTIME_MISC_SET_GPS_WINDOW				0x00000800
#define DSP_RUNTIME_MISC_JUMP_TO_GPS_CHANNEL		0x00001000
//#define DSP_RUNTIME_MISC_ADJUST_30MS_VOLTAGE_OUTPUT	0x00002000
//#define DSP_RUNTIME_MISC_ADJUST_30MS_VOLTAGE		0x00004000
#define DSP_RUNTIME_MISC_REVERSE_RECOVERY_30MS		0x00008000
#define DSP_RUNTIME_MISC_VAD_WAIT_IDLE				0x00010000
#define DSP_RUNTIME_MISC_GPS_PPS_IS_OK				0x00010000
#define DSP_RUNTIME_MISC_AUTOTRACE_SWITCH_WORKMODE	0x00020000
#define DSP_RUNTIME_MISC_DIFF_TR_AT_VOICE_CHANNEL	0x00040000
#define DSP_RUNTIME_MISC_NVQ_TX_MODE				0x00080000/*0-Nor(PDT/N),1-Voice channel(N/Q)*/
//#define DSP_RUNTIME_MISC_DELAY_CHECK_NOISE_EX		0x00100000
#define DSP_RUNTIME_MISC_DSP_ANALOG_PTT_OK			0x00200000
#define DSP_RUNTIME_MISC_PPS_STATE					0x00400000
#define DSP_RUNTIME_MISC_CHECK38P4_DONE				0x03000000
#define DSP_RUNTIME_MISC_SWITCH_VOCODER_SPEED_EN	0x08000000
  #define DSP_RUNTIME_MISC_SWITCH_VOCODER_2400		0x00000000
  #define DSP_RUNTIME_MISC_SWITCH_VOCODER_2200		0x04000000
#define DSP_RUNTIME_MISC_PPS_BEEN_DETECTED			0x10000000
#define DSP_RUNTIME_MISC_ENTER_ADMITTEST_MODE		0x20000000



#define GET_DSP_EX_WORK_TYPE()						(ex_dsp_work_mode & 0x07)
#define CLR_DSP_EX_WORK_TYPE()						ex_dsp_work_mode &= ~0x07
#define SET_DSP_EX_WORK_TYPE(ex)					ex_dsp_work_mode |= ex & 0x07

#define GET_DSP_EX_WORK_MODE()						((ex_dsp_work_mode >> 3) & 0x0f)
#define CLR_DSP_EX_WORK_MODE()						ex_dsp_work_mode &= ~(0x0f << 3)
#define SET_DSP_EX_WORK_MODE(ex)					ex_dsp_work_mode |= (ex & 0x0f) << 3

uint16_t reg_mod_gain_dc[4] = {REG_MOD_GAIN_LB_DEFAULT_VALUE_2P, REG_MOD_GAIN_HB_DEFAULT_VALUE_2P, 0, 0};	// gain[0]/gain[1]/dc[0]/dc[1]


#ifdef AIR_DATA_DEBUG_OUTPUT
void print_air_data(char *prehead, uint8_t *ptr8)
{
	vlog_v("config","[%04d]%s:%02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X",
		slot_rising_edge_int_times, prehead,
				ptr8[0], ptr8[1], ptr8[2], ptr8[3], ptr8[4], ptr8[5], ptr8[6], ptr8[7], ptr8[8], ptr8[9],
				ptr8[10], ptr8[11], ptr8[12], ptr8[13], ptr8[14], ptr8[15], ptr8[16], ptr8[17], ptr8[18], ptr8[19],
				ptr8[20], ptr8[21], ptr8[22], ptr8[23], ptr8[24], ptr8[25], ptr8[26], ptr8[27], ptr8[28], ptr8[29],
				ptr8[30], ptr8[31], ptr8[32], ptr8[33], ptr8[34], ptr8[35]);
}
//#else
//  #define print_air_data(prehead, ptr8)
#endif

void gen_ramp_waveform(uint8_t low_high, uint8_t offset, uint16_t *up, uint16_t *down)	// low_high: 0-low; 1-high; else-tiny
{
	uint8_t i, n, rf_type, up_pts, down_pts;
	uint16_t w_max, step;

	rf_type = get_mobile_rf_type();
	if (rf_type == MOBILE_RF_TYPE_DDS)
	{
		if (low_high == 0)
			w_max = g_factory_inst->low_power_level + offset;
		else if (low_high == 1)
			w_max = g_factory_inst->high_power_level + offset;
		else
			w_max = 1200;
		up_pts = g_factory_inst->rising_points;
		down_pts = g_factory_inst->falling_points;
	}
	else
	{
		if (dev_power_ctrl_by_db() == 0)
		{
			if (low_high == 0)
				w_max = g_rf2p_tune_reg->dsp_2p_pll[offset].tx_low_power;
			else if (low_high == 1)
				w_max = g_rf2p_tune_reg->dsp_2p_pll[offset].tx_high_power;
			else
				w_max = 400;
		}
		else
		{
			w_max = MIN(4095, g_rf2p_tune_reg->power_ctrl[maintain_setup_parameter(PARA_OPERATE_READ, RF_POWER_PARAS_POS, 0)].power_level);
		}
		up_pts = g_rf2p_tune_reg->tx_rising_pts;
		down_pts = g_rf2p_tune_reg->tx_falling_pts;
		if (rf_type == MOBILE_RF_TYPE_2P_FULL_BAND)
		{
			up[0] = w_max;
			up[1] = up_pts;
			up[2] = g_rf2p_tune_reg->tx_pre_rising_pts;
			down[0] = w_max;
			down[1] = down_pts;
			down[2] = g_rf2p_tune_reg->tx_post_falling_pts;
			return;
		}
	}

	// cal up waveform
	if ((up_pts < 1) || (up_pts >= 48))
		up_pts = 35;
	step = w_max / up_pts;
	for (i = 0; i < up_pts; i++)
		up[i] = step * i;
	for (; i < 48; i++)
		up[i] = w_max;

	// cal down waveform
	if ((down_pts < 1) || (down_pts >= 48))
		down_pts = 35;
	step = w_max / down_pts;
	if (get_mobile_rf_type() == MOBILE_RF_TYPE_DDS)
	{
		for (i = 0, n = 48 - down_pts - 1; i < n; i++)
			down[i] = w_max;
		for (n = 0; n < down_pts; n++, i++)
			down[i] = w_max - step * n;
		down[i] = 0;
	}
	else
	{
		for (i = 0; i < down_pts; i++)
			down[i] = w_max - step * i;
		for (; i < 48; i++)
			down[i] = 0;
	}
}

// WRITE_BASE_ADDR+offsetof(REG_WRITE_OBJ,reg_system_update)
uint32_t dsp_config_regs[(4 + sizeof(DSP_REG_TYPEDEF) + 4) / sizeof(uint32_t)];
void dsp_reg_initial(void)
{
	uint8_t  esn = get_real_esn_second_sector();
	uint32_t *addr;

	debug_para_struct_length_check("DSP CONFIG REG", DSP_REG_TYPEDEF, 124 - 32);

//	vlog_v("config","INIT the dsp config reg...");
	addr = dsp_config_regs;
	*addr++ = DSP_CONFIG_REG_INIT_FLAG;
	g_dsp_reg = (DSP_REG_TYPEDEF *)addr;

	g_dsp_reg->reg_vocoder_mode = REG_VOCODER_MODE_DEFAULT_VALUE;	// enable encode & decode of the vocoder
	g_dsp_reg->reg_speech_mode = REG_SPEECH_MODE_DEFAULT_VALUE;		// 3 frames as one unit
	if (get_mobile_rf_type() == MOBILE_RF_TYPE_DDS)
	{
		if (get_mobile_hardware_mode() == MOBILE_MODE_PDT_ZZW)
		{
			g_dsp_reg->reg_hardware_mode = (interphone_mode == INTERPHONE_MODE_ZZW_ADHOC) ? REG_HARDWARE_MODE_DEFAULT_VALUE_ZZW : REG_HARDWARE_MODE_DEFAULT_VALUE_PDT;
			g_dsp_reg->reg_mod_gain[0] = REG_MOD_GAIN_LB_DEFAULT_VALUE;
		}
		else
		{
			g_dsp_reg->reg_hardware_mode = REG_HARDWARE_MODE_DEFAULT_VALUE_PDT;
			g_dsp_reg->reg_mod_gain[0] = REG_MOD_GAIN_LB_DEFAULT_VALUE | REG_MOD_GAIN_INV_MASK;
		}
	}
	else
	{
		if (get_mobile_rf_type() == MOBILE_RF_TYPE_2P_150M)	// 150 �շ�������; 400 �շ�����
//			g_dsp_reg->reg_hardware_mode = REG_HARDWARE_MODE_DEFAULT_VALUE_ZZW;
			g_dsp_reg->reg_hardware_mode = REG_HARDWARE_MODE_DEFAULT_VALUE_PDT;	// // 150 �շ�������
//		else if ((get_mobile_rf_type() == MOBILE_RF_TYPE_2P_400M) || (get_mobile_rf_type() == MOBILE_RF_TYPE_2P_FULL_BAND))
		else
			g_dsp_reg->reg_hardware_mode = REG_HARDWARE_MODE_DEFAULT_VALUE_PDT;

		g_dsp_reg->reg_mod_gain[0] = REG_MOD_GAIN_LB_DEFAULT_VALUE_2P;
	}
	g_dsp_reg->reg_mod_gain[1] = (get_mobile_rf_type() == MOBILE_RF_TYPE_DDS) ? REG_MOD_GAIN_HB_DEFAULT_VALUE : REG_MOD_GAIN_HB_DEFAULT_VALUE_2P;
	g_dsp_reg->reg_rdata_loc = REG_RDATA_LOC_DEFAULT_VALUE;
	g_dsp_reg->reg_analog_in_gain = REG_ANALOG_IN_GAIN_DEFAULT_VALUE;
	g_dsp_reg->reg_analog_in_gain_shift = REG_ANALOG_IN_GAIN_SHIFT_DEFAULT_VALUE;
	g_dsp_reg->reg_analog_out_gain = REG_ANALOG_OUT_GAIN_DEFAULT_VALUE;
	g_dsp_reg->reg_analog_out_gain_shift = REG_ANALOG_OUT_GAIN_SHIFT_DEFAULT_VALUE;
	g_dsp_reg->reg_analog_sub_level = REG_ANALOG_SUB_LEVEL_DEFAULT_VALUE;
	g_dsp_reg->reg_analog_msk_level = REG_ANALOG_MSK_LEVEL_DEFAULT_VALUE;
	g_dsp_reg->reg_analog_mode = REG_ANALOG_MODE_DEFAULT_VALUE;
	if (maintain_setup_parameter(PARA_OPERATE_READ, SQUELCH_DOOR_SETUP_POS, 0) == 0)
		g_dsp_reg->reg_analog_mode &= ~REG_ANALOG_MODE_OPEN_MASK;
	else
		g_dsp_reg->reg_analog_mode |= REG_ANALOG_MODE_OPEN_MASK;
	g_dsp_reg->reg_analog_mode &= ~REG_ANALOG_MODE_MSKMON_MASK;
	g_dsp_reg->reg_analog_audio = REG_ANALOG_AUDIO_DEFAULT_VALUE;
	g_dsp_reg->reg_analog_audio_threshold = REG_ANALOG_AUDIO_THRESHOLD_DEFAULT_VALUE;
	g_dsp_reg->reg_sync_offset = (interphone_mode == INTERPHONE_MODE_ZZW_ADHOC) ? REG_SYNC_OFFSET_DEFAULT_VALUE_ZZW : REG_SYNC_OFFSET_DEFAULT_VALUE_PDT;
	g_dsp_reg->reg_resync_offset = REG_RESYNC_NUM_DEFAULT_VALUE;
	g_dsp_reg->reg_digital_mode = REG_DIGITAL_MODE_VALUE;
	g_dsp_reg->reg_vocoder_config = 0;

	if ((esn == MOBILE_MODE_PDT_ZZW_815V2) || (esn == MOBILE_MODE_PDT_ZZW_815V3) || (esn == MOBILE_MODE_PDT_ZZW_810C) ||
		(esn == MOBILE_MODE_PDT_ZZW_811) || (esn == MOBILE_MODE_PDT_ZZW_811A) ||
		(esn == MOBILE_MODE_PDT_ZZW_816V) || DEV_IS_805_SERIES(esn))	// 2021-02-03 change
	{
		g_dsp_reg->reg_analog_audio_threshold = 5400;
		g_dsp_reg->reg_analog_in_gain = 7000;
		g_dsp_reg->reg_analog_out_gain_shift = 2;
	}
}

void check_dsp_configure(uint8_t cold_start)
{
	set_dsp_op_pointer();

#ifndef BYPASS_DSP_OPERATION

	// check DSP is OK and init it if necessary

#endif
	dsp_interrupt_config(0xf2);
//	if (let_dsp_todo_something(0, BASEBAND_TYPE_VERSION))
		let_dsp_todo_something(0, BASEBAND_TYPE_VERSION);
		set_module_init_done_flag(MODULE_INIT_DONE_DSP);
}

void dsp_wr_data32(uint32_t address, uint32_t val)
{
}

uint32_t dsp_rd_data32(uint32_t address)
{
	return 0;
}

void dsp_send_cmd32(uint16_t cmd)
{
	if (cmd == DSP_CMD_SPEECH_IN_ON)
		aipl_pa_ctrl(1, 1);
	else if (cmd == DSP_CMD_SPEECH_IN_OFF)
		aipl_pa_ctrl(1, 0);
}


uint16_t dsp_reg_read32(uint16_t reg_addr, uint16_t *data)
{
	return 0;
}

uint16_t dsp_reg_write32(uint16_t reg_addr, uint16_t *data)
{
	return 0;
}


void dsp_read_rssi(int16_t *rssi)
{
	int16_t rssi_read[REG_READ_RSSI_DQI_DEMOD_DSP_LEN];

	dsp_reg_read(REG_DIGITAL_RSSI_ADDR, (uint16_t *)rssi_read);
#if DEBUG_DSP_SLOT_RSSI == 1
	vlog_v("config","[%3d %3d %3d]%d/%d/%d/%d/%d/%d",
		slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
		rssi_read[0], rssi_read[1], rssi_read[2], rssi_read[3], rssi_read[4], rssi_read[5]);
#endif
	if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_AUTOTRACE_SWITCH_WORKMODE)
	{
		rssi[0] = rssi_read[dsp_secondary_mode];
		rssi[1] = rssi_read[dsp_primary_mode];
	}
	else
	{
		rssi[0] = rssi_read[dsp_primary_mode];
		rssi[1] = rssi_read[dsp_secondary_mode];
	}
}

void dsp_read_rssi_ex(int16_t *rssi)
{
	int16_t rssi_read[REG_READ_RSSI_DQI_DEMOD_DSP_LEN];

	dsp_reg_read(REG_DSP64_REG_DIGITAL_RSSI_EX_ADDR, (uint16_t *)rssi_read);
#if DEBUG_DSP_SLOT_RSSI == 1
	vlog_v("config","ex[%3d %3d %3d]%d/%d/%d/%d/%d/%d",
		slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
		rssi_read[0], rssi_read[1], rssi_read[2], rssi_read[3], rssi_read[4], rssi_read[5]);
#endif
	rssi[0] = rssi_read[dsp_primary_mode_ex];
}

void dsp_read_dqi(int16_t *dqi)
{
	// get dqi at mcu_inf_rcv_frame() automatically
	dqi[1] = 0;
}

void dsp_read_dqi_ex(int16_t *dqi)
{
	dqi[0] = 0;
	dqi[1] = 0;
}

void dsp_read_demod_state(uint16_t *demod)
{
	uint16_t demod_read[REG_READ_RSSI_DQI_DEMOD_DSP_LEN];

	dsp_reg_read(REG_DEMOD_STATE_ADDR, demod_read);
	demod[0] = demod_read[dsp_primary_mode];
	demod[1] = demod_read[dsp_secondary_mode];
//	vlog_v("config","%x %x %x %x %x %x(%d %d)", demod_read[0], demod_read[1], demod_read[2], demod_read[3], demod_read[4], demod_read[5], dsp_primary_mode, dsp_secondary_mode);
}

void dsp_read_demod_state_ex(uint16_t *demod)
{
	uint16_t demod_read[REG_READ_RSSI_DQI_DEMOD_DSP_LEN];

	dsp_reg_read(REG_DSP64_REG_DEMOD_STATE_EX_ADDR, demod_read);
	demod[0] = demod_read[dsp_primary_mode_ex];
#if DEBUG_DSP_INT_OFFSET_OF_SLOT > 1
//	vlog_v("config","%d:%04X %04X %04X %04X %04X %04X", dsp_primary_mode_ex, demod_read[0], demod_read[1], demod_read[2], demod_read[3], demod_read[4], demod_read[5]);
#endif
}

uint32_t dsp_read_19m2_crystal(void)
{
	uint16_t crystal_19m2[2];

	dsp_reg_read(REG_DSP64_CRYSTAL_19M2_ADDR, crystal_19m2);
//	vlog_v("config","19.2=%d", ((uint32_t)crystal_19m2[0] << 16) | crystal_19m2[1]);
	return ((uint32_t)crystal_19m2[0] << 16) | crystal_19m2[1];
}

uint16_t dsp_read_ext_mic_amp(void)
{
	uint16_t ext_mic_amp;

	dsp_reg_read(REG_DSP64_SPEECH_EXT_AMP_ADDR, &ext_mic_amp);

	return ext_mic_amp;
}

// year: 0-7, start at 2014(e.g. 0==2014)
// month: 1-12
// day: 1-31
// major/minor: 0-3
#define GEN_COMPILE_TIME_VERSION(year,month,day,major,minor)	((((unsigned short)(year)&0x0007)<<13)|(((unsigned short)(month)&0x000f)<<9)|(((unsigned short)(day)&0x001f)<<4)|(((unsigned short)(major)&0x0003)<<2)|((unsigned short)(minor)&0x0003))
void print_dsp_version(void)
{
	vlog_v("config","========%s========", "DSP information");
	vlog_v("config","  compile date: %04d-%02d-%02d", 2000 + g_runtime_inst.device_information.dsp_compile.ct_year,
			g_runtime_inst.device_information.dsp_compile.ct_month, g_runtime_inst.device_information.dsp_compile.ct_day);
	vlog_v("config","  compile version: %x.%x.%x.%x",
			g_runtime_inst.device_information.dsp_soft_ver.ver_major >> 4,
			g_runtime_inst.device_information.dsp_soft_ver.ver_major & 0x0f,
			g_runtime_inst.device_information.dsp_soft_ver.ver_minor >> 4,
			g_runtime_inst.device_information.dsp_soft_ver.ver_minor & 0x0f);

}

void get_dsp_compile_date_version(uint8_t *dat)
{
	uint8_t n;
	char dsp_compile_date[12] = "2024-09-04";

	// 8B: 12 01 06 09 04 00 00 00 - 1.2.01/1.2.0.1(decimal) 2018+6(��)-09(��)-04(��)
	n = sprintf(dsp_compile_date, "%04d-", 2018 + dat[2]);
	n += sprintf(dsp_compile_date + n, "%02d-", dat[3] & 0x0f);
	sprintf(dsp_compile_date + n, "%02d", dat[4] & 0x001f);

	n = 0;	// meaningless at P2
	set_software_version(2, dat[0], dat[1], (uint8_t *)dsp_compile_date, (uint8_t *)0, &n);
	print_dsp_version();
}

void set_voice_source(uint8_t src)
{
	if (src && (src != 3))
		super_frame_count = 0;
	send_inner_vocoder = src;
}

uint16_t is_pll_locked(uint8_t tx_rx, uint16_t check_times)	// 0: rx; !0: tx
{
	uint16_t i, tmp, tmp_ex;

	for (i = 0; i < check_times; i++)
	{
		if (tx_rx == 0)
		{
			dsp_reg_read(REG_PLL_RX_LOCK_ADDR, &tmp);			// rx pll locked flag
			if (dev_is_809())
				dsp_reg_read(REG_DSP64_PLL_RX_LOCK_EX_ADDR, &tmp_ex);// rx2 pll locked flag
			else
				tmp_ex = 0xffff;
			vlog_v("config","[%d]RX1/2 pll locked=%d/%d", i + 1, tmp, tmp_ex);
		}
		else
		{
			tmp_ex = 0xffff;
			dsp_reg_read(REG_PLL_TX_LOCK_ADDR, &tmp);			// tx pll locked flag
			vlog_v("config","[%d]TX pll locked=%d", i + 1, tmp);
		}

		if (tmp && tmp_ex)
			return i + 1;
		else
			sys_delay(20);
	}

	vlog_v("config","[%d]%s pll locked fail(%d/%d)", i + 1, (tx_rx == 0) ? "RX1/2" : "TX", tmp, tmp_ex);
	return 0;
}

uint16_t is_ad9864_tuned(uint16_t check_times)
{
	uint16_t i, tmp[2], tmp_ex[2];

	for (i = 0; i < check_times; i++)
	{
		dsp_reg_read(REG_AD9864_TUNE_READ_ADDR, tmp);		// ad9864 tune state
		if (dev_is_809())
			dsp_reg_read(REG_DSP64_AD9864_TUNE_READ_EX_ADDR, tmp_ex);
		else
			tmp_ex[0] = 0;
		if (((tmp[0] & REG_AD9864_TUNE_READ_FAIL_MASK) == 0) && ((tmp_ex[0] & REG_AD9864_TUNE_READ_FAIL_MASK) == 0))
		{
			vlog_v("config","[%d]Both two AD9864 TUNE successfully!", i + 1);
			return i + 1;
		}
		else
		{
			sys_delay(100);
		}
	}
	vlog_v("config","[%d]AD9864 1/2 TUNE:%s/%s", i + 1, (tmp[0] & REG_AD9864_TUNE_READ_FAIL_MASK) ? "fail" : "succ",
			(tmp_ex[0] & REG_AD9864_TUNE_READ_FAIL_MASK) ? "fail" : "succ");
	return 0;
}

#define RSSI_HISTORY_NUMBER	33
uint8_t phone_update_rssi_each_second(void);
void cal_rssi_value_avg(int16_t rssi)
{
	static uint8_t rssi_history_num = 0;
	static int16_t rssi_history = 0, rssi_min = 256, rssi_max = -256;

	if (phone_update_rssi_each_second())
	{
		rssi_history += rssi;
		rssi_min = MIN(rssi_min, rssi);
		rssi_max = MAX(rssi_max, rssi);
		if (rssi_history_num < RSSI_HISTORY_NUMBER - 1)
		{
#if DEBUG_DSP_SLOT_RSSI == 1
			vlog_v("config","\t[%d]+%d=%d", rssi_history_num, rssi, rssi_history);
#endif
			rssi_history_num++;
			if (rssi > rssi_value)
				rssi_value = rssi;
		}
		else
		{
			rssi_value = (rssi_history - rssi_min - rssi_max) / (RSSI_HISTORY_NUMBER - 2);
#if DEBUG_DSP_SLOT_RSSI == 1
			vlog_v("config","\t[%d]cal=%d", rssi_history_num, rssi_value);
#endif
cal_rssi_value_avg_reset:
			if (rssi_history_num)
			{
				rssi_history_num = 0;
				rssi_history = 0;
				rssi_min = 256;
				rssi_max = -256;
			}
		}
	}
	else
	{
		rssi_value = rssi;
		goto cal_rssi_value_avg_reset;
	}
}

#define CAL_RSSI_VALUE_REAL(rssi)		(g_factory_inst->rssi_regulator + (rssi))
#define CAL_RSSI_VALUE_REAL_EX(rssi)	(g_rssi_regulator_ex + (rssi))
#define CAL_RSSI_VALUE_REL(rssi)		(CAL_RSSI_VALUE_REAL(rssi) + 130)
#define CAL_RSSI_VALUE_REL_EX(rssi)		(CAL_RSSI_VALUE_REAL_EX(rssi) + 130)

uint8_t cal_rssi_value_relative(uint8_t is_ex, int16_t rssi)
{
	int16_t rssi_rel;

	if (is_ex)
		rssi_rel = CAL_RSSI_VALUE_REL_EX(rssi);
	else
		rssi_rel = CAL_RSSI_VALUE_REL(rssi);

	return (rssi_rel <= 0) ? 1 : (uint8_t)rssi_rel;
}

uint16_t cal_back_rssi_value_avg(uint8_t reset)
{
	static uint8_t rssi_history_num = 0;
	static int16_t rssi_history = 0, rssi_min = 256, rssi_max = -256;
	int16_t rssi;

	if (reset == 0)
	{
		rssi = rssi_curr_slot;
		rssi_history += rssi;
		rssi_min = MIN(rssi_min, rssi);
		rssi_max = MAX(rssi_max, rssi);
		if (rssi_history_num < BACK_TSCC_ONCE_CHECK - 1)
		{
			rssi_history_num++;
			rssi = 0x8000 | (uint8_t)cal_rssi_value_relative(0, rssi);
		}
		else
		{
			rssi = (uint8_t)cal_rssi_value_relative(0, (rssi_history - rssi_min - rssi_max) / (BACK_TSCC_ONCE_CHECK - 2));
cal_back_rssi_value_avg_reset:
			if (rssi_history_num)
			{
				rssi_history_num = 0;
				rssi_history = 0;
				rssi_min = 256;
				rssi_max = -256;
			}
		}
	}
	else
	{
		goto cal_back_rssi_value_avg_reset;
	}
	return (uint16_t)rssi;
}

uint8_t get_rssi_value_rel(uint8_t pdn)	// 0:report rssi; 0xff:report noise; 0xfe:previous slot's rssi; else-sync signal's rssi
{
	return cal_rssi_value_relative(0, (pdn == 0) ? rssi_value : ((pdn == 0xff) ? rssi_prev_slot_noise : ((pdn == 0xfe) ? rssi_prev_slot : rssi_sync)));
}

uint8_t get_rssi_value_rel_ex(uint8_t pdn)	// 0:report rssi; 0xff:report noise; 0xfe:previous slot's rssi; else-sync signal's rssi
{
	return cal_rssi_value_relative(1, (pdn == 0) ? rssi_value_ex : ((pdn == 0xff) ? rssi_prev_slot_noise_ex : ((pdn == 0xfe) ? rssi_prev_slot_ex : rssi_sync_ex)));
}

uint8_t get_rssi_value_rel_imme(uint8_t is_ex)	// get the prev noise
{
	if (is_ex)
		return cal_rssi_value_relative(1, rssi_curr_slot_ex);
	else
		return cal_rssi_value_relative(0, rssi_curr_slot);
}

int16_t get_rssi_value_real(void)
{
	return (int16_t)(g_factory_inst->rssi_regulator + rssi_value);
}

int16_t get_rssi_value_real_ex(void)
{
	return (int16_t)(g_rssi_regulator_ex + rssi_prev_slot_ex);
}

uint8_t get_zzwpro_receive_slot(void);
uint8_t get_signal_value_discontinuous(void)
{
	return own_playing_voice_now() ? rssi_voice/*zzwpro_status[replace_voice_flag].rssi*/ : get_rssi_value_rel(0xfd);
}

uint8_t get_signal_level(void)
{
	int16_t rssi;
/*
	if (interphone_mode > INTERPHONE_MODE_ZZWPRO_Q)
//		rssi = -130 + zzwpro_status[own_playing_voice_now() ? replace_voice_flag : get_zzwpro_receive_slot()].rssi;
		rssi = -130 + get_signal_value_discontinuous();
	else
		rssi = get_rssi_value_real();
*/
	if (((interphone_mode >= INTERPHONE_MODE_PDT_TRUNKING) && (interphone_mode <= INTERPHONE_MODE_MPT_CONV)) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_Q))
		rssi = get_rssi_value_real();
	else
		rssi = -130 + get_signal_value_discontinuous();

	if (rssi >= SIGNAL_LEVEL5)
		rssi_level = 5;
	else if (rssi >= SIGNAL_LEVEL4)
		rssi_level = 4;
	else if (rssi >= SIGNAL_LEVEL3)
		rssi_level = 3;
	else if (rssi >= SIGNAL_LEVEL2)
		rssi_level = 2;
	else if (rssi >= SIGNAL_LEVEL1)
		rssi_level = 1;
	else
		rssi_level = 0;

	return rssi_level;
}

uint16_t get_rssi_value_avg(uint16_t times)
{
	int16_t i, rssi_avg;

	for (i = 0, rssi_avg = 0; i < times; i++)
	{
		sys_delay(10);
		rssi_avg += admit_test_mode_is_ex() ? rssi_curr_slot_ex : rssi_curr_slot;
	}
	rssi_avg /= times;
	vlog_v("config","DSP RSSI=%5d(0x%04x)", rssi_avg, rssi_avg);
	return (uint16_t)rssi_avg;
}
/*
const static uint8_t e_tunable_filter_adc_table[2][11] = {
	{0, 4, 13, 23, 34, 44, 55, 68, 79, 96, 101},
	{22, 28, 34, 37, 44, 50, 60, 68, 77, 87, 98}
};
uint8_t get_electrically_tunable_filter_adc(uint8_t tr, float freq)	// 0-tx; else-rx
{
	uint8_t index;

	if ((freq > 349.9875) && (freq < 352.5))
		index = 0;
	else if ((freq > 352.4875) && (freq < 357.5))
		index = 1;
	else if ((freq > 357.4875) && (freq < 362.5))
		index = 2;
	else if ((freq > 362.4875) && (freq < 367.5))
		index = 3;
	else if ((freq > 367.4875) && (freq < 372.5))
		index = 4;
	else if ((freq > 372.4875) && (freq < 377.5))
		index = 5;
	else if ((freq > 377.4875) && (freq < 382.5))
		index = 6;
	else if ((freq > 382.4875) && (freq < 387.5))
		index = 7;
	else if ((freq > 387.4875) && (freq < 392.5))
		index = 8;
	else if ((freq > 392.4875) && (freq < 397.5))
		index = 9;
//	else if ((freq > 397.4875) && (freq < 400.0))
	else
		index = 10;

	return e_tunable_filter_adc_table[(tr == 0) ? 0 : 1][index];
}
*/
void print_freq_paras(uint8_t tr, float freq, uint16_t *pll_ps)	// 0-tx; else-rx
{
#ifndef DSP_NEW_FREQ_INTERFACE
	vlog_v("config","PLL%c(%fMhz): R0=0x%08x, R1=0x%08x, R2=0x%04x, R3=0x%04x, R4=0x%08x, R5=0x%08x, R6=0x%04x",
			(tr == 0) ? 'T': 'R', freq,
			((uint32_t)(pll_ps[0]) << 16) | pll_ps[1],
			((uint32_t)(pll_ps[2]) << 16) | pll_ps[3],
			pll_ps[4],
			pll_ps[5],
			((uint32_t)(pll_ps[6]) << 16) | pll_ps[7],
			((uint32_t)(pll_ps[8]) << 16) | pll_ps[9],
			pll_ps[10]);
#else
	vlog_v("config","PLL%c(%fMhz): R0=0x%04X,R1=0x%04X,R2=0x%04X,R3=0x%04X,R4=0x%04X,R5=0x%04X,R6=0x%04X,R7=0x%04X,R8=0x%04X,R9=0x%04X",
			(tr == 0) ? 'T': 'R', freq, pll_ps[0], pll_ps[1], pll_ps[2], pll_ps[3], pll_ps[4], pll_ps[5], pll_ps[6], pll_ps[7], pll_ps[8], pll_ps[9]);
#endif
}

#define REF_IN		19.2f
#define	D_FACTOR	2.0f
#define DIVIDER		(1 << 18)

/*
	����ģʽ�µ�SKY7231�ο���Ƶ����Ϊ3��Ƶ
	1 PDTģʽ�µ�һ����Ƶ�ʵ�������
	a)	�շ����̶�10MHz��LO1=RX-73.35MHz&LO1=TX-63.35MHz
	b)	�շ�ͬƵ��LO1=RX-73.35MHz&LO1=TX-73.35MHz
	2 ������ģʽ�µ�һ����Ƶ�ʵ�������
	a)	�շ�ͬƵ��LO1=RX-63.35MHz&LO1=TX-63.35MHz
*/
#define RF_DIVISION_FACTOR_INDEX			0
#define BACKGROUND_TSCC_FLAG_INDEX			4
#define ELECTRICALLY_TUNABLE_FILTER_INDEX	5

#ifndef DSP_NEW_FREQ_INTERFACE
uint16_t rf_reg_define[6] = {0x0001, 0x0000, 0x001f, 0x0000, 0x0000, 0x0000};
#else
uint16_t get_rf_2p_tune_level(float mhz);
void config_2p_power(uint16_t lv);
#endif

uint16_t get_tune_adc_table(uint8_t tr, float mhz)	// tr: 0-tx; else-rx
{
	return 0;
}

void freq_to_paras(uint8_t tr, uint8_t is_tscc, float freq, uint16_t *paras)	// tr: 0-tx; else-rx; freq=real
{
//	unsigned int rf3_reg = 0x0001;
//	unsigned int rf4_reg = 0x0000001f;
//	unsigned int rf5_reg = 0x00000000;
//	unsigned int rf6_reg = 0x0000;
#ifndef DSP_NEW_FREQ_INTERFACE
	uint32_t rf0, rf1;
	uint16_t rf2;
#else
	uint32_t rf0;
	uint16_t rf_segment;
#endif
	uint8_t mobile_rf_mode, mobile_hard_mode;
	int32_t divident;
	float n_frac, offset;

	mobile_rf_mode = get_mobile_rf_type();
	mobile_hard_mode = get_mobile_hardware_mode();
	if (mobile_rf_mode == MOBILE_RF_TYPE_DDS)
	{
		if (mobile_hard_mode == MOBILE_MODE_PDT_ONLY)
		{
			n_frac = 2.0;
			offset = 73.35;
		}
		else if (mobile_hard_mode == MOBILE_MODE_ZZW_ONLY)
		{
			n_frac = 2.0;
			offset = 63.35;
		}
		else
		{
			n_frac = 3.0;
			if (interphone_mode == INTERPHONE_MODE_ZZW_ADHOC)
				offset = -63.35;
			else
				offset = -73.35;
		}
#ifndef DSP_NEW_FREQ_INTERFACE
		rf_reg_define[RF_DIVISION_FACTOR_INDEX] = (uint16_t)n_frac - 1;
#else
		paras[3] = (uint16_t)n_frac - 1;			// Reference Frequency Dividers Register
		paras[4] = get_tune_adc_table(tr, freq);	// ����vcoƫ�õ�ѹ
#endif
		n_frac = (freq + offset) * n_frac / REF_IN;
	}
	else
	{
		rf_segment = get_rf_2p_tune_level(freq - g_freq_offset);
		if (mobile_rf_mode == MOBILE_RF_TYPE_2P_150M)
		{
			n_frac = 8.0;
#ifndef DSP_NEW_FREQ_INTERFACE
			rf_reg_define[RF_DIVISION_FACTOR_INDEX] = 4 - 1;// div factor=4, and frequency=real_f*2(so n_frac=factor*2)
#else
			paras[3] = 4 - 1;								// div factor=4, and frequency=real_f*2(so n_frac=factor*2)
#endif
			offset = 45.0;
		}
//		else if (mobile_rf_mode == MOBILE_RF_TYPE_2P_400M)
		else
		{
//			if (mobile_rf_mode == MOBILE_RF_TYPE_2P_FULL_BAND)	// add at 2018-07-13 for test; annotate at 2018-07-16
//				n_frac = 6.0;
//			else
				n_frac = 3.0;
#ifndef DSP_NEW_FREQ_INTERFACE
			rf_reg_define[RF_DIVISION_FACTOR_INDEX] = (uint16_t)n_frac - 1;
#else
			paras[3] = (uint16_t)n_frac - 1;
#endif
//			if (mobile_rf_mode == MOBILE_RF_TYPE_2P_FULL_BAND)	// add at 2018-06-14	// annotate at 2018-07-06: cal pll config word at DSP
//				offset = -73.35;	//  annotate at 2017-04-20
//			else
				offset = -63.35;
		}

		if (tr == 0)
		{
			n_frac = freq * n_frac / REF_IN;
#ifdef DSP_NEW_FREQ_INTERFACE
			paras[4] = g_rf2p_tune_reg->dsp_2p_pll[rf_segment].tx_v_preset4;
			paras[6] = g_rf2p_tune_reg->dsp_2p_pll[rf_segment].tx_mod_gain6;
			if (get_real_esn_second_sector() == MOBILE_MODE_PDT_ZZW_815)
				paras[7] = g_rf2p_tune_reg->dsp_2p_pll[rf_segment].tx_mod_gain7;
			else
//				paras[7] = g_rf2p_tune_reg->dsp_2p_pll[rf_segment].tx_gain10 / 100;	// ������ƣ�10���Ƶĸ�2λΪ������2λΪ�գ���Χ����0-63
				paras[7] = MIN(63, g_rf2p_tune_reg->power_ctrl[maintain_setup_parameter(PARA_OPERATE_READ, RF_POWER_PARAS_POS, 0)].power_tx_gain);
			paras[8] = reg_mod_gain_dc[2] & ~0x8000;	// g_rf2p_tune_reg->tx_mod_dc8;
			paras[9] = reg_mod_gain_dc[3];				// g_rf2p_tune_reg->tx_mod_dc9;
			config_2p_power(rf_segment);
#endif
		}
		else
		{
			n_frac = (freq + offset) * n_frac / REF_IN;
#ifdef DSP_NEW_FREQ_INTERFACE
			paras[4] = g_rf2p_tune_reg->dsp_2p_pll[rf_segment].rx_v_preset4;
			paras[5] = g_rf2p_tune_reg->dsp_2p_pll[rf_segment].rx_v_etf5;
//			paras[7] = g_rf2p_tune_reg->dsp_2p_pll[rf_segment].tx_gain10 % 100;	// ������ƣ�10���Ƶĸ�2λΪ������2λΪ�գ���Χ����0-63
			paras[7] = g_rf2p_tune_reg->power_ctrl[maintain_setup_parameter(PARA_OPERATE_READ, RF_POWER_PARAS_POS, 0)].power_rx_gain;
#endif
		}
	}

	if ((n_frac - (uint32_t)n_frac) >= 0.5f)
		rf0 = (uint32_t)n_frac + 1;
	else
		rf0 = (uint32_t)n_frac;

	rf0 -= 32;
	n_frac = (n_frac - rf0 - 32) * DIVIDER;

	divident = (int32_t)n_frac;
	if (n_frac >= 0.0f)
	{
		if (n_frac - divident >= 0.5f)
			divident += 1;
	}
	else
	{
		if (-n_frac + divident >= 0.5f)
			divident -= 1;
	}

#ifndef DSP_NEW_FREQ_INTERFACE
	rf2 = divident & 0xff;
	rf1 = ((divident >> 8) & 0x3ff) << 16;
	rf0 = (rf0 << 16) | (rf0 >> 16);

	*((uint32_t *)paras) = rf0;
	*((uint32_t *)paras + 1) = rf1;
	paras[4] = rf2;

	memcpy(&paras[5], rf_reg_define, 4 * sizeof(uint16_t));
	paras[9] = is_tscc ? 0xffff : rf_reg_define[BACKGROUND_TSCC_FLAG_INDEX];
	paras[10] = rf_reg_define[ELECTRICALLY_TUNABLE_FILTER_INDEX];
#else
	if (mobile_rf_mode == MOBILE_RF_TYPE_2P_FULL_BAND)
	{
		rf0 = (uint32_t)(freq * 1000000);
//		rf0 = (uint32_t)(155.2725 * 1000000);
		paras[0] = (uint16_t)rf0;
		paras[1] = (uint16_t)(rf0 >> 16);
	}
	else
	{
		paras[0] = (uint16_t)rf0;								// pll_reg0 (Divider Register)
		paras[1] = (uint16_t)(((divident >> 8) & 0x3ff));		// pll_reg1 (Dividend MSB Register)
		paras[2] = (uint16_t)(divident & 0xff);					// pll_reg2 (Dividend LSB Register)
	}
//	paras[5] = 0;												// pll_reg5: �������˲�����ѹ(������)
	if (tr)
		paras[6] = is_tscc ? 1 : 0;								// 0: normal; 1: background tscc
#endif


#ifdef RF_DEUBG_ENABLE
	print_freq_paras(tr, freq, paras);
#endif
}

uint16_t get_new_dsp_mode_reg(uint16_t ori, uint16_t mask, uint16_t ctrl)
{
	uint16_t ret;

	ret = ori;
	ret &= ~mask;
	ret |= ctrl & mask;
	return ret;
}

void set_dsp_digital_mode(uint16_t mask, uint16_t ctrl)
{
	if (mask != SPEECH_USE_DEFAULT)
		g_dsp_reg->reg_digital_mode = get_new_dsp_mode_reg(g_dsp_reg->reg_digital_mode, mask, ctrl);
	else
		g_dsp_reg->reg_digital_mode = REG_DIGITAL_MODE_VALUE;

	dsp_reg_write(REG_DIGITAL_MODE_ADDR, &g_dsp_reg->reg_digital_mode);
}

void set_dsp_protect_mode(uint32_t flag)	// 0-normal; else-use by pdt trunk(tx continuous)
{
	set_dsp_digital_mode(REG_DIGITAL_MODE_PROTECT_MASK, flag ? REG_DIGITAL_MODE_PROTECT_MASK : 0);
}

void set_dsp_gps_locked_status(uint32_t flag)	// 20210306:��վ���µĳ��� �Ѿ�����Ҫ������gpsLock �� ��ֻ�ж����sync����
{
	set_dsp_digital_mode(REG_DIGITAL_MODE_GPS_LOCK_MASK, flag ? REG_DIGITAL_MODE_GPS_LOCK_MASK : 0);
}

void set_dsp_extern_30ms_mode(uint8_t flag)	// flag: 0-normal; 1-use extern
{
	set_dsp_digital_mode(REG_DIGITAL_MODE_EXT_MASK, flag ? REG_DIGITAL_MODE_EXT_MASK : 0);
#if DEBUG_DSP_WRITE_REG > 1
	vlog_v("config","digital_mode=0x%04X(ext=%d)", g_dsp_reg->reg_digital_mode, flag);
#endif
}

uint16_t set_dsp_new_n_mode(uint8_t flag)	// flag: 0-old; 1-new
{
	set_dsp_digital_mode(REG_DIGITAL_MODE_N_MASK, flag ? REG_DIGITAL_MODE_N_MASK : 0);
#if DEBUG_DSP_WRITE_REG > 1
	vlog_v("config","digital_mode=0x%04X(n=%d)", g_dsp_reg->reg_digital_mode, flag);
#endif
	return g_dsp_reg->reg_digital_mode;
}

void set_dsp_base_30ms_pps_offset(uint16_t offset)
{
//	set_dsp_gps_locked_status(0);
	dsp_reg_write(REG_DSP64_REG_GPS_OFFSET_ADDR, &offset);
//	if (get_gps_link_state())
//		set_dsp_gps_locked_status(1);
}

void set_dsp_analog_mode(uint16_t mask, uint16_t ctrl)
{
	if (mask != SPEECH_USE_DEFAULT)
		g_dsp_reg->reg_analog_mode = get_new_dsp_mode_reg(g_dsp_reg->reg_analog_mode, mask, ctrl);
	else
		g_dsp_reg->reg_analog_mode = g_dsp_reg->reg_analog_mode &
		(REG_ANALOG_MODE_TSUB_MASK | REG_ANALOG_MODE_RSUB_MASK | REG_ANALOG_MODE_OPEN_MASK | REG_ANALOG_MODE_MSKMON_MASK);

	dsp_reg_write(REG_ANALOG_MODE_ADDR, &g_dsp_reg->reg_analog_mode);
}

void set_dsp_analog_mskon_squelch(uint8_t flag, uint8_t open)
{
	if (flag == SQUELCH_DOOR_SETUP_POS)
	{
		set_dsp_analog_mode(REG_ANALOG_MODE_OPEN_MASK,
			(maintain_setup_parameter(PARA_OPERATE_READ, SQUELCH_DOOR_SETUP_POS, 0) == 0) ? 0 : REG_ANALOG_MODE_OPEN_MASK);
	}
	else if (flag == SIGNALING_TONE_OUTPUT_POS)
	{
		if (maintain_setup_parameter(PARA_OPERATE_READ, SIGNALING_TONE_OUTPUT_POS, 0))
			set_dsp_analog_mode(REG_ANALOG_MODE_MSKMON_MASK, open ? REG_ANALOG_MODE_MSKMON_MASK : 0);
	}
}

void set_dsp_vocoder_mode(uint16_t mask, uint16_t ctrl)
{
#if 0
	uint8_t  i;
//	uint8_t  j;
	uint16_t vocoder_mode, ctrl_cont;
#endif	// #if 0

	if (mask != SPEECH_USE_DEFAULT)		// mask == SPEECH_USE_DEFAULT: use the value that save at flash
		g_dsp_reg->reg_vocoder_mode = get_new_dsp_mode_reg(g_dsp_reg->reg_vocoder_mode, mask, ctrl);
//	else
//		g_dsp_reg->reg_vocoder_mode = REG_VOCODER_MODE_DEFAULT_VALUE;

//	vlog_v("config","\tvoc_mode:%04x", g_dsp_reg->reg_vocoder_mode);
	dsp_reg_write(REG_VOCODER_MODE_ADDR, &g_dsp_reg->reg_vocoder_mode);

#if 0

	if (mask != SPEECH_USE_DEFAULT)
	{
		ctrl_cont = ctrl & mask;
#ifdef DEBUG_DSP_WRITE_REG
		vlog_v("config","\tSet vocoder=%04x:", ctrl_cont);
#endif
//		for (j = 0; j < 2; j++)
		{
			for (i = 0; i < 10; i++)
			{
				dsp_reg_read(REG_DSP64_READ_VOCODER_MODE_ADDR, &vocoder_mode);
				vocoder_mode &= mask;
				if (vocoder_mode == ctrl_cont)
				{
#ifdef DEBUG_DSP_WRITE_REG
					vlog_v("config","OK");
#endif
					return;
				}
				else
				{
#ifdef DEBUG_DSP_WRITE_REG
					vlog_v("config","[F%d:%04x]", i + 1, vocoder_mode);
#endif
//					sys_delay(500);
				}
			}

//			if (j == 0)
//			{
//				vlog_v("config","reconfig fsmc and retry");
//				dsp_fsmc_config(1);
//				sys_delay(20);
//			}
//			else
//			{
				vlog_v("config","fail and end retry");
//			}
		}
	}

#endif	// #if 0
}

void set_dsp_speech_mode(uint16_t mask, uint16_t ctrl)
{
	if (mask != SPEECH_USE_DEFAULT)		// mask == SPEECH_USE_DEFAULT: use the value that save at flash
	{
		g_dsp_reg->reg_speech_mode = get_new_dsp_mode_reg(g_dsp_reg->reg_speech_mode, mask, ctrl);
	}
	else
	{
		g_dsp_reg->reg_speech_mode &= ~REG_SPEECH_MODE_FRAME_MASK;
		g_dsp_reg->reg_speech_mode |= (2 << REG_SPEECH_MODE_FRAME_SHIFT);
		g_dsp_reg->reg_speech_mode |= REG_SPEECH_MODE_DDE_MASK;
		g_dsp_reg->reg_speech_mode |= REG_SPEECH_MODE_TDE_MASK;
	}

#ifdef DEBUG_DSP_WRITE_REG
	vlog_v("config","\tspeech=%04x", g_dsp_reg->reg_speech_mode);
#endif
	dsp_reg_write(REG_SPEECH_MODE_ADDR, &g_dsp_reg->reg_speech_mode);
}

void set_dsp_pcm_speech_inout(uint8_t en)	// en: 0-disable,else-enable
{
#ifdef DEBUG_DSP_WRITE_REG
	vlog_v("config","En pcm");
#endif
	set_dsp_speech_mode(REG_SPEECH_MODE_SYSTEM_MASK, en ? REG_SPEECH_MODE_SYSTEM_MASK : 0);
	audio_pa_manual_config(en);
}

void set_dsp_vocoder_config(uint16_t mask, uint16_t ctrl)
{
	if (mask != SPEECH_USE_DEFAULT)
		g_dsp_reg->reg_vocoder_config = get_new_dsp_mode_reg(g_dsp_reg->reg_vocoder_config, mask, ctrl);
	else
		g_dsp_reg->reg_vocoder_config = 0;

	dsp_reg_write(REG_DSP64_REG_VOCODER_CONFIG_ADDR, &g_dsp_reg->reg_vocoder_config);
}

void set_vocoder_crc_check(uint8_t enable)	// 0-disable; 1-enable
{
	set_dsp_vocoder_config(REG_VOCODER_CONFIG_CRC_MASK, enable ? REG_VOCODER_CONFIG_CRC_MASK : 0);
}

//void set_dsp_interrupt_enable(uint16_t mask, uint16_t ctrl);
void set_vocoder_mix_enable(uint8_t enable)	// 0-disable; 1-enable
{
	set_dsp_vocoder_config(REG_VOCODER_CONFIG_MIX_MASK, enable ? REG_VOCODER_CONFIG_MIX_MASK : 0);				// config voice mix

//	set_dsp_interrupt_enable(REG_INTERRUPT_ENABLE_SST_MASK, enable ? REG_INTERRUPT_ENABLE_SST_MASK : 0);		// config voice interrupt
}

void set_speech_vad_enable(uint8_t enable)	// 0-disable; 1-enable
{
#ifdef VAD_CONTROL_BY_DSP

	set_dsp_speech_mode(REG_SPEECH_MODE_VAD_MASK, enable ? REG_SPEECH_MODE_VAD_MASK : 0);						// config voice active detect

//	set_dsp_interrupt_enable(REG_INTERRUPT_ENABLE_SST_MASK, enable ? REG_INTERRUPT_ENABLE_SST_MASK : 0);		// config voice interrupt
//	set_dsp_speech_mode(REG_SPEECH_INTERRUPT_STATE_VAD_MASK, enable ? REG_SPEECH_INTERRUPT_STATE_VAD_MASK : 0);	// config VAD interrupt

#else

	/* 20220311
	��Ϊ��׼PDTû��˫��ҵ��������Ҫ��������⣺
	1.�������أ�֧������˫��ҵ�񣩾�û�н��ս���
	2.������˫�������н�����ʾ����
	�����������������õĲ�ͬ��dsp�汾����������ؿ������ر�֪ͨ�����Ļ�����ô�������汾����ͳһ��һ���汾����
	*/
	set_vad_enable_to_dsp_xu(enable);
	if (enable)
		g_dsp_reg->reg_speech_mode |= REG_SPEECH_MODE_VAD_MASK;
	else
		g_dsp_reg->reg_speech_mode &= ~REG_SPEECH_MODE_VAD_MASK;

	force_vad_end("VAD end by toggle when PTT pressed");

#endif
}

uint8_t is_speech_vad_enable(void)
{
	return (g_dsp_reg->reg_speech_mode & REG_SPEECH_MODE_VAD_MASK) ? 1  : 0;
}

#define is_vocoder_mix_enable()		(g_dsp_reg->reg_vocoder_config & REG_VOCODER_CONFIG_MIX_MASK)
//uint8_t is_vocoder_mix_enable(void)
//{
//	return (g_dsp_reg->reg_vocoder_config & REG_VOCODER_CONFIG_MIX_MASK) ? 1 : 0;
//}

void set_vocoder_rate_agc_ns(uint8_t work_mode, uint8_t set_tx_rx, uint8_t dat)	// bit0-vocoder type; bit1-rate; bit2-agc; bit3-ns; bit4-high bit of vocoder type; set_tx_rx:bit0/1:1-set tx/rx
{
	VOCODER_SETUP_TYPEDEF *p_vocoder = (VOCODER_SETUP_TYPEDEF *)&dat;
	uint16_t vocoder_type, vocoder_speed, vocoder_frame, vocoder_fec;

	vocoder_speed = p_vocoder->vocoder_speed;
	vocoder_frame = 2;
	vocoder_fec = 0;	// REG_VOCODER_MODE_FEC_SHIFT: 0-nofec;1-fec
	if (work_mode == INTERPHONE_MODE_ZZWPRO_Q)
	{
		vocoder_type = p_vocoder->vocoder_type_q;
		vocoder_frame = (vocoder_type == VOCODER_TYPE_Q_SELP1200) ? 0 : 1;	// real frames(DSP) == val_setup + 1	// 20210817��ʵ��frame����Ϊ1��3��sout�жϼ������60ms��������Ϊ3���������������֡λ�ã�B18-B26��
	}
	else if ((work_mode == INTERPHONE_MODE_ZZWPRO_N) || (work_mode == INTERPHONE_MODE_ZZWPRO_V25K))
	{
		vocoder_type = VOCODER_TYPE_Q_SELP1200;
	}
	else if (work_mode == INTERPHONE_MODE_ZZW_ADHOC)
	{
		vocoder_type = VOCODER_TYPE_Q_AMBE;
		vocoder_speed = 0;
	}
	else
	{
		vocoder_type = p_vocoder->vocoder_type;
	}

	if (dsp_incompatible_reg_len == REG_READ_RSSI_DQI_DEMOD_DSP_LEN)
	{
		if (work_mode == INTERPHONE_MODE_ZZWPRO_V)	// INTERPHONE_MODE_ZZWPRO_V25K is 2(default is 2, no necessary to set it)
		{
			vocoder_frame = 8;
		}
		else if (work_mode <= INTERPHONE_MODE_PDT_TRUNKING)
		{
			if (((vocoder_type == VOCODER_TYPE_Q_AMBE) || ((vocoder_type == VOCODER_TYPE_Q_NVOC) && (vocoder_speed == 0))) &&
				(crypto_is_aes_mode()/*is_enable_aes256_crypto()*/ == 0))
				vocoder_fec = 1;

//			if ((vocoder_type == VOCODER_TYPE_Q_NVOC) && vocoder_speed && sd_can_be_crypto())
//				dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_SWITCH_VOCODER_SPEED_EN | DSP_RUNTIME_MISC_SWITCH_VOCODER_2200;
//			else
//				dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_SWITCH_VOCODER_SPEED_EN;
			if ((vocoder_type == VOCODER_TYPE_Q_NVOC) && sd_can_be_crypto())
			{
				dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_SWITCH_VOCODER_SPEED_EN;
				if (vocoder_speed)
					dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_SWITCH_VOCODER_2200;
				else
					dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_SWITCH_VOCODER_2200;
			}
		}

//#ifdef VICTEL_ZZWPRO_BASE // 20220917: ת�벻���ô�bit��NVOC��������
		set_dsp_digital_mode(REG_DIGITAL_MODE_VOCODER_MASK, (vocoder_type == VOCODER_TYPE_Q_AMBE) ? 0 : REG_DIGITAL_MODE_VOCODER_MASK);	// 0Ϊ AMBE 1ΪNVOC
//#endif

//		vlog_v("config","[%d]speed=%d,type=%d,fec=%d,frame=%d,agc=%d,ns=%d", work_mode,
//			vocoder_speed, vocoder_type, vocoder_fec, vocoder_frame, p_vocoder->vocoder_agc, p_vocoder->vocoder_ns);
		if (set_tx_rx & 0x01)	// set tx(enc)
		{
			set_dsp_vocoder_mode(REG_VOCODER_MODE_ENC_RATE_MASK | REG_VOCODER_MODE_ENC_TYPE_MASK | REG_VOCODER_MODE_ENC_FEC_MASK | REG_VOCODER_MODE_ENC_FRAME_MASK,
				(vocoder_speed << REG_VOCODER_MODE_ENC_RATE_SHIFT) | (vocoder_type << REG_VOCODER_MODE_ENC_TYPE_SHIFT) |
				(vocoder_fec << REG_VOCODER_MODE_ENC_FEC_SHIFT) | (vocoder_frame << REG_VOCODER_MODE_ENC_FRAME_SHIFT));
		}
		if (set_tx_rx & 0x02)	// set rx(dec)
		{
			set_dsp_vocoder_mode(REG_VOCODER_MODE_DEC_RATE_MASK | REG_VOCODER_MODE_DEC_TYPE_MASK | REG_VOCODER_MODE_DEC_FEC_MASK | REG_VOCODER_MODE_DEC_FRAME_MASK,
				(vocoder_speed << REG_VOCODER_MODE_DEC_RATE_SHIFT) | (vocoder_type << REG_VOCODER_MODE_DEC_TYPE_SHIFT) |
				(vocoder_fec << REG_VOCODER_MODE_DEC_FEC_SHIFT) | (vocoder_frame << REG_VOCODER_MODE_DEC_FRAME_SHIFT));
		}

//		set_vocoder_crc_check(0);
	}
	else
	{
//		vlog_v("config","[%d]speed=%d,type=%d,frame=%d,agc=%d,ns=%d", work_mode,
//			vocoder_speed, vocoder_type, vocoder_frame, p_vocoder->vocoder_agc, p_vocoder->vocoder_ns);
		set_dsp_vocoder_mode(REG_VOCODER_MODE_RATE_MASK | REG_VOCODER_MODE_TYPE_MASK | REG_VOCODER_MODE_CRC_MASK | REG_VOCODER_MODE_FEC_MASK | REG_VOCODER_MODE_FRAME_MASK,
			(vocoder_speed << REG_VOCODER_MODE_RATE_SHIFT) | (vocoder_type << REG_VOCODER_MODE_TYPE_SHIFT) |
			(0 << REG_VOCODER_MODE_CRC_SHIFT) | (0 << REG_VOCODER_MODE_FEC_SHIFT) | (vocoder_frame << REG_VOCODER_MODE_FRAME_SHIFT));
	}

#ifdef DEBUG_DSP_WRITE_REG
	vlog_v("config","Set voc");
#endif
	set_dsp_speech_mode(REG_SPEECH_MODE_AGC_MASK | REG_SPEECH_MODE_NS_MASK,
		(p_vocoder->vocoder_agc << REG_SPEECH_MODE_AGC_SHIFT) | (p_vocoder->vocoder_ns << REG_SPEECH_MODE_NS_SHIFT));

	vocoder_initial();
}

void set_dsp_transcode_mode(void)
{
	uint16_t vocoder_type, vocoder_speed, vocoder_frame, vocoder_fec, vocoder_type_trans, vocoder_speed_trans, vocoder_frame_trans, vocoder_fec_trans;
	uint16_t new_transcode[REG_WRITE_REG_VOCODER_TRANSCODE_MODE_LEN];

	vocoder_type = g_runtime_inst.runtime_paras.pdt_vocoder.vocoder_type;
	vocoder_speed = g_runtime_inst.runtime_paras.pdt_vocoder.vocoder_speed;
	vocoder_frame = 2;
	if ((vocoder_type == VOCODER_TYPE_Q_NVOC) && (vocoder_speed == 1))	// nvoc2200: no fec
		vocoder_fec = 0;
	else
		vocoder_fec = 1;

	vocoder_type_trans = trans_vocoder_type.vocoder_type;
	vocoder_speed_trans = trans_vocoder_type.vocoder_speed;
	vocoder_frame_trans = 0;
	vocoder_fec_trans = 0;

	new_transcode[0] = (vocoder_speed << REG_TRANSCODE_MODE_SRC_RATE_SHIFT) | (vocoder_type << REG_TRANSCODE_MODE_SRC_TYPE_SHIFT) |
					(vocoder_fec << REG_TRANSCODE_MODE_SRC_FEC_SHIFT) | (vocoder_frame << REG_TRANSCODE_MODE_SRC_FRAME_SHIFT) |
					(vocoder_speed_trans << REG_TRANSCODE_MODE_DST_RATE_SHIFT) | (vocoder_type_trans << REG_TRANSCODE_MODE_DST_TYPE_SHIFT) |
					(vocoder_fec_trans << REG_TRANSCODE_MODE_DST_FEC_SHIFT) | (vocoder_frame_trans << REG_TRANSCODE_MODE_DST_FRAME_SHIFT);

	new_transcode[1] = ((new_transcode[0] >> 8) & 0x00ff) | ((new_transcode[0] << 8) & 0xff00);
	new_transcode[2] = new_transcode[0];
	new_transcode[3] = new_transcode[1];
//	vlog_v("config","spd:%d->%d,typ:%d->%d,fec:%d->%d,frm:%d->%d[%04X]",
//		vocoder_speed, vocoder_speed_trans, vocoder_type, vocoder_type_trans, vocoder_fec, vocoder_fec_trans, vocoder_frame, vocoder_frame_trans, new_transcode);
	vlog_v("config","Trn:%04X %04X %04X %04X", new_transcode[0], new_transcode[1], new_transcode[2], new_transcode[3]);
	dsp_reg_write(REG_DSP64_REG_TRANSCODE_MODE_ADDR, new_transcode);
}

void set_dsp_pcm_gain(uint16_t gain, uint16_t gain_shift)
{
	uint16_t gain_to = gain & REG_SYSTEM_PCM_GAIN_GAIN_MASK;
	uint16_t gain_shift_to = gain_shift & REG_SYSTEM_PCM_GAIN_SHIFT_SHIFT_MASK;

	dsp_reg_write(REG_DSP64_REG_PCM_GAIN_ADDR, &gain_to);
	dsp_reg_write(REG_DSP64_REG_PCM_GAIN_SHIFT_ADDR, &gain_shift_to);
//#if DEBUG_DSP_WRITE_REG > 1
	vlog_v("config","[SET]pcm_gain=%d,shift=%d", gain_to, gain_shift_to);
//#endif
}

void set_analog_in_paras(uint16_t ain, uint16_t ashift)
{
	dsp_reg_write(REG_ANALOG_IN_GAIN_ADDR, &ain);
	dsp_reg_write(REG_ANALOG_IN_GAIN_SHIFT_ADDR, &ashift);
#if DEBUG_DSP_WRITE_REG > 1
	vlog_v("config","[SET]ana_in_gain=%d,shift=%d", ain, ashift);
#endif
}

void set_analog_out_paras(uint16_t aout, uint16_t ashift)
{
	dsp_reg_write(REG_ANALOG_OUT_GAIN_ADDR, &aout);
	dsp_reg_write(REG_ANALOG_OUT_GAIN_SHIFT_ADDR, &ashift);
}

uint16_t set_analog_audio(uint16_t set)
{
	uint16_t dat;

	dat = set ? (REG_ANALOG_AUDIO_DEFAULT_VALUE | REG_ANALOG_AUDIO_LPF_MASK) : (REG_ANALOG_AUDIO_DEFAULT_VALUE & ~REG_ANALOG_AUDIO_LPF_MASK);
	dsp_reg_write(REG_ANALOG_AUDIO_ADDR, &dat);
	return dat;
}

void set_analog_audio_th(uint16_t set)
{
	dsp_reg_write(REG_ANALOG_AUDIO_THRESHOLD_ADDR, &set);
}

void set_crystal_step(uint16_t set)
{
	dsp_reg_write(REG_DSP64_REG_CRYSTAL_STEP_ADDR, &set);
}

const uint8_t   q_vocoder_mod_type[4] = {2, 2, 3, 4};	// 0-abme,1-nvoc,2-selp1200,3-selp600
uint16_t set_dsp_mod_type(uint8_t work_mode, uint8_t q_vocoder_speed)
{
	uint16_t dsp_mod_type;

	if (work_mode <= INTERPHONE_MODE_PDT_TRUNKING)
		dsp_mod_type = 1;
	else if (work_mode <= INTERPHONE_MODE_MPT_CONV)
		dsp_mod_type = 0;
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_Q)
		dsp_mod_type = q_vocoder_mod_type[q_vocoder_speed];// 2=Q2400, 3=Q1200, 4=Q600
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_N)
		dsp_mod_type = 5;
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_V)
		dsp_mod_type = 6;
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_V25K)
		dsp_mod_type = 7;
	else				// INTERPHONE_MODE_ZZW_ADHOC
		dsp_mod_type = 1;

	dsp_reg_write(REG_DSP64_REG_MOD_TYPE_ADDR, &dsp_mod_type);
#ifdef DEBUG_DSP_WRITE_REG
	vlog_v("config","\tMod=0x%04x(mode=%d)", dsp_mod_type, work_mode);
#endif
	return dsp_mod_type;
}

uint8_t get_dsp_demod_bit_mask(uint8_t work_mode)
{
	uint8_t dsp_demod_bit;

	if (work_mode <= INTERPHONE_MODE_PDT_TRUNKING)
		dsp_demod_bit = REG_DEMOD_ENABLE_DMR_SHIFT;		// bit1,dmr/pdt
	else if (work_mode <= INTERPHONE_MODE_MPT_CONV)
		dsp_demod_bit = REG_DEMOD_ENABLE_ANALOG_SHIFT;	// bit0,analog
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_Q)
		dsp_demod_bit = REG_DEMOD_ENABLE_MRG_SHIFT;		// bit2,q mode
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_N)
		dsp_demod_bit = REG_DEMOD_ENABLE_VDT19K2_SHIFT;	// bit3,n mode
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_V)
		dsp_demod_bit = REG_DEMOD_ENABLE_ADHOC48K_SHIFT;// bit4,v mode
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_V25K)
		dsp_demod_bit = REG_DEMOD_ENABLE_ADHOC19K2_SHIFT;// bit5,v25k mode
	else				// INTERPHONE_MODE_ZZW_ADHOC
		dsp_demod_bit = REG_DEMOD_ENABLE_DMR_SHIFT;

	return dsp_demod_bit;
}

void set_dsp_demod_enable(uint8_t work_mode1, uint8_t work_mode2)
{
	uint16_t dsp_demod_enable, dsp_demod_enable_ex;

	dsp_primary_mode = get_dsp_demod_bit_mask(work_mode1);
	dsp_secondary_mode = get_dsp_demod_bit_mask(work_mode2);
//	dsp_primary_mode_ex = get_dsp_demod_bit_mask(g_runtime_inst_xvbase->misc_config_base.work_mode_ex);
	dsp_primary_mode_ex = get_dsp_demod_bit_mask(GET_DSP_EX_WORK_MODE());

	dsp_demod_enable = 1 << dsp_primary_mode;
	dsp_demod_enable |= 1 << dsp_secondary_mode;
	dsp_demod_enable_ex = 1 << dsp_primary_mode_ex;

	dsp_reg_write(REG_DSP64_REG_DEMOD_ENABLE_ADDR, &dsp_demod_enable);
	dsp_reg_write(REG_DSP64_REG_DEMOD_ENABLE_EX_ADDR, &dsp_demod_enable_ex);

#if defined DEBUG_DSP_WRITE_REG || defined DEBUG_DSP_MODE
	vlog_v("config","\tDemod=0x%04x(mode1/2=%d/%d),ex=0x%04x(mode=%d)", dsp_demod_enable, dsp_primary_mode, dsp_secondary_mode, dsp_demod_enable_ex, dsp_primary_mode_ex);
#endif
}

#ifndef DSP_NEW_FREQ_INTERFACE
uint16_t test_config_mod_gain[16] = {
	10300, 700,
	10000, 700,
	9800,  700,
	9500,  700,
	9200,  700,
	8900,  700,
	8600,  700,
	8300,  700,
};
uint16_t test_config_power[16] = {
	1900, 1000,
	1900, 1000,
	1850, 1000,
	1850, 950,
	1850, 950,
	1850, 900,
	1850, 900,
	1850, 900,
};

uint16_t get_rf_electrically_tunable_filter_level(float f_rx)
{
	uint8_t rf_type;
	uint16_t tv;

	rf_type == get_mobile_rf_type();
	if (rf_type == MOBILE_RF_TYPE_2P_150M)
	{
		if ((f_rx > 135.9875) && (f_rx < 141.0))
			tv = 0;
		else if ((f_rx > 140.9875) && (f_rx < 146.0))
			tv = 1;
		else if ((f_rx > 145.9875) && (f_rx < 151.0))
			tv = 2;
		else if ((f_rx > 150.9875) && (f_rx < 156.0))
			tv = 3;
		else if ((f_rx > 155.9875) && (f_rx < 161.0))
			tv = 4;
		else if ((f_rx > 160.9875) && (f_rx < 166.0))
			tv = 5;
		else if ((f_rx > 165.9875) && (f_rx < 171.0))
			tv = 6;
//		else if ((f_rx > 170.9875) && (f_rx < 174.0))
		else
			tv = 7;
	}
	else if (rf_type == MOBILE_RF_TYPE_2P_400M)
	{
		if ((f_rx > 399.9875) && (f_rx < 410.0))
			tv = 0;
		else if ((f_rx > 409.9875) && (f_rx < 420.0))
			tv = 1;
		else if ((f_rx > 419.9875) && (f_rx < 430.0))
			tv = 2;
		else if ((f_rx > 429.9875) && (f_rx < 440.0))
			tv = 3;
		else if ((f_rx > 439.9875) && (f_rx < 450.0))
			tv = 4;
		else if ((f_rx > 449.9875) && (f_rx < 460.0))
			tv = 5;
//		else if ((f_rx > 459.9875) && (f_rx < 470.0))
		else
			tv = 6;
	}
	else
	{
		tv = 0;
	}

	return tv;
}

void set_rf_electrically_tunable_filter(float f_rx)
{
	uint16_t tv;

	tv = get_rf_electrically_tunable_filter_level(f_rx);
	g_dsp_reg->reg_hardware_mode &= ~REG_HARDWARE_MODE_2P_TV_MASK;
	g_dsp_reg->reg_hardware_mode |= tv << REG_HARDWARE_MODE_2P_TV_SHIFT;
	dsp_reg_write(REG_HARDWARE_MODE_ADDR, &g_dsp_reg->reg_hardware_mode);
#if DEBUG_DSP_WRITE_REG > 1
	vlog_v("config","hardware_mode=0x%04X(tv=%d)", g_dsp_reg->reg_hardware_mode, tv);
#endif
}

#else	// DSP_NEW_FREQ_INTERFACE

uint16_t get_rf_2p_tune_level(float mhz)	// mhz=real-offset
{
	uint16_t i, f_uint = (uint16_t)(mhz * 80);

	for (i = 0; i < RF_2P_MAX_FREQ_SEGMENT; i++)
	{
		if ((g_rf2p_tune_reg->dsp_2p_pll[i].freq == 0) || (f_uint < g_rf2p_tune_reg->dsp_2p_pll[i].freq))
			break;
	}

	return (i ? (i - 1) : 0);
}

uint8_t get_rf_2p_next_tune_level(uint8_t curr)
{
	uint8_t i;

	i = curr + 1;
	if ((i >= RF_2P_MAX_FREQ_SEGMENT) || (g_rf2p_tune_reg->dsp_2p_pll[i].freq == 0))
		i = 0;
	return i;
}

uint8_t get_rf_2p_total_tune_point(void)
{
	uint8_t i;

	for (i = 0; i < RF_2P_MAX_FREQ_SEGMENT; i++)
	{
		if (get_rf_2p_next_tune_level(i) == 0)
		{
			i++;
			break;
		}
	}

	return (i <= RF_2P_MAX_FREQ_SEGMENT) ? i : RF_2P_MAX_FREQ_SEGMENT;
}

void config_2p_power(uint16_t lv)
{
	set_ramp_waveform_high(lv);
	set_ramp_waveform_low(lv);
}

#endif	// DSP_NEW_FREQ_INTERFACE

#ifdef PDT_DIRECT_SCAN_CHANNEL
uint8_t get_stack_rssi_lower(void);
uint8_t pdt_direct_scan_chan_offset = 0, pdt_direct_scan_chan_rssi_flag = 0;
void pdt_direct_scan_chan_stt(uint8_t flag)		// 0-stop; 1-start
{
	pdt_direct_scan_chan_rssi_flag = flag ? 0x00 : 0x80;
	pdt_direct_scan_chan_offset = 0;
}
#endif

uint16_t get_real_rx_int_ex(void)
{
	if (g_runtime_inst_xvbase->f_module_rx_ex)
		return g_runtime_inst_xvbase->f_module_rx_ex;
	else
		return g_rf2p_tune_reg->dsp_2p_pll[0].freq;
}

void modify_dsp_rx_int_ex(uint16_t rx_int)
{
//	dsp_rx_int_ex = (rx_int == 0xffff) ? g_runtime_inst_xvbase->f_module_rx_ex : rx_int;
	dsp_rx_int_ex = (rx_int == 0xffff) ? get_real_rx_int_ex() : rx_int;
}

uint16_t get_xvbase_ex_rx_freq(void)
{
	return dsp_rx_int_ex;
}

uint8_t set_analog_sub_ctcss(float *ftx, float *frx)
{
	float f_tx, f_rx;
	uint16_t ch_tmp, ret = 1, paras[11];
	USER_BOOK book_for_conv, *book;
	MPT_CONV_BASE_TYPEDEF conv_base;

	if (dev_is_base() && ((interphone_mode == INTERPHONE_MODE_MPT_TRUNKING) || (interphone_mode == INTERPHONE_MODE_MPT_CONV)))
	{
		memcpy((void *)&conv_base, (void *)&g_runtime_inst_xvbase->stack_xv.conv_base_paras, sizeof(g_runtime_inst_xvbase->stack_xv.conv_base_paras));
		book_for_conv.bind_ch = 1;
		book_for_conv.id = ((uint32_t)conv_base.tx_sub << 16) | ((uint32_t)conv_base.rx_sub << 8);
		book = &book_for_conv;
	}
	else
	{
		book_for_conv.bind_ch = 0;
		book = get_watch_at_user_book(1, 0xffff, 0);
	}

	if (book)
	{
		if (book_for_conv.bind_ch == 0)
		{
			if (book->bind_ch == 0x0000ffff)		// 20200421:��֦����̨
			{
				f_tx = 358.7850;
				f_rx = 358.7850;
			}
			else if (book->bind_ch == 0x0000fffe)	// 20200421:��֦����̨
			{
				f_tx = 358.4450;
				f_rx = 358.4450;
			}
			else
			{
				f_tx = (float)(book->bind_ch >> 16) / 80;
				f_rx = (float)(book->bind_ch & 0x0000ffff) / 80;
			}
			vlog_v("config","[%d]MPT conv:t=%3.4f,r=%3.4f;", slot_middle_int_times, /*slot_rising_edge_int_times + slot_falling_edge_int_times,*/ f_tx, f_rx);
			if (ftx)
				*ftx = f_tx;
			if (frx)
				*frx = f_rx;
		}

		paras[0] = 0;
		paras[1] = 0;
		paras[2] = 0;

		ch_tmp = (book->id >> 16) & 0x00ff; 	// TX sub setup
		if ((ch_tmp & 0x80) == 0)				// CTCSS
		{
			vlog_v("config","T_SUB:CTCSS[%d(MAX:50)],", ch_tmp);
			paras[0] = ch_tmp & (REG_ANALOG_SUB_CTCSS_TCTCSS_MASK | REG_ANALOG_SUB_CTCSS_TCMODE_MASK);
			paras[2] = (ch_tmp > 50) ? 0 : (1 << REG_ANALOG_MODE_TSUB_SHIFT);
		}
		else
		{
			ch_tmp &= 0x7f;
			vlog_v("config","T_SUB:DCS[%d(MAX:82)],", ch_tmp);
			paras[1] = ch_tmp & (REG_ANALOG_SUB_DCS_TDCS_MASK | REG_ANALOG_SUB_DCS_TDMODE_MASK | REG_ANALOG_SUB_DCS_TURN_OFF_MASK);
			paras[2] = (ch_tmp > 82) ? 0 : (2 << REG_ANALOG_MODE_TSUB_SHIFT);
		}

		ch_tmp = (book->id >> 8) & 0x00ff;		// RX sub setup
		if ((ch_tmp & 0x80) == 0)				// CTCSS
		{
			vlog_v("config","R_SUB:CTCSS[%d(MAX:50)]", ch_tmp);
			paras[0] |= (ch_tmp << REG_ANALOG_SUB_CTCSS_RCTCSS_SHIFT) & REG_ANALOG_SUB_CTCSS_RCTCSS_MASK;
			if (ch_tmp > 50)
			{
				sub_ctcss_enable = 0;
			}
			else
			{
				paras[2] |= 1 << REG_ANALOG_MODE_RSUB_SHIFT;
				sub_ctcss_enable = 0xff;
			}
		}
		else
		{
			ch_tmp &= 0x7f;
			vlog_v("config","R_SUB:DCS[%d(MAX:82)]", ch_tmp);
			paras[1] |= (ch_tmp << REG_ANALOG_SUB_DCS_RDCS_SHIFT) & REG_ANALOG_SUB_DCS_RDCS_MASK;
			if (ch_tmp > 82)
			{
				sub_ctcss_enable = 0;
			}
			else
			{
				paras[2] |= 2 << REG_ANALOG_MODE_RSUB_SHIFT;
				sub_ctcss_enable = 0xff;
			}
		}

		dsp_reg_write(REG_ANALOG_SUB_CTCSS_ADDR, paras);
		dsp_reg_write(REG_ANALOG_SUB_DCS_ADDR, paras + 1);
		set_dsp_analog_mode(REG_ANALOG_MODE_TSUB_MASK | REG_ANALOG_MODE_RSUB_MASK, paras[2]);
	}
	else
	{
		vlog_v("config","[config_pll_paras]NO item at user book");
		sub_ctcss_enable = 0;
		ret = 0;
	}

	return ret;
}

uint16_t config_pll_paras_base_ex_rx(void)
{
	uint16_t ret = 1, paras[11];
	float f_mhz;

	f_mhz = (float)dsp_rx_int_ex / 80 + g_freq_offset;
	freq_to_paras(1, 0, f_mhz, paras);
	dsp_reg_write(REG_DSP64_REG_PLL_RX_CONFIG_EX_ADDR, paras);
	vlog_v("config","\t[UPDATE ex]R=%3.4f", f_mhz);

	// ret = is_pll_locked(0, 10);
	return ret;
}

#define F_INT_TO_HZ(f_int)			((uint32_t)(((float)(f_int) / 80 + g_freq_offset) * 80) * (1000000 / 80))
uint16_t config_pll_paras_base(uint8_t work_mode, uint8_t flag, uint16_t rx_int, uint16_t tx_int)	// flag: bit0:0-print info; bit1:0-config ex;
{
	uint32_t rx_hz = rx_int ? F_INT_TO_HZ(rx_int) : 0, tx_hz = tx_int ? F_INT_TO_HZ(tx_int) : 0;

	packet_ctrl_to_mcu_inf_frame(BASEBAND_TYPE_SET_FREQ, tx_hz, rx_hz);
/*
	uint16_t ret = 1, paras[11];
	float f_mhz;
	char out[100], out_n;

	out_n = sprintf(out, "\t[UPDATE]");
	if (rx_int)
	{
		f_mhz = (float)rx_int / 80 + g_freq_offset;
		freq_to_paras(1, 0, f_mhz, paras);
		dsp_reg_write(REG_PLL_RX_CONFIG_ADDR, paras);
		out_n += sprintf(out + out_n, "R=%3.4f ", f_mhz);
	}
	if (tx_int)
	{
		f_mhz = (float)tx_int / 80 + g_freq_offset;
		freq_to_paras(0, 0, f_mhz, paras);
		dsp_reg_write(REG_PLL_TX_CONFIG_ADDR, paras);
		out_n += sprintf(out + out_n, "T=%3.4f", f_mhz);
	}

	if ((flag & CONFIG_BASE_FREQ_NO_PRINT) == 0)
		vlog_v("config","%s", out);
	// ret = is_pll_locked(0, 10);

	if ((flag & CONFIG_BASE_FREQ_EXCLUDE_EX) == 0)
		config_pll_paras_base_ex_rx();
	return ret;
*/
	return 0;
}

uint16_t config_pll_paras(uint8_t work_mode, uint8_t is_tscc, uint16_t ch)			// is_tscc: bit7:1-not set rx(set tx only when bit0=0); bit6:1-not print info; bit0:1-not set tx(set rx only when bit7=0)
{
	float f_tx, f_rx;
	uint32_t tx, rx;
	e_baseband_control_type_t cmd;

	chan_to_freq(work_mode, &f_tx, &f_rx, ch);
	if (is_tscc & 0x01)
	{
		tx = 0;
		cmd = BASEBAND_TYPE_CHANNEL_SCAN;
	}
	else
	{
		tx = (uint32_t)(f_tx * 80) * (1000000 / 80);
		cmd = BASEBAND_TYPE_SET_FREQ;
	}
	rx = (is_tscc & 0x80) ? 0 : (uint32_t)(f_rx * 80) * (1000000 / 80);
	packet_ctrl_to_mcu_inf_frame(cmd, tx, rx);

	if ((is_tscc & 0x40) == 0)
	{
		if ((is_tscc & 0x81) == 0x00)
			vlog_v("config","[%d]ch[%d]:t=%3.4f,r=%3.4f", slot_middle_int_times, ch, f_tx, f_rx);
		else if ((is_tscc & 0x81) == 0x80)
			vlog_v("config","[%d]ch[%d]:t=%3.4f", slot_middle_int_times, ch, f_tx);
		else if ((is_tscc & 0x81) == 0x01)
			vlog_v("config","[%d]ch[%d]:r=%3.4f", slot_middle_int_times, ch, f_rx);
		else
			vlog_v("config","[%d]ch[%d]:set nothing", slot_middle_int_times, ch);
	}

	return 1;
}

void unify_nvq_jump_freq(uint16_t set_freq)	// set_freq==0xffff: jump back to watch freq
{
	config_pll_paras(interphone_mode, 0x80, (set_freq == 0xffff) ? get_current_chan() : (uint16_t)get_zzwpro_nvq_q_chan(set_freq, 0));
}

uint8_t is_sub_ctcss_enable(void)
{
	return sub_ctcss_enable;
}

uint8_t adc_result_low_limited = 102/*2.39V*/, adc_result_high_limited = 111/*2.60V*/;
float tune_adc_int_to_float(uint8_t adc)
{
	return (float)(adc * 2 * 3.0 / 256);
}
uint8_t tune_adc_float_to_int(float v_adc)
{
	return (uint8_t)(v_adc * 256 / (2 * 3.0));
}
void change_auto_tune_limited(float low, float high)
{
	adc_result_low_limited = tune_adc_float_to_int(low);
	adc_result_high_limited = tune_adc_float_to_int(high);
}

uint16_t pll_adc_read(uint8_t tr)	// wanted adc voltge: 2.4(adc=102, 2.39v) - 2.6(adc=111, 2.60v)
{
	uint16_t adc;

	if (tr == 0)
		dsp_reg_read(REG_PLL_RX_ADC_ADDR, &adc);
	else
		dsp_reg_read(REG_PLL_TX_ADC_ADDR, &adc);

	return adc & 0x00ff;
}

uint16_t config_pll_test(uint8_t tr, float freq, uint16_t *dac)		// 0: rx; !0: tx
{
	uint16_t pll_ps[11];

	freq_to_paras(1, 0, freq, pll_ps);
	if (tr == 0)		// rx
	{
#ifndef DSP_NEW_FREQ_INTERFACE
		dsp_reg_write(REG_PLL_RX_DAC_ADDR, dac);
#endif
		dsp_reg_write(REG_PLL_RX_CONFIG_ADDR, pll_ps);
	}
	else
	{
#ifndef DSP_NEW_FREQ_INTERFACE
		dsp_reg_write(REG_PLL_TX_DAC_ADDR, dac);
#endif
		dsp_reg_write(REG_PLL_TX_CONFIG_ADDR, pll_ps);
	}
	return is_pll_locked(tr, 10);
}

void dsp_speech_in_pa_ctrl(uint8_t enable, uint8_t flag)
{
	if (enable == 0)
		dsp_speech_pa_ctrl &= ~(SPEECH_PA_CTRL_IN_VOICE | SPEECH_PA_CTRL_IN_POC);
	else
		dsp_speech_pa_ctrl |= (SPEECH_PA_CTRL_IN_VOICE | flag);
}

void dsp_speech_out_pa_ctrl(uint8_t enable, uint8_t flag)
{
	if (enable == 0)
	{
		dsp_speech_pa_ctrl &= ~flag;
	}
	else
	{
		if (((flag & SPEECH_PA_CTRL_OUT_VOICE) && (voice_is_mute() == 0)) || ((flag & SPEECH_PA_CTRL_OUT_TIP) && (tip_is_mute() == 0)))
			audio_pa_ctrl_process(audio_pa_manual_config(0xff) | 1);
		dsp_speech_pa_ctrl |= flag;
//		vlog_v("config","Open PA");
	}
}

#define CHECK_DSP_MPT_VOICE_FLAG	(ALG_ANALOG_DEMOD_STATE_SQUELCH | ALG_ANALOG_DEMOD_STATE_CARRIER)
uint8_t is_mpt_open_analog_voice(void)
{
	uint8_t ret = 0;

	if ((interphone_mode == INTERPHONE_MODE_MPT_TRUNKING) || (interphone_mode == INTERPHONE_MODE_MPT_CONV))
	{
		ret = 1;
	}
	else if (dsp_secondary_mode == REG_DEMOD_ENABLE_ANALOG_SHIFT)
	{
		if (is_sub_ctcss_enable())
		{
			if (dsp_demod_state[1] & ALG_ANALOG_DEMOD_STATE_CTCSS_DETECT)
				ret = 1;
		}
		else
		{
			if ((dsp_demod_state[1] & CHECK_DSP_MPT_VOICE_FLAG) == CHECK_DSP_MPT_VOICE_FLAG)
				ret = 1;
		}
	}

	return ret;
}

void speech_pa_ctrl_process(void)
{
	if (dsp_speech_pa_ctrl & (SPEECH_PA_CTRL_OUT_TIP | SPEECH_PA_CTRL_OUT_VOICE))	// want to open pa out
	{
		if ((dsp_speech_pa_ctrl & SPEECH_PA_OPEN_OUT) == 0)
		{
			dsp_speech_pa_ctrl |= SPEECH_PA_OPEN_OUT;
			speech_out_pa_close_delay = 0;
//			dsp_send_cmd(DSP_CMD_SPEECH_OUT_ON);
//			audio_pa_ctrl_process(audio_pa_manual_config(0xff) | 1);				// open at dsp_speech_out_pa_ctrl() immediately
			if (is_mpt_open_analog_voice())
				dsp_send_cmd(DSP_CMD_ANALOG_OUT_ON);
#ifdef POWER_SAVE_ENABLE_TIP_SOUND
			if (dev_is_base())
			{
			}
			else // dev_is_base
			{
				set_dsp_temporary_to_normal(0xff);
			}
#endif
		}
	}
	else
	{
		if (dsp_speech_pa_ctrl & SPEECH_PA_OPEN_OUT)
		{
//			if (speech_out_pa_close_delay == 1)
//			if (((interphone_mode <= INTERPHONE_MODE_PDT_TRUNKING) && (speech_out_pa_close_delay >= 2)) ||
//				((interphone_mode > INTERPHONE_MODE_PDT_TRUNKING) && (speech_out_pa_close_delay >= 1)))
			if (speech_out_pa_close_delay >= 3)
			{
				dsp_speech_pa_ctrl &= ~SPEECH_PA_OPEN_OUT;
//				dsp_send_cmd(DSP_CMD_SPEECH_OUT_OFF);
				audio_pa_ctrl_process(audio_pa_manual_config(0xff) | 0);
				if (is_mpt_open_analog_voice())
					dsp_send_cmd(DSP_CMD_ANALOG_OUT_OFF);
				speech_out_pa_close_delay = 0;
			}
			else
			{
				speech_out_pa_close_delay++;
			}
		}
	}

	if (dsp_speech_pa_ctrl & SPEECH_PA_CTRL_IN_VOICE)								// want to open pa in
	{
		if (((dsp_speech_pa_ctrl & SPEECH_PA_OPEN_IN) == 0) &&
			(is_vocoder_mix_enable() || ((dsp_speech_pa_ctrl & SPEECH_PA_OPEN_OUT) == 0)))
		{
			if (interphone_mode != INTERPHONE_MODE_MPT_TRUNKING)
			{
open_speech_pa_in:
				dsp_speech_pa_ctrl |= SPEECH_PA_OPEN_IN;
				if (is_speech_vad_enable() == 0)
				{
					dsp_send_cmd(DSP_CMD_SPEECH_IN_ON);
#ifdef DEBUG_VOICE_SEQUENCE
					vlog_v("config","[%d]PA_in ON", slot_rising_edge_int_times);
#endif
				}
				if (is_mpt_open_analog_voice())
					dsp_send_cmd(DSP_CMD_ANALOG_IN_ON);
			}
			else
			{
				if (speech_out_pa_close_delay == 4)
				{
					speech_out_pa_close_delay = 0;
					goto open_speech_pa_in;
				}
				else
				{
					speech_out_pa_close_delay++;
				}
			}
		}
	}
	else
	{
		if (dsp_speech_pa_ctrl & SPEECH_PA_OPEN_IN)
		{
			dsp_speech_pa_ctrl &= ~SPEECH_PA_OPEN_IN;
			if (is_speech_vad_enable() == 0)
			{
				dsp_send_cmd(DSP_CMD_SPEECH_IN_OFF);
#ifdef DEBUG_VOICE_SEQUENCE
				vlog_v("config","[%d]PA_in OFF", slot_rising_edge_int_times);
#endif
			}
			if (is_mpt_open_analog_voice())
				dsp_send_cmd(DSP_CMD_ANALOG_IN_OFF);
		}
	}
}

#define SET_BT_SPEECH_MASK	(REG_SPEECH_MODE_SPEAKER_MASK | REG_SPEECH_MODE_CHAN_MASK)

uint8_t set_speaker_chan(uint8_t flag)	// 0-mobile; 1-earphone; else-toggle
{
  #ifdef DEBUG_DSP_WRITE_REG
	vlog_v("config","Set spk=%d", flag);
  #endif
	if (flag == 0)
	{
set_speaker_to_inner:
		set_dsp_speech_mode(REG_SPEECH_MODE_SPEAKER_MASK, 0);
	}
	else if (flag == 1)
	{
set_speaker_to_outside:
		set_dsp_speech_mode(REG_SPEECH_MODE_SPEAKER_MASK, REG_SPEECH_MODE_SPEAKER_MASK);
	}
	else
	{
		if (g_dsp_reg->reg_speech_mode & REG_SPEECH_MODE_SPEAKER_MASK)
			goto set_speaker_to_inner;
		else
			goto set_speaker_to_outside;
	}

	return (g_dsp_reg->reg_speech_mode & REG_SPEECH_MODE_SPEAKER_MASK) ? 1 : 0;
}

extern GUI_INTERACTIVE_TYPEDEF *g_gui_interactive;
void set_to_bt_set(uint8_t flag)		// 0-mobile; 1-earphone
{
	uint16_t set_bt_speech = REG_SPEECH_MODE_CHAN_MASK | (g_static_ptr->misc_static_config.tactics_radio ? REG_SPEECH_MODE_SPEAKER_MASK : 0);

#ifdef DEBUG_DSP_WRITE_REG
	vlog_v("config","Set bt=%d", flag);
#endif
	if (flag == 0)
	{
		g_gui_interactive->dev_misc_notify.earphone = 0;
		set_dsp_speech_mode(set_bt_speech, 0);
	}
	else
	{
		g_gui_interactive->dev_misc_notify.earphone = 1;
		set_dsp_speech_mode(set_bt_speech, set_bt_speech);
	}
}

uint8_t set_zzw_demod_slot(uint8_t slot)	// 0-the good at 0-3; 1-slot0; 2-slot1; 3-slot2; 4-slot3; 5-the good at 1-3; 0xff-read the slot; else-toggle
{
	static uint8_t slot_set = (REG_DIGITAL_MODE_VALUE & REG_DIGITAL_MODE_SLOT_MASK) >> REG_DIGITAL_MODE_SLOT_SHIFT;

	if (slot != 0xff)
	{
		if (slot <= 5)
			slot_set = slot;
		else
			slot_set = (slot_set >= 5) ? 0 : (slot_set + 1);

		set_dsp_digital_mode(REG_DIGITAL_MODE_SLOT_MASK, slot_set << REG_DIGITAL_MODE_SLOT_SHIFT);
	}
	return slot_set;
}

void set_zzw_stime(uint8_t stime)	// 0��ʾ��ʱ�رգ�1-255��ʾ��ʱʱ�䣬������10��
{
	set_dsp_digital_mode(REG_DIGITAL_MODE_STIME_MASK, ((uint16_t)stime) << REG_DIGITAL_MODE_STIME_SHIFT);
}

void set_dsp_interrupt_enable(uint16_t mask, uint16_t ctrl)
{
	static uint16_t reg_interrupt_enable = REG_INTERRUPT_ENABLE_RDATA_MASK;
	uint8_t work_mode = maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0);

	if (mask != SPEECH_USE_DEFAULT)
	{
		reg_interrupt_enable = get_new_dsp_mode_reg(reg_interrupt_enable, mask, ctrl);
	}
	else
	{
		if (dev_is_809())
			reg_interrupt_enable |= REG_INTERRUPT_ENABLE_DATA_EX_MASK;
		if (dev_is_base())
		{
			if ((work_mode == INTERPHONE_MODE_MPT_TRUNKING) || (work_mode == INTERPHONE_MODE_MPT_CONV))
			{
				reg_interrupt_enable |= REG_INTERRUPT_ENABLE_DATA_MASK;
			}
			else
			{
				reg_interrupt_enable |= REG_INTERRUPT_ENABLE_DATA_MASK | REG_INTERRUPT_ENABLE_ENC_MASK | REG_INTERRUPT_ENABLE_DEC_MASK;
				if (trans_vocoder_type.vocoder_trans_enable)
				{
					reg_interrupt_enable |= REG_INTERRUPT_ENABLE_TRANS_0_MASK | REG_INTERRUPT_ENABLE_TRANS_1_MASK | REG_INTERRUPT_ENABLE_TRANS_2_MASK | REG_INTERRUPT_ENABLE_TRANS_3_MASK;
					set_dsp_transcode_mode();
				}
			}
		}
		else // dev_is_base
		{
			if (work_mode >= INTERPHONE_MODE_ZZW_ADHOC)
				reg_interrupt_enable |= REG_INTERRUPT_ENABLE_DATA_MASK;
			else if ((work_mode == INTERPHONE_MODE_MPT_TRUNKING) || (work_mode == INTERPHONE_MODE_MPT_CONV))
				reg_interrupt_enable = 0;
//			else
//				reg_interrupt_enable |= REG_INTERRUPT_ENABLE_RDATA_MASK;	// use rdata int to drive sout at mpt mode
		} // dev_is_base
	}

	dsp_reg_write(REG_INTERRUPT_ENABLE_ADDR, &reg_interrupt_enable);
//	vlog_v("config","set dsp_int_enable=%04x", reg_interrupt_enable);
}

void set_dsp_save_power_slot(uint16_t wake, uint16_t sleep)
{
	dsp_reg_write(REG_DSP64_WAKE_PERIOD_ADDR, &wake);
	dsp_reg_write(REG_DSP64_SLEEP_PERIOD_ADDR, &sleep);
	if ((wake == 0) && (sleep == 0))
	{
		dsp_send_cmd(DSP_CMD_IDLE_EXIT);
	}
}

void set_channel_gain_modulation(uint8_t lr, uint16_t gain)
{
	if (lr != SPEECH_USE_DEFAULT)		// lr == 0: use the default; else: save new volume and change it
	{
		if (lr == SPEECH_LEFT_CHANNEL)
		{
			g_dsp_reg->reg_mod_gain[0] = gain;
		}
		else if (lr == SPEECH_RIGHT_CHANNEL)
		{
			g_dsp_reg->reg_mod_gain[1] = gain;
		}
		else				// together left & right
		{
			g_dsp_reg->reg_mod_gain[0] = gain;
			g_dsp_reg->reg_mod_gain[1] = gain;
		}
	}

	dsp_reg_write(REG_MOD_GAIN_ADDR, g_dsp_reg->reg_mod_gain);
//	vlog_v("config","reg_mod_gain[0]=0x%04x, reg_mod_gain[1]=0x%04x", g_dsp_reg->reg_mod_gain[0], g_dsp_reg->reg_mod_gain[1]);
}

void rf_hardware_check(void)
{
	is_pll_locked(0, 1);
	is_pll_locked(1, 1);
	is_ad9864_tuned(1);
}
/*
uint8_t slot_timer_check(void)
{
	if (slot_timer_flag_check)
	{
		slot_timer_flag_check = 0;
		return 1;
	}
	else
	{
		return 0;
	}
}
*/
uint8_t slot_60ms_check(void)
{
	static uint8_t slot_60ms_backup = 0;

	if (slot_60ms_backup != slot60ms_counter)
	{
		slot_60ms_backup = slot60ms_counter;
		return 1;
	}
	else
	{
		return 0;
	}
}

// ts_10236101v010405p.pdf, page 84
// bs SYNC voice: 7 55 fd 7d f7 5f 7
//         data : d ff 57 d7 5d f5 d
// ms SYNC voice: 7 f7 d5 dd 57 df d
//         data : d 5d 7f 77 fd 75 7
//         RC   : 7 7d 55 f7 df d7 7
const uint8_t ms_voice_sync[7] = {0x07, 0xf7, 0xd5, 0xdd, 0x57, 0xdf, 0xd0};
const uint8_t ms_data_sync[7] = {0x0d, 0x5d, 0x7f, 0x77, 0xfd, 0x75, 0x70};
const uint8_t ms_rc_sync[7] = {0x07, 0x7d, 0x55, 0xf7, 0xdf, 0xd7, 0x70};
const uint8_t bs_data_sync[7] = {0x0d, 0xff, 0x57, 0xd7, 0x5d, 0xf5, 0xd0};
void vocoder_to_signaling(uint8_t *vocoder, uint8_t *signaling, uint8_t *sync, uint8_t *cach, uint8_t vocoder_has_gap)		// all data must be little-endian; sync=7B: first byte's low 4bit & last byte's high 4bit; vocoder_has_gap:0-no gap,1-9B+1gap,3-no gap and trans to nofec format(9B only with 60ms)
{
	uint8_t i, *p_vocoder = (uint8_t *)vocoder, *p_signaling = signaling + 3, *p_sync;

	if (cach)
		memcpy(signaling, cach, 3);
	else
		memset(signaling, 0, 3);

	if (vocoder_has_gap == 3)
	{
		// S0 S1 S2 S3 S4 S5 0  0  0  S6 S7 S8  S9  S10.H4bit
		memcpy(p_signaling, p_vocoder, 6);
		p_vocoder += 6;
		memcpy(p_signaling + 9, p_vocoder, 3);
		p_vocoder += 3;
		// do NOT set signaling[12]=vocoder[9]
		p_vocoder++;
	}
	else
	{
		for (i = 0; i < 13; i++)
		{
			if ((i == 9) && vocoder_has_gap)
				p_vocoder++;
			p_signaling[i] = *p_vocoder++;
		}
	}
	if (sync)
	{
		p_sync = sync;	// ms_data_sync/ms_voice_sync
		p_signaling[13] = (*p_vocoder & 0xf0) | *p_sync++;		// p_signaling[13]'s low 4bit is EBS/SYNC
		p_signaling[14] = *p_sync++;							// 14
		p_signaling[15] = *p_sync++;							// 15
		p_signaling[16] = *p_sync++;							// 16
		p_signaling[17] = *p_sync++;							// 17
		p_signaling[18] = *p_sync++;							// 18
		p_signaling[19] = (*p_vocoder++ & 0x0f) | *p_sync;		// p_signaling[19]'s high 4bit is EBS/SYNC
	}
	else
	{
		p_signaling[13] = *p_vocoder & 0xf0;
		memset(&p_signaling[14], 0, 5);
		p_signaling[19] = *p_vocoder++ & 0x0f;
	}
	for (i = 20; i < 33; i++)
	{
		if ((i == 24) && vocoder_has_gap)
			p_vocoder++;
		p_signaling[i] = *p_vocoder++;		// p_signaling[19]'s high 4bit is EBS/SYNC
	}
}

void vocoder_to_signaling_xv(uint8_t *vocoder, uint32_t *signaling, uint8_t vocoder_len, uint8_t vocoder_one_frame_len)		// all data must be little-endian
{
	uint8_t i, n;
	uint8_t *p_vocoder = (uint8_t *)vocoder, *p_signaling;

	signaling[0] = 0;
	n = vocoder_len / ((9 + 1) / sizeof(uint16_t));	// 20ms' vocoder code is 9B(add 1B gap); so X(60ms) is 3frame, V(180ms) is 9frame
//	p_signaling = (uint8_t *)signaling + (interphone_mode == INTERPHONE_MODE_ZZWPRO_Q ? 3 : 4);
	p_signaling = (uint8_t *)signaling + SIGNALING_XV_STACK_EMBEDDED;	// fixed fill to index 4
	for (i = 0; i < n; i++, p_signaling += vocoder_one_frame_len, p_vocoder += DSP_VOCODER_INTERFACE_BYTE_ALIGNED)
		memcpy(p_signaling, p_vocoder, vocoder_one_frame_len);
	memset(p_signaling, 0, 3);
}

void set_dsp_vocoder_length_paras(uint8_t work_mode, uint8_t q_vocoder_speed)
{
	zzwpro_vocoder_one_frame_to_dsp = 6;
	zzwpro_vocoder_one_frame_fr_dsp = 6;

	if (work_mode == INTERPHONE_MODE_ZZWPRO_V)
	{
		if (ZZWPRO_BASE_IS_V_FEC_MODE())
		{
			zzw_xv_frame_to_dsp_length = (REG_WRITE_REG_PACKET_IN_ADHOC_V_FEC_BYTELEN + 1) / sizeof(uint16_t);
		}
		else
		{
			zzw_xv_frame_to_dsp_length = (REG_WRITE_REG_PACKET_IN_ADHOC_V_BYTELEN + 1) / sizeof(uint16_t);
		}
		vocoder_data_to_dsp_length = REG_WRITE_REG_VOCODER_DATA_IN_LEN;
		vocoder_data_fr_dsp_length = REG_READ_REG_VOCODER_DATA_OUT_LEN;
	}
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_V25K)
	{
		zzw_xv_frame_to_dsp_length = (REG_WRITE_REG_PACKET_IN_ADHOC_V25K_BYTELEN + 1) / sizeof(uint16_t);
		vocoder_data_to_dsp_length = REG_VOCODER_DATA_IN_LENGTH;			// same as N
		vocoder_data_fr_dsp_length = REG_VOCODER_DATA_OUT_LENGTH;			// same as N
		zzwpro_vocoder_one_frame_to_dsp = 9;								// same as N
		zzwpro_vocoder_one_frame_fr_dsp = 9;
	}
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_Q)
	{
		zzw_xv_frame_to_dsp_length = (REG_WRITE_REG_PACKET_IN_ADHOC_Q_BYTELEN + 1) / sizeof(uint16_t);
		if (q_vocoder_speed == VOCODER_TYPE_Q_SELP1200)
		{
			vocoder_data_to_dsp_length = REG_VOCODER_DATA_IN_LENGTH / 3;	// 1200 read/write 1frame once(9B), 60ms
			vocoder_data_fr_dsp_length = REG_VOCODER_DATA_OUT_LENGTH / 3;
			zzwpro_vocoder_one_frame_to_dsp = 9;
			zzwpro_vocoder_one_frame_fr_dsp = 9;
		}
		else
		{
			vocoder_data_to_dsp_length = REG_VOCODER_DATA_IN_LENGTH / 3 * 2;// *2: 2400/600 read/write 2frames once
			vocoder_data_fr_dsp_length = REG_VOCODER_DATA_OUT_LENGTH / 3 * 2;
		}
	}
	else
	{
		zzw_xv_frame_to_dsp_length = (REG_WRITE_REG_PACKET_IN_ADHOC_N_BYTELEN + 1) / sizeof(uint16_t);	// PDT not use it
		vocoder_data_to_dsp_length = REG_VOCODER_DATA_IN_LENGTH;			// N&PDT:����3֡��N��ÿ֡60ms������9B(��FEC)����PDT��ÿ֡20ms������9B(��FEC)��6B(��FEC)
		vocoder_data_fr_dsp_length = REG_VOCODER_DATA_OUT_LENGTH;
		if (work_mode == INTERPHONE_MODE_ZZWPRO_N)
		{
			zzwpro_vocoder_one_frame_to_dsp = 9;
			zzwpro_vocoder_one_frame_fr_dsp = 9;
		}
	}
}

static uint8_t *p_dsp_test_vocoder_fetch = (uint8_t *)0xffffffff, *dsp_test_vocoder_address = (uint8_t *)dsp_ambe_4620ms;
static uint16_t dsp_test_vocoder_total_byte = TEST_VOICE_VOCODER_NOFEC2400_LENGTH * sizeof(uint16_t);
void set_dsp_test_vocoder_pointer(uint8_t work_mode, uint8_t q_vocoder_speed)
{
	// PDT have FEC:20ms=9B(6B vocoder + 3B fec, 60ms=27B,��3֡);
	// V:20ms=6B(60ms=18B,��3֡); N:20ms=3B(60ms=9B,��3֡); Q2400:40ms=12B(��2֡); Q1200:60ms=9B(��1֡); Q600:160ms=12B(��2֡)
	if (work_mode == INTERPHONE_MODE_ZZWPRO_Q)
	{
		if (q_vocoder_speed == VOCODER_TYPE_Q_SELP1200)
		{
			goto vocoder_selp_1200;
		}
		else if (q_vocoder_speed == VOCODER_TYPE_Q_SELP600)
		{
			dsp_test_vocoder_address = (uint8_t *)dsp_selp600_4640ms_nofec;
			zzwpro_vocoder_offset_of_one_frame = 6;
			dsp_test_vocoder_total_byte = TEST_VOICE_VOCODER_NOFEC600_LENGTH * sizeof(uint16_t);
		}
		else
		{
			goto vocoder_ambe_nvoc_2400;
		}
	}
	else if ((work_mode == INTERPHONE_MODE_ZZWPRO_N) || (work_mode == INTERPHONE_MODE_ZZWPRO_V25K))
	{
vocoder_selp_1200:
		dsp_test_vocoder_address = (uint8_t *)dsp_vtec1200_4680ms_nofec;//dsp_selp1200_4620ms_nofec;
		zzwpro_vocoder_offset_of_one_frame = 10;
		dsp_test_vocoder_total_byte = TEST_VOICE_VOCODER_NOFEC1200_LENGTH * sizeof(uint16_t);
	}
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_V)
	{
vocoder_ambe_nvoc_2400:
		if (maintain_setup_parameter(PARA_OPERATE_READ, VOCODER_ZZW_SETUP_TYPE, 0))			// 1-nvoc
		{
			if (maintain_setup_parameter(PARA_OPERATE_READ, VOCODER_ZZW_SETUP_SPEED, 0))	// 1-2200
				dsp_test_vocoder_address = (uint8_t *)dsp_nvoc2200_4620ms_nofec;
			else
				dsp_test_vocoder_address = (uint8_t *)dsp_nvoc_4620ms_nofec;
		}
		else
		{
			dsp_test_vocoder_address = (uint8_t *)dsp_ambe_4620ms_nofec;
		}

		zzwpro_vocoder_offset_of_one_frame = 6;
		dsp_test_vocoder_total_byte = TEST_VOICE_VOCODER_NOFEC2400_LENGTH * sizeof(uint16_t);
	}
	else
	{
		dsp_test_vocoder_address = (uint8_t *)dsp_ambe_4620ms;
		zzwpro_vocoder_offset_of_one_frame = 9;										// pdt do NOT use this variable
		dsp_test_vocoder_total_byte = TEST_VOICE_VOCODER_LENGTH;					// pdt do NOT use this variable
		if (maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0) < INTERPHONE_MODE_ZZW_ADHOC)
		{
			if (maintain_setup_parameter(PARA_OPERATE_READ, VOCODER_PDT_SETUP_TYPE, 0))		// 1-nvoc
			{
				if (maintain_setup_parameter(PARA_OPERATE_READ, VOCODER_PDT_SETUP_SPEED, 0))// 1-2200
					dsp_test_vocoder_address = (uint8_t *)dsp_nvoc2200_4620ms;
				else
					dsp_test_vocoder_address = (uint8_t *)dsp_nvoc_4620ms;
			}
		}
	}
	p_dsp_test_vocoder_fetch = dsp_test_vocoder_address;
}

void test_vocoder_to_vocoder(uint16_t *vocoder)		// all data must be little-endian
{
	uint8_t i;

	if (((uint32_t)p_dsp_test_vocoder_fetch - (uint32_t)dsp_test_vocoder_address) >= TEST_VOICE_VOCODER_LENGTH)
		p_dsp_test_vocoder_fetch = dsp_test_vocoder_address;

	for (i = 0; i < 30; i++)
	{
		if ((i == 9) || (i == 19) || (i == 29))
			((uint8_t *)vocoder)[i] = 0;
		else
			((uint8_t *)vocoder)[i] = *p_dsp_test_vocoder_fetch++;
	}
}

void signaling_to_vocoder(uint8_t *vocoder, uint8_t *signaling, uint8_t *sync, uint8_t *cach, uint8_t vocoder_has_gap)		// all data must be little-endian; sync=7B: first byte's low 4bit & last byte's high 4bit
{
	uint8_t i, *p_vocoder = (uint8_t *)vocoder, *p_signaling = signaling + 3;

	if (cach)
		memcpy(cach, signaling, 3);

	if (sync)	// p_signaling[16]'s low 4bit is EBS/SYNC
		memcpy(sync, &signaling[3 + 13], 7);

	if (vocoder_has_gap == 3)
	{
		// S0 S1 S2 S3 S4 S5 0  0  0  S6 S7 S8  S9  S10.H4bit
		memcpy(p_vocoder, p_signaling, 6);
		p_vocoder += 6;
		memcpy(p_vocoder, p_signaling + 9, 3);
		p_vocoder += 3;
		// do NOT set vocoder[9]=signaling[12]
		p_vocoder++;
	}
	else
	{
		for (i = 0; i < 13; i++)
		{
			if ((i == 9) && vocoder_has_gap)
				*p_vocoder++ = 0;
			*p_vocoder++ = p_signaling[i];
		}
	}
	*p_vocoder = p_signaling[13] & 0xf0;	// p_vocoder[13]'s low 4bit is EBS/SYNC
	*p_vocoder++ |= p_signaling[19] & 0x0f;	// p_signaling[19]'s high 4bit is EBS/SYNC
	for (i = 20; i < 33; i++)
	{
		if ((i == 24) && vocoder_has_gap)
			*p_vocoder++ = 0;
		*p_vocoder++ = p_signaling[i];
	}
	*p_vocoder = 0;
}

void signaling_to_vocoder_xv(uint8_t *vocoder, uint32_t *signaling, uint8_t vocoder_len, uint8_t vocoder_one_frame_len)			// all data must be little-endian
{
	uint8_t i, n, crc;
	uint8_t *p_vocoder = (uint8_t *)vocoder, *p_signaling;

	n = vocoder_len / ((9 + 1) / sizeof(uint16_t));						// 20ms' vocoder code is 9B(add 1B gap); so X(60ms) is 3frame, V(180ms) is 9frame
//	p_signaling = (uint8_t *)signaling + (interphone_mode == INTERPHONE_MODE_ZZWPRO_Q ? 3 : 4);
	p_signaling = (uint8_t *)signaling + SIGNALING_XV_STACK_EMBEDDED;					// fixed fill to index 4
	crc = (((uint8_t *)signaling)[VPLUS_SIGNALING_BUF_LENGTH - 1] >> 1) & 0x01;		// bit1 is total crc(Q/V/N(20190215ȥ����֡ת����������CRC�ϲ���)
	for (i = 0; i < n; i++, p_signaling += vocoder_one_frame_len, p_vocoder += DSP_VOCODER_INTERFACE_BYTE_ALIGNED)
	{
		memcpy(p_vocoder, p_signaling, vocoder_one_frame_len);
//		p_vocoder[vocoder_one_frame_len] = crc;
		p_vocoder[DSP_VOCODER_INTERFACE_BYTE_ALIGNED - 1] = crc;
	}
}

void xv_voice_to_vocoder(void)
{
	uint8_t i, vocoder_get_times, vocoder_offset;
#if 0 // def VICTEL_ZZWPRO_BASE
	uint32_t signal[CODEC_RX_BUFFER_SIZE * 2 / sizeof(uint32_t)];
#endif

	if (send_inner_vocoder == 0)										// mic
	{
#if 0 // def VICTEL_ZZWPRO_BASE // only for real base. 805 base should use itself(at DSP)
		memcpy((uint8_t *)signal + SIGNALING_XV_STACK_EMBEDDED, get_can_vocoder_address(), vocoder_payload_fr_dsp_length);
		signaling_to_vocoder_xv((uint8_t *)vocoder_data, signal, vocoder_data_fr_dsp_length, zzwpro_vocoder_one_frame_fr_dsp);
		ntohs_mul(vocoder_data, vocoder_data_fr_dsp_length);
#else
		get_mic_encode_data((uint8_t *)vocoder_data);
#endif
	}
	else if (send_inner_vocoder == 3)									// internal
	{
		if (((uint32_t)p_dsp_test_vocoder_fetch - (uint32_t)dsp_test_vocoder_address) >= dsp_test_vocoder_total_byte)
			p_dsp_test_vocoder_fetch = dsp_test_vocoder_address;

		// dsp_ambe_4620ms_nofec:		6(6B is valid)(once get 2 frames(Q,vocoder_data_fr_dsp_length==10) or 9 frames(V,vocoder_data_fr_dsp_length==45))
		// dsp_nvoc_4620ms_nofec:		same as dsp_ambe_4620ms_nofec
		// dsp_nvoc2200_4620ms:			same as dsp_ambe_4620ms_nofec, but data pattern is 9B aligned
		// dsp_selp1200_4620ms_nofec/dsp_vtec1200_4680ms_nofec:	10(9B is valid)(once get one frame); vocoder_data_fr_dsp_length == 5
		// dsp_selp600_4640ms_nofec:	6(6B is valid)(once get 2 frames); vocoder_data_fr_dsp_length == 10
		vocoder_get_times = vocoder_data_fr_dsp_length * sizeof(uint16_t) / DSP_VOCODER_INTERFACE_BYTE_ALIGNED;	// /5: dsp vocoder interface is 10B(U16 of 5) aligned of one frame
		for (i = 0, vocoder_offset = 0; i < vocoder_get_times; i++)
		{
			memcpy((uint8_t *)vocoder_data + vocoder_offset, p_dsp_test_vocoder_fetch, zzwpro_vocoder_offset_of_one_frame/*zzwpro_vocoder_one_frame_fr_dsp*/);
//			if (zzwpro_vocoder_one_frame_fr_dsp & 0x01)
//				*((uint8_t *)vocoder_data + vocoder_offset + zzwpro_vocoder_one_frame_fr_dsp - 1) = *(p_dsp_test_vocoder_fetch + zzwpro_vocoder_one_frame_fr_dsp);	// copy the last byte(at high BYTE of the U16)
//			memset((uint8_t *)vocoder_data + vocoder_offset + zzwpro_vocoder_one_frame_fr_dsp, 0, DSP_VOCODER_INTERFACE_BYTE_ALIGNED - zzwpro_vocoder_one_frame_fr_dsp);
			p_dsp_test_vocoder_fetch += zzwpro_vocoder_offset_of_one_frame;
			vocoder_offset += DSP_VOCODER_INTERFACE_BYTE_ALIGNED;
		}
	}
	else										// 1-1031; 2:O.153; replace O.153 by 1031 now
	{
		memcpy(vocoder_data, xv_test_1031, vocoder_data_fr_dsp_length * sizeof(uint16_t));
	}
}

void o153_to_vocoder(uint16_t *vocoder)		// all data must be little-endian
{
	uint16_t sing[REG_PACKET_IN_LENGTH];

	memcpy(sing, ((send_inner_vocoder == 1) ? test_1031 : test_o_153) + super_frame_count * REG_PACKET_IN_LENGTH * 2,
		REG_PACKET_IN_LENGTH * 2);
	signaling_to_vocoder((uint8_t *)vocoder, (uint8_t *)sing, 0, 0, 1);
	if (++super_frame_count >= 6)
		super_frame_count = 0;
}

void format_nofec_voice_signaling_to_ccm(uint8_t super_frame_counter, uint8_t *signaling)
{
	// BYTE num:   0  1  2  3  4  5  6  7  8  9  10 11 12 13 14  15  16                 17-21 22                 23  24  25  26  27  28  29  30  31  32  33 34 35
	// signaling:  C0 C1 C2 S0 S1 S2 S3 S4 S5 0  0  0  S6 S7 S8  S9  S10.H4bit+V0.H4bit V(5B) V5.L4bit+S10.L4bit S11 0   0   0   S12 S13 S14 S15 S16 S17 0  0  0
	// ccm format: C0 C1 C2 ST S0 S1 S2 S3 S4 S5 S6 S7 S8 S9 S10 S11 S10.H4bit+V0.H4bit V(5B) V5.L4bit+S10.L4bit V20 S12 S13 S14 S15 S16 S17 S18 V28 CODE[4]
	uint8_t *ptr8 = signaling, vocoder[12];

	memcpy(vocoder, ptr8 + 3, 6);
	memcpy(vocoder + 6, ptr8 + 12, 5);
	vocoder[10] &= 0xf0;					// low 4bit is EBS/SYNC
	vocoder[10] |= ptr8[22] & 0x0f;			// high 4bit is EBS/SYNC
	vocoder[11] = ptr8[23];

	ptr8[3] = (super_frame_counter == 0) ? 14 : 15;	// ST: ��Ƶ��֡��ͷ֡=14������5֡=15
	memcpy(ptr8 + 4, vocoder, 12);
	ptr8[23] = 0;							// V20
	memcpy(vocoder, ptr8 + 27, 6);
	memcpy(ptr8 + 24, vocoder, 6);
//	ptr8[30] = 0;							// S18
	ptr8[30] = ptr8[33];					// 20221010: PDT��չ���ܣ�������3+33B��Ϊ3+34B,����Ǹ��ֽڷ��������B33λ�ã���������CCM֡�е�S18��
	ptr8[31] = 0;							// V28
	memset(ptr8 + 32, 0, 4);
}

uint8_t format_ccm_to_nofec_voice_signaling(uint8_t *signaling)
{
	uint8_t *ptr8 = signaling, vocoder[12], super_frame = signaling[3];

	memcpy(vocoder, ptr8 + 4, 12);
	memcpy(ptr8 + 3, vocoder, 6);
	memcpy(ptr8 + 12, vocoder + 6, 4);
	ptr8[16] &= 0x0f;
	ptr8[16] |= vocoder[10] & 0xf0;
	ptr8[22] &= 0xf0;
	ptr8[22] |= vocoder[10] & 0x0f;
	ptr8[23] = vocoder[11];

	memcpy(vocoder, ptr8 + 24, 6);
	memcpy(ptr8 + 27, vocoder, 6);

	return super_frame;
}
/*
const uint8_t bs_data_sync_compress[6] = {0xdd, 0xff, 0x57, 0xd7, 0x5d, 0xf5};
uint8_t sync_is_base_signal_format(uint8_t *signaling)
{
	uint8_t sync0 = (signaling[16] & 0x0f) | (signaling[22] & 0xf0);

	return ((sync0 == bs_data_sync_compress[0]) && (signaling[17] == bs_data_sync_compress[1]) &&
		(signaling[18] == bs_data_sync_compress[2]) && (signaling[19] == bs_data_sync_compress[3]) &&
		(signaling[20] == bs_data_sync_compress[4]) && (signaling[21] == bs_data_sync_compress[5])) ? 1 : 0;
}
*/
const uint32_t bs_data_sync_compress[2] = {0xd757ffdd, 0x0000f55d};
const uint32_t ms_data_sync_compress[2] = {0x777f5d7d, 0x000075fd};
uint8_t sync_is_base_signal_format(uint8_t *signaling)
{
	uint8_t sync0 = (signaling[16] & 0x0f) | (signaling[22] & 0xf0);
	uint32_t sync32_l, sync32_h;

	sync32_l = COMBINE_CHAR_TO_LONG(sync0, signaling[17], signaling[18], signaling[19]);
	sync32_h = COMBINE_CHAR_TO_LONG(signaling[20], signaling[21], 0, 0);
	return (((sync32_l == bs_data_sync_compress[0]) && (sync32_h == bs_data_sync_compress[1])) ||
			((sync32_l == ms_data_sync_compress[0]) && (sync32_h == ms_data_sync_compress[1]))) ? 1 : 0;
}

void set_dsp_30ms_counter(uint8_t flag)		// flag: 0-air(local), 1-can(link)
{
	uint8_t dir = flag ? CALL_BY_LINK : CALL_BY_LOCAL;

	if (op_dsp_30ms.call_direct == CALL_IS_IDLE)
	{
		op_dsp_30ms.call_direct = dir;
		op_dsp_30ms.call_timeslot = CALL_MAX_TIMESLOT;
		set_dsp_extern_30ms_mode(flag);
#ifdef DEBUG_DSP_TRANSCODE
		vlog_v("config","Ext=%d", flag);
#endif
	}
	else if (op_dsp_30ms.call_direct == dir)
	{
		op_dsp_30ms.call_timeslot = CALL_MAX_TIMESLOT;
	}
}

void clr_dsp_30ms_counter(void)
{
	if (op_dsp_30ms.call_timeslot && (op_dsp_30ms.call_direct < CALL_FALLOW_DISABLE))
	{
		if (--op_dsp_30ms.call_timeslot == 0)
		{
			op_dsp_30ms.call_direct = CALL_IS_IDLE;
//			set_dsp_extern_30ms_mode(0);	// 20220811: test not reset
//  #ifdef DEBUG_DSP_TRANSCODE
//			vlog_v("config","Reset ext");
//  #endif
		}
	}
}

/*
const int8_t single_tone8[REG_SPEECH_OUT_LENGTH] = {
	0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,
	90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,
	-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,
	0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,
	90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,
	-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,
	0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,
	90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,
	-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,
	0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,
	90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,-127,-90,0,90,127,90,0,-90,
	-127,-90,0,90,127,90,0,-90,-127,-90
};
*/
/*
const unsigned char single_tone_alaw[REG_SPEECH_OUT_LENGTH] = {
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,
	213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3,213,131,138,131,213,3,10,3
};
*/
/*
const int16_t single_tone16[REG_SPEECH_OUT_LENGTH] = {
	0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,
	23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,
	-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,
	0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,
	0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,
	0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,
	0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,
	0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,
	0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,
	0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,
	0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,
	0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,
	0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,
	0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,
	0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,
	0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,
	0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,0,23170,32767,23170,0,-23170,-32767,-23170,
	0,23170,32767,23170,0,-23170,-32767,-23170
};
*/

void set_dsp_save_power_mode_absolutely(uint8_t flag)	// 0-enable rf save power, else-disable
{
	dsp_save_power_status = 0;
	if (flag == 0)
	{
		dsp_send_cmd(DSP_CMD_IDLE_ENTER);
	}
	else
	{
		dsp_send_cmd(DSP_CMD_IDLE_EXIT);
	}
//	set_module_save_power_status_to_can(flag);
}

#define DSP_SAVE_POWER_BASE_CONDITION()		(dsp_is_64xx() && (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC))
#define DSP_SAVE_POWER_CONFIG_ENABLE()		(rf_save_power.enable && rf_save_power.wake && rf_save_power.sleep && rf_save_power.keep)
uint8_t cal_save_power_division(void)
{
	uint8_t ret = 1;

	if (interphone_mode == INTERPHONE_MODE_ZZWPRO_Q)
	{
		if (g_runtime_inst.runtime_paras.zzw_vocoder.vocoder_type_q == VOCODER_TYPE_Q_SELP1200)
			ret = 2;
		else if (g_runtime_inst.runtime_paras.zzw_vocoder.vocoder_type_q == VOCODER_TYPE_Q_SELP600)
			ret = 3;
	}
	return ret;
}

void set_delay_to_sleep_counter(uint16_t time_slot);
void dsp_save_power_init(uint8_t flag)				// 0-boot(delay 60s before enter savepower); 1-runtime switch
{
/*	uint8_t dev_esn = get_real_esn_second_sector();

	set_dsp_save_power_slot(0, 0);
	dsp_save_power_status = 0;
	memcpy(&rf_save_power, &g_runtime_inst.runtime_paras.save_power, sizeof(SAVE_POWER_CONFIG_TYPEDEF));
	if (DSP_SAVE_POWER_BASE_CONDITION())
//	if (dsp_is_64xx())
	{
		if (DSP_SAVE_POWER_CONFIG_ENABLE())
		{
dsp_save_power_enable:
			dsp_save_power_status = DSP_SAVE_POWER_ENABLE_MASK;
			if ((interphone_mode != INTERPHONE_MODE_ZZWPRO_Q) && (flag == 0))
				set_delay_to_sleep_counter(SECOND_TO_SLOT(60));
		}
		else
		{
			if (!(dev_is_base() || DEV_IS_805_SERIES(dev_esn) || (dev_esn == MOBILE_MODE_PDT_ZZW_816V)))
			{
				dev_esn = 1;	// cal_save_power_division();
				rf_save_power.enable = 1;
				rf_save_power.wake = 9 / dev_esn;
				rf_save_power.sleep = 9 / dev_esn;
				rf_save_power.keep = 500 / dev_esn;
				goto dsp_save_power_enable;
			}
		}
	}*/
}

uint8_t set_dsp_save_power_paras(uint16_t wake, uint16_t sleep, uint16_t cont)
{
	if ((wake == 0) && (sleep == 0) && (cont == 0))
	{
		g_runtime_inst.runtime_paras.save_power.enable = 0;
	}
	else if ((wake == 0) && (sleep == 0) && (cont == 1))
	{
		g_runtime_inst.runtime_paras.save_power.enable = 1;
	}
	else if ((wake == 0) && (sleep == 0) && (cont == 2))
	{
		g_runtime_inst.runtime_paras.save_power.enable = g_runtime_inst.runtime_paras.save_power.enable ? 0 : 1;
	}
	else
	{
		g_runtime_inst.runtime_paras.save_power.wake = wake & 0x1f;
		g_runtime_inst.runtime_paras.save_power.sleep = sleep & 0x3ff;
		g_runtime_inst.runtime_paras.save_power.keep = cont;
	}

	dsp_save_power_init(0);

	return IS_DSP_SAVE_POWER_ENABLE();
}

void stack_force_set_savepower(uint8_t no_savepower)
{
	if (no_savepower)
	{
		dsp_enter_save_power = null_void_u16;
		set_dsp_save_power_slot(0, 0);
#ifdef NV_TRACE_MULTI_HEARTBEAT
		vlog_v("config","\tstack force no savepower");
#endif
	}
	else
	{
		dsp_enter_save_power = dsp_send_cmd;
#ifdef NV_TRACE_MULTI_HEARTBEAT
		vlog_v("config","\tstack enter normal savepower");
#endif
	}
}

uint8_t temp_to_set_dsp_save_power(uint16_t wake, uint16_t sleep, uint16_t cont)
{
	if ((wake == 0) && (sleep == 0) && (cont == 0))
	{
		dsp_save_power_status = 0;
		set_dsp_save_power_slot(0, 0);
	}
	else if ((wake == 0) && (sleep == 0) && (cont == 1))
	{
		dsp_save_power_init(0);
	}
	else
	{
		g_runtime_inst.runtime_paras.save_power.wake = wake & 0x1f;
		g_runtime_inst.runtime_paras.save_power.sleep = sleep & 0x3ff;
		g_runtime_inst.runtime_paras.save_power.keep = cont;
		dsp_save_power_init(1);
	}

	return IS_DSP_SAVE_POWER_ENABLE();
}

void set_dsp_temporary_to_normal(uint8_t flag)
{
	dsp_save_power_status |= DSP_SAVE_POWER_TEMP_ENABLE;
}

void print_dsp_save_power_paras(void)
{
	vlog_v("config","Save power/Paras=%s/%s,wake=%d/%d,sleep=%d/%d,keep=%d/%d",
		rf_save_power.enable ? "EN" : "DIS", g_runtime_inst.runtime_paras.save_power.enable ? "EN" : "DIS",
		rf_save_power.wake, g_runtime_inst.runtime_paras.save_power.wake,
		rf_save_power.sleep, g_runtime_inst.runtime_paras.save_power.sleep,
		rf_save_power.keep, g_runtime_inst.runtime_paras.save_power.keep);
}

void set_dsp_save_power_mode(uint8_t mode)	// 0-to normal; 1-to sleep; else-toggle
{
	if (mode == 0)
	{
set_dsp_save_power_to_normal:
		dsp_send_cmd(DSP_CMD_IDLE_EXIT);
		dsp_save_power_status &= ~DSP_SAVE_POWER_SLEEP_NOW;
//		dsp_save_power_counter = rf_save_power.wake;
#ifdef DEBUG_DSP_SAVE_POWER
		vlog_v("config","[%d %d]To nor:%02x %d %d", slot_rising_edge_int_times, slot_falling_edge_int_times, dsp_save_power_status, dsp_save_power_counter9s, dsp_save_power_counter);
#endif
	}
	else if (mode == 1)
	{
set_dsp_save_power_to_sleep:
		dsp_enter_save_power(DSP_CMD_IDLE_ENTER);	// dsp_send_cmd(DSP_CMD_IDLE_ENTER);
		dsp_save_power_status |= DSP_SAVE_POWER_SLEEP_NOW;
//		dsp_save_power_counter = rf_save_power.sleep;
#ifdef DEBUG_DSP_SAVE_POWER
		vlog_v("config","[%d %d]To Slp:%02x %d %d", slot_rising_edge_int_times, slot_falling_edge_int_times, dsp_save_power_status, dsp_save_power_counter9s, dsp_save_power_counter);
#endif
	}
	else
	{
		if (dsp_save_power_status & DSP_SAVE_POWER_SLEEP_NOW)
		{
			dsp_save_power_counter = rf_save_power.wake;
			goto set_dsp_save_power_to_normal;
		}
		else
		{
			dsp_save_power_counter = rf_save_power.sleep;
			goto set_dsp_save_power_to_sleep;
		}
	}
}

void set_delay_to_sleep_counter(uint16_t time_slot)
{
#ifdef DEBUG_DSP_SAVE_POWER
	vlog_v("config","[%d %d]Dy2Slp:%02x %d %d->%d", slot_rising_edge_int_times, slot_falling_edge_int_times, dsp_save_power_status, dsp_save_power_counter9s, dsp_save_power_counter, time_slot);
#endif
	dsp_save_power_status |= DSP_SAVE_POWER_DELAY_TO_SLEEP;
	if (dsp_save_power_counter < time_slot)
		dsp_save_power_counter = time_slot;
}

void set_dsp_exit_to_normal(uint16_t time_slot)
{
//	if (IS_DSP_SAVE_POWER_ENABLE())
	{
		set_dsp_save_power_mode(0);
		set_delay_to_sleep_counter(time_slot);
	}
}

void set_dsp_exit_to_normal_abs(uint16_t time_slot)
{
	if (IS_DSP_SAVE_POWER_ENABLE())
	{
		if (((dsp_save_power_status & DSP_SAVE_POWER_DELAY_TO_SLEEP) == 0) || (dsp_save_power_status & DSP_SAVE_POWER_SLEEP_NOW))
			set_dsp_exit_to_normal(time_slot);	// rf_save_power.keep
		else
			set_delay_to_sleep_counter(time_slot);
	}
}

void set_dsp_to_save_power(uint8_t flag)	// 0-edge; 1-calling setup; 2-calling end
{
//	if (IS_DSP_SAVE_POWER_ENABLE())
	{
		if ((dsp_save_power_status & DSP_SAVE_POWER_DELAY_TO_SLEEP) == 0)
		{
			if (dsp_save_power_status & DSP_SAVE_POWER_TEMP_ENABLE)
			{
				if ((dsp_save_power_status & DSP_SAVE_POWER_TEMP_ENABLE) <= DSP_SAVE_POWER_TEMP_ENABLE_DONE)
				{
#if defined DEBUG_DSP_SAVE_POWER || (NV_TRACE_MULTI_HEARTBEAT > 1)
					vlog_v("config","\t[%d %d]TE:end %02X", slot_rising_edge_int_times, slot_falling_edge_int_times, dsp_save_power_status);
#endif
					dsp_save_power_status &= ~DSP_SAVE_POWER_TEMP_ENABLE;
					if (dsp_save_power_status & DSP_SAVE_POWER_SLEEP_NOW)
					{
						dsp_enter_save_power(DSP_CMD_IDLE_ENTER);	// dsp_send_cmd(DSP_CMD_IDLE_ENTER);
					}
				}
				else
				{
#if defined DEBUG_DSP_SAVE_POWER || (NV_TRACE_MULTI_HEARTBEAT > 1)
					vlog_v("config","\t[%d %d]TE:dec %02X", slot_rising_edge_int_times, slot_falling_edge_int_times, dsp_save_power_status);
#endif
					if (((dsp_save_power_status & DSP_SAVE_POWER_TEMP_ENABLE) == DSP_SAVE_POWER_TEMP_ENABLE) & (dsp_save_power_status & DSP_SAVE_POWER_SLEEP_NOW))
					{
						dsp_send_cmd(DSP_CMD_IDLE_EXIT);
					}
					dsp_save_power_status -= 0x10;
				}
			}
			else
			{
				if (dsp_save_power_counter)
					dsp_save_power_counter--;
				else
					set_dsp_save_power_mode(2);
			}
		}
		else if (dsp_save_power_status & DSP_SAVE_POWER_DELAY_TO_SLEEP)
		{
			if (dsp_save_power_status & DSP_SAVE_POWER_TEMP_ENABLE)
			{
#if defined DEBUG_DSP_SAVE_POWER || (NV_TRACE_MULTI_HEARTBEAT > 1)
				vlog_v("config","\t[%d %d]TE:append %d %02X", slot_rising_edge_int_times, slot_falling_edge_int_times, dsp_save_power_counter, dsp_save_power_status);
#endif
				dsp_save_power_status &= ~DSP_SAVE_POWER_TEMP_ENABLE;
				if (dsp_save_power_counter < DSP_SAVE_POWER_TEMP_ENABLE_SLOT)
					dsp_save_power_counter = DSP_SAVE_POWER_TEMP_ENABLE_SLOT;
			}

			if (dsp_save_power_counter)
				dsp_save_power_counter--;
			else
				dsp_save_power_status &= ~DSP_SAVE_POWER_DELAY_TO_SLEEP;
		}
	}
}

void zzwpro_slot_end_process(uint8_t total_slot);
// uint32_t offset_time_check_1ms = 0;

void set_time_gap_for_insert_slot_sync(uint8_t gap)
{
#ifdef SUPPORT_EXTEND_FUNCTION
	if (gap)
	{
		time_gap_for_insert_slot_sync = gap + 30;
		vlog_v("config","set insert sync gap=%d", time_gap_for_insert_slot_sync);
	}
	else
	{
		time_gap_for_insert_slot_sync = 0;
		vlog_v("config","disable insert sync");
	}
#endif
}

void set_slot_adjust_timeout(void)
{
	dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_SLOT_ADJUST_TIMEOUT;
	if ((interphone_mode == INTERPHONE_MODE_ZZWPRO_V) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_V25K) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_N))
		dsp_runtime_misc_flag |= 6;
	else
		dsp_runtime_misc_flag |= 2;
}

uint8_t is_slot_adjust_timeout(void)
{
	if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_SLOT_ADJUST_TIMEOUT)
	{
		dsp_runtime_misc_flag--;
		return 1;
	}
	else
	{
		return 0;
	}
}

uint8_t is_gps_pps_ok(void)
{
	return (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_GPS_PPS_IS_OK) ? 1 : 0;
}

void set_gps_pps_state(uint8_t pps_good)
{
	if (pps_good)
		dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_GPS_PPS_IS_OK;
	else
		dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_GPS_PPS_IS_OK;
}

void set_check38p4_state(uint8_t flag)	// 0-fail, 1-ok
{
	if (flag == 0)
		dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_CHECK38P4_DONE;
	else if (flag == 1)
		dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_CHECK38P4_DONE;
}

uint8_t ui_enter_admittest_mode(uint8_t flag)			// 0-normal; 1-admittest; else-get the status
{
	if (flag == 0)
		dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_ENTER_ADMITTEST_MODE;
	else if (flag == 1)
		dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_ENTER_ADMITTEST_MODE;

	return (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_ENTER_ADMITTEST_MODE) ? 1 : 0;
}

void vocoder_switch_speed(uint8_t speed)	// 0-2400, else-2200
{
	if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_SWITCH_VOCODER_SPEED_EN)
	{
		if ((speed == 0) && (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_SWITCH_VOCODER_2200))
		{
			dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_SWITCH_VOCODER_2200;		// switch to 2400 & add fec
			set_dsp_vocoder_mode(REG_VOCODER_MODE_ENC_RATE_MASK | REG_VOCODER_MODE_ENC_FEC_MASK | REG_VOCODER_MODE_DEC_RATE_MASK | REG_VOCODER_MODE_DEC_FEC_MASK,
				(1 << REG_VOCODER_MODE_ENC_FEC_SHIFT) | (1 << REG_VOCODER_MODE_DEC_FEC_SHIFT));
//			vlog_v("config","\t2400");
		}
		else if (speed && ((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_SWITCH_VOCODER_2200) == 0))
		{
			dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_SWITCH_VOCODER_2200;		// switch to 2200 & del fec
			set_dsp_vocoder_mode(REG_VOCODER_MODE_ENC_RATE_MASK | REG_VOCODER_MODE_ENC_FEC_MASK | REG_VOCODER_MODE_DEC_RATE_MASK | REG_VOCODER_MODE_DEC_FEC_MASK,
				(1 << REG_VOCODER_MODE_ENC_RATE_SHIFT) | (1 << REG_VOCODER_MODE_DEC_RATE_SHIFT));
//			vlog_v("config","\t2200");
		}
//		else
//		{
//			vlog_v("config","\t%s->%s", (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_SWITCH_VOCODER_2200) ? "2200" : "2400", speed ? "2200" : "2400");
//		}
	}
//	else
//	{
//		vlog_v("config","\tvoc switch disable");
//	}
}

#if defined PDT_CONV_VOICE_AT_QMODE || defined NV_AUTO_TRACE_VOICE_AT_QMODE

#define is_diff_tr_mode_at_voice_channel()		(dsp_runtime_misc_flag & DSP_RUNTIME_MISC_DIFF_TR_AT_VOICE_CHANNEL)
//uint32_t f_is_diff_tr_mode_at_voice_channel(void)
//{
//	return dsp_runtime_misc_flag & DSP_RUNTIME_MISC_DIFF_TR_AT_VOICE_CHANNEL;
//}

uint8_t is_nq_double_receive(void)
{
	return (auto_trace_nv25k & ZZWPRO_AUTOTRACE_VQ_ENABLE_MASK) ? 1 : 0;
}

void temp_to_switch_workmode(uint8_t work_mode)
{
	uint8_t vocoder_type = get_vocoder_content_with_work_mode(work_mode);
//	uint8_t vocoder_q_type = get_q_vocoder_speed();

	dsp_work_mode_switch(work_mode);

//	set_dsp_vocoder_length_paras(work_mode, vocoder_q_type);
	set_vocoder_rate_agc_ns(work_mode, 3, vocoder_type);
//	set_dsp_test_vocoder_pointer(work_mode, vocoder_q_type);
//	interphone_mode = INTERPHONE_MODE_ZZWPRO_Q;
}

#define NVQ_JUMP_FREQ_NOW(set_freq)		if (nvq_tx_freq_index != set_freq)\
										{\
											nvq_tx_freq_index = set_freq;\
											unify_nvq_jump_freq(nvq_tx_freq_index);\
										}

void set_trunk_at_voice_channel(uint8_t flag, uint16_t set_freq)  // 0: voice->ctrl; 1: ctrl->voice; 2-toggle; else-get
{
//	if (auto_trace_nv25k & (ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK | ZZWPRO_AUTOTRACE_VQ_ENABLE_MASK))
	{
		// 20220514:��ǰֻ������Q�������Ҫ��Ƶ��Nֻ��һ���غ�Ƶ�ʣ����ڿ�����N��ҲҪ��Ƶ���䣨��������NF2�Ϸ�������������ֻҪ���䲻��ʲô��ʽ��Ҫ�ж��Ƿ���Ҫ��Ƶ
#ifdef NVQ_UNIFY_JUMP_FREQ
		NVQ_JUMP_FREQ_NOW(set_freq);
#endif
		if (flag == 1)
		{
#ifndef NVQ_UNIFY_JUMP_FREQ
			NVQ_JUMP_FREQ_NOW(set_freq);
#endif
			if (is_diff_tr_mode_at_voice_channel() == 0)
			{
pdt_nv_voice_at_q_set_to_voice:
				dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_DIFF_TR_AT_VOICE_CHANNEL;
				vocoder_data_fr_dsp_length = REG_VOCODER_DATA_IN_LENGTH / 3;
				zzwpro_vocoder_one_frame_fr_dsp = 9;	// Q is 9
				temp_to_switch_workmode(INTERPHONE_MODE_ZZWPRO_Q);
#ifdef DEBUG_NVQ_PRINT_INFO
				vlog_v("config","\t[%d/%d]To Q%d", slot_rising_edge_int_times, slot_falling_edge_int_times, set_freq);
#endif
			}
		}
		else if (flag == 0)
		{
			if (is_diff_tr_mode_at_voice_channel())
			{
pdt_nv_voice_at_q_set_to_ctrl:
				dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_DIFF_TR_AT_VOICE_CHANNEL;
				vocoder_data_fr_dsp_length = REG_VOCODER_DATA_IN_LENGTH;
				zzwpro_vocoder_one_frame_fr_dsp = 9;	// PDT not need; but N is 9
#ifndef NVQ_UNIFY_JUMP_FREQ
				nvq_tx_freq_index = 0xffff; 			// for setting freq at next switch to Q
				unify_nvq_jump_freq(nvq_tx_freq_index);
#endif
				if (auto_trace_nv25k & ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK)
				{
					temp_to_switch_workmode(INTERPHONE_MODE_PDT_CONV);
#ifdef DEBUG_NVQ_PRINT_INFO
					vlog_v("config","\t[%d/%d]To C", slot_rising_edge_int_times, slot_falling_edge_int_times);
#endif
				}
				else
				{
					if (auto_trace_nv25k & ZZWPRO_AUTOTRACE_NV_ENABLE_MASK)
					{
						temp_to_switch_workmode(INTERPHONE_MODE_ZZWPRO_V25K);
#ifdef DEBUG_NVQ_PRINT_INFO
						vlog_v("config","\t[%d/%d]To NV", slot_rising_edge_int_times, slot_falling_edge_int_times);
#endif
					}
					else
					{
						temp_to_switch_workmode(interphone_mode);
#ifdef DEBUG_NVQ_PRINT_INFO
						vlog_v("config","\t[%d/%d]To %c", slot_rising_edge_int_times, slot_falling_edge_int_times, (interphone_mode == INTERPHONE_MODE_ZZWPRO_N) ? 'N' : 'V');
#endif
					}
				}
			}
		}
		else if (flag == 2)
		{
			if (is_diff_tr_mode_at_voice_channel())
				goto pdt_nv_voice_at_q_set_to_ctrl;
			else
				goto pdt_nv_voice_at_q_set_to_voice;
		}
	}

//	return is_diff_tr_mode_at_voice_channel() ? 1 : 0;
}

void nvq_switch_only_set_tx_mode_and_freq(uint8_t flag, uint16_t set_freq)	// 0: to NV; 1: to Q
{
	uint8_t work_mode;

	// 20220514:��ǰֻ������Q�������Ҫ��Ƶ��Nֻ��һ���غ�Ƶ�ʣ����ڿ�����N��ҲҪ��Ƶ���䣨��������NF2�Ϸ�������������ֻҪ���䲻��ʲô��ʽ��Ҫ�ж��Ƿ���Ҫ��Ƶ
//	vlog_v("config","->%c:%04x->%04x", flag ? 'Q' : 'N', nvq_tx_freq_index, set_freq);
#ifdef NVQ_UNIFY_JUMP_FREQ
	NVQ_JUMP_FREQ_NOW(set_freq);
#endif

	if (flag == 1)			// NV->Q
	{
#ifndef NVQ_UNIFY_JUMP_FREQ
		NVQ_JUMP_FREQ_NOW(set_freq);
#endif
		if (is_diff_tr_mode_at_voice_channel() == 0)
		{
			dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_DIFF_TR_AT_VOICE_CHANNEL;
			work_mode = INTERPHONE_MODE_ZZWPRO_Q;
			vocoder_data_fr_dsp_length = REG_VOCODER_DATA_IN_LENGTH / 3;
			zzwpro_vocoder_one_frame_fr_dsp = 9;		// Q is 9
#ifdef DEBUG_NVQ_PRINT_INFO
			vlog_v("config","\t[%d/%d]To Q%d", slot_rising_edge_int_times, slot_falling_edge_int_times, set_freq);
#endif
nvq_set_tx_mode:
			set_dsp_mod_type(work_mode, get_q_vocoder_speed());
			set_vocoder_rate_agc_ns(work_mode, 1, get_vocoder_content_with_work_mode(work_mode));
		}
	}
	else if (flag == 0)		// Q->NV
	{
		if (is_diff_tr_mode_at_voice_channel())
		{
			dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_DIFF_TR_AT_VOICE_CHANNEL;
			vocoder_data_fr_dsp_length = REG_VOCODER_DATA_IN_LENGTH;
			zzwpro_vocoder_one_frame_fr_dsp = 9;		// PDT not need; but N is 9
#ifndef NVQ_UNIFY_JUMP_FREQ
			nvq_tx_freq_index = 0xffff; 				// for setting freq at next switch to Q
			unify_nvq_jump_freq(nvq_tx_freq_index);		// reset to normal freq
#endif
			if (auto_trace_nv25k & ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK)
			{
				work_mode = INTERPHONE_MODE_PDT_CONV;
#ifdef DEBUG_NVQ_PRINT_INFO
				vlog_v("config","\t[%d/%d]To C", slot_rising_edge_int_times, slot_falling_edge_int_times);
#endif
			}
			else
			{
				work_mode = (auto_trace_nv25k & ZZWPRO_AUTOTRACE_NV_ENABLE_MASK) ? INTERPHONE_MODE_ZZWPRO_V25K : interphone_mode;
#ifdef DEBUG_NVQ_PRINT_INFO
				vlog_v("config","\t[%d/%d]To %c", slot_rising_edge_int_times, slot_falling_edge_int_times, (work_mode == INTERPHONE_MODE_ZZWPRO_N) ? 'N' : 'V');
#endif
			}
			goto nvq_set_tx_mode;
		}
	}
}

#endif // defined PDT_CONV_VOICE_AT_QMODE || defined NV_AUTO_TRACE_VOICE_AT_QMODE


void print_report_downlink_rssi(uint8_t rssi, uint16_t ext_cont)
{
//	if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_REPORT_DOWNLINK_RSSI)
	{
		vlog_v("config","%s=%02x%02x%02x%02x%04x%02x%02x%04x%04x%02x%04x",
		reportrssi_string, rssi, rmc_data.gps_state,
		rmc_data.lat_degree, rmc_data.lat_integer, rmc_data.lat_decimal,
		rmc_data.lon_degree, rmc_data.lon_integer, rmc_data.lon_decimal,
		get_current_chan(), rmc_data.speed, ext_cont);
	}
}

uint32_t setup_report_downlink_rssi(uint8_t enable, uint16_t report_time)
{
	uint16_t time_div;

	if (enable == 0)
	{
		dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_REPORT_DOWNLINK_RSSI;
	}
	else if (enable == 1)
	{
		dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_REPORT_DOWNLINK_RSSI;

		if (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING)
			time_div = MPT_TRUNK_STACK_POLL_PERIOD;
		else if (interphone_mode == INTERPHONE_MODE_MPT_CONV)
			time_div = MPT_CONV_STACK_POLL_PERIOD;
		else
			time_div = 30;

		if (report_time >= 120)
			pdt_report_rssi_time = report_time / time_div + (report_time % time_div ? 1 : 0);
	}

	return dsp_runtime_misc_flag & DSP_RUNTIME_MISC_REPORT_DOWNLINK_RSSI;
}

void conv_report_rssi_handle(void)
{
	if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_REPORT_DOWNLINK_RSSI)
	{
		if (++pdt_report_rssi_cnt >= pdt_report_rssi_time)
		{
			pdt_report_rssi_cnt = 0;
			print_report_downlink_rssi(get_rssi_value_rel(0), get_current_lai());
		}
	}
}

/*
void set_dsp_sync_at_next_second(void)
{
	dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_SET_SYNC_NEXT_SECOND;
}
*/
#define REG_DSP_CMD_SYNC_NEXT_SECOND_CMD_MASK			0x001F
#define REG_DSP_CMD_SYNC_NEXT_SECOND_PARA_MASK			0x0060
#define REG_DSP_CMD_SYNC_NEXT_SECOND_PARA_SHIFT			5
#define REG_DSP_CMD_SYNC_NEXT_SECOND_PARA				0x0000
#define REG_DSP_CMD_GPS_LOCKED_NOW						0x0020
#define REG_DSP_CMD_GPS_UNLOCKED_NOW					0x0040
void set_dsp_sync_at_next_second(uint16_t para)						// 0-9s sync; 1-gps locked; 2-gps unlocked
{
//	dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_SET_SYNC_NEXT_SECOND;
	dsp_send_cmd(DSP_CMD_SYNC_NEXT_SECOND | ((para & 0x03) << REG_DSP_CMD_SYNC_NEXT_SECOND_PARA_SHIFT));
//	check_dsp_9s_sync_counter = check_dsp_9s_sync_bak;
//	check_dsp_9s_sync_bak = slot_rising_edge_int_times + slot_falling_edge_int_times;
//	vlog_v("config","9s:R=%d,F=%d,g=%d,T=%d", slot_rising_edge_int_times, slot_falling_edge_int_times,
//		check_dsp_9s_sync_bak - check_dsp_9s_sync_counter, gps_minute_second);
}

#define VAD_TIP_CYCLE			(3000 / 30)
static uint8_t vad_tip_counter = 0;
void vad_tip_process(void)
{
	if (phone_is_busy_now() == 0)
	{
		if (vad_tip_counter++ >= VAD_TIP_CYCLE)
		{
			vad_tip_counter = 0;
			send_tip_sound(TIP_TYPE_VAD_ENABLE, 1);
		}
	}
}

#if defined VAD_KEY_USE_AS_STICK_PTT || defined PTT_KEY_USE_AS_STICK

void set_ptt_stick_pressed(void)
{
	dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_PTT_DETECTED;
}

void clear_ptt_stick_status(void)
{
	dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_PTT_PRESSED;
}

void vad_key_process(void)
{
	if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_PTT_DETECTED)
	{
		dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_PTT_DETECTED;
		if ((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_PTT_PRESSED) == 0)
		{
			dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_PTT_PRESSED;
			set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
			vlog_v("config","stick detect");
		}
		else
		{
			dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_PTT_PRESSED;
			set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
			vlog_v("config","stick end");
		}
	}
}

#elif defined VAD_CONTROL_BY_DSP	// VAD_KEY_USE_AS_STICK_PTT/VAD_CONTROL_BY_DSP

void vad_key_process(void)
{
	uint16_t speech_state;

	if (is_vocoder_mix_enable())
	{
		dsp_reg_read(REG_DSP64_SPEECH_INTERRUPT_STATE_ADDR, &speech_state);
		if (speech_state & REG_SPEECH_INTERRUPT_STATE_VAD_MASK)
		{
			if ((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_PTT_PRESSED) == 0)
			{
				dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_PTT_PRESSED;
				set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
				vlog_v("config","VAD detect");
			}
		}
		else
		{
			if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_PTT_PRESSED)
			{
				dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_PTT_PRESSED;
				set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
				vlog_v("config","VAD end");
			}
		}
	}
}

#else	// VAD_KEY_USE_AS_STICK_PTT/VAD_CONTROL_BY_DSP

static uint16_t vad_ext_noise = 60, vad_mic_difference = 30;
static uint8_t vad_detect_time = 0;
void vad_adc_value_check(uint16_t *int_mic, uint16_t *ext_mic);
void set_vad_paras(uint16_t ext_noise, uint16_t mic_diff)
{
	if ((ext_noise == 0) || (mic_diff == 0))
	{
		set_speech_vad_enable(0);
	}
	else
	{
		vad_ext_noise = ext_noise;
		vad_mic_difference = mic_diff;

		set_speech_vad_enable(1);
	}
	vlog_v("config","\tVAD %s,noise=%d,diff=%d,trail=%d", is_speech_vad_enable() ? "enable" : "disable",
		vad_ext_noise, vad_mic_difference, (uint16_t)g_static_ptr->misc_static_config.dsp_vad_trailing_time * 100);
}

static const uint8_t vad_sensitivity_table[2 * 8] = {	// real value = talbe * 5
	6, 3,
	7, 4,
	12, 6,
	20, 14,
	54, 48,
	90, 86,
	112, 106,
	140, 130,
};

void force_vad_end(char *str)
{
	if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_PTT_PRESSED)
	{
		vad_tip_counter = 0;
		dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_PTT_PRESSED;
		set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
		dsp_send_cmd(DSP_CMD_SPEECH_IN_OFF);
		if (str)
			vlog_v("config","%s", str);
	}
}

void vad_key_process(void)
{
	uint8_t vad_detect;
	uint16_t int_mic, ext_mic;
	int32_t  mic_diff;

	if (is_speech_vad_enable())
	{
		if ((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_VAD_WAIT_IDLE) == 0)
		{
			if (own_playing_voice_now() == 0)
			{
				if (check_ext_mic_amp)
				{
					ext_mic = dsp_read_ext_mic_amp();
					vad_detect = (ext_mic > check_ext_mic_amp) ? 1 : 0;
					mic_diff = check_ext_mic_amp;	// just for debug printf
				}
				else
				{
					vad_adc_value_check(&int_mic, &ext_mic);
					mic_diff = (int32_t)ext_mic - (int32_t)int_mic;
					vad_detect = ((ext_mic > vad_ext_noise) && (mic_diff > vad_mic_difference)) ? 2 : 0;
				}

				if (vad_detect)
				{
					if ((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_PTT_PRESSED) == 0)
					{
						dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_PTT_PRESSED;
						set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
						dsp_send_cmd(DSP_CMD_SPEECH_IN_ON);
						vlog_v("config","\t%s detect:%d:%d", vad_detect == 1 ? "AMP" : "VAD", ext_mic, mic_diff);
					}
					vad_detect_time = (uint16_t)g_static_ptr->misc_static_config.dsp_vad_trailing_time * 100 / 30;
				}
				else
				{
					if (vad_detect_time)
					{
						vad_detect_time--;
					}
					else
					{
						if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_PTT_PRESSED)
						{
							force_vad_end((char *)0);
							vlog_v("config","\t%s end:%d:%d", vad_detect == 1 ? "AMP" : "VAD", ext_mic, mic_diff);
						}
					}
				}

				if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_PTT_PRESSED)
				{
//					vlog_v("config","\t%d:%d:%d(%d)", ext_mic, int_mic, mic_diff, vad_detect_time);
				}
				else if (check_ext_mic_amp == 0)		// VAD tip
				{
					vad_tip_process();
				}
			}
			else	// be call or forced break and play voice
			{
				dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_VAD_WAIT_IDLE;
				if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_PTT_PRESSED)
				{
					force_vad_end("VAD end when PTT pressed");
				}
			}
		}
		else
		{
			if (phone_is_busy_now() == 0)
				dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_VAD_WAIT_IDLE;
		}
	}
}

#endif	// VAD_KEY_USE_AS_STICK_PTT/VAD_CONTROL_BY_DSP



#define print_10us_offset(pre_str, start_10us)

#define PRINT_10US_OFFSET(check_times, i, syn, timestamp_start)	vlog_v("config","\t\t[%d:%d]%s(%d)", check_times, i, (syn) ? "SYN" : "INV", get_40us_measure_timer_difference(get_timestamp_40us_measure(), timestamp_start));

#ifndef print_10us_offset
void print_10us_offset(char *pre_str, uint32_t start_10us)
{
	vlog_v("config","\t\t%s 10us=%d", pre_str, get_40us_measure_timer_difference(get_timestamp_40us_measure(), start_10us));
}
#endif

uint8_t dsp_is_sync_to_9spps(void)
{
//	if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_GPS_PPS_IS_OK)
//		return 1;
//	else
		return 0;
}

void zzwpro_9s_process(uint8_t is_base)
{
	if (num_of_sync_9s & 0x0f)	// number of heartbeat that received(theoretical value is 50, actual value less 10)
	{
		rssi_sync = rssi_sync_max;
		rssi_sync_max = SIGNAL_LEVEL0;

		if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_REPORT_DOWNLINK_RSSI)
			print_report_downlink_rssi(get_rssi_value_rel(0xfd), num_of_sync_9s & 0x0f);
		num_of_sync_9s = 0;
	}
	else
	{
		if ((num_of_sync_9s >> 4) >= 6)		// 2:18-27s; 6:54-63s
		{
			rssi_sync = SIGNAL_LEVEL0;
			rssi_sync_max = rssi_sync;
			num_of_sync_9s = 0;
		}
		else
		{
			num_of_sync_9s += 0x10;
		}
	}

	if (rssi_rel_base)						// ÿ9s����һ�λ�վ���ǿ���������û�յ���վ�ź���ά��ǰһ�εĳ�ǿ�������㣩
	{
		rssi_rel_base_max = rssi_rel_base;
		rssi_rel_base = 0;
	}
}

void clr_heartbeat_rssi(void)
{
	num_of_sync_9s = 0;
	rssi_sync_max = SIGNAL_LEVEL0;
	rssi_sync = SIGNAL_LEVEL0;
}

void set_nvq_sync_flag(void)
{
	xcs_sync_mask = STACK_SET_FORWARD_FRAME() ? STACK_XCS_MOBILE_BASE_GPS_SYNC : STACK_XCS_HEARTBEAT_SYNC;	// 20220803a: ��վ����GPS���վͬ����������������ͬ��
}

#ifdef DEBUG_REAL_BT
extern uint8_t  test_bt_send_num_of_30ms;
extern uint16_t test_bt_performance_start;
void test_bt_send_one_frame_fixed_data(void)
{
	static uint8_t index = 0;
	uint8_t i, data8[BT_DMA_DATA_DLEN];

	if (uart2_use_as_dataport() && test_bt_performance_start)
	{
		if (test_bt_performance_start != 0xffff)
			test_bt_performance_start--;
		memset(data8, index++, BT_DMA_DATA_DLEN);
		if (index == '-')
			index++;
		for (i = 0; i < test_bt_send_num_of_30ms; i++)
		{
			send_bt_frame_to_host(data8, BT_DMA_DATA_DLEN, 0xf077 + (i << 8), 96, 0);
			vlog_v("config","\t[BT]%05d %d %d", test_bt_performance_start, test_bt_send_num_of_30ms, get_timestamp_measure());
		}
	}
}
#endif

//uint32_t check_dsp_9s_sync_bak = 0;
#ifdef DEBUG_DSP_TRANSCODE
uint16_t dsp_slot_edge_time = 0;
#endif
#ifdef DEBUG_GPS_WINDOW
extern uint16_t gps_time_pluse_1ms;
#endif


#define ZZWPRO_MARKED_SLOT_COUNTER_INC()	if (++zzwpro_marked_slot >= ZZWPRO_TR_STATUS_MAX_SLOT)\
												zzwpro_marked_slot = 0

#define CHECK_AND_ADJUST38P4()				if (reg_mod_gain_dc[2] & 0x8000)\
											{\
												reg_mod_gain_dc[2] &= ~0x8000;\
												dsp_reg_write(REG_MOD_DC_ADDR, (uint16_t *)&reg_mod_gain_dc[2]);\
											}
void check_and_adjust38p4(void)
{
	CHECK_AND_ADJUST38P4();
}

#if defined DEBUG_DSP_INT_OFFSET_OF_SLOT || defined DEBUG_SPI_TX_STRESS_TEST
uint32_t dsp_slot_start_1ms = 0;
#endif

uint32_t RNG_GetRandomNumber(void)
{
	return 12345678;
}

void set_dsp_sync_with_pps(void)
{
	uint8_t pps_state = at32_button_state(GPS_PPS);

	if (((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_PPS_STATE) == 0) && pps_state)	// PPS 0->1 is time pluse
	{
		dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_PPS_BEEN_DETECTED;
#ifdef DEBUG_GPS_WINDOW
		vlog_v("config","\t[%d]PPS present:%d(%d:%d)", get_gps_link_state(), get_measure_timer_difference(get_timestamp_measure(), gps_time_pluse_1ms),
			rmc_data.utc_minute, rmc_data.utc_second);
#endif
		set_base_dsp_sync();
	}
//	else if ((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_PPS_STATE) && (pps_state == 0))
//	{
//		set_base_bkio();
//	}

	if (pps_state)
		dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_PPS_STATE;
	else
		dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_PPS_STATE;
}

#ifdef DEBUG_SPI_TX_STRESS_TEST
void spi_stress_test_sending(uint8_t flag);
#endif
//void dsp_slot_edge_interrupt(uint8_t slot) __attribute((section(".ARM.__at_0x8030000")));
//#pragma arm section code=".ARM.__at_0x8030000"
//#pragma arm section

#ifdef FAKE_MOBILE_BE_CALLED
uint32_t fake_stack_para_aux2 = 0;
uint8_t  fake_speaker_voice = 0, fake_speaker_slot = 0;
void set_fake_stack_para_aux2(uint8_t flag)
{
	if (flag)
	{
		fake_stack_para_aux2 = STACK_VS_CALLING | STACK_VS_CONVENTIONAL | STACK_VS_GROUP_CALL | STACK_VS_PLAY_VOICE;
		fake_speaker_voice = STACK_RETURN_TYPE_VOICE;
	}
	else
	{
		fake_stack_para_aux2 = 0;
		fake_speaker_voice = 0;
	}
}
#endif

void dsp_slot_edge_interrupt(uint8_t slot)
{
	uint32_t stack_ret;
#if defined SUPPORT_EXTEND_FUNCTION || (DEBUG_GPS_SYNC_PPS > 1)
	uint32_t timestamp_1ms = get_timestamp_measure();
#endif
	uint32_t timestamp_10us = get_timestamp_40us_measure();
#ifdef SUPPORT_EXTEND_FUNCTION
	uint32_t time_offset;
#endif
//	uint32_t check_dsp_9s_sync_counter;
	uint8_t  device_is_base = dev_is_base();

	dsp_edge_slot = slot;
#ifdef DEBUG_DSP_TRANSCODE
	dsp_slot_edge_time = get_timestamp_measure();
	vlog_v("config","\r\n%d/%d", dsp_edge_slot, dsp_slot_edge_time);
#endif

#if DEBUG_DSP_INT_OFFSET_OF_SLOT > 1
	stack_ret = get_timestamp_measure();
	vlog_v("config","\r\n-%d/%d-", stack_ret, get_measure_timer_difference(stack_ret, dsp_slot_start_1ms));
	dsp_slot_start_1ms = stack_ret;
#endif

#ifdef DEBUG_SPI_TX_STRESS_TEST
	spi_stress_test_sending(0);
#endif

	if (slot == 0)
		slot_rising_edge_int_times++;
	else
		slot_falling_edge_int_times++;

	dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_INQUIRE_DONE;

	stack_background_tscc_data_proc();


#ifdef PDT_DIRECT_SCAN_CHANNEL
  	if (interphone_mode < INTERPHONE_MODE_PDT_CONV)
  	{
		pdt_direct_scan_chan_rssi_flag |= (get_rssi_value_rel(0) > get_stack_rssi_lower()) ? (1 << dsp_edge_slot) : 0;
		if (dsp_edge_slot)
		{
			if (pdt_direct_scan_chan_rssi_flag == 0)	// 2 solt have not signal
			{
				pdt_direct_scan_chan_offset = (pdt_direct_scan_chan_offset + 1) & 0x01;
//				pdt_direct_scan_chan_offset = pdt_direct_scan_chan_offset ? 0 : 200;
			}
			else
			{
				pdt_direct_scan_chan_rssi_flag &= ~0x03;
			}
		}
  	}
#endif

	if (device_is_base || dev_have_base_feature())
	{
		if (dsp_is_sync_to_9spps())
			stack_para_aux1 = 1;
		else
			stack_para_aux1 = 0;
	}
	else
	{
//		dsp_save_power_rx_on();
		stack_para_aux1 = 0;
	}

	timer9to14_start(TIMER_MISC_INDEX);

	if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
	{
		if (++dsp_save_power_counter9s == DSP_SAVE_POWER_SLOT_COUNTER9S)
			dsp_save_power_counter9s = 0;
		if (dsp_save_power_counter9s == 20)	// 20221014:����Э��ջ����ʱ϶�����²�����ʱ����DSP����ʱ϶��Э��ջ������������ʱ϶�������ﲻ��299
			zzwpro_9s_process(device_is_base);

		if ((gps_minute_second % zzw_gps_poll_periods_time) == 0)
		{
			if (STACK_SET_FORWARD_FRAME())
			{
				if ((zzwpro_marked_slot == (ZZWPRO_TR_STATUS_MAX_SLOT - 1)) && ((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_SET_GPS_WINDOW) == 0))
				{
					dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_SET_GPS_WINDOW;
					stack_para_aux1 |= 2;		// b1��=1��GPS������ʼ��ʵ���ϣ���վ���ܲ���Ҫ���
#ifdef DEBUG_GPS_WINDOW
					vlog_v("config","[%d/%d/%d]START:%d(%d:%d)(%d/%d)", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
						get_measure_timer_difference(get_timestamp_measure(), gps_time_pluse_1ms), rmc_data.utc_minute, rmc_data.utc_second, zzw_gps_poll_periods_time, gps_minute_second);
#endif
				}
			}
			else
			{
				if ((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_SET_GPS_WINDOW) == 0)
				{
					dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_SET_GPS_WINDOW;
					stack_para_aux1 |= g_runtime_inst.runtime_paras.misc_runtime_config.locater_machine ? 3 : 2;// �ǻ�վģʽ�Ķ�λ��������GPS������ʼͬʱ����GPSͬ����־����ʹ��Э��ջ����ʱ϶����Ϊ0�����������豸��ʱ϶0ƫ�����30ms
#ifdef DEBUG_GPS_WINDOW
					vlog_v("config","[%d/%d/%d]START&SYNC:%d(%d:%d)(%d/%d)", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,
						get_measure_timer_difference(get_timestamp_measure(), gps_time_pluse_1ms), rmc_data.utc_minute, rmc_data.utc_second, zzw_gps_poll_periods_time, gps_minute_second);
#endif
				}
			}
		}
		else
		{
			dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_SET_GPS_WINDOW;
		}

		if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_JUMP_TO_GPS_CHANNEL)
		{
			dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_JUMP_TO_GPS_CHANNEL;
			config_pll_paras(interphone_mode, 0, get_current_chan());
		}

		if (device_is_base)
		{
			stack_call_buffer[0] = 0;
			stack_call_buffer[1] = 0;
  #ifndef MAIN_DONOT_PROCESS_VO_MODE
			if (ZZWPRO_BASE_IS_VO_MODE())
				get_exchange_info((uint8_t *)&stack_call_buffer[2]);
  #endif
			clr_dsp_30ms_counter();

  			if (zzw_forward_downcount & 0xf8)
  			{
				zzw_forward_downcount -= 0x08;
				if ((zzw_forward_downcount & 0xf8) == 0)
				{
					zzw_forward_downcount = 0;
					forwarding_forbidden_slot = 0;
				}
  #ifdef DEBUG_FORWARDING_LOGIC
				vlog_v("config","\tfor:%d %d", zzw_forward_downcount & 0x07, zzw_forward_downcount >> 3);
  #endif
  			}
		}
		else // dev_is_base
		{
			memset(stack_call_buffer, 0, 16);
		} // dev_is_base

		stack_ret = CALL_STACK_PROCESS(STACK_CALL_TYPE_SLOT_SYNC);
//		vlog_v("config","mode=%d,ret=%d", interphone_mode, stack_ret);
		if (stack_ret == STACK_RETURN_TYPE_STATE)
		{
			if (interphone_mode != INTERPHONE_MODE_ZZWPRO_Q)	// stack return 0 always at Q mode
			{
#ifdef DEBUG_GPS_WINDOW
				vlog_v("config","[%d/%d/%d]slot save=%d,state=%d", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times, zzwpro_marked_slot, ZZWPRO_FRAME_GET_SLOT(stack_call_buffer));
#endif
				zzwpro_marked_slot = ZZWPRO_FRAME_GET_SLOT(stack_call_buffer);
			}
			zzwpro_xcs = GET_STACK_XCS(stack_call_buffer);

			device_is_sync = zzwpro_xcs & xcs_sync_mask;	// bit6������ͬ����bit5:��̨ͬ����bit4:��վͬ����bit3:GPSͬ��
			if (device_is_base)
			{
				send_exchange_info((uint8_t *)GET_STACK_XCS_OFFSET(stack_call_buffer));
				if ((slot == 0) && (zzwpro_marked_slot & 0x01))									// slot==0��ʾ������(ʱ϶0������վ�������෴��)���ߵ�ƽʱVʱ϶����Ϊ0/2/4�����ж��½�����Ϊ�˼��ٴ�����
				{
  #ifdef DEBUG_INSERT_RECOVERY_30MS
					vlog_v("config","Rising&slot=%d", zzwpro_marked_slot);
  #endif
//					dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_ADJUST_30MS_VOLTAGE;
				}
			}
			else
			{
//				set_zzwpro_nvq_q_chan((uint16_t *)((uint32_t)stack_call_buffer + NVQ_Q_CHAN_OFFSET));
			}

			set_zzwpro_nvq_q_chan((uint16_t *)((uint32_t)stack_call_buffer + NVQ_Q_CHAN_OFFSET));

			if (device_is_sync & STACK_XCS_BASE_GPS_HB_SYNC)
			{
				if ((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_SYNC_WITH_BASE) == 0)
					set_dsp_mode_sync(0);

				if ((device_desync_counter & 0x8000) == 0)		// base/GPS desync->sync
				{
					if ((device_desync_counter > 60) && !(maintain_setup_parameter(PARA_OPERATE_READ, TIP_CONFIG_PARA_POS, 0) & TIP_CONFIG_MASK_PTT_AUTH))
						send_tip_sound(TIP_TYPE_RESYNC_AFTER_TIMEOUT, 5);
					device_desync_counter = 0x8000;
				}
			}
			else
			{
				if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_SYNC_WITH_BASE)
					set_dsp_mode_sync(1);

				if (device_desync_counter & 0x8000)				// base/GPS sync->desync
					device_desync_counter = 0;
			}

			if ((device_is_sync && (pre_device_sync == 0)) || ((device_is_sync == 0) && pre_device_sync))
			{
				if (pre_device_sync == 0)													// desync->sync
				{
					if (delay_to_sync == 0xff)
					{
						delay_to_sync = RNG_GetRandomNumber() % 250 + 4;
						vlog_v("config","\tFirst sync, delay=%d(0x%02x),time=%d", delay_to_sync, device_is_sync, timestamp_1s);
					}
					else
					{
//						vlog_v("config","\tSync, delay=%d(0x%02x),time=%d", delay_to_sync, device_is_sync, timestamp_1s);
						if (delay_to_sync == 0)												// delay 120ms
							goto set_new_sync_state;
						else
							delay_to_sync--;
					}
					device_is_sync = 0;
				}
				else
				{
//					vlog_v("config","\tDesync,time=%d", timestamp_1s);
					if (get_auto_switch_rf_power_enable_zzwpro())
						auto_switch_rf_power(-130);
set_new_sync_state:
					delay_to_sync = 4;
					pre_device_sync = device_is_sync;
				}
			}
		}
		else
		{
			if (interphone_mode != INTERPHONE_MODE_ZZWPRO_Q)
			{
				ZZWPRO_MARKED_SLOT_COUNTER_INC();
#ifdef DEBUG_GPS_WINDOW
				vlog_v("config","[%d/%d/%d]slot inc=%d", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times, zzwpro_marked_slot);
#endif
			}
		}

		if (interphone_mode == INTERPHONE_MODE_ZZWPRO_Q)
		{
			ZZWPRO_MARKED_SLOT_COUNTER_INC();
#ifdef DEBUG_GPS_WINDOW
			vlog_v("config","[%d/%d/%d](Q)slot inc=%d", slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times, zzwpro_marked_slot);
#endif
		}

		stack_return_process(stack_ret);

//		vlog_v("config","\tmarked=%d", zzwpro_marked_slot);
		if (zzwpro_marked_slot == 0)	// it must be present the falling edge(slot=0)
		{
			zzwpro_slot_end_process(ZZWPRO_TR_STATUS_MAX_SLOT);

//			if (device_is_sync)
//			{
//				device_is_sync--;
//				vlog_v("config","Sync--=%d(%d)", device_is_sync, pre_device_sync);
//			}
//			if ((device_is_sync && (pre_device_sync == 0)) || ((device_is_sync == 0) && pre_device_sync))
//			{
//				pre_device_sync = device_is_sync;
//			}
			if (is_vocoder_mix_enable() && dsp_mix_flag)
			{
				dsp_mix_flag = (dsp_mix_flag & ~REG_DSP_CMD_MIX_CMD_MASK) | DSP_CMD_MIX;
				dsp_send_cmd(dsp_mix_flag);
//				vlog_v("config","\t[%d]mix=%04X", slot_middle_int_times, dsp_mix_flag);
				dsp_mix_flag = 0;
			}
		}

#if (defined PDT_CONV_VOICE_AT_QMODE || defined NV_AUTO_TRACE_VOICE_AT_QMODE) && !defined NVQ_SENDING_SWITCH_TX_ONLY
//		if ((auto_trace_nv25k & ZZWPRO_AUTOTRACE_VQ_ENABLE_MASK) && is_diff_tr_mode_at_voice_channel() && ((zzwpro_xcs & STACK_XCS_Q_MODE) == 0))
		if ((auto_trace_nv25k & (ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK | ZZWPRO_AUTOTRACE_VQ_ENABLE_MASK)) && is_diff_tr_mode_at_voice_channel())
		{
			if (nvq_send_q_downcount)
			{
				if (--nvq_send_q_downcount == 0)
					set_trunk_at_voice_channel(0, 0xffff);
			}
		}
#endif
	}
	else
	{
		zzwpro_marked_slot = dsp_edge_slot;	// for power check at middle int(average_power_check_adc(zzwpro_marked_slot);)
		if (dsp_edge_slot == 0)	// it must be present the falling edge(slot=0)
			zzwpro_slot_end_process(2);

		if (device_is_base)
			clr_dsp_30ms_counter();

#ifdef SUPPORT_EXTEND_FUNCTION
		if (time_gap_for_insert_slot_sync)
		{
			time_offset = get_measure_timer_difference(timestamp_1ms, prev_slot_edge_timestamp);
			if ((time_offset > time_gap_for_insert_slot_sync) && (time_offset < 60))
				CALL_STACK_PROCESS(STACK_CALL_TYPE_SLOT_SYNC);
		}
#endif
		conv_report_rssi_handle();

		stack_ret = CALL_STACK_PROCESS(STACK_CALL_TYPE_SLOT_SYNC);
#ifdef FAKE_MOBILE_BE_CALLED
		stack_para_aux2 = fake_stack_para_aux2;
#endif
		if ((interphone_mode <= INTERPHONE_MODE_PDT_CONV) && (stack_ret == STACK_RETURN_TYPE_STATE))
			set_pdt_conv_q_chan((uint16_t *)((uint32_t)stack_call_buffer + NVQ_Q_CHAN_OFFSET));
//		vlog_v("config","mode=%d,ret=%d", interphone_mode, stack_ret);
		stack_return_process(stack_ret);
	}

	speech_pa_ctrl_process();

	if (own_talking_now())		// stack set it at slot_sync
	{
#if defined PDT_CONV_VOICE_AT_QMODE || defined NV_AUTO_TRACE_VOICE_AT_QMODE
		if (auto_trace_nv25k & ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK)
		{
			if (is_diff_tr_mode_at_voice_channel()) // 20210717:should be NV mode when at normal(rx), Q mode at voice(tx)
			{
  #ifdef NVQ_QMODE_SLOT_IS_30MS
				sent_dsp_slot = (sent_dsp_slot + 1) & 0x01;	// Q's slot is 30ms
  #else
				sent_dsp_slot = 0;							// Q's slot is 60ms
  #endif
			}
			else
			{
				sent_dsp_slot = (sent_dsp_slot + 1) & 0x01;	// PDT
			}
		}
		else if (auto_trace_nv25k & ZZWPRO_AUTOTRACE_VQ_ENABLE_MASK)
		{
			if (is_diff_tr_mode_at_voice_channel()) // 20210717:should be NV mode when at normal(rx), Q mode at voice(tx)
			{
  #ifdef NVQ_QMODE_SLOT_IS_30MS
				sent_dsp_slot = (sent_dsp_slot + 1) & 0x01;	// Q's slot is 30ms
  #else
				sent_dsp_slot = 0;							// Q's slot is 60ms
  #endif
			}
			else
			{
				if (++sent_dsp_slot >= 6)					// NV
					sent_dsp_slot = 0;
			}
		}
		else
#endif // PDT_CONV_VOICE_AT_QMODE or NV_AUTO_TRACE_VOICE_AT_QMODE
		{
			if ((interphone_mode == INTERPHONE_MODE_ZZWPRO_V) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_V25K) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_N))
			{
				if (++sent_dsp_slot >= 6)
					sent_dsp_slot = 0;
			}
#ifdef NVQ_QMODE_SLOT_IS_30MS
			else if (interphone_mode == INTERPHONE_MODE_ZZW_ADHOC)
#else
			else if ((interphone_mode == INTERPHONE_MODE_ZZWPRO_Q) || (interphone_mode == INTERPHONE_MODE_ZZW_ADHOC))
#endif
			{
				sent_dsp_slot = 0;
			}
			else
			{
				sent_dsp_slot = (sent_dsp_slot + 1) & 0x01;
			}
		}
	}
	else
	{
		sent_dsp_slot = ((interphone_mode == INTERPHONE_MODE_ZZWPRO_V) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_V25K) || (interphone_mode == INTERPHONE_MODE_ZZWPRO_N)) ? 5 : 1;
	}

	vad_key_process();

	stack_call_sync_to_slot();

	if (own_talking_now() && (is_ambience_listening() == 0))
	{
		led_play(BAR_TYPE_TR_ARROW_UP);
	}
	else if (phone_is_busy_now())
	{
		led_play(BAR_TYPE_TR_ARROW_DOWN);
	}
	else
	{
		led_play(BAR_TYPE_TR_ARROW_CLEAR);
	}

	// use slot time to generate 60ms signal
	if ((interphone_mode == INTERPHONE_MODE_ZZW_ADHOC) || slot)	// zzw: 60ms(rising); other: 30ms(rising & falling)
	{
		if (++slot60ms_counter > SLOT_TIMER_CHECK_TIME / 60)
		{
			slot60ms_counter = 0;
//			slot_timer_flag_check = 1;
//			vlog_v("config","DSP 1200ms");
			send_status_to_bt(2, 0);	// only mobile
		}
	}

#ifdef DEBUG_REAL_BT
	test_bt_send_one_frame_fixed_data();
#endif

	CHECK_AND_ADJUST38P4();
    // return;
	if (stack_tx_slot_downcounter)
		stack_tx_slot_downcounter--;

//	vlog_v("config","%d.e.end", timestamp_1ms);
	print_10us_offset("Edge", timestamp_10us);

#ifdef SUPPORT_EXTEND_FUNCTION
	prev_slot_edge_timestamp = timestamp_1ms;
#endif
}

uint8_t is_pdt_voice_with_sync(uint32_t aux2, uint32_t buff)
{
	return (((aux2 & ZZWPRO_STACK_VOICE_TYPE_ZZW) == 0) && (sync_is_base_signal_format((uint8_t *)buff) == 0)) ? 1 : 0; // 1: voice
}

uint8_t is_pdt_voice(uint32_t aux2, uint32_t buff)	// 20230216: PDT��Ƶ�ǰ������־��������Ƶ/����֡��B15��־����Ƶ��������ģ�0E/0F����Ƶ��������������
{
	uint32_t pdt_sig_flag_addr_at_can_frame = buff + 3;

	return (((aux2 & ZZWPRO_STACK_VOICE_TYPE_ZZW) == 0) && PDT_FRAME_IS_VOICE(pdt_sig_flag_addr_at_can_frame)) ? 1 : 0;		// 1: voice
}

uint32_t is_base_at_wireless_mode(void)
{
	return dsp_runtime_misc_flag & DSP_RUNTIME_MISC_CCM_VOCODER_NOFEC;
}

void set_ccm_vocoder_fec_flag(uint16_t flag)	// 1-������·(��fec����������ѡ�߼�)��0-������·(��fec���ҽ�����ѡ�߼�)
{
	if (flag == 0)
		dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_CCM_VOCODER_NOFEC;
	else
		dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_CCM_VOCODER_NOFEC;
//	vlog_v("config","\t->%s", (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_CCM_VOCODER_NOFEC) ? "wireless" : "wire");
}

static uint8_t transcode_n2p_sync[2][7], transcode_n2p_cach[2][3];
void dsp_n2p_send_vocoder_to_dsp(uint8_t slot, uint32_t buff)
{
	uint16_t trans_vocoder_data[MAX(REG_READ_REG_VOCODER_DATA_OUT_LEN, REG_WRITE_REG_VOCODER_DATA_IN_LEN)];

	format_ccm_to_nofec_voice_signaling((uint8_t *)buff);
	signaling_to_vocoder((uint8_t *)trans_vocoder_data, (uint8_t *)buff, transcode_n2p_sync[slot >> 1], transcode_n2p_cach[slot >> 1], 3);
	ntohs_mul(trans_vocoder_data, REG_WRITE_REG_VOCODER_TRANSCODE_IN_0_LEN); // use 10B(5words) really
	dsp_reg_write(REG_DSP64_REG_TRANSCODE_IN_0_ADDR + slot, trans_vocoder_data);
	dsp_send_cmd(DSP_CMD_TRANSCODE | (slot << REG_DSP_CMD_TRANSCODE_CHOICE_SHIFT));
}

#ifdef NVQ_UNIFY_JUMP_FREQ
  #define GET_NVQ_JUMP_FREQ()	(stack_para_aux2 >> 16)/*(float)freq*80*/
#else
  #define GET_NVQ_JUMP_FREQ()	ZZWPRO_FRAME_GET_SLOT(&stack_para_aux2)/*get freq's index*/
#endif
#define CONV_IS_STACK_FORCE_THROUTOUT()	(stack_para_aux2 & 0x00000100)

void send_data_format_switch(void)
{
#ifndef NVQ_SENDING_SWITCH_TX_ONLY
	if (auto_trace_nv25k & (ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK | ZZWPRO_AUTOTRACE_VQ_ENABLE_MASK))
	{
		if (ZZWPRO_FRAME_IS_CUSTOM_CMD(stack_call_buffer) && ((auto_trace_nv25k & ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK) == 0))
			set_trunk_at_voice_channel(0, 0xffff);
		else
			set_trunk_at_voice_channel(ZZWPRO_FRAME_IS_Q_FRAME(stack_call_buffer, (uint8_t)stack_para_aux2) ? 1 : 0, GET_NVQ_JUMP_FREQ());
	}
#else
	if (auto_trace_nv25k & (ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK | ZZWPRO_AUTOTRACE_VQ_ENABLE_MASK))
	{
		if (ZZWPRO_FRAME_IS_CUSTOM_CMD(stack_call_buffer) && ((auto_trace_nv25k & ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK) == 0))
			nvq_switch_only_set_tx_mode_and_freq(0, 0xffff);
		else
			nvq_switch_only_set_tx_mode_and_freq(ZZWPRO_FRAME_IS_Q_FRAME(stack_call_buffer, (uint8_t)stack_para_aux2) ? 1 : 0, GET_NVQ_JUMP_FREQ());
	}
#endif
}

void send_data_sync_to_slot_middle(uint8_t type, uint32_t aux2)		// type(only use by zzw): 0-frame_voice; 1-frame_info; 2-frame_rc(interrupt-call); 3-frame_rc_end(interrupt-call)
{
//#if defined(__cplusplus) || !defined(__STRICT_ANSI__)
//#elif !defined(__size_t)
//#endif
	uint8_t tmp_slot, device_is_base = dev_is_base(), is_forward = 1;
	uint16_t tmp_data[REG_WRITE_REG_PACKET_IN_ADHOC_LEN];
	uint32_t rssi_can;

	switch (interphone_mode)
	{
		case INTERPHONE_MODE_MPT_TRUNKING:
			ntohs_mul((uint16_t *)stack_call_buffer, REG_ANALOG_MSK_DATA_OUT_LENGTH);
			dsp_reg_write(REG_ANALOG_MSK_DATA_IN_ADDR, (uint16_t *)stack_call_buffer);
			set_dsp_analog_mode(REG_ANALOG_MODE_AUDIO_MASK, 4 << REG_ANALOG_MODE_AUDIO_SHIFT);	// mpt signaling(MSK)
			break;

		case INTERPHONE_MODE_ZZWPRO_Q:
		case INTERPHONE_MODE_ZZWPRO_N:
		case INTERPHONE_MODE_ZZWPRO_V:
		case INTERPHONE_MODE_ZZWPRO_V25K:
			is_forward = ZZWPRO_FRAME_IS_VOICE(stack_call_buffer) ? 1 : 0;	// VXS.7-4 == 14: voice
  #ifndef MAIN_DONOT_PROCESS_VO_MODE
			if (device_is_base && ((stack_para_aux2 & ZZWPRO_STACK_SIG_PDN_TYPE_CAN) || ZZWPRO_BASE_IS_VO_LOW()))	// pdn or VO.low
  #else
			if (device_is_base && (stack_para_aux2 & ZZWPRO_STACK_SIG_PDN_TYPE_CAN))	// pdn: to CAN
  #endif
			{
				rssi_can = get_rssi_value_rel(0);		// RSSI, 25
				stack_para_aux2 &= 0x7f;				// cmdx
				send_signaling_to_can(&rssi_can, &stack_para_aux2, (uint32_t)stack_call_buffer);
			}
			else // dev_is_base
			{
#ifdef NV_AUTO_TRACE_VOICE_AT_QMODE
				send_data_format_switch();
				if (is_diff_tr_mode_at_voice_channel())
				{
  #if defined NV_AUTO_TRACE_VOICE_AT_QMODE && !defined NVQ_SENDING_SWITCH_TX_ONLY
					nvq_send_q_downcount = 4;
  #endif
					goto zzwpro_q_gen_frame;
				}
#endif
				tmp_slot = GET_SAVE_TX_STATUS_SLOT(interphone_mode);
				zzwpro_status[tmp_slot].sta.tx = ZZWPRO_STATUS_BUSY;
				zzwpro_status[tmp_slot].sta.tx_slot = ZZWPRO_FRAME_GET_SLOT(stack_call_buffer);
				zzwpro_send_slot = zzwpro_status[tmp_slot].sta.tx_slot;

				if (aux2 & ZZWPRO_STACK_JUMP_TO_SEND_GPS)				// send GPS at special channel
				{
					dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_JUMP_TO_GPS_CHANNEL;
					config_pll_paras(interphone_mode, 0, get_current_chan() + 10);
				}

#ifdef NV_AUTO_TRACE_VOICE_AT_QMODE
zzwpro_q_gen_frame:
#endif
				zzwpro_send_slot = 0;	// Q has only one slot; if not set here, the screen will not update the tx slot
				memcpy(tmp_data, (uint8_t *)stack_call_buffer + SIGNALING_XV_STACK_EMBEDDED, SIGNALING_XV_Q_MAX_LENGTH);	//Q2400/600 have 2frames at one slot
				memcpy((uint8_t *)stack_call_buffer + 3, tmp_data, SIGNALING_XV_Q_MAX_LENGTH);
#ifdef PDT_CONV_VOICE_AT_QMODE
zzwpro_send_frame_to_air:
#endif
				print_simple_data_flow("zzw_toAir");
				packet_signaling_to_mcu_inf_frame((uint8_t *)stack_call_buffer);
			}// dev_is_base
			break;

		default:
			if ((interphone_mode < INTERPHONE_MODE_PDT_TRUNKING) && ((stack_para_aux2 & ZZWPRO_STACK_SIG_PDN_TYPE_CAN) == 0) && dev_is_use_xv_stack())
			{
				if (conv_tx_freq_index != (GET_NVQ_JUMP_FREQ() & 0x0fff))
				{
					conv_tx_freq_index = GET_NVQ_JUMP_FREQ() & 0x0fff;
					set_dev_force_throughout(CONV_IS_STACK_FORCE_THROUTOUT() ? 1 : 0);
					unify_nvq_jump_freq(conv_tx_freq_index);
				}
			}

#ifdef PDT_CONV_VOICE_AT_QMODE
			send_data_format_switch();
			if (is_diff_tr_mode_at_voice_channel())
			{
				tmp_slot = GET_SAVE_TX_STATUS_SLOT(INTERPHONE_MODE_ZZWPRO_Q);
				zzwpro_status[tmp_slot].sta.tx = ZZWPRO_STATUS_BUSY;
				zzwpro_status[tmp_slot].sta.tx_slot = tmp_slot;
				goto zzwpro_send_frame_to_air;
			}
#endif

			tmp_slot = GET_SAVE_TX_STATUS_SLOT(interphone_mode);
			zzwpro_status[tmp_slot].sta.tx = ZZWPRO_STATUS_BUSY;
			zzwpro_status[tmp_slot].sta.tx_slot = tmp_slot;
			print_air_data("pdt_toAir", (uint8_t *)stack_call_buffer);
#ifdef DEBUG_TX_SEQ_TIME
			vlog_v("config","toDSP:%d", GET_SLOT_TO_CURR_TIME());
#endif
			packet_signaling_to_mcu_inf_frame((uint8_t *)stack_call_buffer);
			break;
	}

	if ((interphone_mode <= INTERPHONE_MODE_ZZW_ADHOC) || is_forward)
		stack_tx_slot_downcounter = 100;
}

void send_vocoder_to_speaker_at_slot_middle(void)
{
	if (interphone_mode <= INTERPHONE_MODE_ZZW_ADHOC)
	{
#ifdef DEBUG_RCV_RSSI_IS_255
		vlog_v("config","\tvslot=%d/%d:%d %d", dsp_edge_slot, zzwpro_marked_slot, zzwpro_status[0].rssi, zzwpro_status[1].rssi);
#endif
		if (replace_voice_flag == 0)
		{
			signaling_to_vocoder((uint8_t *)vocoder_data, (uint8_t *)stack_call_buffer, 0, 0, 1);
			if (sd_voice_decrypt((uint8_t *)vocoder_data, ((uint8_t *)stack_call_buffer)[2]) == 0)
			{
send_real_vocoder_to_dsp:
				save_spk_codeword((uint8_t *)vocoder_data);
			}
		}
		else
		{
			test_vocoder_to_vocoder(vocoder_data);
			goto send_real_vocoder_to_dsp;
		}
	}
	else
	{
		replace_voice_flag = ZZWPRO_FRAME_GET_SLOT(stack_call_buffer);
		rssi_voice = zzwpro_status[replace_voice_flag].rssi;
#ifdef DEBUG_RCV_RSSI_IS_255
		vlog_v("config","\tvslot=%d/%d", replace_voice_flag, rssi_voice);
#endif
		signaling_to_vocoder_xv((uint8_t *)vocoder_data, stack_call_buffer, vocoder_data_to_dsp_length, zzwpro_vocoder_one_frame_to_dsp);	// ����Ҫ�޸Ĳ�����ӦP1
		goto send_real_vocoder_to_dsp;
	}

	print_air_data("toSpk", (uint8_t *)vocoder_data);
}

// ts_10236101v010405p.pdf, page 84
// ms SYNC voice: 7f 7d 5d d5 7d fd
//         data : d5 d7 f7 7f d7 57
//         RC   : 77 d5 5f 7d fd 77

uint8_t is_poc_open_voice_pa(void)
{
	return dsp_speech_pa_ctrl & SPEECH_PA_CTRL_OUT_POC;
}

uint8_t is_poc_open_mic_pa(void)
{
	return dsp_speech_pa_ctrl & SPEECH_PA_CTRL_IN_POC;
}

void dsp_sout_int_handle(void)
{
	if ((g_gui_interactive->dev_misc_notify.poc_mode == 0) || (poc_get_alaw_to_play() == 0))
	{
		if (dsp_speech_pa_ctrl & SPEECH_PA_CTRL_OUT_POC)	// ��ʾUI����POC����ģʽ
		{
//			vlog_v("config","[POCvoc] stop tip:%02X", dsp_speech_pa_ctrl);
			stop_send_tip_sound();
		}
		else
		{
			if (g_talk_tip_sound == 0)
			{
				dsp_speech_out_pa_ctrl(0, SPEECH_PA_CTRL_OUT_TIP);
			}
			else
			{
				packet_spk_voice_to_mcu_inf_frame(SET_SPEAKER_TIP_VOLUME, (int16_t *)(g_talk_tip_address + g_talk_tip_count * SELP_FRAME_SIZE * sizeof(int16_t)));
                // vlog_v("dsp config","%s %d",__FUNCTION__,__LINE__);
//#if defined DEBUG_TX_SEQ_TIME || defined DEBUG_RX_SEQ_TIME
//				vlog_v("config","tip%d(%d):%d-%d %d", g_talk_tip_sound, g_talk_tip_times, g_talk_tip_count_total, g_talk_tip_count,
//					get_measure_timer_difference(get_timestamp_measure(), dsp_slot_start_1ms));
//#endif

//				packet_spk_voice_to_mcu_inf_frame(SET_SPEAKER_TIP_VOLUME, (int16_t *)(g_talk_tip_address + g_talk_tip_count * SELP_FRAME_SIZE * sizeof(int16_t)));
//				packet_spk_voice_to_mcu_inf_frame(SET_SPEAKER_TIP_VOLUME, (int16_t *)(g_talk_tip_address + g_talk_tip_count * SELP_FRAME_SIZE * sizeof(int16_t)));
				if (++g_talk_tip_count >= g_talk_tip_count_total)
					send_tip_sound(0, 0);
			}
		}
	}

	poc_send_mic_voice();
    // vlog_v("dsp config","%s %d",__FUNCTION__,__LINE__);
}

static uint8_t transcode_p2n_sync[2][7], transcode_p2n_cach[2][3];
void dsp_p2n_send_vocoder_to_dsp(uint8_t slot, uint32_t buff)		// slot: 0 or 2
{
	uint16_t trans_vocoder_data[MAX(REG_READ_REG_VOCODER_DATA_OUT_LEN, REG_WRITE_REG_VOCODER_DATA_IN_LEN)];

	signaling_to_vocoder((uint8_t *)trans_vocoder_data, (uint8_t *)buff, transcode_p2n_sync[slot >> 1], transcode_p2n_cach[slot >> 1], 1);
	ntohs_mul(trans_vocoder_data, REG_WRITE_REG_VOCODER_TRANSCODE_IN_0_LEN);			// e.g. ambe2400(len=15,60ms,9B*3)->vtec1200(len=5,60ms,9B)
	dsp_reg_write(REG_DSP64_REG_TRANSCODE_IN_0_ADDR + slot, trans_vocoder_data);
	dsp_send_cmd(DSP_CMD_TRANSCODE | (slot << REG_DSP_CMD_TRANSCODE_CHOICE_SHIFT));
}

static uint32_t trans_p2n_aux1[2], trans_p2n_aux2[2];
uint8_t p2n_trans_process(uint32_t *aux1, uint32_t *aux2, uint32_t buff)				// return: 0-send nothing to can; 1-send pdt frame to can immediately(call at STACK_CALL_TYPE_AIR_DATA)
{
	uint8_t save_slot, data_slot;

	if (trans_vocoder_type.vocoder_trans_enable)
	{
		save_slot = dsp_edge_slot ? 0 : 2;
		data_slot = save_slot >> 1;
		if ((dsp_demod_state[0] & REG_DEMOD_STATE_TYPE_MASK) == 1)
		{
#ifdef DEBUG_DSP_TRANSCODE
			vlog_v("config","P2N:Voc2DSP_%d/%d", data_slot, get_measure_timer_difference(get_timestamp_measure(), dsp_slot_edge_time));
#endif
			trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_vocoder_cnt0 = (((dsp_demod_state[0] & REG_DEMOD_STATE_CNT_MASK) >> REG_DEMOD_STATE_CNT_SHIFT) == 0) ? 0 : 1;
			dsp_p2n_send_vocoder_to_dsp(save_slot, buff);
//			memcpy(trans_vocoder_type.pdt_vocoder[data_slot], stack_call_buffer, SIGNALING_MAX_LENGTH);
//			trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_vocoder_ok = 1;
		}
		else
		{
#ifdef DEBUG_DSP_TRANSCODE
			vlog_v("config","P2N:savSig_%d/%d", data_slot, get_measure_timer_difference(get_timestamp_measure(), dsp_slot_edge_time));
#endif
			memcpy(trans_vocoder_type.pdt_signal[data_slot], stack_call_buffer, SIGNALING_MAX_LENGTH);
			trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_signal_ok = 1;
		}
		trans_p2n_aux1[data_slot] = *aux1;
		trans_p2n_aux2[data_slot] = *aux2;
		return 0;
	}
	else
	{
		return 1;
	}
}

uint8_t p2n_trans_trigger(uint32_t *aux1, uint32_t *aux2, uint32_t buff)				// return: 0-send nothing to can; 1-send pdt frame to can now(call at STACK_CALL_TYPE_INQUIRY)
{
	uint8_t save_slot, data_slot;

	if (trans_vocoder_type.vocoder_trans_enable)
	{
		save_slot = dsp_edge_slot ? 2 : 0;	// get another slot's data(sig or voc all must delay 30ms)
		data_slot = save_slot >> 1;
		if (trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_signal_ok)
		{
#ifdef DEBUG_DSP_TRANSCODE
			vlog_v("config","P2N:Sig2CAN_%d(Get=%d)/%d", dsp_edge_slot ? 0 : 1, data_slot, get_measure_timer_difference(get_timestamp_measure(), dsp_slot_edge_time));
#endif
			trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_signal_ok = 0;
			memcpy((void *)buff, trans_vocoder_type.pdt_signal[data_slot], SIGNALING_MAX_LENGTH);
p2n_trans_return_frame_to_can:
			*aux1 = trans_p2n_aux1[data_slot];
			*aux2 = trans_p2n_aux2[data_slot];		// cmdx
			return 1;
		}
		else if (trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_vocoder_ok)
		{
#ifdef DEBUG_DSP_TRANSCODE
			vlog_v("config","P2N:Voc2CAN_%d(Get=%d)/%d", dsp_edge_slot ? 0 : 1, data_slot, get_measure_timer_difference(get_timestamp_measure(), dsp_slot_edge_time));
#endif
			trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_vocoder_ok = 0;
			memcpy((void *)buff, trans_vocoder_type.pdt_vocoder[data_slot], SIGNALING_MAX_LENGTH);
			// ����ת�����dsp_middle_int�в�����뽫stack_para_aux2��PDT_FRAME_IS_NO_FEC_MASK����䣬������STACK_CALL_TYPE_AIR_DATA������aux2Ҳ������ZZWPRO_STACK_VOICE_TYPE_PDT�����Ա����ڴ�ǿ����λ
			trans_p2n_aux2[data_slot] |= ZZWPRO_STACK_VOICE_TYPE_PDT_NOFEC;	// ����1.2K��CMDX.bit3=1��
			goto p2n_trans_return_frame_to_can;
		}
#ifdef DEBUG_DSP_TRANSCODE
		else
		{
			vlog_v("config","P2N:Notrg_%d(Get=%d)/%d", dsp_edge_slot ? 0 : 1, data_slot, get_measure_timer_difference(get_timestamp_measure(), dsp_slot_edge_time));
		}
#endif
	}

	return 0;
}

static uint32_t trans_n2p_aux1[2], trans_n2p_aux2[2];
uint8_t n2p_trans_process(uint32_t *aux1, uint32_t *aux2, uint32_t buff)
{
	uint8_t save_slot, data_slot;

	if (trans_vocoder_type.vocoder_trans_enable)
	{
		save_slot = dsp_edge_slot ? 1 : 3;
		data_slot = save_slot >> 1;
		if (is_pdt_voice(*aux2, buff))
		{
#ifdef DEBUG_DSP_TRANSCODE
			vlog_v("config","N2P:Voc2DSP_%d/%d", data_slot, get_measure_timer_difference(get_timestamp_measure(), dsp_slot_edge_time));
#endif
			dsp_n2p_send_vocoder_to_dsp(save_slot, (uint32_t)stack_call_buffer);
//			memcpy(trans_vocoder_type.pdt_can_vocoder[data_slot], stack_call_buffer, SIGNALING_MAX_LENGTH);
//			trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_can_vocoder_ok = 1;
		}
		else
		{
#ifdef DEBUG_DSP_TRANSCODE
			vlog_v("config","N2P:savSig_%d/%d", data_slot, get_measure_timer_difference(get_timestamp_measure(), dsp_slot_edge_time));
#endif
			memcpy(trans_vocoder_type.pdt_can_signal[data_slot], stack_call_buffer, SIGNALING_MAX_LENGTH);
			trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_can_signal_ok = 1;
		}
		trans_n2p_aux1[data_slot] = *aux1;
		trans_n2p_aux2[data_slot] = *aux2;
		return 0;
	}
	else
	{
		return 1;
	}
}

uint8_t n2p_trans_trigger(uint32_t *aux1, uint32_t *aux2, uint32_t buff)				// return: 0-send nothing to air; 1-send pdt frame to air now(call at STACK_CALL_TYPE_INQUIRY)
{
	uint8_t save_slot, data_slot;

	if (trans_vocoder_type.vocoder_trans_enable)
	{
		save_slot = dsp_edge_slot ? 3 : 1;
		data_slot = save_slot >> 1;
		if (trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_can_signal_ok)
		{
#ifdef DEBUG_DSP_TRANSCODE
			vlog_v("config","N2P:Sig2AIR_%d(Get=%d)/%d", dsp_edge_slot ? 0 : 1, data_slot, get_measure_timer_difference(get_timestamp_measure(), dsp_slot_edge_time));
#endif
			trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_can_signal_ok = 0;
			memcpy((void *)buff, trans_vocoder_type.pdt_can_signal[data_slot], SIGNALING_MAX_LENGTH);
n2p_trans_return_frame_to_air:
			*aux1 = trans_n2p_aux1[data_slot];
			*aux2 = trans_n2p_aux2[data_slot];		// cmdx
			return 1;
		}
		else if (trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_can_vocoder_ok)
		{
#ifdef DEBUG_DSP_TRANSCODE
			vlog_v("config","N2P:Voc2AIR_%d(Get=%d)/%d", dsp_edge_slot ? 0 : 1, data_slot, get_measure_timer_difference(get_timestamp_measure(), dsp_slot_edge_time));
#endif
			trans_vocoder_type.vocoder_trans_ok[save_slot].pdt_can_vocoder_ok = 0;
			memcpy((void *)buff, trans_vocoder_type.pdt_can_vocoder[data_slot], SIGNALING_MAX_LENGTH);
			goto n2p_trans_return_frame_to_air;
		}
#ifdef DEBUG_DSP_TRANSCODE
		else
		{
			vlog_v("config","N2P:Notrg_%d(Get=%d)/%d", dsp_edge_slot ? 0 : 1, data_slot, get_measure_timer_difference(get_timestamp_measure(), dsp_slot_edge_time));
		}
#endif
	}

	return 0;
}


uint8_t device_is_sync_now(void)
{
	uint16_t mod_stat;

	dsp_reg_read(REG_MOD_STATE_ADDR, &mod_stat);
	return (mod_stat & REG_MOD_STATE_SYNC_MASK) ? 1 : 0;	// �Ƿ��������Ƶ�ź�(������̨)
}

uint8_t device_is_rc_end(void)
{
	uint16_t mod_stat;

	dsp_reg_read(REG_MOD_STATE_ADDR, &mod_stat);
	return (mod_stat & REG_MOD_STATE_RC_MASK) ? 0 : 1;
}

void preset_return_can_frame(uint8_t *can_frame, uint8_t style);
void save_bt_voice_frame(uint8_t *data, uint8_t num, uint8_t *can_frame)		// num: can-40,uart-80
{
//	alaw_to_linear();
//	linear_to_alaw();
	save_mic_voice((void *)data, num);
	if (dev_is_base() && can_frame)
		preset_return_can_frame(can_frame, 0);
}

void save_bt_vocoder_frame(uint8_t *data, uint8_t index, uint8_t num, uint8_t *can_frame)	// index: 0-first frame
{
	// P1�����ṩ����빦�ܣ�����������ȡ����
}

void zzwpro_base_inquire_data_to_can(void)
{
//	uint32_t inq_ret;

	if ((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_INQUIRE_DONE) == 0)
	{
		dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_INQUIRE_DONE;
		stack_para_aux1 = 1;	// cws: 0-wireless; 1-wired(can)
/*		inq_ret = CALL_STACK_PROCESS(STACK_CALL_TYPE_INQUIRY);					// data of slot 1/2
		if (inq_ret == STACK_RETURN_TYPE_SIGNALING)
		{
			stack_return_process(inq_ret);
		}
		else if ((interphone_mode > INTERPHONE_MODE_ZZWPRO_Q) && (ZZWPRO_BASE_IS_VPLUS_MODE() == 0))
		{
			stack_para_aux1 = 1;	// cws: 0-wireless; 1-wired(can)
			stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_INQUIRY)); 	// inquire again for slot 3/4
		}
*/
		stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_INQUIRY));
//		if ((interphone_mode > INTERPHONE_MODE_ZZWPRO_Q) && (ZZWPRO_BASE_IS_VPLUS_MODE() == 0))	// 20220408:ȥ�����β�ѯ����
//		{
//			stack_para_aux1 = 1;	// cws: 0-wireless; 1-wired(can)
//			stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_INQUIRY));
//		}
	}
}

void process_sync_signaling(uint8_t sync_rssi)
{
	if ((num_of_sync_9s & 0x0f) < 0x0f)
		num_of_sync_9s++;
	if (rssi_sync_max < rssi_value)
		rssi_sync_max = rssi_value;
	if (rssi_sync < SIGNAL_LEVEL1)
	{
		rssi_sync = rssi_value;
#ifdef NV_TRACE_MULTI_HEARTBEAT
		vlog_v("config","Syn:%d %02x,time=%d", CAL_RSSI_VALUE_REAL(rssi_value), num_of_sync_9s, timestamp_1s);
#endif
	}
#ifdef NV_TRACE_MULTI_HEARTBEAT
	else
	{
		vlog_v("config","Syn:%d(%d) %02x", CAL_RSSI_VALUE_REAL(rssi_value), rssi_sync, num_of_sync_9s);
	}
#endif

	if (get_auto_switch_rf_power_enable_zzwpro())
		auto_switch_rf_power(CAL_RSSI_VALUE_REAL(rssi_value));
}

void dsp_slot_middle_interrupt_proc_talking(void)
{
#ifdef READ_CODEWORD_DELAY_BY_LC_NUM
	if (delay_to_read_vocoder == 0)
#endif
	{
		if (sent_dsp_slot == 0)
		{
			// 9 voice frames(one frame is 20ms) at zzw_v(send it at one slot(30ms)); else is 3 voice frames(send it at one slot(30ms) also)
			if (interphone_mode <= INTERPHONE_MODE_ZZW_ADHOC)
			{
#if defined PDT_CONV_VOICE_AT_QMODE || defined NV_AUTO_TRACE_VOICE_AT_QMODE
				if ((auto_trace_nv25k & ZZWPRO_AUTOTRACE_PQ_ENABLE_MASK) && is_diff_tr_mode_at_voice_channel())
				{
					goto zzw_get_voice_now;
				}
				else
#endif
				{
					if (send_inner_vocoder == 0)
					{
						get_mic_encode_data((uint8_t *)vocoder_data);
					}
					else if (send_inner_vocoder == 3)
					{
						test_vocoder_to_vocoder(vocoder_data);
					}
					else
					{
						o153_to_vocoder(vocoder_data);
					}
					print_air_data("pdt_mic", (uint8_t *)vocoder_data);
					sd_voice_encrypt((uint8_t *)vocoder_data);
					vocoder_to_signaling((uint8_t *)vocoder_data, (uint8_t *)stack_call_buffer, 0, 0, 1);// 3B + 33B
				}
			}
			else
			{
#if defined PDT_CONV_VOICE_AT_QMODE || defined NV_AUTO_TRACE_VOICE_AT_QMODE
zzw_get_voice_now:
#endif
#ifdef DEBUG_VOICE_SEQUENCE
				vlog_v("config","[%d]VOC out", slot_rising_edge_int_times);
#endif
				xv_voice_to_vocoder();
				print_air_data("zzw_mic", (uint8_t *)vocoder_data);
				vocoder_to_signaling_xv((uint8_t *)vocoder_data, stack_call_buffer, vocoder_data_fr_dsp_length, zzwpro_vocoder_one_frame_fr_dsp);
//				vlog_v("config","MIC=%04X %04X %04X %04X %04X %04X %04X %04X %04X %04X %04X %04X %04X %04X %04X",
//						vocoder_data[0], vocoder_data[1], vocoder_data[2], vocoder_data[3], vocoder_data[4], vocoder_data[5], vocoder_data[6], vocoder_data[7], vocoder_data[8],
//						vocoder_data[9], vocoder_data[10], vocoder_data[11], vocoder_data[12], vocoder_data[13], vocoder_data[14]);
			}

			stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_MIC_VOICE));
		}
	}
#ifdef READ_CODEWORD_DELAY_BY_LC_NUM
	else
	{
		delay_to_read_vocoder--;
	}
#endif
}

uint32_t stack_call_interface_conv_base_use_stack(uint32_t type, uint32_t *aux1, uint32_t *aux2, uint32_t buff);
void pdt_unify_receive_data_process(uint8_t device_is_base, uint8_t is_ex)
{
	uint8_t type = GET_SAVE_RX_STATUS_SLOT(interphone_mode);
	ZZWPRO_STATUS_STRUCT *ptr_slot_status;

	stack_para_aux2 = 0;					// STACK_EX_MODULE_MASK bit1: first/second receiver
	ptr_slot_status = zzwpro_status + type;	// zzwpro_status_ex + type

	ptr_slot_status->sta.rx = ZZWPRO_STATUS_BUSY;
	ptr_slot_status->sta.rx_slot = type;
	ptr_slot_status->rssi = get_rssi_value_rel(0);	// get_rssi_value_rel_ex(0)
	ptr_slot_status->dqi = dqi_prev_slot;

//	if (is_ex != 3)							// == 3: null signaling, save noise rssi only
	{
		if (own_playing_voice_now())		// pdt���治ȷ�����������ĸ�ʱ϶��ֻ�ܽ����������Ǹ�ʱ϶��������ʱ϶��ֻ��һ��ʱ϶��û���⣬����ʱ϶���յ���������ܲ���ȷ��
			rssi_voice = ptr_slot_status->rssi;

#ifdef DEBUG_RCV_RSSI_IS_255
		vlog_v("config","\t[%d %d->%d]%02x:%d %d %d %d(%d)", interphone_mode, zzwpro_marked_slot, type, *((uint8_t *)stack_call_buffer),
			zzwpro_status[type].sta.rx, zzwpro_status[type].sta.rx_slot, zzwpro_status[type].rssi, zzwpro_status[type].dqi, rssi_voice);
#endif

		/* 20220528: ����PDTֱͨ�м̹����Ѿ���Э��ջʵ�֣�������Ҫʵ��˫���չ��ܣ����ơ������������ʱR2λ���壺
				b0=0��ʱ϶1��Ƶ���=1��ʱ϶2��Ƶ���b1=0������1��Ƶ���=1������2��Ƶ���b3=0����FEC��Ƶ���=1�������������Ƶ��
		*/
		stack_para_aux1 = ptr_slot_status->rssi | ((uint16_t)ptr_slot_status->dqi << 8);	// RSSI, 25
		if (g_runtime_inst_xvbase->misc_config_base.pdt_reverse_slot)
			stack_para_aux2 |= dsp_edge_slot ? 1 : 0;
		else
			stack_para_aux2 |= dsp_edge_slot ? 0 : 1;

		print_air_data("pdt_frAir", (uint8_t *)stack_call_buffer);
		num_of_frame_received++;

		print_simple_data_flow("pdt_frAir");
#if DEBUG_DSP_SLOT_RSSI == 1
		vlog_v("config","[%d]%d/%d/%d", ptr_slot_status->sta.rx_slot, ptr_slot_status->sta.rx, ptr_slot_status->rssi, ptr_slot_status->dqi);
		type = (type + 1) & 0x01;
		vlog_v("config","[%d]%d/%d/%d", type, ptr_slot_status->sta.rx, ptr_slot_status->rssi, ptr_slot_status->dqi);
#endif

		stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_AIR_DATA));
	}
}

uint8_t get_zzwpro_frame_statistic(uint8_t flag, uint32_t *data)
{
	uint8_t i;

	if (flag == 0x4d)							// fail frame
	{
		for (i = 0; i < 6; i++)
			data[i] = zzwpro_frame[i].rx_fail;
		i *= sizeof(uint32_t);
	}
	else if (flag == 0x4e)						// total frames
	{
		for (i = 0; i < 6; i++)
			data[i] = zzwpro_frame[i].rx_total;
		i *= sizeof(uint32_t);
	}
	else if ((flag >= 0x40) && (flag <= 0x45))	// one slot frame
	{
		i = flag - 0x40;
		data[0] = zzwpro_frame[i].rx_total;
		data[1] = zzwpro_frame[i].rx_fail;
		i = 8;
	}
	else if (flag == 0x4f)						// clear
	{
		memset(zzwpro_frame, 0, sizeof(ZZWPRO_FRAME_STATISTIC_TYPEDEF) * ZZWPRO_TR_STATUS_MAX_SLOT);
		i = 0;
	}
	else
	{
		i = 0xff;
	}

	return i;
}


uint16_t switch_dsp_vocoder(uint8_t work_mode, uint8_t old_vocoder_speed, uint8_t q_vocoder_speed, uint8_t vocoder_cont)
{
	uint8_t vocoder_speed = q_vocoder_speed & ZZWPRO_VOCODER_TYPE_MASK;
	uint16_t ret;

	set_dsp_vocoder_length_paras(work_mode, vocoder_speed);
	set_vocoder_rate_agc_ns(work_mode, 3, vocoder_cont);
	set_dsp_test_vocoder_pointer(work_mode, vocoder_speed);

	ret = set_dsp_mod_type(work_mode, vocoder_speed);
//	vlog_v("config","[V]%d->%d,set mod=%x", old_vocoder_speed, vocoder_speed, ret);

	auto_trace_q_vocoder = q_vocoder_speed;

	return ret;
}

void runtime_to_switch_dsp_mod(uint8_t vocoder_demod_speed)	// 0xff-reset; else-dsp demod type
{
	uint8_t vocoder_type;
	VOCODER_SETUP_TYPEDEF set_vocoder, *new_vocoder = (VOCODER_SETUP_TYPEDEF *)&vocoder_type;

	if (vocoder_demod_speed >= 0xf0)
	{
reset_runtime_switch_dsp:
		if (dsp_runtime_misc_flag & DSP_RUNTIME_MISC_SWITCH_MOD)
		{
			dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_SWITCH_MOD;
			switch_dsp_vocoder(interphone_mode, vocoder_demod_speed, get_q_vocoder_speed(), get_vocoder_setup_content());
		}
	}
	else
	{
		if (vocoder_demod_speed == 0)
		{
			goto reset_runtime_switch_dsp;
		}
		else if ((vocoder_demod_speed & ZZWPRO_VOCODER_CRC_MASK) && ((vocoder_demod_speed & ZZWPRO_VOCODER_TYPE_MASK) != (auto_trace_q_vocoder & ZZWPRO_VOCODER_TYPE_MASK)))
		{
			dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_SWITCH_MOD;
			*((uint8_t *)&set_vocoder) = get_vocoder_setup_content();
			new_vocoder->vocoder_type = set_vocoder.vocoder_type;
			new_vocoder->vocoder_speed = set_vocoder.vocoder_speed;
			new_vocoder->vocoder_agc = set_vocoder.vocoder_agc;
			new_vocoder->vocoder_ns = set_vocoder.vocoder_ns;
			new_vocoder->vocoder_type_q = (vocoder_demod_speed <= 1) ? 0 : vocoder_demod_speed;	// vocoder tpye: 2400 fixed to ambe now
			switch_dsp_vocoder(interphone_mode, auto_trace_q_vocoder, vocoder_demod_speed, vocoder_type);
		}
	}
}

//#define set_delay_to_enter_sleep_external(second)	set_dsp_exit_to_normal_abs(SECOND_TO_SLOT(second))
void set_delay_to_enter_sleep_external(uint16_t time_second)
{
	if (IS_DSP_SAVE_POWER_ENABLE())
		sync_to_middle_exit_save_power = (uint8_t)time_second;
}

void dsp_slot_middle_xv_check_uart_data(void);
void dsp_slot_middle_interrupt(void)
{
//	uint16_t mod_stat;
	uint8_t device_is_base = dev_is_base();
	uint32_t stack_ret;
#ifndef print_10us_offset
	uint32_t timestamp_10us = get_timestamp_40us_measure();
#endif

///////////////////// tip sound /////////////////////

	dsp_reg_read(REG_INTERRUPT_STATE_ADDR, &int_stat);
//	vlog_v("config","int=%04x", int_stat);

#if DEBUG_DSP_INT_OFFSET_OF_SLOT > 1
	vlog_v("config","-m:%d/%04x-", GET_SLOT_TO_CURR_TIME(), int_stat);
#endif

#if DEBUG_DSP_SLOT_RSSI == 1
	vlog_v("config","\r\n[%d %d %d]int=%04x", slot_rising_edge_int_times, slot_middle_int_times, slot_rising_edge_int_times, int_stat);
#endif

#ifdef DEBUG_SPI_TX_STRESS_TEST
	spi_stress_test_sending(1);
#endif

//	if (int_stat & REG_INTERRUPT_STATE_RDATA_MASK)
	{
		slot_middle_int_times++;

		set_dsp_sync_with_pps();

		if (own_talking_now())
			dsp_slot_middle_interrupt_proc_talking();

		average_power_check_adc(zzwpro_marked_slot);

		stack_background_tscc_switch_chan();

		stack_ret = get_air_data_buffer((uint8_t *)stack_call_buffer);
		if (stack_ret != AIR_SIGNAL_TYPE_NONE)
		{
			cal_rssi_value_avg((stack_ret == AIR_SIGNAL_TYPE_VALID) ? rssi_prev_slot : rssi_prev_slot_noise);
//			if (interphone_mode <= INTERPHONE_MODE_ZZW_ADHOC)
			if (stack_ret == AIR_SIGNAL_TYPE_VALID)
				pdt_unify_receive_data_process(device_is_base, (uint8_t)stack_ret);
		}
		else
		{
			cal_rssi_value_avg(rssi_curr_slot);
		}

//		if (int_stat & REG_INTERRUPT_STATE_RDATA_MASK)		// 20220622: not inquire at receive data����20230506�����������ӣ���ֹ���������������ѯ֮�����Ϊdsp_reveived_data_int��
		{
			if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
			{
				dsp_slot_middle_xv_check_uart_data();
				stack_para_aux1 = 0;	// cws: 0-wireless; 1-wired(can)
				stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_INQUIRY));
			}
			else
			{
				if (pdu_data_available(stack_call_buffer))
				{
					stack_ret = CALL_STACK_PROCESS(STACK_CALL_TYPE_SEND_PDU_DATA);
				}
				else
				{
					stack_para_aux1 = 0;
					stack_ret = CALL_STACK_PROCESS(STACK_CALL_TYPE_INQUIRY);
#ifdef FAKE_MOBILE_BE_CALLED
					if (fake_speaker_voice)
					{
						if (fake_speaker_slot++ & 0x01)
						{
							stack_ret = fake_speaker_voice;
							test_vocoder_to_vocoder(vocoder_data);
							vocoder_to_signaling((uint8_t *)vocoder_data, (uint8_t *)stack_call_buffer, 0, 0, 1);
						}
					}
#endif
				}

				stack_return_process(stack_ret);
			}
		}
	}

	print_10us_offset("Midd", timestamp_10us);
}

#define MPT_VOICE_DSP_FRAME_LENGTH		MAX(REG_WRITE_REG_ANALOG_PCM_IN_LEN, REG_READ_REG_ANALOG_PCM_OUT_LEN)
#define MPT_VOICE_DSP_TO_CAN_NUM		(20 / 5)
#define MPT_VOICE_ONE_CANFRAME_LENGTH	(MPT_VOICE_DSP_FRAME_LENGTH / MPT_VOICE_DSP_TO_CAN_NUM)

#define MPT_VOICE_STATE_IDLE			0
#define MPT_VOICE_STATE_VALID			1
#define MPT_VOICE_STATE_BUSY			2
typedef struct
{
	uint8_t mpt_voice_state[2];				// which mpt_pcm_alaw valid
	uint8_t mpt_voice_sn;					// sn of send to can, 160B -> 4 can frams(40B)
	uint8_t mpt_voice_reserved;
	uint8_t mpt_pcm_alaw[2][MPT_VOICE_DSP_FRAME_LENGTH];
}MPT_DSP_VOICE_SAVE_TYPEDEF;

void mpt_send_dsp_voice_to_can_process(void);
MPT_DSP_VOICE_SAVE_TYPEDEF *mpt_dsp_voice, *mpt_can_voice, *mpt_voice_source;
void mpt_base_set_loopback_mode(uint8_t flag)	// flag: 0-loop by ccm; 1-internal loopback
{
	if (interphone_mode == INTERPHONE_MODE_MPT_CONV)
	{
		if (flag)
		{
			timer9to14_stop(TIMER_MISC_INDEX);
			mpt_voice_source = mpt_dsp_voice;
			set_timer12_handler((uint32_t)null_void_void);
		}
		else
		{
			mpt_voice_source = mpt_can_voice;
			set_timer12_handler((uint32_t)mpt_send_dsp_voice_to_can_process);
			timer9to14_start(TIMER_MISC_INDEX);
		}
		memset(mpt_dsp_voice, 0, sizeof(MPT_DSP_VOICE_SAVE_TYPEDEF));
		memset(mpt_can_voice, 0, sizeof(MPT_DSP_VOICE_SAVE_TYPEDEF));
	}
}

void init_mpt_conv_pointer(void)
{
	mpt_dsp_voice = (MPT_DSP_VOICE_SAVE_TYPEDEF *)long_message_tmp_save;
	mpt_can_voice = (MPT_DSP_VOICE_SAVE_TYPEDEF *)((uint32_t)long_message_tmp_save + sizeof(MPT_DSP_VOICE_SAVE_TYPEDEF));
	mpt_base_set_loopback_mode(1);
#if DEBUG_MPT_BASE > 1
	vlog_v("config","squel door=%s,squle level=%d,sig tone=%s",
		maintain_setup_parameter(PARA_OPERATE_READ, SQUELCH_DOOR_SETUP_POS, 0) ? "close" : "open",
		maintain_setup_parameter(PARA_OPERATE_READ, NOISE_THRESHOLD_LEVEL, 0) - 130,
		maintain_setup_parameter(PARA_OPERATE_READ, SIGNALING_TONE_OUTPUT_POS, 0) ? "enable" : "disable");
#endif
}


///////////////// mpt dsp -> can /////////////////
static uint8_t tim_mpt_conv_set_gate = 0xff;
void mpt_conv_base_reset_gate(void)
{
	set_dev_gate(IS_BASE_GATE, 0);
	tim_mpt_conv_set_gate = timer_destroy(tim_mpt_conv_set_gate);
}

void mpt_conv_base_set_gate(void)
{
	if (tim_mpt_conv_set_gate == 0xff)
	{
		tim_mpt_conv_set_gate = timer_initial(0, 120, mpt_conv_base_reset_gate);
		if (tim_mpt_conv_set_gate != 0xff)
			set_dev_gate(IS_BASE_GATE, 1);
	}
	else
	{
		timer_reset(tim_mpt_conv_set_gate);
	}
}

uint8_t mpt_save_dsp_voice_process(void)
{
	uint16_t i, which_buf, pcm_alaw[MPT_VOICE_DSP_FRAME_LENGTH];	// ģ��ת��̨ ÿ��RDATA��ʱ�� �����������λ��  ����PCM 20ms��RDATAһ�Σ�20230719Э��ΪA�ɣ�5msΪ40B��20ms����160B��

	if (mpt_dsp_voice->mpt_voice_state[0] == MPT_VOICE_STATE_IDLE)
		which_buf = 0;
	else if (mpt_dsp_voice->mpt_voice_state[1] == MPT_VOICE_STATE_IDLE)
		which_buf = 1;
	else
		return 0xff;

	dsp_reg_read(REG_DSP64_ANALOG_PCM_OUT_ADDR, pcm_alaw);
	if (g_runtime_inst_xvbase->misc_config_base.mpt_voice_format)
	{
		for (i = 0; i < MPT_VOICE_DSP_FRAME_LENGTH; i++)
			mpt_dsp_voice->mpt_pcm_alaw[which_buf][i] = (uint8_t)pcm_alaw[i];			// A�ɣ����ݷ��ڵ�8bit
	}
	else
	{
		for (i = 0; i < MPT_VOICE_DSP_FRAME_LENGTH; i++)
			mpt_dsp_voice->mpt_pcm_alaw[which_buf][i] = (uint8_t)(pcm_alaw[i] >> 8);	// ����PCM��ֻȡ��8bitʹ��
	}
	mpt_dsp_voice->mpt_voice_state[which_buf] = MPT_VOICE_STATE_VALID;

	return which_buf;
}

#if (DEBUG_MPT_BASE == 4) || (DEBUG_MPT_BASE == 6)
static uint32_t mpt_conv_timer_measure = 0;
#endif
static uint8_t mpt_sync_counter5ms = 0;
void mpt_send_dsp_voice_to_can_process(void)
{
#if (DEBUG_MPT_BASE == 4) || (DEBUG_MPT_BASE == 6)
	uint32_t new_mpt_conv_timer_measure = get_timestamp_measure();
#endif
	uint8_t  which_buf;

	if (mpt_sync_counter5ms >= 2)	// 20230731:ģ���ŵ���ģ��ʱ϶ͬ���źŻ���30ms��ģ��2.4K��ƵӦ����60ms��Ƶ֡��ģ���̨ʱ϶ͬ�����ò���10MS
	{
		toggle_base_sync();
		mpt_sync_counter5ms = 0;
	}
	else
	{
		mpt_sync_counter5ms++;
	}

	if (mpt_dsp_voice->mpt_voice_state[0] == MPT_VOICE_STATE_BUSY)
	{
		which_buf = 0;
send_mpt_voice_to_can_entry:
#if (DEBUG_MPT_BASE == 4) || (DEBUG_MPT_BASE == 6)
		vlog_v("config","mpt%d.%d->can(%d)", which_buf, mpt_dsp_voice->mpt_voice_sn, get_measure_timer_difference(new_mpt_conv_timer_measure, mpt_conv_timer_measure));
#endif
		send_mpt_voice_to_can(mpt_dsp_voice->mpt_pcm_alaw[which_buf] + mpt_dsp_voice->mpt_voice_sn * MPT_VOICE_ONE_CANFRAME_LENGTH, MPT_VOICE_ONE_CANFRAME_LENGTH);
		if (mpt_dsp_voice->mpt_voice_sn >= MPT_VOICE_DSP_TO_CAN_NUM - 1)
			mpt_dsp_voice->mpt_voice_state[which_buf] = MPT_VOICE_STATE_IDLE;
		else
			mpt_dsp_voice->mpt_voice_sn++;
	}
	else if (mpt_dsp_voice->mpt_voice_state[1] == MPT_VOICE_STATE_BUSY)
	{
		which_buf = 1;
		goto send_mpt_voice_to_can_entry;
	}
	else if (mpt_dsp_voice->mpt_voice_state[0] == MPT_VOICE_STATE_VALID)
	{
		which_buf = 0;
send_mpt_voice_to_can_ready:
		mpt_dsp_voice->mpt_voice_state[which_buf] = MPT_VOICE_STATE_BUSY;
		mpt_dsp_voice->mpt_voice_sn = 0;
		goto send_mpt_voice_to_can_entry;
	}
	else if (mpt_dsp_voice->mpt_voice_state[1] == MPT_VOICE_STATE_VALID)
	{
		which_buf = 1;
		goto send_mpt_voice_to_can_ready;
	}
#if (DEBUG_MPT_BASE == 4) || (DEBUG_MPT_BASE == 6)
	else
	{
		vlog_v("config","mpt timer(%d)", get_measure_timer_difference(new_mpt_conv_timer_measure, mpt_conv_timer_measure));
	}
	mpt_conv_timer_measure = new_mpt_conv_timer_measure;
#endif
}

///////////////// mpt can -> dsp /////////////////
static uint8_t tim_mpt_conv_set_ptt = 0xff;
void mpt_conv_base_reset_ptt(void)
{
	set_dsp_analog_ptt_on_off(0);
	tim_mpt_conv_set_ptt = timer_destroy(tim_mpt_conv_set_ptt);
}

void mpt_conv_base_set_ptt(void)
{
	if (tim_mpt_conv_set_ptt == 0xff)
	{
		tim_mpt_conv_set_ptt = timer_initial(0, 120, mpt_conv_base_reset_ptt);
		if (tim_mpt_conv_set_ptt != 0xff)
		{
#if defined VICTEL_ZZWPRO_BASE || defined STM32F469_479xx
			dsp_analog_ptt_ctrl_with_hp();
#else
			set_dsp_analog_ptt_on_off(1);	// voice
#endif
		}
	}
	else
	{
		timer_reset(tim_mpt_conv_set_ptt);
#if defined VICTEL_ZZWPRO_BASE || defined STM32F469_479xx
		dsp_analog_ptt_ctrl_with_hp();
#else
		set_dsp_analog_ptt_on_off(1);		// voice
#endif
	}
}

#if (DEBUG_MPT_BASE == 7) || (DEBUG_MPT_BASE == 8)
static uint32_t mpt_conv_can_pcm_timer_measure = 0;
#endif
void mpt_save_can_voice(uint8_t *can_pcm)
{
#if (DEBUG_MPT_BASE == 7) || (DEBUG_MPT_BASE == 8)
		uint32_t new_mpt_conv_can_pcm_timer_measure = get_timestamp_measure();
#endif
	uint8_t which_buf;

	if (mpt_can_voice->mpt_voice_state[0] == MPT_VOICE_STATE_BUSY)
	{
		which_buf = 0;
save_can_voice_to_buffer_entry:
#if (DEBUG_MPT_BASE == 7) || (DEBUG_MPT_BASE == 8)
		vlog_v("config","mpt can->%d.%d(%d)", which_buf, mpt_can_voice->mpt_voice_sn, get_measure_timer_difference(new_mpt_conv_can_pcm_timer_measure, mpt_conv_can_pcm_timer_measure));
#endif
		memcpy(mpt_can_voice->mpt_pcm_alaw[which_buf] + mpt_can_voice->mpt_voice_sn * MPT_VOICE_ONE_CANFRAME_LENGTH, can_pcm, MPT_VOICE_ONE_CANFRAME_LENGTH);
		if (mpt_can_voice->mpt_voice_sn >= MPT_VOICE_DSP_TO_CAN_NUM - 1)
			mpt_can_voice->mpt_voice_state[which_buf] = MPT_VOICE_STATE_VALID;
		else
			mpt_can_voice->mpt_voice_sn++;
	}
	else if (mpt_can_voice->mpt_voice_state[1] == MPT_VOICE_STATE_BUSY)
	{
		which_buf = 1;
		goto save_can_voice_to_buffer_entry;
	}
	else if (mpt_can_voice->mpt_voice_state[0] == MPT_VOICE_STATE_IDLE)
	{
		which_buf = 0;
save_can_voice_to_buffer_ready:
		mpt_can_voice->mpt_voice_state[which_buf] = MPT_VOICE_STATE_BUSY;
		mpt_can_voice->mpt_voice_sn = 0;
		goto save_can_voice_to_buffer_entry;
	}
	else if (mpt_can_voice->mpt_voice_state[1] == MPT_VOICE_STATE_IDLE)
	{
		which_buf = 1;
		goto save_can_voice_to_buffer_ready;
	}
#if (DEBUG_MPT_BASE == 7) || (DEBUG_MPT_BASE == 8)
	else
	{
		vlog_v("config","mpt can:%d/%d(%d)", mpt_can_voice->mpt_voice_state[0], mpt_can_voice->mpt_voice_state[1], get_measure_timer_difference(new_mpt_conv_can_pcm_timer_measure, mpt_conv_can_pcm_timer_measure));
	}
	mpt_conv_can_pcm_timer_measure = new_mpt_conv_can_pcm_timer_measure;
#endif
}

uint8_t mpt_send_can_voice_to_dsp_process(void)
{
//	ģ��ת��̨ ÿ��RDATA��ʱ�� �����������λ��  ����PCM 20ms��RDATAһ�Σ�20230719Э��ΪA�ɣ�20230825�ٴθ�Ϊ����PCM�������ģ���վ����5msΪ40B��20ms����160B����20230908��Ϊ������
	uint16_t i, which_buf, pcm_alaw[MPT_VOICE_DSP_FRAME_LENGTH];

	if (mpt_voice_source->mpt_voice_state[0] == MPT_VOICE_STATE_VALID)
		which_buf = 0;
	else if (mpt_voice_source->mpt_voice_state[1] == MPT_VOICE_STATE_VALID)
		which_buf = 1;
	else
		return 0xff;

	if (g_runtime_inst_xvbase->misc_config_base.mpt_voice_format)
	{
		for (i = 0; i < MPT_VOICE_DSP_FRAME_LENGTH; i++)
			pcm_alaw[i] = (uint16_t)mpt_voice_source->mpt_pcm_alaw[which_buf][i];		// A�ɣ�����ڵ�8bit
	}
	else
	{
		for (i = 0; i < MPT_VOICE_DSP_FRAME_LENGTH; i++)
			pcm_alaw[i] = (uint16_t)mpt_voice_source->mpt_pcm_alaw[which_buf][i] << 8;	// ����PCM�����ڸ�8bit
	}
	mpt_voice_source->mpt_voice_state[which_buf] = MPT_VOICE_STATE_IDLE;
	dsp_reg_write(REG_DSP64_REG_ANALOG_PCM_IN_ADDR, pcm_alaw);

	return which_buf;
}


void mpt_conv_base_slot_middle_process(void)
{
#if 0
	static uint8_t slot20ms_count = 0, mpt_conv_voice_delay_times = 0;
#endif
	static uint8_t slot20ms_count = 0;

	slot_middle_int_times++;

//	update_analog_rssi_value();
#if 0
	led_warning_player_mpt_conv();

	speech_pa_ctrl_process();
#endif

#if defined VICTEL_ZZWPRO_BASE || defined STM32F469_479xx
	average_power_check_adc(0);
	average_power_adc(1);
#endif

///////////////////// tip sound /////////////////////
#if 0
	if (dsp_sout_interrupt())
		dsp_sout_int_handle();
#endif

	conv_report_rssi_handle();

#if 0
	if (is_mpt_conv_receiving_voice())
	{
		incoming_pcm_voice_num++;
//		vlog_v("config","[%d]receiving: %d", slot_middle_int_times, incoming_pcm_voice_num);
	}
#endif

	if (++slot20ms_count > 2)
	{
		slot20ms_count = 0;
		if (++slot60ms_counter > SLOT_TIMER_CHECK_TIME / 60)
		{
			slot60ms_counter = 0;
//			slot_timer_flag_check = 1;
		}

		check_and_adjust38p4();
	}
/*
#if defined VICTEL_ZZWPRO_BASE || defined STM32F469_479xx
	dsp_analog_ptt_ctrl_with_hp();
#else
	set_dsp_analog_ptt_on_off(1);	// voice
#endif
*/
#if 0
	if (own_talking_now())
		GL_LCDPrintBar(BAR_TYPE_TR_ARROW, BAR_TYPE_TR_ARROW_UP, 0);
	else if (someone_talking_now())
		GL_LCDPrintBar(BAR_TYPE_TR_ARROW, BAR_TYPE_TR_ARROW_DOWN, 0);
	else
		GL_LCDPrintBar(BAR_TYPE_TR_ARROW, BAR_TYPE_TR_ARROW_CLEAR, 0);

	if (stack_state.vs & STACK_VS_CALLING)							// phone busy
	{
		if (stack_state.vs & STACK_VS_OWN_CALLING)					// self-calling
		{
			if ((stack_state.vs & STACK_VS_OWN_SPEAKING) == 0)		// speaking stop (PTT released already)
			{
				if (mpt_conv_voice_delay_times++ > 10)
				{
					mpt_conv_voice_delay_times = 0;
					stack_state.vs = 0;
				}
			}
			else
			{
#if defined VICTEL_ZZWPRO_BASE || defined STM32F469_479xx
				dsp_analog_ptt_ctrl_with_hp();
#else
				set_dsp_analog_ptt_on_off(1);	// voice
#endif
			}
		}
		else
		{
			if (incoming_pcm_voice_old < incoming_pcm_voice_num)	// someone else still talking
			{
				incoming_pcm_voice_old = incoming_pcm_voice_num;
				mpt_conv_voice_delay_times = 0;
				dsp_speech_out_pa_ctrl(1, SPEECH_PA_CTRL_OUT_VOICE);// maybe close if PTT pressed
			}
			else
			{
				if (mpt_conv_voice_delay_times > 1)
				{
//					vlog_v("config","[%d]stop talking: %d(old=%d, dly=%d)", slot_middle_int_times, incoming_pcm_voice_num, incoming_pcm_voice_old, mpt_conv_voice_delay_times);
					mpt_conv_voice_delay_times = 0;
					stack_state.vs = 0;
					dsp_speech_out_pa_ctrl(0, SPEECH_PA_CTRL_OUT_VOICE);
					set_operation_timeout_time(maintain_setup_parameter(PARA_OPERATE_READ, LCD_TURN_OFF_TIME_POS, 0));
				}
				else
				{
					mpt_conv_voice_delay_times++;
//					vlog_v("config","[%d]stop talking delay: %d(old=%d, dly=%d)", slot_middle_int_times, incoming_pcm_voice_num, incoming_pcm_voice_old, mpt_conv_voice_delay_times);
				}
			}
		}
	}
	else														// phone idle
	{
		set_dsp_analog_ptt_on_off(0);
		if (incoming_pcm_voice_old + 1 < incoming_pcm_voice_num)// someone else talking longer than 20ms
		{
//			vlog_v("config","[%d]start talking: %d(old=%d)", slot_middle_int_times, incoming_pcm_voice_num, incoming_pcm_voice_old);
			incoming_pcm_voice_old = incoming_pcm_voice_num;
			stack_state.vs = STACK_VS_CALLING | STACK_VS_PLAY_VOICE;
			dsp_speech_out_pa_ctrl(1, SPEECH_PA_CTRL_OUT_VOICE);
			exit_operation_timeout(0);
			if (g_static_ptr->misc_static_config.lcd_on_when_calling)
				set_operation_timeout_time(0);	// maintain_setup_parameter(PARA_OPERATE_READ, LCD_TURN_OFF_TIME_POS, 0)
		}
	}
#endif // #if 0
}

#if (DEBUG_MPT_BASE == 3) || (DEBUG_MPT_BASE == 5) || (DEBUG_MPT_BASE == 6) || (DEBUG_MPT_BASE == 8)
static uint32_t mpt_conv_timer_measure_rdata = 0;
#endif
#if (DEBUG_MPT_BASE == 2) || (DEBUG_MPT_BASE == 5) || (DEBUG_MPT_BASE == 6)
static uint32_t mpt_conv_timer_measure_data = 0;
#endif
void dsp_slot_middle_interrupt_for_mpt(void)
{
#if DEBUG_MPT_BASE > 1
	uint8_t n;
#endif
	uint32_t new_mpt_conv_timer_measure_int = get_timestamp_measure();

	dsp_reg_read(REG_INTERRUPT_STATE_ADDR, &int_stat);

	if (int_stat & REG_INTERRUPT_STATE_RDATA_MASK)
	{
#if (DEBUG_MPT_BASE == 3) || (DEBUG_MPT_BASE == 5) || (DEBUG_MPT_BASE == 6) || (DEBUG_MPT_BASE == 8)
		n = mpt_send_can_voice_to_dsp_process();
		if (n <= 1)
		{
			vlog_v("config","(%d)mpt_rdata(%d):%d", get_rssi_value_real(), get_measure_timer_difference(new_mpt_conv_timer_measure_int, mpt_conv_timer_measure_rdata), n);
			mpt_conv_base_set_ptt();
		}
		else
			vlog_v("config","(%d)mpt_rdata empty(%d)", get_rssi_value_real(), get_measure_timer_difference(new_mpt_conv_timer_measure_int, mpt_conv_timer_measure_rdata));
		mpt_conv_timer_measure_rdata = new_mpt_conv_timer_measure_int;
#else
		if (mpt_send_can_voice_to_dsp_process() <= 1)
			mpt_conv_base_set_ptt();
#endif

		mpt_conv_base_slot_middle_process();
	}
	else if (int_stat & REG_INTERRUPT_STATE_DATA_MASK)
	{
		if (is_mpt_conv_receiving_voice())
		{
			mpt_conv_base_set_gate();

#if (DEBUG_MPT_BASE == 2) || (DEBUG_MPT_BASE == 5) || (DEBUG_MPT_BASE == 6)
			n = mpt_save_dsp_voice_process();
			if (n <= 1)
			{
				vlog_v("config","(%d)mpt_data(%d)->%d:%04X %04X %04X %04X", get_rssi_value_real(), get_measure_timer_difference(new_mpt_conv_timer_measure_int, mpt_conv_timer_measure_data), n,
					mpt_dsp_voice->mpt_pcm_alaw[n][0], mpt_dsp_voice->mpt_pcm_alaw[n][1], mpt_dsp_voice->mpt_pcm_alaw[n][2], mpt_dsp_voice->mpt_pcm_alaw[n][3]);
			}
			else
			{
				vlog_v("config","(%d)mpt_data(%d) full", get_rssi_value_real(), get_measure_timer_difference(new_mpt_conv_timer_measure_int, mpt_conv_timer_measure_data));
			}
#else
			mpt_save_dsp_voice_process();
#endif
		}
#if (DEBUG_MPT_BASE == 2) || (DEBUG_MPT_BASE == 5) || (DEBUG_MPT_BASE == 6)
		else
		{
			vlog_v("config","(%d)mpt_data(%d)", get_rssi_value_real(), get_measure_timer_difference(new_mpt_conv_timer_measure_int, mpt_conv_timer_measure_data));
		}
		mpt_conv_timer_measure_data = new_mpt_conv_timer_measure_int;
#endif
	}

}

void dsp_slot_middle_conv_check_ccm_data(void)
{
//	uint32_t inq_ret;
//	if (get_signaling_fr_can((uint32_t)stack_call_buffer))
//		send_data_sync_to_slot_middle(STACK_RETURN_TYPE_SIGNALING, 0);

	timer9to14_stop(TIMER_MISC_INDEX);
//	stack_para_aux1 = 1;							// CWS.bit0: 0-air, 1-can
	stack_para_aux1 = 0;							// 20220622: ��ѯ��CAN���ݣ��Ƿ���������տڣ���ǰ�ǽ�bit0��1����ѯ��
/*	20230107: ������CCM���ܽ�����Ӧ���޸ĺ���ʹ�ô˴���
	inq_ret = CALL_STACK_PROCESS(STACK_CALL_TYPE_INQUIRY);
	stack_return_process(inq_ret);					// pdt conv: can data->air throughout my conv stack
//	if (inq_ret != STACK_RETURN_TYPE_SIGNALING)		// inquire again if there is data output to air; 20220622 annotate it because inquire CAN data at dsp_slot_middle_interrupt already
//	{
//		stack_para_aux1 &= ~1;
//		stack_para_aux1 |= 1;						// 20220622
//		stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_INQUIRY));
//	}
*/
	if (dev_is_bypass_stack() == 0)
	{
		stack_call_interface_conv_base_use_stack(STACK_CALL_TYPE_INQUIRY, &stack_para_aux1, &stack_para_aux2, (uint32_t)stack_call_buffer);
	}
	else
	{
		stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_INQUIRY));
	}
}

void set_stack_paras_with_can_data(void)
{
	uint8_t src_canid, cmdx;

//	vlog_v("config","id=%d,cmdx=%d", *((uint8_t *)stack_call_buffer + VPLUS_SIGNALING_BUF_LENGTH + 4), *((uint8_t *)stack_call_buffer + VPLUS_SIGNALING_BUF_LENGTH + 1));
	stack_para_aux1 = *((uint8_t *)stack_call_buffer + VPLUS_SIGNALING_BUF_LENGTH); // rssi
	cmdx = *((uint8_t *)stack_call_buffer + VPLUS_SIGNALING_BUF_LENGTH + 1);
	stack_para_aux2 = ZZWPRO_STACK_SIG_PDN_TYPE_CAN | cmdx; 			// CAN��b7=1��b6~0��CAN������Ƶ֡CMDX
//	if (ZZWPRO_FRAME_IS_Q_FRAME(stack_call_buffer, stack_para_aux2))
	{
		src_canid = *((uint8_t *)stack_call_buffer + VPLUS_SIGNALING_BUF_LENGTH + 4);
		if (src_canid >= 8)
		{
//			20220329: PDN�ĵ�3λ=Դ����ģ���ַ-8+Qģ������֡CMDX�����λ
			stack_para_aux2 &= ~0x07;
			stack_para_aux2 |= (src_canid - ((get_xvbase_canid() == LBUS_IM) ? 13 : 8) + (cmdx & 0x01)) & 0x07; // dec 8(Q's canid=8/9/10/11/12) when self can id=4; dec 13(Q's canid=13/14/15) when self can id=6
		}
		else
		{
			stack_para_aux2 |= src_canid & 0x01;						// source canid is odd means that it is Q channel 2(transmit to stack is pdn.0=1)
		}
	}
}

extern uint32_t timer3_pause_counter;
void check_can_frame_and_put_to_stack(void)
{
	if (get_signaling_fr_can((uint32_t)stack_call_buffer))
	{
//		vlog_v("config","\tget can data:%08x %08x %08x", stack_call_buffer[0], stack_call_buffer[16], stack_call_buffer[17]);
#ifndef MAIN_DONOT_PROCESS_VO_MODE
		if (ZZWPRO_BASE_IS_VO_MODE())
		{
			stack_para_aux2 &= 0x7f;											// clear b7 for sending to air
			dump_data_bypass_stack((uint8_t *)stack_call_buffer, (uint8_t *)stack_call_buffer + STACK_XV_DATA_DUMP_LENGTH, STACK_CALL_TYPE_VO_CAN_DATA, STACK_XV_DATA_DUMP_LENGTH);
			send_data_sync_to_slot_middle(0, 0);								// do not give to stack and send to high freq
		}
		else
#endif
		{
			set_stack_paras_with_can_data();
			print_simple_data_flow("zzw_frCan");
			stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_AIR_DATA)); // xv: can data->xv stack
		}
	}
}

void dsp_slot_middle_xv_check_ccm_data(void)
{
	// 20230714:���������յ�can�����������Э��ջ�����ڶ�ʱ������ʱ�Ų�ѯ�Ƿ���������տ� */
	timer9to14_stop(TIMER_MISC_INDEX);
#ifdef DEBUG_INQUIRE_AFTER_23MS
	vlog_v("config","xv_check_ccm:%d %d", timer3_pause_counter, get_timestamp_measure());
#endif

	if (timer3_pause_counter)	// pause(receive can/uart frame), so call STACK_CALL_TYPE_AIR_DATA only; inquire at T3 end
	{
		timer9to14_start(TIMER_MISC_INDEX);
		timer3_pause_counter = 0;
		check_can_frame_and_put_to_stack();
	}
	else						// end really, so call STACK_CALL_TYPE_INQUIRY; 20240528����ѯ�Ƿ�����һ��ʱ϶����ʱ����յ���can֡
	{
		// 20230506��ͳһΪ��ʱ������ʱ��ѯ���Ƿ�������͵��տڣ�
		check_can_frame_and_put_to_stack();
		stack_para_aux1 = 0;	// cws: 0-wireless; 1-wired(can)
		stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_INQUIRY));
	}
}

void dsp_slot_middle_xv_check_uart_data(void)
{
//	timer9to14_stop(TIMER_MISC_INDEX);
	if (get_signaling_fr_can((uint32_t)stack_call_buffer))
	{
//#ifndef MAIN_DONOT_PROCESS_VO_MODE
//		if (ZZWPRO_BASE_IS_VO_MODE())
//		{
//			stack_para_aux2 &= 0x7f;										// clear b7 for sending to air
//			dump_data_bypass_stack((uint8_t *)stack_call_buffer, (uint8_t *)stack_call_buffer + STACK_XV_DATA_DUMP_LENGTH, STACK_CALL_TYPE_VO_CAN_DATA, STACK_XV_DATA_DUMP_LENGTH);
//			send_data_sync_to_slot_middle(0, 0);							// do not give to stack and send to high freq
//		}
//		else
//#endif
		{
			stack_para_aux1 = *((uint8_t *)stack_call_buffer + VPLUS_SIGNALING_BUF_LENGTH);											// rssi
			stack_para_aux2 = ZZWPRO_STACK_SIG_PDN_TYPE_CAN | *((uint8_t *)stack_call_buffer + VPLUS_SIGNALING_BUF_LENGTH + 1);		// CAN��b7=1��b6~0��CAN������Ƶ֡CMDX
			print_simple_data_flow("zzw_frUart");
			stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_AIR_DATA));	// xv: can data->xv stack
		}
	}
}

void test_print_rssi_dqi(void)
{
	uint8_t i, rssi_dqi_str[20];

	for (i = 0; i < 2; i++)
	{
		sprintf((char *)rssi_dqi_str, "%d: %04d %03d", i, -130 + zzwpro_status[i].rssi, zzwpro_status[i].dqi);
//		vlog_v("config","%s", (char *)rssi_dqi_str);
		GL_LCDPrintString(20, 20 + i * 20, rssi_dqi_str, LCD_COLOR_WHITE, back_ground_color, ADMIT_TEST_FONT_STYLE, FONT_NONTRANSPARENT);
	}

	sprintf((char *)rssi_dqi_str, "GPS pwr=%d", g_runtime_inst.runtime_paras.pwr_ctrl.pwr_gps);
	GL_LCDPrintString(160, 20, rssi_dqi_str, LCD_COLOR_WHITE, back_ground_color, ADMIT_TEST_FONT_STYLE, FONT_NONTRANSPARENT);
//	vlog_v("config","%s", (char *)rssi_dqi_str);
}

void zzwpro_slot_end_process(uint8_t total_slot)
{
	uint8_t i, rx = 0;

	average_power_adc(total_slot);

	if (dev_is_base())	// can NOT use dev_have_base_feature() because 826 have lcd but no CAN
		send_channel_status_to_can((uint32_t)zzwpro_status, (uint32_t)zzwpro_status_ex);
	else
		copy_channel_status_to_can_frame((uint32_t)zzwpro_status, (uint32_t)zzwpro_status_ex);

	for (i = 0; i < total_slot; i++)
	{
#ifdef SHOW_SOLT_DETAIL_WITH_VOICE
		if (receive_voice_counter[i])
			receive_voice_counter[i]--;
#endif
//		vlog_v("config","\t%d:%d %d  %d %d %d %d", i, zzwpro_status[i].sta.tx, zzwpro_status[i].sta.tx_slot,
//			zzwpro_status[i].sta.rx, zzwpro_status[i].sta.rx_slot, zzwpro_status[i].rssi, zzwpro_status[i].dqi);
		if (zzwpro_status[i].sta.tx >= ZZWPRO_STATUS_CRIT)
		{
			zzwpro_status[i].sta.tx = 0;
			zzwpro_status[i].sta.tx_slot = 0;
		}
		else if (zzwpro_status[i].sta.tx)
		{
			zzwpro_status[i].sta.tx = ZZWPRO_STATUS_CRIT;
		}

		if (zzwpro_status[i].sta.rx >= ZZWPRO_STATUS_CRIT)
		{
			zzwpro_status[i].sta.rx = 0;
			zzwpro_status[i].sta.rx_slot = 0;
			zzwpro_status[i].dqi = 0;
		}
		else if (zzwpro_status[i].sta.rx)
		{
			rx++;
			zzwpro_status[i].sta.rx = ZZWPRO_STATUS_CRIT;
		}
	}

	set_dev_gate(IS_BASE_GATE, rx);
}

/*
uint8_t get_zzwpro_send_slot(void)
{
	uint8_t i, zzwpro_max_slot = GET_ZZWPRO_TR_STATUS_MAX_SLOT();

	for (i = 0; i < zzwpro_max_slot; i++)
	{
		if (zzwpro_status[i].sta.tx)
			return ((uint8_t)zzwpro_status[i].sta.tx_slot << 4) | i;
	}
	return 0;
}
*/

uint8_t get_zzwpro_receive_slot(void)
{
	static uint8_t search_start = 0;
	uint8_t i, zzwpro_max_slot = GET_ZZWPRO_TR_STATUS_MAX_SLOT();

	for (i = 0; i < zzwpro_max_slot; i++)
	{
		if (++search_start >= zzwpro_max_slot)
			search_start = 0;
		if (zzwpro_status[search_start].sta.rx)
			return ((uint8_t)zzwpro_status[search_start].sta.rx_slot << 4) | search_start;
	}
	return 0;
}
uint8_t get_dsp_demod_zzw_slot(void)
{
	if (interphone_mode == INTERPHONE_MODE_ZZW_ADHOC)
		return (dsp_demod_state[0] & REG_DEMOD_STATE_SLOT_MASK) >> REG_DEMOD_STATE_SLOT_SHIFT;
	else
//		return (own_calling_now() ? get_zzwpro_send_slot() : get_zzwpro_receive_slot()) & 0x0f;
//		return own_talking_now() ? zzwpro_send_slot : (own_playing_voice_now() ? replace_voice_flag : get_zzwpro_receive_slot());
		return (STACK_SET_FORWARD_FRAME() || own_talking_now()) ? zzwpro_send_slot : (own_playing_voice_now() ? replace_voice_flag : get_zzwpro_receive_slot());
}

uint8_t get_dsp_play_voice_rssi(void)
{
	uint8_t ret = 0xff;

	if (interphone_mode > INTERPHONE_MODE_ZZW_ADHOC)
	{
		if (own_playing_voice_now())
			ret = zzwpro_status[replace_voice_flag].rssi;
	}

	return ret;
}

uint8_t dsp_sout_interrupt(void)
{
	uint16_t mpt_int;

	dsp_reg_read(REG_INTERRUPT_STATE_ADDR, &mpt_int);
	return (mpt_int & REG_INTERRUPT_STATE_SOUT_MASK) ?  1 : 0;
}

void set_dsp_analog_ptt_on_off(uint8_t on)	// 0-off, 1-on
{
	if (on == 0)
	{
		dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_DSP_ANALOG_PTT_OK;
		set_dsp_analog_mode(REG_ANALOG_MODE_PTT_MASK, 0);	// voice stop
	}
	else
	{
		dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_DSP_ANALOG_PTT_OK;
		set_dsp_analog_mode(REG_ANALOG_MODE_PTT_MASK, 1);		// voice start
	}
}

void dsp_analog_ptt_ctrl_with_hp(void)
{
	if ((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_DSP_ANALOG_PTT_OK) && is_high_rp_protect())
		set_dsp_analog_ptt_on_off(0);
	else if (((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_DSP_ANALOG_PTT_OK) == 0) && (is_high_rp_protect() == 0))
		set_dsp_analog_ptt_on_off(1);
}

void mpt_poll_stack(void)
{
	static uint8_t slot10ms_counter = 0;


	led_warning_player();

	slot_middle_int_times++;

	speech_pa_ctrl_process();

///////////////////// edge /////////////////////
//	slot_rising_edge_int_times++;
//	slot_falling_edge_int_times++;

	if (own_talking_now())
	{
		led_play(BAR_TYPE_TR_ARROW_UP);
	}
//	else if (mpt_trunk_is_someone_talking())
	else if (phone_is_busy_now())
	{
		if (tx_icon_delay_clean == 0)
		{
			led_play(BAR_TYPE_TR_ARROW_DOWN);
		}
		else
		{
			tx_icon_delay_clean--;
		}
	}
	else
	{
		led_play(BAR_TYPE_TR_ARROW_CLEAR);
	}

	if (++slot10ms_counter > 6)
	{
		slot10ms_counter = 0;
		if (++slot60ms_counter > SLOT_TIMER_CHECK_TIME / 60)
		{
			slot60ms_counter = 0;
//			slot_timer_flag_check = 1;
		}

		CHECK_AND_ADJUST38P4();
	}

	dsp_reg_read(REG_ANALOG_INTERRUPT_STATE_ADDR, &int_stat);
	if ((int_stat & REG_ANALOG_INTERRUPT_STATE_SQU_MASK) == REG_ANALOG_INTERRUPT_STATE_SQU_MASK)
		stack_para_aux1 = get_rssi_value_rel(0);			// RSSI, 25
	else
		stack_para_aux1 = 0;
	stack_para_aux2 = 0;							// MS
	stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_SLOT_SYNC));

	stack_call_sync_to_slot();

	average_power_check_adc(0);
	average_power_adc(1);


///////////////////// middle /////////////////////

	if (int_stat & REG_ANALOG_INTERRUPT_STATE_MDM_MASK)
	{
		dsp_reg_read(REG_ANALOG_MSK_DATA_OUT_ADDR, (uint16_t *)stack_call_buffer);
		ntohs_mul((uint16_t *)stack_call_buffer, REG_ANALOG_MSK_DATA_OUT_LENGTH);
		print_air_data("mpt_airDat", (uint8_t *)stack_call_buffer);
		num_of_frame_received++;

	 	stack_para_aux1 = get_rssi_value_rel(0);	// RSSI, 25
		stack_return_process(CALL_STACK_PROCESS(STACK_CALL_TYPE_AIR_DATA));
	}

	if (own_talking_now())
		dsp_analog_ptt_ctrl_with_hp();
}

uint8_t is_mpt_conv_receiving_voice(void)
{
	uint16_t mpt_conv_int_stat;

	dsp_reg_read(REG_ANALOG_INTERRUPT_STATE_ADDR, &mpt_conv_int_stat);
	if (is_sub_ctcss_enable())
	{
		return (mpt_conv_int_stat & (REG_ANALOG_INTERRUPT_STATE_CDT_MASK | REG_ANALOG_INTERRUPT_STATE_SQU_MASK)) ==
				(REG_ANALOG_INTERRUPT_STATE_CDT_MASK | REG_ANALOG_INTERRUPT_STATE_SQU_MASK) ? 0xff : 0;
	}
	else
	{
		return (mpt_conv_int_stat & REG_ANALOG_INTERRUPT_STATE_SQU_MASK) == REG_ANALOG_INTERRUPT_STATE_SQU_MASK ? 0xff : 0;
	}
//	return (mpt_conv_int_stat & REG_ANALOG_INTERRUPT_STATE_CAR_MASK) ? 0xff : 0;
}



#define TIP_HEADER_LENGTH	128
void stop_send_tip_sound(void)
{
	g_talk_tip_sound = 0;
	g_talk_tip_count = 0;
}

void send_tip_sound(uint8_t flag, uint32_t times)		// flag: tip type; 0: no tip to talk
{
	uint32_t talk_tip_address;
	uint8_t talk_tip_times;
	uint16_t tip_index[2];
//	char tip_string[5][4] = {"KEY", "PTT", "SUC", "FAL", "END"};

#ifndef POWER_SAVE_ENABLE_TIP_SOUND
	if (IS_DSP_SAVE_POWER_ENABLE() == 0)
	{
#endif
	if (flag == 0)														// stop or send again
	{
//		vlog_v("config","tip[%d][talk=%d, times=%d]", flag, g_talk_tip_sound, g_talk_tip_times);
		if ((g_talk_tip_times <= 1) || times)
		{
			g_talk_tip_sound = 0;
		}
		else
		{
			g_talk_tip_times--;
		}
		g_talk_tip_count = 0;
	}
	else if ((flag <= TIP_HEADER_LENGTH / 4) && (someone_talking_now() == 0))	// range: 1-32
	{
//		talk_tip_address = get_dsp_image_address() + 8;							// point to the tip index table
//		flash_spi_read_data((uint8_t *)tip_index, talk_tip_address + (flag - 1) * 4, 4);	// read from the tip offset
		talk_tip_address = (uint32_t)tip_lib_array;								// point to the tip index table
		tip_index[0] = *((uint16_t *)(talk_tip_address + (flag - 1) * 4));		// point to the tip offset
		tip_index[1] = *((uint16_t *)(talk_tip_address + (flag - 1) * 4 + 2));	// point to the tip slice

		if (tip_index[1])											// point to the length(unit of the slice is 60ms)
		{
			talk_tip_times = tip_index[1];
			talk_tip_address += TIP_HEADER_LENGTH + tip_index[0];
		}
		else
		{
			talk_tip_times = *((uint16_t *)(talk_tip_address + 2));// the first tip length(keyboard voice default)
			talk_tip_address += TIP_HEADER_LENGTH;
		}

		if (talk_tip_times && (talk_tip_times <= 8))				// point to the length(unit is 60ms), max 480ms
		{
			dsp_speech_out_pa_ctrl(1, SPEECH_PA_CTRL_OUT_TIP);
			g_talk_tip_count = 0;
			g_talk_tip_address = talk_tip_address;
			g_talk_tip_count_total = talk_tip_times * 3;			// P1ÿ��ֻ�ܷ�20ms�����ݣ���ÿ20ms��һ�Σ�ÿ��sliceΪ60ms��Ҫ��3��
			g_talk_tip_times = (times < 60) ? times : (times / (60 * talk_tip_times));
			g_talk_tip_sound = flag;
		}
//		vlog_v("config","tip[%d:%s]", flag, (flag < 6) ? tip_string[flag - 1] : "UNDEF");
//		vlog_v("config","tip[%d][slice=%d, times=%d]", flag, talk_tip_times, g_talk_tip_times);
	}
#ifndef POWER_SAVE_ENABLE_TIP_SOUND
	}
	else
	{
	g_talk_tip_sound = 0;
	g_talk_tip_count = 0;
	}
#endif
}

void printf_waveform_data(uint16_t *wave);
void set_ramp_waveform_high(uint8_t offset)	// offset: dds-offset; 2p-the segment of freq
{
	uint16_t up[48], down[48];

	gen_ramp_waveform(1, offset, up, down);	// high power
#ifdef RF_DEUBG_ENABLE
	vlog_v("config","\r\n== high power rising(offset=%d) ==", offset);
	printf_waveform_data(up);
	vlog_v("config","\r\n== high power falling ==");
	printf_waveform_data(down);
#endif
	dsp_reg_write(REG_RAMP_UP_HIGH_ADDR, up);
	dsp_reg_write(REG_RAMP_DOWN_HIGH_ADDR, down);
}

void set_ramp_waveform_low(uint8_t offset)
{
	uint16_t up[48], down[48];

	gen_ramp_waveform(0, offset, up, down);	// low power
#ifdef RF_DEUBG_ENABLE
	vlog_v("config","\r\n== low power rising(offset=%d) ==", offset);
	printf_waveform_data(up);
	vlog_v("config","\r\n== low power falling ==");
	printf_waveform_data(down);
#endif
	dsp_reg_write(REG_RAMP_UP_LOW_ADDR, up);
	dsp_reg_write(REG_RAMP_DOWN_LOW_ADDR, down);
}

void set_crystal_bias(void)
{
	uint16_t temp;

	temp = (uint16_t)g_factory_inst->crystal_bias;
	dsp_reg_write(REG_CRYSTAL_DC_ADDR, &temp);
}

void set_sync_offset(uint8_t offset)
{
	g_dsp_reg->reg_sync_offset = (uint16_t)offset;
	dsp_reg_write(REG_SYNC_OFFSET_ADDR, &g_dsp_reg->reg_sync_offset);
}

void set_mod_att(uint8_t low_high)	// low_high: 0-low; 1-high; else-tiny
{
	uint16_t temp;

	temp = (low_high == 1) ? 0 : g_factory_inst->mod_att;
	dsp_reg_write(REG_MOD_ATT_ADDR, &temp);
}

void set_ramp_waveform_tiny(uint8_t offset)
{
	uint16_t up[48], down[48];

	gen_ramp_waveform(2, 0, up, down);
	dsp_reg_write(REG_RAMP_UP_MICRO_ADDR, up);
	dsp_reg_write(REG_RAMP_DOWN_MICRO_ADDR, down);
}

void set_noise_threshold_level(void)
{
	int16_t tmp[2];

	tmp[0] = g_factory_inst->rssi_regulator;
//	tmp[0] = -100 - tmp[0];		// dsp original value at -100dB
//	tmp[0] += -130 + maintain_setup_parameter(PARA_OPERATE_READ, NOISE_THRESHOLD_LEVEL, 0) + 100;	// threthold offset by 100
	tmp[0] = -130 + maintain_setup_parameter(PARA_OPERATE_READ, NOISE_THRESHOLD_LEVEL, 0) - tmp[0];
	tmp[1] = tmp[0] - 3;
	dsp_reg_write(REG_ANALOG_SQUELCH_LEVEL_ADDR, (uint16_t *)tmp);
}

void Set_Mic_Gain(uint8_t gain)
{
	if (gain < MICROPHONE_GAIN_LEVEL)
		maintain_setup_parameter(PARA_OPERATE_WRITE, MICROPHONE_PARAS_POS, gain);

//	packet_ctrl_to_mcu_inf_frame(BASEBAND_TYPE_SET_MIC_GAIN, mic_gain_factor[maintain_setup_parameter(PARA_OPERATE_READ, MICROPHONE_PARAS_POS, 0)], 0);
	set_mic_voice_gain(mic_gain_factor[maintain_setup_parameter(PARA_OPERATE_READ, MICROPHONE_PARAS_POS, 0)]);
}

int8_t set_mic_gain_free(int8_t gain)
{
	return set_mic_voice_gain(gain);
}

void Set_Speak_Volume(uint8_t Digital_Vol)
{
	if (Digital_Vol < VOICE_GAIN_LEVEL)
		maintain_setup_parameter(PARA_OPERATE_WRITE, VOICE_PARAS_POS, Digital_Vol);

//	packet_ctrl_to_mcu_inf_frame(BASEBAND_TYPE_SET_VOLUME, (uint32_t)voice_gain_factor[maintain_setup_parameter(PARA_OPERATE_READ, VOICE_PARAS_POS, 0)], 0);
	set_speaker_volume(SET_SPEAKER_VOICE_VOLUME, voice_gain_factor[maintain_setup_parameter(PARA_OPERATE_READ, VOICE_PARAS_POS, 0)]);
}

int8_t set_speaker_volume_free(int8_t vol)
{
	return set_speaker_volume(SET_SPEAKER_VOICE_VOLUME, vol);
}

uint8_t Get_Speak_Volume(void)
{
	return maintain_setup_parameter(PARA_OPERATE_READ, VOICE_PARAS_POS, 0);
}

void Set_Tip_Volume(uint8_t Digital_Vol)	// 0xfe: use maximal; else: use paras setup
{
	if (Digital_Vol < VOICE_GAIN_LEVEL)
		maintain_setup_parameter(PARA_OPERATE_WRITE, TIP_SOUND_PARAS_POS, Digital_Vol);

//	packet_ctrl_to_mcu_inf_frame(BASEBAND_TYPE_SET_VOLUME, (uint32_t)tip_voice_gain_factor[(Digital_Vol == 0xfe) ? (VOICE_GAIN_LEVEL - 1) : maintain_setup_parameter(PARA_OPERATE_READ, TIP_SOUND_PARAS_POS, 0)], 0);
	set_speaker_volume(SET_SPEAKER_TIP_VOLUME, tip_voice_gain_factor[(Digital_Vol == 0xfe) ? (VOICE_GAIN_LEVEL - 1) : maintain_setup_parameter(PARA_OPERATE_READ, TIP_SOUND_PARAS_POS, 0)]);
}

uint8_t Get_Tip_Volume(void)
{
	return maintain_setup_parameter(PARA_OPERATE_READ, TIP_SOUND_PARAS_POS, 0);
}

void mute_speaker(void)
{
	set_speaker_volume(SET_SPEAKER_VOICE_VOLUME, voice_gain_factor[0]);
	set_speaker_volume(SET_SPEAKER_TIP_VOLUME, tip_voice_gain_factor[0]);
}

void set_dsp_op_pointer(void)
{
	dsp_send_cmd = dsp_send_cmd32;
	dsp_reg_read = dsp_reg_read32;
	dsp_reg_write = dsp_reg_write32;

	dsp_enter_save_power = dsp_send_cmd;
}

void soft_set_dsp_work_mode_ex(uint8_t ex_work_mode)
{
	CLR_DSP_EX_WORK_MODE();
	if (ex_work_mode != 0xff)
		SET_DSP_EX_WORK_MODE(ex_work_mode);
	else
		SET_DSP_EX_WORK_MODE(g_runtime_inst_xvbase->misc_config_base.work_mode_ex);
}

void dsp_power_on(void)
{
	uint16_t esb[5];

	sent_dsp_slot = (interphone_mode == INTERPHONE_MODE_ZZW_ADHOC) ? 0 : 1;		// default: zzw-slot only can equal 0; pdt-write data to dsp at 1 and sent at next slot 0

#ifdef FORBIDDEN_SWITCH_Q_SPEED
	g_runtime_inst.runtime_paras.zzw_vocoder.vocoder_type_q = VOCODER_TYPE_Q_SELP1200;
#endif


	dsp_work_mode = ((g_runtime_inst_xvbase->misc_config_base.config_dsp_mode == 0) ||
			(g_runtime_inst_xvbase->misc_config_base.config_dsp_mode > REG_DSP_MODE_WT_MASK + 1)) ? 0 : (0x80 | (g_runtime_inst_xvbase->misc_config_base.config_dsp_mode - 1));
	dsp_work_mode |= set_dsp_work_mode_bit6_2(interphone_mode);
	if ((g_runtime_inst_xvbase->misc_config_base.config_dsp_mode_ex >= 1) && (g_runtime_inst_xvbase->misc_config_base.config_dsp_mode_ex <= REG_DSP_MODE_WT_MASK + 1))
		ex_dsp_work_mode = 0x80 | (g_runtime_inst_xvbase->misc_config_base.config_dsp_mode_ex - 1);
	else
		ex_dsp_work_mode = 0x82;
	soft_set_dsp_work_mode_ex(0xff);
	dsp_rx_int_ex = get_real_rx_int_ex();	// g_runtime_inst_xvbase->f_module_rx_ex;
	esb[0] = BASE_TRANSCODE_VOCODER_TYPE();
	if (esb[0])
	{
		trans_vocoder_type.vocoder_type = esb[0] - 1; /*VOCODER_TYPE_Q_AMBE/VOCODER_TYPE_Q_NVOC/VOCODER_TYPE_Q_SELP1200*/
		trans_vocoder_type.vocoder_speed = BASE_TRANSCODE_VOCODER_SPEED();
		trans_vocoder_type.vocoder_trans_enable = 1;
	}
	else
	{
		trans_vocoder_type.vocoder_trans_enable = 0;
	}
  #ifdef DEBUG_DSP_MODE
	vlog_v("config","\tsoft dsp_mode=%04x,ex_dsp_mode=%04x,freq_ex=%d,transcode type/speed=%d/%d",
		dsp_work_mode, ex_dsp_work_mode, dsp_rx_int_ex, BASE_TRANSCODE_VOCODER_TYPE(),BASE_TRANSCODE_VOCODER_SPEED());
  #endif


	auto_trace_q_vocoder = (interphone_mode == INTERPHONE_MODE_ZZWPRO_Q) ? get_q_vocoder_speed() : (get_vocoder_setup_content() & 0x0f);
	set_dsp_vocoder_length_paras(interphone_mode, auto_trace_q_vocoder & ZZWPRO_VOCODER_TYPE_MASK);

	if ((g_runtime_inst.runtime_paras.adc38p4 == 0) || (abs(g_rf2p_tune_reg->tx_mod_dc8 - g_runtime_inst.runtime_paras.adc38p4) >= 400))
		g_runtime_inst.runtime_paras.adc38p4 = g_rf2p_tune_reg->tx_mod_dc8;
//	reg_mod_gain_dc[2] = g_rf2p_tune_reg->tx_mod_dc8;
	reg_mod_gain_dc[2] = g_runtime_inst.runtime_paras.adc38p4;
	reg_mod_gain_dc[3] = g_rf2p_tune_reg->tx_mod_dc9;
	vlog_v("config","\tADC38.4=%d(ori=%d)", reg_mod_gain_dc[2], g_rf2p_tune_reg->tx_mod_dc8);

	dsp_reg_write(REG_RDATA_LOC_ADDR, &g_dsp_reg->reg_rdata_loc);
	dsp_reg_write(REG_HARDWARE_MODE_ADDR, &g_dsp_reg->reg_hardware_mode);

	set_dsp_digital_mode(SPEECH_USE_DEFAULT, 0);
	set_dsp_analog_mode(SPEECH_USE_DEFAULT, 0);
	set_dsp_analog_mode(REG_ANALOG_MODE_PCM_MASK, g_runtime_inst_xvbase->misc_config_base.mpt_voice_format ? 0 : REG_ANALOG_MODE_PCM_MASK);	// 20230825:ģ���վPCM��ʽ��PCM=0��A�ɣ�PCM=1�����ԣ�(DSP)Ĭ��A��
	set_dsp_interrupt_enable(SPEECH_USE_DEFAULT, 0);
	set_dsp_interrupt_enable(REG_INTERRUPT_ENABLE_SIN_MASK | REG_INTERRUPT_ENABLE_SOUT_MASK, REG_INTERRUPT_ENABLE_SIN_MASK | REG_INTERRUPT_ENABLE_SOUT_MASK);
	set_dsp_pcm_speech_inout(uart2_use_as_voiceport() ? 1 : 0);
#ifdef DEBUG_DSP_WRITE_REG
	vlog_v("config","Default speech");
#endif
	set_dsp_speech_mode(SPEECH_USE_DEFAULT, 0);
	set_vocoder_rate_agc_ns(interphone_mode, 3, get_vocoder_setup_content());
//	sys_delay(100);
//	set_vocoder_rate_agc_ns(interphone_mode, 3, get_vocoder_setup_content());
//	set_to_bt_set(is_bt_insert());	// do NOT init check pin, it is 0 always(NOT insert), set BT at keyboard_check later
	Set_Mic_Gain(0xff);
	Set_Speak_Volume(0xff);
	Set_Tip_Volume(0xff);

	set_channel_gain_modulation(SPEECH_USE_DEFAULT, 0);

	dsp_reg_write(REG_ANALOG_IN_GAIN_ADDR, &g_dsp_reg->reg_analog_in_gain);
	dsp_reg_write(REG_ANALOG_IN_GAIN_SHIFT_ADDR, &g_dsp_reg->reg_analog_in_gain_shift);
#if DEBUG_DSP_WRITE_REG > 1
	vlog_v("config","[DSP ON]ana_in_gain=%d,shift=%d", g_dsp_reg->reg_analog_in_gain, g_dsp_reg->reg_analog_in_gain_shift);
#endif
	dsp_reg_write(REG_ANALOG_OUT_GAIN_ADDR, &g_dsp_reg->reg_analog_out_gain);
	dsp_reg_write(REG_ANALOG_OUT_GAIN_SHIFT_ADDR, &g_dsp_reg->reg_analog_out_gain_shift);
	dsp_reg_write(REG_ANALOG_SUB_LEVEL_ADDR, &g_dsp_reg->reg_analog_sub_level);
	dsp_reg_write(REG_ANALOG_MSK_LEVEL_ADDR, &g_dsp_reg->reg_analog_msk_level);
	dsp_reg_write(REG_ANALOG_AUDIO_ADDR, &g_dsp_reg->reg_analog_audio);
	dsp_reg_write(REG_ANALOG_AUDIO_THRESHOLD_ADDR, &g_dsp_reg->reg_analog_audio_threshold);
	dsp_reg_write(REG_SYNC_OFFSET_ADDR, &g_dsp_reg->reg_sync_offset);
	dsp_reg_write(REG_RESYNC_NUM_ADDR, &g_dsp_reg->reg_resync_offset);

	set_noise_threshold_level();

	esb[0] = 0x1234;
	esb[1] = 0x5678;
	esb[2] = 0x9abc;
	esb[3] = 0xdef0;
	esb[4] = 0x55aa;
	dsp_reg_write(REG_EBS_IN_ADDR, esb);

	esb[0] = 0x9;		// 4bit only
	dsp_reg_write(REG_CC_IN_ADDR, esb);

	set_crystal_bias();

	if (get_mobile_rf_type() != MOBILE_RF_TYPE_DDS)
	{
#ifndef DSP_NEW_FREQ_INTERFACE
		esb[0] = 0;
		dsp_reg_write(REG_DELAY_FRAC_ADDR, esb);
//		vlog_v("config","DELAY_FRAC=0x%04x", esb[0]);
#else
		if ((dsp_incompatible_reg_len == REG_READ_RSSI_DQI_DEMOD_DSP_LEN) && ((g_rf2p_tune_reg->delay_frac == 0) || (g_rf2p_tune_reg->delay_frac == 0xffff)))
		{
			esb[0] = 1175;		// 20200407; 20210221->1175
			dsp_reg_write(REG_DELAY_FRAC_ADDR, esb);
//			vlog_v("config","DELAY_FRAC=0x%04x", esb[0]);
		}
		else
		{
			dsp_reg_write(REG_DELAY_FRAC_ADDR, &g_rf2p_tune_reg->delay_frac);
//			vlog_v("config","DELAY_FRAC=0x%04x", g_rf2p_tune_reg->delay_frac);
		}

		esb[0] = 0x3;		// just active at 2p mode at new dsp interface
		dsp_reg_write(REG_PLL_RX_DAC_ADDR, esb);	// FGU_FS_SW
		esb[0] = 0x4;		// just active at 2p mode at new dsp interface
		dsp_reg_write(REG_PLL_TX_DAC_ADDR, esb);	// APC/TV1
#endif
	}
	else
	{
		set_ramp_waveform_high(0);
//		set_ramp_waveform_low(0);
		set_ramp_waveform_tiny(0);
	}

	if (interphone_mode == INTERPHONE_MODE_ZZW_ADHOC)
	{
		esb[0] = 0;
		maintain_stack_parameter(PARA_OPERATE_READ, STACK_ZZW_ADHOC_WIN, (void *)esb);		// ��ѡ����
		if ((esb[0] == 0) || (esb[0] == 0xff))
			esb[0] = 100;
		dsp_reg_write(REG_ADHOC_WIN_ADDR, esb);

//		maintain_stack_parameter(PARA_OPERATE_READ, STACK_ZZW_ADHOC_RCMAX, (void *)esb);	// ǿ������
		esb[0] = 1;
		dsp_reg_write(REG_ADHOC_RC_MAX_ADDR, esb);
	}
	else if (interphone_mode <= INTERPHONE_MODE_PDT_TRUNKING)
	{
		set_dsp_pcm_gain(16000, 1);
	}

	set_dsp_test_vocoder_pointer(interphone_mode, auto_trace_q_vocoder & ZZWPRO_VOCODER_TYPE_MASK);

	memset(zzwpro_status, 0, sizeof(ZZWPRO_STATUS_STRUCT) * ZZWPRO_TR_STATUS_MAX_SLOT);
	memset(zzwpro_status_ex, 0, sizeof(ZZWPRO_STATUS_STRUCT) * ZZWPRO_TR_STATUS_MAX_SLOT);
	memset(zzwpro_frame, 0, sizeof(ZZWPRO_FRAME_STATISTIC_TYPEDEF) * ZZWPRO_TR_STATUS_MAX_SLOT);

	set_dsp_sync_at_next_second(2);
	dsp_save_power_init(0);

	if (dsp_incompatible_reg_len == REG_READ_RSSI_DQI_DEMOD_DSP_LEN)
	{
		set_dsp_vocoder_config(SPEECH_USE_DEFAULT, 0);
		set_dsp_gps_sync((uint8_t)(g_runtime_inst_xvbase->fpga_write_gcr & FPGA_REG_GCR_60MS_SEL_MASK));

		esb[0] = ((uint16_t)g_static_ptr->misc_static_config.dsp_vad_trailing_time) * 5;	// dsp unit: 20ms; xiepin unit: 100ms
		dsp_reg_write(REG_DSP64_REG_VAD_NUM_ADDR, esb);
		esb[0] = (uint16_t)g_static_ptr->misc_static_config.dsp_vad_sensitivity;
		dsp_reg_write(REG_DSP64_REG_VAD_SHIFT_ADDR, esb);

		if ((interphone_mode == INTERPHONE_MODE_ZZWPRO_N) && ZZWPRO_STACK_IS_MIX_MODE())
			set_vocoder_mix_enable(1);
		set_speech_vad_enable(0);
		check_ext_mic_amp = (uint16_t)ext_mic_amp_table[g_static_ptr->misc_static_config.dsp_vad_sensitivity];
	}

#ifndef PTT_KEY_USE_AS_STICK
  #if !defined VAD_KEY_USE_AS_STICK_PTT && !defined VAD_CONTROL_BY_DSP
	vad_ext_noise = (uint16_t)vad_sensitivity_table[g_static_ptr->misc_static_config.dsp_vad_sensitivity * 2] * 5;
	vad_mic_difference = (uint16_t)vad_sensitivity_table[g_static_ptr->misc_static_config.dsp_vad_sensitivity * 2 + 1] * 5;
  #endif
#endif

	/* just for simulation standard checking - 2018-08-27*/
//	set_analog_audio_th(5000);
//	set_analog_in_paras(23330, 2);
//	set_analog_audio(1);

	if (g_runtime_inst_xvbase->misc_config_base.dsp_30ms_mode <= 1)
	{
		op_dsp_30ms.call_direct = CALL_FALLOW_DISABLE;
		set_dsp_extern_30ms_mode(g_runtime_inst_xvbase->misc_config_base.dsp_30ms_mode);
	}
	else
	{
		op_dsp_30ms.call_direct = CALL_IS_IDLE;
		op_dsp_30ms.call_timeslot = 0;
		set_dsp_extern_30ms_mode(0);
	}

	esb[0] = (g_runtime_inst_xvbase->valid_flag == USER_FLASH_SAVE_FLAG) ? (uint16_t)g_runtime_inst_xvbase->fpga_write_tcr : 0;
	set_dsp_base_30ms_pps_offset(esb[0]);
	if (g_runtime_inst_xvbase->misc_config_base.config_fec == 1)
	{
//		vlog_v("config","force wired");
		set_ccm_vocoder_fec_flag(0);
	}
	else if (g_runtime_inst_xvbase->misc_config_base.config_fec == 2)
	{
//		vlog_v("config","force wireless");
		set_ccm_vocoder_fec_flag(1);
	}
	else if (g_runtime_inst_xvbase->misc_config_base.config_fec == 0)
	{
//		vlog_v("config","trace by BA");
	}
}

void dsp_power_off(void)
{
	dsp_send_cmd(DSP_CMD_RF_POWER_OFF);
}

void dsp_switch_work_freq(uint8_t flag)			// 0: config at post-poweron/reboot(according workmode); 1: config at pre-reboot(force to high freq)
{
#if 0
	uint16_t work_mode;

	if (flag == 0)
	{
		work_mode = maintain_setup_parameter(PARA_OPERATE_READ, WORK_MODE_PARAS_POS, 0);
		if (work_mode <= INTERPHONE_MODE_ZZW_ADHOC)
			work_mode = DSP_CMD_CHANGE_FREQ;
		else
			work_mode = 0;
	}
	else
	{
//		if ((interphone_mode <= INTERPHONE_MODE_ZZW_ADHOC) && (work_mode > INTERPHONE_MODE_ZZW_ADHOC))
//			work_mode = DSP_CMD_CHANGE_FREQ | (1 << REG_DSP_CMD_FREQ_SHIFT);	// ��λǰ������Ƶ
//		else if ((interphone_mode > INTERPHONE_MODE_ZZW_ADHOC) && (work_mode <= INTERPHONE_MODE_ZZW_ADHOC))
//			work_mode = DSP_CMD_CHANGE_FREQ;
//		else
//			work_mode = 0;

//		work_mode = DSP_CMD_CHANGE_FREQ | (1 << REG_DSP_CMD_FREQ_SHIFT);
		if (interphone_mode <= INTERPHONE_MODE_ZZW_ADHOC)
			work_mode = DSP_CMD_CHANGE_FREQ | (1 << REG_DSP_CMD_FREQ_SHIFT);
		else
			work_mode = 0;
	}

	if (work_mode)
	{
//		vlog_v("config","[%d]Set DSP=%cclk...", get_timestamp_measure(), (work_mode == DSP_CMD_CHANGE_FREQ) ? 'L' : 'H');
		dsp_send_cmd(work_mode);
//		vlog_v("config","Done[%d]", get_timestamp_measure());
		vlog_v("config","Set DSP=%cclk...", (work_mode == DSP_CMD_CHANGE_FREQ) ? 'L' : 'H');
	}
#endif
}


typedef struct
{
	uint16_t check_38p4_dead	: 4;
	uint16_t check_38p4_gps_bad	: 4;
	uint16_t check_38p4_cmp		: 2;
	uint16_t check_38p4_act		: 2;
	uint16_t check_38p4_reserved: 4;
}CHECK_38P4_TYPEDEF;
#define CHECK_38P4_PPS_FAIL_DEADLINE	14
#define CHECK_38P4_GPS_FAIL_DEADLINE	3

#define CRYSTAL_FREQUENCE			38400000
uint32_t g_timer2_ccr2 = 0, g_timer2_ccr2_bak = 0, g_38400000hz = 0;
static uint8_t tim_id_check_38p4 = 0xff;
static CHECK_38P4_TYPEDEF check_38p4_update;

void stop_to_calibrate_38p4(uint8_t flag)		// 0-succ, 1-fail
{
	stop_measure_timer();
	dsp_save_power_init(1);
	tim_id_check_38p4 = timer_destroy(tim_id_check_38p4);
	vlog_v("config","END(%s),38.4=%d(%d/%d)(%d),%d/%d/%d/%d", (flag == 0) ? "OK" : "FAIL",
		g_38400000hz, reg_mod_gain_dc[2], g_rf2p_tune_reg->tx_mod_dc8, timestamp_1s,
		check_38p4_update.check_38p4_dead, check_38p4_update.check_38p4_gps_bad, check_38p4_update.check_38p4_cmp, check_38p4_update.check_38p4_act);
}

void check_38p4_is_alive(void)
{
	if (check_38p4_update.check_38p4_cmp == check_38p4_update.check_38p4_act)
	{
		check_38p4_update.check_38p4_dead++;
		if (get_gps_link_state() == 0)
			check_38p4_update.check_38p4_gps_bad++;
		if (check_38p4_update.check_38p4_dead >= CHECK_38P4_PPS_FAIL_DEADLINE)	// ����14��δ��������
		{
			stop_to_calibrate_38p4(1);
			if (check_38p4_update.check_38p4_gps_bad >= CHECK_38P4_GPS_FAIL_DEADLINE)
				set_check38p4_state(0);											// GPSδ����3�Σ�˵��������GPS���ȶ�����PPS����������ȴ���һ�������ٴο�ʼ���������������Ӳ����֧�֣���ֹУ׼
		}
	}
	else
	{
		check_38p4_update.check_38p4_cmp = check_38p4_update.check_38p4_act;
		check_38p4_update.check_38p4_dead = 0;
		check_38p4_update.check_38p4_gps_bad = 0;
	}
}

void start_to_calibrate_38p4(void)
{
	if (((dsp_runtime_misc_flag & DSP_RUNTIME_MISC_CHECK38P4_DONE) == 0) && (tim_id_check_38p4 == 0xff))
	{
		set_check38p4_state(1);
		temp_to_set_dsp_save_power(0, 0, 0);// �ر���Ƶʡ��
		start_measure_timer();
		memset(&check_38p4_update, 0, sizeof(CHECK_38P4_TYPEDEF));
		tim_id_check_38p4 = timer_initial(0, 2200, check_38p4_is_alive);
		vlog_v("config","Start 38.4(%d)", timestamp_1s);
	}
}

#define ADJUST_38P4_TOTAL	6
uint8_t gap_38p4_sav_pos = 0;
int16_t gap_38p4_sum = 0, gap_38p4_queue[ADJUST_38P4_TOTAL] = {0, 0, 0, 0, 0, 0/*, 0, 0, 0, 0, 0, 0, 0, 0, 0*/};
int16_t update_average_38p4_gap(int32_t gap)
{
	gap_38p4_queue[gap_38p4_sav_pos] = gap;

	gap_38p4_sum += gap;

	if (gap_38p4_sav_pos >= ADJUST_38P4_TOTAL - 1)
		gap_38p4_sav_pos = 0;
	else
		gap_38p4_sav_pos += 1;

	gap_38p4_sum -= gap_38p4_queue[gap_38p4_sav_pos];

	return gap_38p4_sum / ADJUST_38P4_TOTAL;
}

#if DEBUG_GPS_SYNC_PPS == 1
  #define OUTPUT_GPS_SYNC_PPS_GAP(cal, gap, locked)	vlog_v("config","\r\nADJ%d:%02d:%02d(%8d %4d/%04d:%4d)", locked, rmc_data.utc_minute, rmc_data.utc_second, g_38400000hz, cal, gap, reg_mod_gain_dc[2])
#elif DEBUG_GPS_SYNC_PPS > 1
  #define OUTPUT_GPS_SYNC_PPS_GAP(cal, gap, locked)	vlog_v("config","\r\n[%3d %3d %3d]ADJ%d:%02d:%02d/%8d-%04d/%04d %d", locked, slot_rising_edge_int_times, slot_middle_int_times, slot_falling_edge_int_times,\
														rmc_data.utc_minute, rmc_data.utc_second, timestamp_gps_2pps, cal, gap, reg_mod_gain_dc[2])
#else
  #define OUTPUT_GPS_SYNC_PPS_GAP(cal, gap, locked)
#endif
void adjust_crystal_freq(void)
{
	int16_t crystal_freq_gap_cal, crystal_freq_gap;

  #ifdef DEBUG_GPS_SYNC_PPS
	timestamp_gps_2pps = get_timestamp_measure();
  #endif

	check_38p4_update.check_38p4_act++;
//	vlog_v("config","GPS:PPS act(%d/%d),lock=%d,bad=%d(%d)", check_38p4_update.check_38p4_act, check_38p4_update.check_38p4_cmp,
//		get_gps_link_state(), check_38p4_update.check_38p4_gps_bad, reg_mod_gain_dc[2]);
	g_38400000hz = abs(g_timer2_ccr2 - g_timer2_ccr2_bak);
	g_timer2_ccr2_bak = g_timer2_ccr2;
	crystal_freq_gap_cal = g_38400000hz - CRYSTAL_FREQUENCE;
	crystal_freq_gap = abs(crystal_freq_gap_cal);
	if (get_gps_link_state() && (crystal_freq_gap < 2000))	// adj_38p4_queue
	{
#ifdef CHECK_PPS_WITH_CRYSTAL_38P4M
		set_gps_pps_state(1);
#endif
		if (g_runtime_inst_xvbase->misc_config_base.anti_pps_shake)
		{
			crystal_freq_gap = update_average_38p4_gap(crystal_freq_gap_cal);
			OUTPUT_GPS_SYNC_PPS_GAP(crystal_freq_gap_cal, crystal_freq_gap, 1);
			crystal_freq_gap = abs(crystal_freq_gap);
		}
		else
		{
			OUTPUT_GPS_SYNC_PPS_GAP(crystal_freq_gap_cal, crystal_freq_gap, 1);
		}

		if ((crystal_freq_gap > 2) && (reg_mod_gain_dc[2] > g_rf2p_tune_reg->tx_mod_dc8 / 2) && (reg_mod_gain_dc[2] < g_rf2p_tune_reg->tx_mod_dc8 * 2))
		{
			if (crystal_freq_gap <= 6)
			{
				if (g_38400000hz > CRYSTAL_FREQUENCE)
					reg_mod_gain_dc[2] -= 1;
				else
					reg_mod_gain_dc[2] += 1;
			}
			else
			{
				if (g_38400000hz > CRYSTAL_FREQUENCE)
					reg_mod_gain_dc[2] -= 9;
				else
					reg_mod_gain_dc[2] += 11;
			}

			g_runtime_inst.runtime_paras.adc38p4 = reg_mod_gain_dc[2];
			reg_mod_gain_dc[2] |= 0x8000;
		}
		else
		{
			if (tim_id_check_38p4 != 0xff)
				stop_to_calibrate_38p4(0);		// У׼�ɹ�������ѳ��ޣ���ֹ
		}
	}
	else
	{
#ifdef CHECK_PPS_WITH_CRYSTAL_38P4M
		set_gps_pps_state(0);
#endif
		OUTPUT_GPS_SYNC_PPS_GAP(crystal_freq_gap_cal, crystal_freq_gap, get_gps_link_state());
	}
}

void print_crystal(void)
{
	vlog_v("config","38.4=%d(adc=%d),19.2=%d", g_38400000hz, reg_mod_gain_dc[2], dsp_read_19m2_crystal());
}

//////////////////////////////////////////////////////////////////////////////////
////////////////////////// call by system only ///////////////////////////////////
uint16_t set_dsp_work_mode_bit6_2(uint8_t work_mode)
{
	uint16_t tmp = 0;

	if (work_mode == INTERPHONE_MODE_ZZWPRO_Q)
		tmp |= (0x0001 << REG_DSP_MODE_DM_SHIFT);
	else if (work_mode == INTERPHONE_MODE_ZZWPRO_N)
		tmp |= (0x0002 << REG_DSP_MODE_DM_SHIFT);
	else if ((work_mode == INTERPHONE_MODE_ZZWPRO_V) || (work_mode == INTERPHONE_MODE_ZZWPRO_V25K)) // not use it at new dsp version
		tmp |= (0x0003 << REG_DSP_MODE_DM_SHIFT);
	else if (work_mode == INTERPHONE_MODE_MPT_TRUNKING)
		tmp |= (0x0000 << REG_DSP_MODE_DM_SHIFT) | (0x0001 << REG_DSP_MODE_WM_SHIFT) | (0x0001 << REG_DSP_MODE_WT_SHIFT);	// ���ǵ��ն˵������ģ���ͬʱ��λbit1-0
	else if (work_mode == INTERPHONE_MODE_MPT_CONV)
		tmp |= (0x0000 << REG_DSP_MODE_DM_SHIFT) | (0x0001 << REG_DSP_MODE_WM_SHIFT) | (0x0000 << REG_DSP_MODE_WT_SHIFT);
//	else if (work_mode == INTERPHONE_MODE_PDT_CONV)
//	else
//		tmp |= (0x0000 << REG_DSP_MODE_DM_SHIFT); // pdt digital base, support pdt conventional only now(20190723)

	return tmp;
}

void set_dsp_work_mode(uint32_t mode)	// user should call the dsp_work_mode_switch()
{
//	dsp_work_mode = mode;
}

uint8_t set_base_dsp_work_mode(uint8_t op)				// op: 0-conv terminal, 1-trunk terminal, 2-base, 3-freq div terminal
{
	return dsp_work_mode;
}

uint16_t set_iq_inv_mode(uint8_t mode)
{
	if (mode)
		g_dsp_reg->reg_hardware_mode |= REG_HARDWARE_MODE_INV_MASK;
	else
		g_dsp_reg->reg_hardware_mode &= ~REG_HARDWARE_MODE_INV_MASK;

	dsp_reg_write(REG_HARDWARE_MODE_ADDR, &g_dsp_reg->reg_hardware_mode);

#if DEBUG_DSP_WRITE_REG > 1
	vlog_v("config","hardware_mode=0x%04X(iq inv=%d)", g_dsp_reg->reg_hardware_mode, mode);
#endif

	return g_dsp_reg->reg_hardware_mode;
}

uint16_t set_vad_enable_to_dsp_xu(uint8_t en)
{
	if (en)
		g_dsp_reg->reg_hardware_mode |= REG_HARDWARE_MODE_VAD_ENABLE_MASK;
	else
		g_dsp_reg->reg_hardware_mode &= ~REG_HARDWARE_MODE_VAD_ENABLE_MASK;

	dsp_reg_write(REG_HARDWARE_MODE_ADDR, &g_dsp_reg->reg_hardware_mode);

#if DEBUG_DSP_WRITE_REG > 1
	vlog_v("config","hardware_mode=0x%04X(vad=%d)", g_dsp_reg->reg_hardware_mode, en);
#endif

	return g_dsp_reg->reg_hardware_mode;
}

void set_rf_carrier_mode(uint8_t onoff)
{
	if (onoff == 0)		// send carrier stop
		g_dsp_reg->reg_hardware_mode &= ~REG_HARDWARE_MODE_TX_MASK;	// reset to normal
	else
		g_dsp_reg->reg_hardware_mode |= REG_HARDWARE_MODE_TX_MASK;	// 3: send carrier

	dsp_reg_write(REG_HARDWARE_MODE_ADDR, &g_dsp_reg->reg_hardware_mode);
#if DEBUG_DSP_WRITE_REG > 1
	vlog_v("config","hardware_mode=0x%04X(rf carr=%d)", g_dsp_reg->reg_hardware_mode, onoff);
#endif
}

void set_rf_tr_freq_different(uint8_t flag)	// 0-equal; else-different
{
//	vlog_v("config","[set freq]flag=%d,hardware_mode=%04x", flag, g_dsp_reg->reg_hardware_mode);
	if (flag == 0)
		g_dsp_reg->reg_hardware_mode &= ~REG_HARDWARE_MODE_FREQ_MASK;	// tx == rx
	else
		g_dsp_reg->reg_hardware_mode |= REG_HARDWARE_MODE_FREQ_MASK;	// tx != rx

	dsp_reg_write(REG_HARDWARE_MODE_ADDR, &g_dsp_reg->reg_hardware_mode);
#if DEBUG_DSP_WRITE_REG > 1
	vlog_v("config","hardware_mode=0x%04X(tr freq diff=%d)", g_dsp_reg->reg_hardware_mode, flag);
#endif
}
/*
const uint8_t cheji_electrically_tunable_table[2][10] = {
//	{32, 41, 49, 58, 67, 76, 86, 95, 104, 107},	// tx
//	{37, 46, 54, 67, 74, 83, 95, 102, 112, 120}	// rx
//	{36, 45, 52, 62, 71, 78, 86, 93, 104, 113},	// tx
//	{41, 50, 59, 68, 78, 89, 97, 107, 117, 126}	// rx

//	{38, 46, 54, 62, 72, 80, 90, 98, 108, 117},	// tx
	{22, 30, 38, 46, 54, 62, 72, 80, 90, 98},	// tx, 20161031
//	{46, 56, 65, 73, 82, 91, 101, 109, 117, 125}	// rx
	{31, 38, 46, 56, 65, 73, 82, 91, 101, 109}	// rx, 20161024
};
uint16_t get_cheji_electrically_tunable_filter_adc(uint8_t tr, float mhz)	// tr: 0-tx; else-rx
{
	uint8_t tv;

	if ((mhz > 349.9875) && (mhz < 355.0))
		tv = 0;
	else if ((mhz > 354.9875) && (mhz < 360.0))
		tv = 1;
	else if ((mhz > 359.9875) && (mhz < 365.0))
		tv = 2;
	else if ((mhz > 364.9875) && (mhz < 370.0))
		tv = 3;
	else if ((mhz > 369.9875) && (mhz < 375.0))
		tv = 4;
	else if ((mhz > 374.9875) && (mhz < 380.0))
		tv = 5;
	else if ((mhz > 379.9875) && (mhz < 385.0))
		tv = 6;
	else if ((mhz > 384.9875) && (mhz < 390.0))
		tv = 7;
	else if ((mhz > 389.9875) && (mhz < 395.0))
		tv = 8;
//	else if ((mhz > 394.9875) && (mhz < 400.0))
	else
		tv = 9;

	return cheji_electrically_tunable_table[tr ? 1 : 0][tv];
}
*/


/********************************************* �������� ********************************************/
extern uint8_t *p_admitance_testing_data;
extern uint8_t admit_test_start_flag;
extern uint8_t admit_test_data_index;
extern const uint16_t rssi_disp_coor[2][8];

void get_ber_from_dsp(uint32_t *error, uint32_t *total, uint32_t *lost)
{
	uint16_t ber[REG_READ_REG_BER_TEST_STATE_LEN];

	dsp_reg_read(admit_test_mode_is_ex() ? REG_DSP64_REG_BER_TEST_STATE_EX_ADDR : REG_BER_TEST_STATE_ADDR, ber);
	*error = combine_short_to_long(ber[0], ber[1]);
	*total = combine_short_to_long(ber[2], ber[3]);
	*lost = combine_short_to_long(ber[4], ber[5]);
}

void print_rssi_dqi_ber(uint8_t mode, uint8_t to_uart)
{
	char str_err[20], str_total[20], str_lost[20], str_rate[20];
	uint16_t ber[8];
	uint32_t err, total, lost;

	get_ber_from_dsp(&err, &total, &lost);

	if (mode == 0)						// admit test
	{
		sprintf(str_err, "E%08d", err);
		sprintf(str_total, "T%08d", total);
		sprintf(str_lost, "L%08d", lost);
		sprintf(str_rate, "%02d%%", err * 100 / total);
		memcpy(ber, rssi_disp_coor[0], sizeof(uint16_t) * 8);
	}
	else
	{
		sprintf(str_err, "%08d", err);
		sprintf(str_total, "%08d", total);
		sprintf(str_lost, "%08d", lost);
		sprintf(str_rate, "%02d%%[%d][%03d]", err * 100 / total, get_rssi_value_real(), dqi_prev_slot);
		memcpy(ber, rssi_disp_coor[1], sizeof(uint16_t) * 8);
	}

	if (to_uart == 0)
	{
		GL_LCDPrintString(ber[0], ber[1], (uint8_t *)str_err, LCD_COLOR_WHITE, back_ground_color, ADMIT_TEST_FONT_STYLE, FONT_NONTRANSPARENT);
		GL_LCDPrintString(ber[2], ber[3], (uint8_t *)str_total, LCD_COLOR_WHITE, back_ground_color, ADMIT_TEST_FONT_STYLE, FONT_NONTRANSPARENT);
		GL_LCDPrintString(ber[4], ber[5], (uint8_t *)str_lost, LCD_COLOR_WHITE, back_ground_color, ADMIT_TEST_FONT_STYLE, FONT_NONTRANSPARENT);
		GL_LCDPrintString(ber[6], ber[7], (uint8_t *)str_rate, LCD_COLOR_WHITE, back_ground_color, ADMIT_TEST_FONT_STYLE, FONT_NONTRANSPARENT);
	}
	else
	{
		vlog_v("config","%s %s %s %s ", str_err, str_total, str_lost, str_rate);
		if (to_uart != 0xff)
		{
			vlog_v("config","[%d %d %d %d %d %d][%d %d %d %d %d %d]",
				zzwpro_status[0].rssi - 130, zzwpro_status[1].rssi - 130, zzwpro_status[2].rssi - 130,
				zzwpro_status[3].rssi - 130, zzwpro_status[4].rssi - 130, zzwpro_status[5].rssi - 130,
				zzwpro_status[0].dqi, zzwpro_status[1].dqi, zzwpro_status[2].dqi,
				zzwpro_status[3].dqi, zzwpro_status[4].dqi, zzwpro_status[5].dqi);
		}
	}
}

static const uint8_t admit_test_ber_string_head[] = "\r\n+BEROUT:2,6,   ,";
void admit_test_check_reveive_data(void)
{
	static uint8_t admit_test_ber_frame = 0;
	uint16_t demod_stat[REG_READ_RSSI_DQI_DEMOD_REAL_LEN], ber[6];
	uint8_t *ptr8;

//	if (int_stat & REG_INTERRUPT_STATE_DATA_MASK)			// receive air data
	{
		dsp_read_demod_state(demod_stat);
		if ((demod_stat[0] & REG_DEMOD_STATE_TYPE_MASK) == 1)	// voice
		{
			if (((admit_test_ber_frame == 0) && (((demod_stat[0] & REG_DEMOD_STATE_CNT_MASK) >> REG_DEMOD_STATE_CNT_SHIFT) == 0))/* super frame start */ || admit_test_ber_frame)
			{
				dsp_reg_read(REG_PACKET_OUT_ADDR, (uint16_t *)stack_call_buffer);
				ntohs_mul((uint16_t *)stack_call_buffer, REG_PACKET_OUT_LENGTH);
				ptr8 = (uint8_t *)stack_call_buffer;

				sprintf((char *)&stack_message_buffer[128] + p_strlen(&stack_message_buffer[128]),
					"%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X",
					ptr8[3], ptr8[4], ptr8[5], ptr8[6], ptr8[7], ptr8[8], ptr8[9], ptr8[10], ptr8[11], ptr8[12], ptr8[13], ptr8[14], ptr8[15],
					(ptr8[16] & 0xF0) | (ptr8[22] & 0x0F),
					ptr8[23], ptr8[24], ptr8[25], ptr8[26], ptr8[27], ptr8[28], ptr8[29], ptr8[30], ptr8[31], ptr8[32], ptr8[33], ptr8[34], ptr8[35]);

				if (admit_test_ber_frame >= 5)
				{
					dsp_reg_read(admit_test_mode_is_ex() ? REG_DSP64_REG_BER_TEST_STATE_EX_ADDR : REG_BER_TEST_STATE_ADDR, ber);
					demod_stat[0] = combine_short_to_long(ber[0], ber[1]) * 1000 / combine_short_to_long(ber[2], ber[3]);
					if (demod_stat[0] > 999)
						demod_stat[0] = 999;
					sprintf((char *)ber, "%03d", demod_stat[0]);
					((uint8_t *)&stack_message_buffer[128])[14] = ((uint8_t *)ber)[0];
					((uint8_t *)&stack_message_buffer[128])[15] = ((uint8_t *)ber)[1];
					((uint8_t *)&stack_message_buffer[128])[16] = ((uint8_t *)ber)[2];
					p_strcat(&stack_message_buffer[128], "");
					admit_test_ber_frame = 0;
					vlog_v("config","%s", (char *)&stack_message_buffer[128]);
					((uint8_t *)&stack_message_buffer[128])[p_strlen(admit_test_ber_string_head)] = 0;
				}
				else
				{
					admit_test_ber_frame++;
				}
			}
		}
	}
}

void high_rp_protect_at_carrier(uint8_t rp_is_high);
void dsp_hpi_interrupt_for_admit_test(void)
{
	uint8_t work_mode, dsp_cmd, total_slots;
//	uint16_t mod_stat;
	int16_t  admit_dqi[REG_READ_RSSI_DQI_DEMOD_REAL_LEN];
	uint32_t zzwpro_signaling[VPLUS_SIGNALING_BUF_LENGTH / sizeof(uint32_t)], tmp_data[SIGNALING_XV_MAX_LENGTH / sizeof(uint32_t)];

	slot_middle_int_times++;

	average_power_check_adc(sent_dsp_slot);

	speech_pa_ctrl_process();

	dsp_reg_read(REG_INTERRUPT_STATE_ADDR, &int_stat);

//	dsp_reg_read(REG_MOD_STATE_ADDR, &mod_stat);
//	sent_dsp_slot = (mod_stat & REG_MOD_STATE_SLOT_MASK) ? 1 : 0;		// 0: slot 0; 1: slot 1

	work_mode = get_admit_test_workmode();
	if (int_stat & REG_INTERRUPT_STATE_RDATA_MASK)
	{
		if ((work_mode == INTERPHONE_MODE_ZZWPRO_N) || (work_mode == INTERPHONE_MODE_ZZWPRO_V) || (work_mode == INTERPHONE_MODE_ZZWPRO_V25K))
		{
			total_slots = ZZWPRO_TR_STATUS_MAX_SLOT;
			if (++sent_dsp_slot >= ZZWPRO_TR_STATUS_MAX_SLOT)
				sent_dsp_slot = 0;
		}
		else if (work_mode == INTERPHONE_MODE_ZZWPRO_Q)
		{
			total_slots = 1;
			sent_dsp_slot = 0;
		}
		else
		{
			total_slots = 2;
			sent_dsp_slot = (sent_dsp_slot + 1) & 0x01;
		}

		if (sent_dsp_slot == 0)
		{
			zzwpro_slot_end_process(total_slots);

			high_rp_protect_at_carrier(is_high_rp_protect());
		}

		if (work_mode != INTERPHONE_MODE_ZZW_ADHOC)
		{
			if ((sent_dsp_slot == 1) || (work_mode == INTERPHONE_MODE_ZZWPRO_Q) || get_admit_test_send_all_slot())
			{
				if (((admit_test_start_flag && (admit_test_start_flag != 0xff)) && ((admit_test_data_index & 0x0F) < 2)) || super_frame_count)
				{
					if (work_mode < INTERPHONE_MODE_ZZW_ADHOC)
					{
						memcpy(vocoder_data, p_admitance_testing_data + super_frame_count * REG_PACKET_IN_LENGTH * 2, REG_PACKET_IN_LENGTH * 2);
						ntohs_mul(vocoder_data, REG_PACKET_IN_LENGTH);
						if (is_high_rp_protect() == 0)
						{
							dsp_reg_write(REG_PACKET_IN_ADDR, vocoder_data);
							dsp_send_cmd(DSP_CMD_FRAME_INFO);
						}
						if (++super_frame_count >= 6)
							super_frame_count = 0;
					}
					else
					{
						memcpy(vocoder_data, xv_test_1031, vocoder_data_fr_dsp_length * sizeof(uint16_t));
						ntohs_mul(vocoder_data, vocoder_data_fr_dsp_length);			// ������������ʱ��DSP������ߵ�ַ���赹�õ��͵�ַ������U8ȡ����ֵ
						vocoder_to_signaling_xv((uint8_t *)vocoder_data, zzwpro_signaling, vocoder_data_fr_dsp_length, zzwpro_vocoder_one_frame_fr_dsp);
						zzwpro_signaling[0] = 0xA55A3412;
						if (work_mode == INTERPHONE_MODE_ZZWPRO_Q)
						{
							memcpy(tmp_data, (uint8_t *)zzwpro_signaling + SIGNALING_XV_STACK_EMBEDDED, SIGNALING_XV_Q_MAX_LENGTH);	//Q2400/600 have 2frames at one slot
							memcpy((uint8_t *)zzwpro_signaling + 3, tmp_data, SIGNALING_XV_Q_MAX_LENGTH);
							dsp_cmd = auto_trace_q_vocoder & ZZWPRO_VOCODER_TYPE_MASK;
//							dsp_cmd = (dsp_cmd == VOCODER_TYPE_Q_SELP1200) ? DSP_CMD_FRAME_MRG_1200 : ((dsp_cmd == VOCODER_TYPE_Q_SELP600) ? DSP_CMD_FRAME_MRG_600 : DSP_CMD_FRAME_MRG_2400);	// set mod_type at runtime_to_switch_dsp_mod
//							dsp_cmd = (dsp_cmd == VOCODER_TYPE_Q_SELP1200) ? DSP_CMD_FRAME_MRG_1200 : DSP_CMD_FRAME_MRG_600;	// do NOT use MRG2400 because cmd 23 use as DSP_CMD_SYNC_NEXT_SECOND
							dsp_cmd = DSP_CMD_FRAME_MRG_1200;	// do NOT use MRG600 because cmd 25 use as DSP_CMD_CHANGE_FREQ-20210401
						}
						else if (work_mode == INTERPHONE_MODE_ZZWPRO_V25K)
						{
							memcpy(tmp_data, (uint8_t *)zzwpro_signaling + SIGNALING_XV_STACK_EMBEDDED, SIGNALING_XV_NV25K_MAX_LENGTH);
							memcpy((uint8_t *)zzwpro_signaling + 3, tmp_data, SIGNALING_XV_NV25K_MAX_LENGTH);
							dsp_cmd = DSP_CMD_FRAME_ADHOC19K2;
						}
						else
						{
							dsp_cmd = (work_mode == INTERPHONE_MODE_ZZWPRO_N) ? DSP_CMD_FRAME_VDT19K2_VOICE : DSP_CMD_FRAME_ADHOC48K;
						}

						ntohs_mul((uint16_t *)zzwpro_signaling, zzw_xv_frame_to_dsp_length);	// ���ֽ�ʱ�����ú����һ���ֽڷ��õ��ߵ�ַ��DSP�Ӹߵ�ַȡ
						if (is_high_rp_protect() == 0)
						{
							dsp_reg_write(REG_DSP64_ADHOC_PACKET_IN_ADDR, (uint16_t *)zzwpro_signaling);
							dsp_send_cmd(dsp_cmd);
						}
					}
				}
			}

			if ((admit_test_data_index & 0xF0) == 0)
			{
				print_rssi_dqi_ber(0, 0);
			}
		}
		else
		{
			if (((admit_test_start_flag && (admit_test_start_flag != 0xff)) && ((admit_test_data_index & 0x0F) < 2)) || super_frame_count)
			{
				memcpy(stack_call_buffer, p_admitance_testing_data + super_frame_count * REG_PACKET_IN_LENGTH * 2, REG_PACKET_IN_LENGTH * 2);
				signaling_to_vocoder((uint8_t *)vocoder_data, (uint8_t *)stack_call_buffer, 0, 0, 1);
				ntohs_mul(vocoder_data, REG_VOCODER_DATA_IN_LENGTH);
				if (is_high_rp_protect() == 0)
				{
					dsp_reg_write(REG_PACKET_IN_ADDR, vocoder_data);
					dsp_send_cmd(DSP_CMD_FRAME_VOICE);
				}
				if (++super_frame_count >= 6)
					super_frame_count = 0;
			}
			print_rssi_dqi_ber(0, 0);
		}
	}

	if (int_stat & REG_INTERRUPT_STATE_DATA_MASK)			// receive air data
	{
		zzwpro_status[sent_dsp_slot].sta.rx = ZZWPRO_STATUS_BUSY;
		zzwpro_status[sent_dsp_slot].rssi = get_rssi_value_rel_imme(0);
		dsp_read_dqi(admit_dqi);
		zzwpro_status[sent_dsp_slot].dqi = (uint8_t)admit_dqi[0];

		if (g_print_adc & 0x02)
		{
			vlog_v("config","\t[%d %04X]", sent_dsp_slot, int_stat);
			print_rssi_dqi_ber(0, 1);
		}

		if ((get_admit_test_mode() == 0) && (work_mode <= INTERPHONE_MODE_PDT_TRUNKING))
			admit_test_check_reveive_data();
	}

	CHECK_AND_ADJUST38P4();
}

//#define SCANNOISE_PRINT_MEASURE_TIME		1
#ifdef  SCANNOISE_PRINT_MEASURE_TIME
static uint32_t timestamp_scannoise_bak = 0;
#endif
void send_config_module_freq_frame(void);
uint32_t stack_call_interface_conv_scan_noise(uint32_t type, uint32_t *aux1, uint32_t *aux2, uint32_t buff, uint16_t buff_len)
{
	uint16_t a_rssi[2];
	int16_t noise;
	float tx, rx;
#ifdef  SCANNOISE_PRINT_MEASURE_TIME
	uint32_t timestamp_scannoise;
#endif

	switch (type)
	{
		case STACK_CALL_TYPE_READ_VERSION:
			set_stack_version_same_as_main();
			break;

		case STACK_CALL_TYPE_SLOT_SYNC:
//			update_xvbase_freq();
			send_config_module_freq_frame();
			break;

		case STACK_CALL_TYPE_INQUIRY:
			if (*aux1 == 0)
			{
				if (conv_scan_noise_counter <= scan_noise_total_pointers)
				{
					if (conv_scan_noise_counter)		// first point is the original frequency
					{
						dsp_reg_read(REG_ANALOG_RSSI_ADDR, (uint16_t *)a_rssi);
						noise = CAL_RSSI_VALUE_REAL(a_rssi[0]);
						chan_to_freq(interphone_mode, &tx, &rx, conv_scan_noise_base_freq + conv_scan_noise_counter - 1);
#ifdef  SCANNOISE_PRINT_MEASURE_TIME
						timestamp_scannoise = get_timestamp_measure();
						vlog_v("config","[%d]%3.4f=%d(%04x)(%dms,aux1=%d)", conv_scan_noise_counter - 1, rx, noise, (uint16_t)noise,
							get_measure_timer_difference(timestamp_scannoise, timestamp_scannoise_bak), *aux1);
						timestamp_scannoise_bak = timestamp_scannoise;
#endif
						draw_scanning_noise_point(conv_scan_noise_counter - 1, noise);
						if (conv_scan_noise_counter == scan_noise_total_pointers)
						{
							scanning_noise_done();
						}
					}
//					config_pll_paras(interphone_mode, 0x40, conv_scan_noise_base_freq + conv_scan_noise_counter);	// 20221013:Ŀǰ��վ����ʹ�ô˽ӿ���Ƶ
					chan_to_freq(interphone_mode, &tx, &rx, conv_scan_noise_base_freq + conv_scan_noise_counter);
					config_pll_paras_base(interphone_mode, CONFIG_BASE_FREQ_NO_PRINT | CONFIG_BASE_FREQ_EXCLUDE_EX, (uint16_t)(rx * 80), (uint16_t)(tx * 80));
					conv_scan_noise_counter++;
				}
			}
			break;

//		case STACK_CALL_TYPE_AIR_DATA:
//			break;

		default:
			break;
	}

	return STACK_RETURN_TYPE_NULL_REALLY;
}

void set_scanning_noise_enable(uint16_t base_freq, uint16_t total_points, uint8_t draw_lcd, uint8_t save_stack_pointer)	// save_stack_pointer: bit0:1-save stack pointer; bit1:1-save history record
{
	if (base_freq == 0xffff)
	{
		conv_scan_noise_counter = 0xff00;
		p_stack_call_interface = (uint32_t (*)(uint32_t, uint32_t *, uint32_t *, uint32_t, uint16_t))conv_scan_noise_bak_p_stack;
	}
	else
	{
		conv_scan_noise_base_freq = base_freq;
		scan_noise_total_pointers = total_points;
		conv_scan_noise_counter = 0;
		conv_scan_noise_draw_index = draw_lcd ? 0 : 0xffff;
		if ((save_stack_pointer & SCAN_NOISE_SAVE_HISTORY_RECORD) == 0)
			clear_scanning_noise_pattern();
		if (save_stack_pointer & SCAN_NOISE_SAVE_STACK_POINTER)
		{
			conv_scan_noise_bak_p_stack = (uint32_t)p_stack_call_interface;
			p_stack_call_interface = stack_call_interface_conv_scan_noise;
		}
	}
}

uint8_t module_type_is_scan_noise(void)
{
	return (p_stack_call_interface == stack_call_interface_conv_scan_noise) ? 1 : 0;
}

void set_dsp_middle_handler(uint32_t p_func);
void set_dsp_ber_test_mode(uint8_t flag)
{
	set_dsp_digital_mode(REG_DIGITAL_MODE_O153_MASK, flag ? REG_DIGITAL_MODE_O153_MASK : (~REG_DIGITAL_MODE_O153_MASK));
}

void set_dsp_ber_test_mode_ex(uint8_t flag)
{
	set_dsp_digital_mode(REG_DIGITAL_MODE_O153_EX_MASK, flag ? REG_DIGITAL_MODE_O153_EX_MASK : (~REG_DIGITAL_MODE_O153_EX_MASK));
}

void set_dsp_gps_sync(uint8_t flag)	// 0-time slot follow with FPGA; 1-time slot follow with DSP
{
	set_dsp_digital_mode(REG_DIGITAL_MODE_GPS_MASK, flag ? REG_DIGITAL_MODE_GPS_MASK : (~REG_DIGITAL_MODE_GPS_MASK));
#ifdef DEBUG_DSP_WRITE_REG
	vlog_v("config","\t30ms=%s", flag ? "DSP" : "FPGA/GPS");
#endif
}

void set_dsp_mode_sync(uint32_t flag)
{
	if (flag)
	{
		dsp_runtime_misc_flag &= ~DSP_RUNTIME_MISC_SYNC_WITH_BASE;
		set_dsp_digital_mode(REG_DIGITAL_MODE_SYNC_MASK, REG_DIGITAL_MODE_SYNC_MASK);
	}
	else
	{
		dsp_runtime_misc_flag |= DSP_RUNTIME_MISC_SYNC_WITH_BASE;
		set_dsp_digital_mode(REG_DIGITAL_MODE_SYNC_MASK, ~REG_DIGITAL_MODE_SYNC_MASK);
	}
//	vlog_v("config","set dsp sync=%d", flag);
}

void set_dsp_fast_forward(uint8_t flag)
{
	if (interphone_mode == INTERPHONE_MODE_ZZWPRO_N)
		set_dsp_digital_mode(REG_DIGITAL_MODE_HALF_MASK, flag ? REG_DIGITAL_MODE_HALF_MASK : (~REG_DIGITAL_MODE_HALF_MASK));
}

void admittance_testing_dsp_init(void)
{
	auto_trace_nv25k = 0;

	if (admit_test_mode_is_ex())
	{
		soft_set_dsp_work_mode_ex(get_admit_test_workmode());
		set_dsp_ber_test_mode_ex(1);
	}
	else
	{
		soft_set_dsp_work_mode_ex(0xff);
		set_dsp_ber_test_mode(1);				// zzw send data decide by DSP
	}

	if (get_admit_test_send_all_slot() == 0)	// single slot transmit
	{
		set_dsp_work_mode((uint32_t)get_admit_test_workmode());
	}
	else
	{
		// pdt send data decide by DSP(both 2 slots send O.153 for ��վ���� 20211108): set_dsp_ber_test_mode(1)
		set_dsp_work_mode((uint32_t)get_admit_test_workmode());
	}
	set_dsp_middle_handler((uint32_t)dsp_hpi_interrupt_for_admit_test);
	super_frame_count = 0;
	p_strcpy(&stack_message_buffer[128], admit_test_ber_string_head);
	if (admit_test_mode_is_ex())
		memset(zzwpro_status_ex, 0, sizeof(ZZWPRO_STATUS_STRUCT) * ZZWPRO_TR_STATUS_MAX_SLOT);
	else
		memset(zzwpro_status, 0, sizeof(ZZWPRO_STATUS_STRUCT) * ZZWPRO_TR_STATUS_MAX_SLOT);
}

void admittance_testing_clear_berr(uint16_t index)
{
	uint16_t tmp = REG_BER_TEST_MODE_CLR_MASK;

	dsp_reg_write(admit_test_mode_is_ex() ? REG_DSP64_REG_BER_TEST_MODE_EX_ADDR : REG_BER_TEST_MODE_ADDR, &tmp);
}


#if 0	/* do not use those function*/


uint16_t uart_cmd_state;
uint16_t uart_cmd_addr;
uint16_t uart_cmd_read_write;
uint16_t uart_cmd_data_index;
uint16_t uart_cmd_data_len;
uint16_t uart_cmd_data_buf[UART_CMD_DATA_BUF_LEN];
uint8_t  uart_cmd_check_sum;

uint16_t uart_cmd_read_fifo[100];
uint16_t uart_cmd_read_fifo_in=0;
uint16_t uart_cmd_read_fifo_out=0;
uint16_t uart_cmd_read_fifo_cnt=0;


#define TOTAL_PARAS_LENGTH		0x4000
#define DATA_HEADER1			0x16
#define DATA_HEADER2			0xe9
#define CMD_RET_NULL			0x00
#define CMD_RET_WRITE_DSP_REG	0x01
#define CMD_RET_READ_DSP_REG	0x02

#define CMD_WRITE_DSP_REG		0xff
#define CMD_READ_DSP_REG		0x00

/********************************************* ͨ��Э�鶨�� ********************************************

1. ��λ�����
	a) DATA_HEADER1 DATA_HEADER2 CMD_WRITE_DSP_REG addr_h addr_l length DATA checksum	// ����DSP�Ĵ�����У����������ֽڿ�ʼ���㣨��������
	b) DATA_HEADER1 DATA_HEADER2 CMD_READ_DSP_REG addr_h addr_l	checksum				// ��ȡDSP�Ĵ�����У����������ֽڿ�ʼ���㣨��������
2. �豸Ӧ��
	a) ����Ӧ
	b) DATA_HEADER1 DATA_HEADER2 CMD_WRITE_DSP_REG addr_h addr_l length DATA checksum
3. ����
	a) ���ڲ�����460800bps / ����λ8 / ֹͣλ1 / ����żУ�� / ��Ӳ�����أ�
	b) �ڷ�������ʱ��������������ɺ����ʵ��ٴ��伸���ֽڵ�0xff���Ա��⴫�������ʧ���ݵ����豸�ȴ���

********************************************* ͨ��Э�鶨�� ********************************************/
/**
  * @brief
  * @param
  * @retval
  */
void uart_read_cmd_fifo_push(uint16_t* cmd_buf, uint16_t len)
{
	uint16_t i;

	for(i = 0; i < len; i++)
	{
		uart_cmd_read_fifo[uart_cmd_read_fifo_in++] = cmd_buf[i];
		if(uart_cmd_read_fifo_in == 100)
			uart_cmd_read_fifo_in = 0;
		uart_cmd_read_fifo_cnt++;
	}
}

uint16_t uart_read_cmd_fifo_get(uint16_t* ch)
{
	 uint16_t fun_return;

	 if(uart_cmd_read_fifo_cnt)
	 {
		 *ch = uart_cmd_read_fifo[uart_cmd_read_fifo_out++];
		 if(uart_cmd_read_fifo_out == 100)
			 uart_cmd_read_fifo_out = 0;
		 uart_cmd_read_fifo_cnt--;
		 fun_return = 1;
	 }
	 else
	 {
		fun_return=0;
	 }
	 return fun_return;
}

/**
  * @brief
  * @param
  * @retval
  */
void uart_write_cmd_process()
{
	uint16_t i, j, n, temp_buf[48];

	n = uart_cmd_data_len / 2;
	for (i = 0, j = 0; i < n; i++, j += 2)
		temp_buf[i] = (uart_cmd_data_buf[j] << 8) | uart_cmd_data_buf[j + 1];

	i = temp_buf[0];
	j = dsp_reg_write(uart_cmd_addr, temp_buf);
	if (j == n)
		sprintf((char *)temp_buf, "д��%04x ��%04x", i, uart_cmd_addr);
	else
		sprintf((char *)temp_buf, "д����(%02d)��:%02d", n, j);
	GL_LCD_TipDraw(0, (uint8_t *)temp_buf, 0, 1000);
}

void uart_read_cmd_process(void)
{
	uint16_t i, j, n;
	uint16_t temp_buf[48];
	uint16_t cmd_buf[120];

	n = dsp_reg_read(uart_cmd_addr, temp_buf);
	if (n)
	{
		i = uart_cmd_addr - READ_BASE_ADDR;
		cmd_buf[0]=DATA_HEADER1;
		cmd_buf[1]=DATA_HEADER2;
		cmd_buf[2]=CMD_WRITE_DSP_REG;
		cmd_buf[3]=n;
		cmd_buf[4] = i >> 8;
		cmd_buf[5] = i & 0x00ff;
		for (i = 0, j = 0; i < n; i++, j += 2)
		{
			cmd_buf[6 + j] = temp_buf[i] >> 8;
			cmd_buf[7 + j] = temp_buf[i] & 0x00ff;
		}
		cmd_buf[6 + j] = 0;
		uart_read_cmd_fifo_push(cmd_buf, 7 + j);
	}

	i = temp_buf[0];
	j = uart_cmd_data_len / 2;
	if (n == j)
		sprintf((char *)temp_buf, "����%04x ��%04x", i, uart_cmd_addr);
	else
		sprintf((char *)temp_buf, "������(%02d)��:%02d", j, n);
	GL_LCD_TipDraw(0, (uint8_t *)temp_buf, 0, 1000);
}

/**
  * @brief
  * @param
  * @retval
  */
uint16_t uart_cmd_analysis(uint8_t recev_data)
{
	uint16_t fun_return = CMD_RET_NULL;

	switch (uart_cmd_state)
	{
		case 0:		// header 1
			if (recev_data == DATA_HEADER1)
				uart_cmd_state = 1;
			else
				uart_cmd_state = 0;
			break;
		case 1:		// header 2
			if (recev_data == DATA_HEADER2)
				uart_cmd_state = 2;
			else
				uart_cmd_state = 0;
			break;
		case 2:		// command
			uart_cmd_state = 3;
			uart_cmd_check_sum = recev_data;
			if(recev_data == CMD_WRITE_DSP_REG)
				uart_cmd_read_write = 1;		// write dsp reg
			else if(recev_data == CMD_READ_DSP_REG)
				uart_cmd_read_write = 0;		// read dsp reg
			else
				uart_cmd_state = 0;
			break;
		case 3:		// operate dsp's high address
			uart_cmd_addr = (uint16_t)recev_data << 8;
			uart_cmd_state = 4;
			uart_cmd_check_sum ^= recev_data;
			break;
		case 4:		// operate dsp's low address
			uart_cmd_addr |= (uint16_t)recev_data;
			uart_cmd_addr = (uart_cmd_read_write == 1) ? (uart_cmd_addr + WRITE_BASE_ADDR) : (uart_cmd_addr + READ_BASE_ADDR);
			uart_cmd_state = 10;
			uart_cmd_check_sum ^= recev_data;
			break;
		case 10:	// data length
			if (recev_data)
			{
				uart_cmd_data_len = recev_data;
				uart_cmd_check_sum ^= recev_data;
				uart_cmd_data_index = 0;
				uart_cmd_state = (uart_cmd_read_write == 1) ? 5 : 6;
			}
			else
				uart_cmd_state = 0;
			break;
		case 5:		// data that write to dsp
			uart_cmd_data_buf[uart_cmd_data_index++] = (uint16_t)recev_data;
			uart_cmd_check_sum ^= recev_data;
			if(uart_cmd_data_index == uart_cmd_data_len)
				uart_cmd_state = 6;
			break;
		case 6:		// operate dsp's checksum
			uart_cmd_check_sum = recev_data;	// debug: invalidate the checksum; comment this line for using this function
			if(uart_cmd_check_sum == recev_data)
			{
				if(uart_cmd_read_write == 1)
					fun_return = CMD_RET_WRITE_DSP_REG;
				else
					fun_return = CMD_RET_READ_DSP_REG;
			}
			uart_cmd_state = 0;
			break;

		default:
			break;
	}
	return fun_return;
}

void uart_for_dsp_poll(void)
{
	uint16_t putchar;
	uint16_t fun_return;
	uint8_t  recev_data;

	if (uart_read_cmd_fifo_get(&putchar))
	{
		//putch_noblock(UART_DEBUG_INDEX, (uint8_t)putchar);	// putch_noblock return 0: fail; 1: OK
		put_data(UART_DEBUG_INDEX, (uint8_t *)&putchar, 1);		// waiting
	}

	if(getch_noblock(UART_DEBUG_INDEX, &recev_data))
	{
		fun_return = uart_cmd_analysis(recev_data);
		switch (fun_return)
		{
			case CMD_RET_WRITE_DSP_REG:
				uart_write_cmd_process();
				break;
			case CMD_RET_READ_DSP_REG:
				uart_read_cmd_process();
				break;
			default:
				break;
		}
	}
}

#endif	/* do not use those function*/

