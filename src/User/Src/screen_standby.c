/**
  ******************************************************************************
  *                Copyright (c) 2012, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    25-December-2012
  * @brief   This file provides
  *            - standby screen process
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "global_define.h"
#include <victel_digital_board.h>
#include "victel_digital_lcd.h"
#include "stack_config.h"


//#define MPT_CONV_SHOW_FREQ				1


#define DIAL_LABEL_Y						(LCD_ACTIVE_HEIGHT + (LCD_PIXEL_HEIGHT - LCD_ACTIVE_HEIGHT - DIAL_LABEL_LARGE_FONTS_HEIGHT) / 2)
#define DIAL_LABEL_Y_HEIGHT					(DIAL_LABEL_LARGE_FONTS_HEIGHT + 4)
#define DIAL_LABEL_PRINT_X					((LCD_PIXEL_WIDTH - DIAL_NUMBER_FIRST_LENGTH * DEFAULT_FONT_WIDTH) / 2)
#define DIAL_LABEL_PRINT_Y					(LCD_ACTIVE_HEIGHT + (LCD_PIXEL_HEIGHT - LCD_ACTIVE_HEIGHT - DEFAULT_FONT_HEIGHT * 2 - 8) / 2)

#define DIAL_LABEL_ID						0
  #define DIAL_LABEL_LARGE_FONTS_WIDTH		12
  #define DIAL_LABEL_LARGE_FONTS_HEIGHT		24
  #define DIAL_NUMBER_FIRST_LENGTH			13
  #define DIAL_LABEL_LARGE_FONTS_STYLE		DEFAULT_FONT_STYLE
  #define MEETING_LABEL_FIRST_X				((LCD_PIXEL_WIDTH - 128) / 2)

#define DIAL_LABEL_ID2						1
  #define DIAL_LABEL_ID2_WIDTH				((DIAL_NUMBER_MAX_LENGTH - DIAL_NUMBER_FIRST_LENGTH) * DEFAULT_FONT_WIDTH)
  #define DIAL_LABEL_ID2_X					((LCD_PIXEL_WIDTH - DIAL_LABEL_ID2_WIDTH) / 2)
  #define DIAL_LABEL_ID2_Y					(DIAL_LABEL_PRINT_Y + DEFAULT_FONT_HEIGHT + 8)

#define WATCH_NAME_LABEL_ID					2
  #define WATCH_NAME_LABEL_Y				LABEL_PRINT_Y1_OFFSET

#define WATCH_NUMBER_LABEL_ID				3
  #define WATCH_NUMBER_LABEL_Y				LABEL_PRINT_Y2_OFFSET

#define LABEL_DEFAULT_HEIGHT				(DEFAULT_FONT_HEIGHT + 2)

#define MPT_FREQ_INFO_CHAR_NUM				13
#define MPT_FREQ_INFO_STRING_WIDTH			(MPT_FREQ_INFO_CHAR_NUM * DEFAULT_FONT_WIDTH)
#define MPT_FREQ_INFO_X						((LCD_PIXEL_WIDTH - MPT_FREQ_INFO_STRING_WIDTH) / 2)
#define MPT_FREQ_INFO_Y						(LCD_PIXEL_HEIGHT - DEFAULT_FONT_HEIGHT * 2 - 4)

#define ZZW_SLOT_DETAIL_OBJ_ID				(WATCH_NUMBER_LABEL_ID + 1)

#define CLEAR_MPT_FREQ_INFO_STRING			"             "
#define DRAW_MPT_FREQ_INFO1(buffer)			GL_LCDPrintString(MPT_FREQ_INFO_X, MPT_FREQ_INFO_Y, buffer, front_ground_color, back_ground_color, 2, FONT_NONTRANSPARENT)
#define DRAW_MPT_FREQ_INFO2(buffer)			GL_LCDPrintString(MPT_FREQ_INFO_X, MPT_FREQ_INFO_Y + DEFAULT_FONT_HEIGHT + 2 , buffer, front_ground_color, back_ground_color, 2, FONT_NONTRANSPARENT)

#define KEY_KNOB_SWITCH_VOL_GRP_MASK				0x01
#define KEY_KNOB_SWITCH_GRP_WITHOUT_CONFIRM_MASK	0x40
#define KEY_KNOB_SWITCH_GRP_WITHOUT_CONFIRM_FOREVER	0x80
#define IS_SWITCH_GRP_WITHOUT_CONFIRM()				(key_knob_mode & KEY_KNOB_SWITCH_GRP_WITHOUT_CONFIRM_MASK)
#define IS_SWITCH_GRP_FIXED_CONFIRM()				(key_knob_mode & KEY_KNOB_SWITCH_GRP_WITHOUT_CONFIRM_FOREVER)

extern uint8_t interphone_mode;
extern uint8_t mpt_rmode_is_cps;
#ifdef SHOW_SOLT_DETAIL_WITH_VOICE
extern uint8_t receive_voice_counter[ZZWPRO_TR_STATUS_MAX_SLOT];
#endif
extern uint32_t zzwpro_xcs;
extern STATIC_PARAMETERS_TYPEDEF *g_static_ptr;
extern RUNTIME_PARAMETERS_TYPEDEF g_runtime_inst;


//const uint8_t menu_label_prefix[20][3] = {"��","��","��","��","��","��","��","��","��","��","��","��","��","��","��","��","��","��","��","��"};
const uint8_t speaker_position_tip[2][6][14] = {
		{"���ȣ�����", "���ȣ�����"},
		{"SPK:INNER", "SPK:OUTSIDE"}
};

GL_Page_TypeDef *page_standby;
// watch_index: ��ǰ�غ�������Ⱥ�б��е���������������=get_books_item_count()
uint16_t watch_index, watch_index_bak = 0xffff, a1_watch_index = 0xffff, a1_watch_index_bak = 0xffff;	// a1_watch_index:С��MAX_USER_BOOK_COUNT��ʾ��Ч��ʹ��ABCָ�����飩��xxxx_bak:���水���л�ǰ�غ����
uint8_t g_dial_number_index = 0, g_dial_number[DIAL_NUMBER_MAX_LENGTH + 1] = "";
uint32_t last_call_id_save = 0;
static uint8_t switch_delay_timer_id = 0xff, update_standby_timer_id = 0xff, last_call_timer_id = 0xff;
#ifdef KNOB_SWITCH_GRP_WITHOUT_CONFIRM
static uint8_t key_knob_mode = KEY_KNOB_SWITCH_GRP_WITHOUT_CONFIRM_MASK | KEY_KNOB_SWITCH_GRP_WITHOUT_CONFIRM_FOREVER;
#else
static uint8_t key_knob_mode = 0;			// bit0:0-adjust volume,1-adjust group; bit6:0-switch group with confirm,1-without confirm; bit7:0-clear once at a time; 1-set forever
#endif



const uint8_t meeting_label[STACK_ZZW_TOTAL_MEETING_NUM + 2] = {'1', '2', '3', '4', '5', '6', 'A', 'M'};

#define SLOT_DETAIL_LABEL_FONT_STYEL		DEFAULT_FONT_STYLE
#define SLOT_DETAIL_LABEL_FONT_WIDTH		DEFAULT_FONT_WIDTH
#define SLOT_DETAIL_LABEL_FONT_HEIGHT		DEFAULT_FONT_HEIGHT
#define SLOT_DETAIL_LABEL_FRAME_W_GAP		8

#define SLOT_DETAIL_LABEL_FRAME_H_GAP		4
//#define SLOT_DETAIL_LABEL_TOTAL				ZZWPRO_TR_STATUS_MAX_SLOT
#define SLOT_DETAIL_LABEL_WIDTH				(SLOT_DETAIL_LABEL_FONT_WIDTH + SLOT_DETAIL_LABEL_FRAME_W_GAP)/*oled:6+4,else:12+4*/
#define SLOT_DETAIL_LABEL_HEIGHT			(SLOT_DETAIL_LABEL_FONT_HEIGHT + SLOT_DETAIL_LABEL_FRAME_H_GAP)/*oled:8+4,else:24+4*/
#define SLOT_DETAIL_LABEL_X_OFFSET			(SLOT_DETAIL_LABEL_WIDTH + 8)
#define SLOT_DETAIL_LABEL_FIRST_X			((LCD_PIXEL_WIDTH - slot_detail_total * SLOT_DETAIL_LABEL_X_OFFSET + SLOT_DETAIL_LABEL_FRAME_W_GAP) / 2)
#define SLOT_DETAIL_LABEL_GET_X(i)			(SLOT_DETAIL_LABEL_FIRST_X + (i) * SLOT_DETAIL_LABEL_X_OFFSET)
#define SLOT_DETAIL_LABEL_GET_Y()			(LCD_PIXEL_HEIGHT - (SLOT_DETAIL_LABEL_HEIGHT + 4) + 2)

#define SET_SLOT_DETAIL_TO_SCREEN()			show_slot_detail_pre_status |= 0x80
#define CLR_SLOT_DETAIL_TO_SCREEN()			show_slot_detail_pre_status = 0/*show_slot_detail_pre_status &= ~0x80*/
#define IS_SHOW_SLOT_DETAIL_TO_SCREEN()		(show_slot_detail_pre_status & 0x80)

static uint8_t slot_detail_total = ZZWPRO_TR_STATUS_MAX_SLOT, slot_occupy_mask[3];
static uint8_t show_slot_detail_pre_status = 0;	// bit7:0-no widget,1-add done; bit5-0:6slots;0-no solid,1-frame the number


void print_mpt_freq_info(uint32_t b_ch)
{
#ifdef MPT_CONV_SHOW_FREQ

	uint8_t buffer[30];

	if (b_ch == 0xffffffff)
	{
		DRAW_MPT_FREQ_INFO1(CLEAR_MPT_FREQ_INFO_STRING);
		DRAW_MPT_FREQ_INFO2(CLEAR_MPT_FREQ_INFO_STRING);
	}
	else
	{
		sprintf((char *)buffer, "T=%3.4fMHz", (float)(b_ch >> 16) / 80);
		DRAW_MPT_FREQ_INFO1(buffer);
		sprintf((char *)buffer, "R=%3.4fMHz", (float)(b_ch & 0x0000ffff) / 80);
		DRAW_MPT_FREQ_INFO2(buffer);
	}

#endif
}

void reverse_calling_watch_name(void);

uint8_t key_knob_adjust_volume(void)
{
	return (is_kb_knob_enter_disable() && ((key_knob_mode & KEY_KNOB_SWITCH_VOL_GRP_MASK) == 0)) ? 1 : 0;
}

void reverse_standby_watch_name(uint8_t flag)
{
	if (is_kb_knob_enter_disable() && flag)
		DrawControlObjReverse(page_standby->PageControls[WATCH_NAME_LABEL_ID]);
}

void reserve_key_knob_mode(void)
{
	if (key_knob_mode & KEY_KNOB_SWITCH_VOL_GRP_MASK)
		key_knob_mode &= ~KEY_KNOB_SWITCH_VOL_GRP_MASK;
	else
		key_knob_mode |= KEY_KNOB_SWITCH_VOL_GRP_MASK;
}

uint8_t set_switch_group_without_confirm(uint8_t forever, uint8_t flag)				// 0-must confirm with middle key,1-without confirm
{
#ifndef KNOB_SWITCH_GRP_WITHOUT_CONFIRM

	if (flag <= 1)
	{
		if (flag)
			key_knob_mode |= KEY_KNOB_SWITCH_GRP_WITHOUT_CONFIRM_MASK;
		else
			key_knob_mode &= ~KEY_KNOB_SWITCH_GRP_WITHOUT_CONFIRM_MASK;
	}

	if (forever)
		key_knob_mode |= KEY_KNOB_SWITCH_GRP_WITHOUT_CONFIRM_FOREVER;
//	else																			// ����ģʽֻ����λ�������
//		key_knob_mode &= ~KEY_KNOB_SWITCH_GRP_WITHOUT_CONFIRM_FOREVER;

#endif

	return key_knob_mode & KEY_KNOB_SWITCH_GRP_WITHOUT_CONFIRM_MASK;
}

uint16_t get_watch_id_index(void)
{
	return watch_index;
}

void reset_key_knob_mode(void)
{
	if (key_knob_mode & KEY_KNOB_SWITCH_VOL_GRP_MASK)
	{
		if (now_is_at_standby_screen())
			reverse_standby_watch_name(1);
		else
			reverse_calling_watch_name();
	}
	key_knob_mode &= ~KEY_KNOB_SWITCH_VOL_GRP_MASK;
}

uint8_t general_input_a_number(uint8_t *str, uint8_t *index, uint8_t max_len, uint8_t number, uint8_t input_dot)	// input_dot: when press '#', 0-the previous number add 1; else-insert a '.'
{
	uint8_t c, ret;

	ret = 1;
	if (number == KEY_CODE_STAR)
	{
		if (*index)
		{
			*index -= 1;
			str[*index] = 0;
		}
		else
			ret = 0;
	}
	else
	{
		if ((number == KEY_CODE_WELL) && (input_dot == 0))
		{
			c = str[*index - 1];
			if (((c >= '0') && (c < '9')) || ((c >= 'a') && (c < 'f')) || ((c >= 'A') && (c < 'F')))
				str[*index - 1] += 1;
			else if (c == '9')
				str[*index - 1] = '0';
			else if (c == 'f')
				str[*index - 1] = 'a';
			else if (c == 'F')
				str[*index - 1] = 'A';
		}
		else if (*index < max_len)
		{
			if (number == KEY_CODE_WELL)
				str[*index] = '.';
			else
				str[*index] = number + '0' - KEY_CODE_0;
			*index += 1;
			str[*index] = 0;
		}
		else
			ret = 0;
	}

	return ret;
}

void insert_one_dial_number(uint32_t code)
{
	if (g_dial_number_index < DIAL_NUMBER_MAX_LENGTH)
	{
		if (g_dial_number_index == 0)
		{
			SetControlObjVisible(page_standby->PageControls[WATCH_NAME_LABEL_ID], 0);
			DrawControlObjInvisible(page_standby->PageControls[WATCH_NAME_LABEL_ID]);

			SetControlObjVisible(page_standby->PageControls[WATCH_NUMBER_LABEL_ID], 0);
			DrawControlObjInvisible(page_standby->PageControls[WATCH_NUMBER_LABEL_ID]);
			SetControlObjVisible(page_standby->PageControls[DIAL_LABEL_ID], 1);

			if (IS_SHOW_SLOT_DETAIL_TO_SCREEN())
			{
				set_slot_detail_widget_visible(0);
			}

			if (interphone_mode == INTERPHONE_MODE_MPT_CONV)
				print_mpt_freq_info(0xffffffff);
		}
		else if (g_dial_number_index == DIAL_NUMBER_FIRST_LENGTH)
		{
			SetControlObjVisible(page_standby->PageControls[DIAL_LABEL_ID], 0);
			DrawControlObjInvisible(page_standby->PageControls[DIAL_LABEL_ID]);
			GL_LCDPrintString(DIAL_LABEL_PRINT_X, DIAL_LABEL_PRINT_Y, g_dial_number, front_ground_color, back_ground_color, DEFAULT_FONT_STYLE, FONT_NONTRANSPARENT);

			SetControlObjVisible(page_standby->PageControls[DIAL_LABEL_ID2], 1);
		}

		g_dial_number[g_dial_number_index] = '0' + code - KEY_CODE_0;
		g_dial_number_index++;
		g_dial_number[g_dial_number_index] = 0;
		if (g_dial_number_index <= DIAL_NUMBER_FIRST_LENGTH)
		{
			SetLabel(page_standby, DIAL_LABEL_ID, g_dial_number);
			RefreshPageControl(page_standby, DIAL_LABEL_ID);
		}
		else
		{
			SetLabel(page_standby, DIAL_LABEL_ID2, g_dial_number + DIAL_NUMBER_FIRST_LENGTH);
			RefreshPageControl(page_standby, DIAL_LABEL_ID2);
		}
	}
}

void clr_one_line_string(uint16_t xpos, uint16_t ypos)
{
	uint8_t space_for_clr[DIAL_NUMBER_FIRST_LENGTH + 1];

	memset(space_for_clr, ' ', DIAL_NUMBER_FIRST_LENGTH);
	space_for_clr[DIAL_NUMBER_FIRST_LENGTH] = 0;
	GL_LCDPrintString(xpos, ypos, space_for_clr, front_ground_color, back_ground_color, DEFAULT_FONT_STYLE, FONT_NONTRANSPARENT);
}


uint32_t get_standby_watch_number_info(uint8_t *number_string)
{
//	USER_BOOK *book;
	uint32_t local_id;

	if (g_runtime_inst.runtime_paras.misc_runtime_config.local_id_at_standby == 0)
	{
//		book = get_books_item_content(watch_index, 1);	// refresh_standby_screen_name_and_number(book, 0);
//		book = get_watch_at_user_book(1, 0xffff, 0);
		local_id = (get_watch_at_user_book(1, 0xffff, 0))->id;
	}
	else
	{
		maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, &local_id);
	}

	if (number_string)
		gen_user_number_info(local_id, number_string);

	return local_id;
}

#define LAST_CALL_CAN_BE_CALLED_BACK()			((last_call_id_save & ((uint32_t)USER_ID_BACKGROUND << 24)) == 0)
#define LAST_CALL_IS_GROUP()					((last_call_id_save & ((uint32_t)USER_ID_INDIVIDUAL << 24)) == 0)
void clr_last_call_id(uint8_t is_ptt, uint8_t draw_standby)
{
	uint8_t temp[30];

	last_call_timer_id = timer_destroy(last_call_timer_id);
	if (last_call_id_save)
	{
		if (is_ptt && (g_dial_number_index == 0) && LAST_CALL_CAN_BE_CALLED_BACK() &&
				(LAST_CALL_IS_GROUP() || maintain_setup_parameter(PARA_OPERATE_READ, DIAL_IND_CONV_PERMIT, 0)))
		{
			call_ptt_async(CALL_PTT_ASYNC_CALLING, last_call_id_save & (~((uint32_t)USER_ID_BACKGROUND << 24)), 0, 0, g_dial_number);
		}
		last_call_id_save = 0;

		if (draw_standby && now_is_at_standby_screen())
		{
			get_standby_watch_number_info(temp);
			SetLabel(page_standby, WATCH_NUMBER_LABEL_ID, temp);
			RefreshPageControl(page_standby, WATCH_NUMBER_LABEL_ID);
		}
	}
}

void last_call_timer_handle(void)
{
	clr_last_call_id(0, 1);
}

void refresh_dial_number(void)
{
	USER_BOOK *book;

	if (g_dial_number_index > DIAL_NUMBER_FIRST_LENGTH)
	{
		SetLabel(page_standby, DIAL_LABEL_ID2, g_dial_number + DIAL_NUMBER_FIRST_LENGTH);
		RefreshPageControl(page_standby, DIAL_LABEL_ID2);
	}
	else if (g_dial_number_index == DIAL_NUMBER_FIRST_LENGTH)
	{
		SetControlObjVisible(page_standby->PageControls[DIAL_LABEL_ID2], 0);
		DrawControlObjInvisible(page_standby->PageControls[DIAL_LABEL_ID2]);
		clr_one_line_string(DIAL_LABEL_PRINT_X, DIAL_LABEL_PRINT_Y);

		SetControlObjVisible(page_standby->PageControls[DIAL_LABEL_ID], 1);
		RefreshPageControl(page_standby, DIAL_LABEL_ID);
	}
	else if (g_dial_number_index)
	{
		SetLabel(page_standby, DIAL_LABEL_ID, g_dial_number);
		RefreshPageControl(page_standby, DIAL_LABEL_ID);
	}
	else
	{
		SetControlObjVisible(page_standby->PageControls[DIAL_LABEL_ID], 0);
		DrawControlObjInvisible(page_standby->PageControls[DIAL_LABEL_ID]);

		SetControlObjVisible(page_standby->PageControls[DIAL_LABEL_ID2], 0);
		DrawControlObjInvisible(page_standby->PageControls[DIAL_LABEL_ID2]);
		clr_one_line_string(DIAL_LABEL_PRINT_X, DIAL_LABEL_PRINT_Y);

		SetControlObjVisible(page_standby->PageControls[WATCH_NAME_LABEL_ID], 1);
		RefreshPageControl(page_standby, WATCH_NAME_LABEL_ID);
		SetControlObjVisible(page_standby->PageControls[WATCH_NUMBER_LABEL_ID], 1);
		RefreshPageControl(page_standby, WATCH_NUMBER_LABEL_ID);

		if (IS_SHOW_SLOT_DETAIL_TO_SCREEN())
		{
			set_slot_detail_widget_visible(1);
		}

		if (interphone_mode == INTERPHONE_MODE_MPT_CONV)
		{
			book = get_books_item_content(watch_index, 1);
			if (book)
			{
				print_mpt_freq_info(book->bind_ch);
			}
			else
			{
				print_mpt_freq_info(0);
			}
		}
	}
}

void delete_dial_number(uint8_t num, uint8_t refresh)	// num: 0-delete all; else-delete last
{
	USER_BOOK *book = (USER_BOOK *)0;

	if (g_dial_number_index)
	{
		if (num)
			g_dial_number_index--;
		else
			g_dial_number_index = 0;

		g_dial_number[g_dial_number_index] = 0;

		if (refresh || book)
			refresh_dial_number();
	}
	else if (book)
	{
		refresh_dial_number();
	}
}

uint8_t get_input_status_code(uint8_t *str, uint8_t *max_length)
{
	uint8_t *ptr = str, ret = 0, n = 0;

	while ((*ptr != '*') && (n < *max_length))
	{
		ret = ret * 10 + *ptr - '0';
		ptr++;
		n++;
	}

	*max_length = n;
	return ret;
}

#define MPT_DISPATCH_ID_NUMBER		10
#define MPT_DISPATCH_ID_START		8170
const uint8_t mpt_dispatch_id_array[MPT_DISPATCH_ID_NUMBER] = {100, 111, 121, 131, 141, 151, 161, 171, 181, 191};
uint16_t get_mpt_dispatch_id(uint16_t number)
{
	uint8_t i;

	for (i = 0; i < MPT_DISPATCH_ID_NUMBER; i++)
	{
		if (number == mpt_dispatch_id_array[i])
			break;
	}

	return (i < MPT_DISPATCH_ID_NUMBER) ? (MPT_DISPATCH_ID_START + i) : 0xffff;
}

#define BROADCASE_TOTAL_TYPE	4
#define BROADCAST_TYPE_ALL		0
#define BROADCAST_TYPE_REGION	1
#define BROADCAST_TYPE_LOCAL	2
#define BROADCAST_TYPE_BASE		3
const uint8_t  broadcast_type_str[BROADCASE_TOTAL_TYPE][9] = {"ȫ��", "����ȫ��", "����ȫ��", "��վȫ��"};
const uint32_t broadcast_id[BROADCASE_TOTAL_TYPE] = {0xFFFFFF, 0xFFFFFE, 0xFFFFFD, 0xFFFFFC};
uint32_t get_broadcast_id(uint8_t type, uint8_t **str)
{
	uint32_t ret;

	if (type < BROADCASE_TOTAL_TYPE)
	{
		ret = broadcast_id[type];
		if (str)
			*str = (uint8_t *)broadcast_type_str[type];
	}
	else
	{
		ret = 0;
		if (str)
			*str = 0;
	}

	return ret;
}

uint8_t *get_broadcast_name(uint32_t id)
{
	uint32_t i, id_cmp = id & 0x00ffffff;

	for (i = 0; i < BROADCASE_TOTAL_TYPE; i++)
	{
		if (id_cmp == broadcast_id[i])
			return (uint8_t *)broadcast_type_str[i];
	}

	return 0;
}

uint32_t dial_number_to_id(uint8_t *dial_num)
{
	uint32_t id, self_id;
	uint16_t fgn;
	uint8_t dial_num_len, id_str[12], *dial_number;

	maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, (void *)&self_id);
	self_id = id_to_number(interphone_mode, 0, self_id, 0, id_str) & 0x00000fff;		// id_str: 328-20-200

	id = 0;
	if (dial_num[0] == THE_STAR_INPUT_CHAR)
	{
		if(!strncmp((const char *)dial_num, "*0", 2))			// send status message
		{
			dial_num_len = 3;
			dial_num[2] = get_input_status_code(dial_num + 2, &dial_num_len);	// return status code at dial_num[2]
			dial_num_len += 3;	// *0nnn*
		}
		else if(!strncmp((const char *)dial_num, "*9*", 3))		// emergency
		{
			dial_num_len = 3;
			id = (uint32_t)USER_ID_EMERGENCY << 24;
		}
		else if(!strncmp((const char *)dial_num, "*11*", 4))	// broadcast
		{
			dial_num_len = 4;
			id = (uint32_t)USER_ID_BROADCAST << 24;
		}
		else if(!strcmp((const char *)dial_num, "*1982"))		// ����ȫ��ȫ��
		{
			return ((uint32_t)(USER_ID_EMERGENCY | USER_ID_BROADCAST) << 24) | 0xFFFFFF;
		}
		else if(!strcmp((const char *)dial_num, "*1987"))		// ��ͨ����ȫ��ȫ��
		{
			return ((uint32_t)USER_ID_BROADCAST << 24) | 0xFFFFFF;
		}
		else if(!strcmp((const char *)dial_num, "*1972"))		// ��������ȫ��
		{
			return ((uint32_t)(USER_ID_EMERGENCY | USER_ID_BROADCAST) << 24) | 0xFFFFFE;
		}
		else if(!strcmp((const char *)dial_num, "*1977"))		// ��ͨ��������ȫ��
		{
			return ((uint32_t)USER_ID_BROADCAST << 24) | 0xFFFFFE;
		}
		else if(!strcmp((const char *)dial_num, "*1962"))		// ��������ȫ��
		{
			return ((uint32_t)(USER_ID_EMERGENCY | USER_ID_BROADCAST) << 24) | 0xFFFFFD;
		}
		else if(!strcmp((const char *)dial_num, "*1967"))		// ��ͨ��������ȫ��
		{
			return ((uint32_t)USER_ID_BROADCAST << 24) | 0xFFFFFD;
		}
		else if(!strcmp((const char *)dial_num, "*1952"))		// ������վȫ��
		{
			return ((uint32_t)(USER_ID_EMERGENCY | USER_ID_BROADCAST) << 24) | 0xFFFFFC;
		}
		else if(!strcmp((const char *)dial_num, "*1957"))		// ��ͨ������վȫ��
		{
			return ((uint32_t)USER_ID_BROADCAST << 24) | 0xFFFFFC;
		}
		else
		{
			return 0;
		}
	}
	else if ((dial_num[0] == '0') && (dial_num[1] == '1'))		// �������ߵ绰���㽭����λ��������01xxx
	{
		if ((is_zhejiang_tmd_code(self_id)) && (p_strlen(dial_num) == 5))
			dial_num_len = 0;
		else
			return PDT_OUTSIDE_CALL_ID;
	}
	else
	{
		dial_num_len = 0;
	}

	if (dial_num[dial_num_len] == REPLACE_WELL_INPUT_CHAR)		// the string is the id
	{
		dial_num_len++;
	}
	else if (self_id)											// self id is PDT code
	{
		dial_num_len |= 0x80;
	}

	if ((dial_num_len & 0x80) == 0)								// look the string as id
	{
		id |= string2decimal(dial_num + dial_num_len) | (USER_ID_INDIVIDUAL << 24);
	}
	else														// PDT code
	{
		dial_number = &dial_num[dial_num_len & 0x7f];
		dial_num_len = p_strlen(dial_number);
		if ((interphone_mode == INTERPHONE_MODE_MPT_TRUNKING) && (mpt_rmode_is_cps == 0))
		{
			if (dial_num_len == 10)
				p_strcpy(id_str, dial_number);
			else if (dial_num_len == 7)
				p_strcpy(&id_str[3], dial_number);
			else if (dial_num_len == 6)
			{
				goto dispatch_console_n6;
			}
			else if (dial_num_len == 3)
			{
				fgn = string2decimal(dial_number);
				if (get_mpt_dispatch_id(fgn) != 0xffff)
				{
					p_strcpy(&id_str[3], "-$$-");
				}
				else if (number_is_group(fgn))
				{
					maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_MPT1343_FGN, &fgn);
					decimal2string(fgn, 4, &id_str[3]);
				}
				p_strcpy(&id_str[7], dial_number);
			}
			else
				return 0;
		}
		else
		{
			if (dial_num_len == 8)
			{
				p_strcpy(id_str, dial_number);
			}
			else if (dial_num_len == 5)			// ʡ������λ���ţ��������㽭�ĵ�������
			{
				dial_num_len = (dial_number[0] - '0') * 10 + (dial_number[1] - '0');
				if (dial_num_len == 98)
				{
					if (maintain_stack_parameter(PARA_OPERATE_READ, STACK_PROVINCE_AREA_CODE, &fgn) == 0)
						return 0;
					decimal2string((uint32_t)fgn, 3, id_str);
				}
				else if (dial_num_len == 99)
				{
					if (maintain_stack_parameter(PARA_OPERATE_READ, STACK_MINISTRY_AREA_CODE, &fgn) == 0)
						return 0;
					decimal2string((uint32_t)fgn, 3, id_str);
				}
				p_strcpy(&id_str[3], dial_number);
			}
			else if (is_zhejiang_tmd_code(self_id) == 0)
			{
				if (dial_num_len == 6)			// dispatch console
				{
dispatch_console_n6:
					p_strncpy(id_str, dial_number, 3);
					p_strcpy(&id_str[3], (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING) ? "-$$-" : "-!!-");	// dispatch number
					p_strcpy(&id_str[7], dial_number + 3);
				}
				else if (dial_num_len == 3)
				{
					fgn = num_string2decimal(dial_num, 3);
					if (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING)
					{
						if (get_mpt_dispatch_id(fgn) != 0xffff)
							p_strcpy(&id_str[3], "-$$-");
					}
					else
					{
						if ((fgn >= 100) && (fgn <= 109))
							p_strcpy(&id_str[3], "-!!-");
					}
					p_strcpy(&id_str[7], dial_number);
				}
				else
					return 0;
			}
			else
				return 0;
		}

		self_id = number_to_id(interphone_mode, 0, id_str);
		if (self_id)
			id |= self_id;
		else
			id = 0;
	}

	return id;
}

uint32_t dial_decemal_to_id(uint32_t dec)
{
	uint8_t temp[20];

	decimal2string(dec, 0, temp);
	return dial_number_to_id(temp);
}

void switch_watch_channel_delay_no_confirm(void)
{
	if (phone_is_busy_now() == 0)
	{
		switch_delay_timer_id = timer_destroy(switch_delay_timer_id);
		set_sync_call_stack_type(SYNC_CALL_STACK_SWITCH_WATCH);
		printf("Delay to set watch and no confirm");
	}
}

void switch_watch_channel_no_confirm(uint8_t switch_delay)
{
	uint16_t index;

	maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_WATCH_ID, &index);
	if ((index != watch_index) && (watch_index != 0xffff))
	{
		maintain_stack_parameter(PARA_OPERATE_WRITE, STACK_PARA_WATCH_ID, &watch_index);
		save_user_data_to_flash(1);
	}

	if (switch_delay)
	{
		if (switch_delay_timer_id == 0xff)
			switch_delay_timer_id = timer_initial(0, 400, switch_watch_channel_delay_no_confirm);
		else
			timer_reset(switch_delay_timer_id);
	}
	else
	{
		set_sync_call_stack_type(SYNC_CALL_STACK_SWITCH_WATCH);
	}
}

void delay_to_set_stack_watch_no_confirm(void)
{
	if ((interphone_mode == INTERPHONE_MODE_PDT_TRUNKING) || (interphone_mode == INTERPHONE_MODE_MPT_TRUNKING))
	{
		if (phone_is_busy_now())										// switch at standard iop(should switch at calling)
			switch_watch_channel_no_confirm(1);
		else
			switch_watch_channel_no_confirm(0);
	}
	else
	{
		switch_watch_channel_no_confirm(0);
	}
}

void switch_watch_channel_with_confirm(void)
{
	if (switch_delay_timer_id != 0xff)
	{
		switch_delay_timer_id = timer_destroy(switch_delay_timer_id);
		set_sync_call_stack_type(SYNC_CALL_STACK_SWITCH_WATCH);
		printf("Delay to set watch with confirm");
	}
}

void refresh_standby_screen_name_and_number(USER_BOOK *book, uint8_t reverse_name)
{
	uint8_t temp[30];

	SetLabel(page_standby, WATCH_NAME_LABEL_ID, book->name);
	gen_user_number_info(book->id, temp);
	SetLabel(page_standby, WATCH_NUMBER_LABEL_ID, temp);

	RefreshPageControl(page_standby, WATCH_NAME_LABEL_ID);
	RefreshPageControl(page_standby, WATCH_NUMBER_LABEL_ID);

	if (reverse_name)
		DrawControlObjReverse(page_standby->PageControls[WATCH_NAME_LABEL_ID]);
}

void refresh_standby_screen_watch_group(void)
{
	USER_BOOK book;

	memcpy(&book, get_watch_at_user_book(1, 0xffff, 0), sizeof(USER_BOOK));
	book.id = get_standby_watch_number_info(0);
	refresh_standby_screen_name_and_number(&book, 0);
}

void switch_watch_channel_delay_with_confirm(void)	// ��ʱ����ʱ������û�а���ȷ�ϣ��ָ���ʼ״̬
{
	if (switch_delay_timer_id != 0xff)
	{
		switch_delay_timer_id = timer_destroy(switch_delay_timer_id);

		maintain_stack_parameter(PARA_OPERATE_WRITE, STACK_PARA_WATCH_ID, &watch_index_bak);
		watch_index = watch_index_bak;
		a1_watch_index = a1_watch_index_bak;
		if (now_is_at_standby_screen())
			refresh_standby_screen_watch_group();
	}
}

void delay_to_set_stack_watch_with_confirm(void)
{
	save_user_data_to_flash(1);
	if (switch_delay_timer_id == 0xff)
		switch_delay_timer_id = timer_initial(0, 4000, switch_watch_channel_delay_with_confirm);
	else
		timer_reset(switch_delay_timer_id);
}

void leave_the_standby_screen(void)
{
	switch_watch_channel_delay_with_confirm();
	clr_last_call_id(0, 0);
	update_standby_timer_id = timer_destroy(update_standby_timer_id);
	CLR_SLOT_DETAIL_TO_SCREEN();
}

void free_adjust_volume(uint32_t code)
{
	uint16_t next;
	uint8_t lang_type, temp[16];

	next = screen_volume_adjust(1, code, 0);
	lang_type = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0;
	if (next < VOICE_GAIN_LEVEL)
	{
//		printf("vol now:%d", next);
		sprintf((char *)temp, lang_type ? "Volume:%02d" : "��ǰ����:%02d", next);
	}
	else if (next == 0xfffd)
	{
		p_strcpy(temp, lang_type ? "Minimal volume" : "�����Ѵ���Сֵ");
	}
	else if (next == 0xfffe)
	{
		p_strcpy(temp, lang_type ? "Maximal volume" : "�����Ѵ����ֵ");
	}

	if (next != 0xffff)
	{
		save_user_data_to_flash(1);
	}
}

void ptt_pressed_tip(uint32_t id, uint8_t *tip, uint32_t t_time)
{
	p_strcpy(tip, maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? "Call" : "����");
	id_to_number(interphone_mode, 0, id, 0, tip + 4);
	GL_LCD_TipDraw(0, tip, 0, t_time);
}

#if (TEST_STACK_BACKGROUND_TSCC > 1)
extern uint16_t test_back_tscc;
#endif
uint8_t* clear_dynamic_conf_data(void);
uint8_t* sd_init_control(void);

void standby_screen_process_well(void)
{
	uint32_t rcode;
	uint16_t next;
	uint8_t temp[30], *tip = 0;
	float freq_tx, freq_rx;

	if (g_dial_number_index == 0)
	{
		insert_one_dial_number((uint32_t)REPLACE_WELL_INPUT_CHAR + KEY_CODE_0 - '0');
	}
	else
	{
		if (g_dial_number[0] == REPLACE_WELL_INPUT_CHAR)
		{
			if (g_dial_number_index == 2)
			{
				if (get_test_mode_flag())
				{
					if (g_dial_number[1] == '0')
					{
						set_gps_data_output(0xff);
					}
					else if (g_dial_number[1] == '1')
					{
						set_voice_source(0);	// change to mic
						set_sync_call_stack_type(get_setup_ptt_event_with_ptt_key());
					}
					else if (g_dial_number[1] == '3')
					{
						set_voice_source(3);	// change to internal
						set_sync_call_stack_type(get_setup_ptt_event_with_ptt_key());
					}
					else if (g_dial_number[1] == '6')
					{
						set_voice_source(2);	// change to O.153
						set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
					}
					else if (g_dial_number[1] == '7')
					{
						set_voice_source(1);	// change to O.1031
						set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
					}
//					else if (g_dial_number[1] == '8')	// 80020214: 16515087(FC000F) -> 284950543; 80020900: 16515073(FC0001); 32820900: 1048577(100001)
//					{
//						send_data_async((USER_ID_INDIVIDUAL << 24) | 0xFC0012);
//						send_data_async(0xFC0001);
//						if (is_qrcode_valid(0))
//							SwitchPage(show_draw_picture_screen);
//					}
				}
			}
			else if (g_dial_number_index == 3)		// zzw test or stack mode test
			{
				if (g_dial_number[1] == '1')		// zzwpro remote ctrl set base jump number
				{
					if (interphone_mode > INTERPHONE_MODE_ZZWPRO_Q)
						set_zzwpro_remote_ctrl_calling(g_dial_number[2] - '0');
				}
				else if (get_test_mode_flag())
				{
					if (g_dial_number[1] == '0')
					{
					}
					else if (g_dial_number[1] == '9')
					{
						if ((g_dial_number[2] >= '0') && (g_dial_number[2] <= '3'))
						{
							tip = stack_mode_test_something(g_dial_number[2] - '0');
						}
						else if (g_dial_number[2] == '6')
						{
							tip = clear_dynamic_conf_data();
						}
						else if (g_dial_number[2] == '7')
						{
							tip = sd_init_control();
						}
						else if ((g_dial_number[2] == '8') || (g_dial_number[2] == '9'))
						{
							tip = iop_scan_channel_stt(g_dial_number[2] - '8');
						}
					}
				}
			}
			else if ((g_dial_number_index == 4) || (g_dial_number_index == 9))	// send individual call ring
			{
send_individual_call_ring:
				rcode = dial_number_to_id(&g_dial_number[1]);
				send_single_call_ring_frame(1, rcode, MISC_OP_TYPE_RING, 0);
			}
			else if (g_dial_number_index == 6)
			{
				if (strncmp((char *)&g_dial_number[1], "16117", 5) == 0)		// open test mode
				{
					if (set_test_mode_flag(1))
						tip = maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? (uint8_t *)"Debug mode open" : (uint8_t *)"��������ģʽ";
				}
				else
				{

					if (get_test_mode_flag())
					{
						if (strncmp((char *)&g_dial_number[1], "63264", 5) == 0)
						{
#ifndef USE_DMA_TO_TRANSMIT_UART3
							usart_configuration(UART_DEBUG_INDEX, 115200, 0);		// usart3 115200
#else
							usart_configuration(UART_DEBUG_INDEX, 115200, 1);		// usart3 115200
							sys_delay(40);
#endif
							set_output_stack_dump_data(UART_LINKTO_DSP_INDEX);
							tip = (uint8_t *)"DEBUG<->DSP";
						}
						else if (strncmp((char *)&g_dial_number[1], "63253", 5) == 0)
						{
							set_output_stack_dump_data(UART_LINKTO_SD_INDEX);
							tip = (uint8_t *)"DEBUG<->SD";
						}
#if (TEST_STACK_BACKGROUND_TSCC > 1)
						else
						{
							if (strncmp((char *)&g_dial_number[1], "98", 2) == 0)
								test_back_tscc = (g_dial_number[3] - '0') * 100 + (g_dial_number[4] - '0') * 10 + (g_dial_number[5] - '0');
							else if (strncmp((char *)&g_dial_number[1], "99", 2) == 0)
								test_back_tscc = 0x8000 | ((g_dial_number[3] - '0') * 100 + (g_dial_number[4] - '0') * 10 + (g_dial_number[5] - '0'));
						}
#endif
					}

					goto send_individual_call_ring;
				}
			}
			else if (g_dial_number_index == 8)
			{
				if (get_test_mode_flag() && (interphone_mode < INTERPHONE_MODE_PDT_CONV))
				{
					if (p_strncmp(&g_dial_number[1], "000", 3) == 0)	// input is channel
					{
						p_strncpy(temp, &g_dial_number[4], 4);
						temp[4] = 0;
						next = string2decimal(temp);
						chan_to_freq(interphone_mode, &freq_tx, &freq_rx, next);
					}
					else
					{
						p_strncpy(temp, &g_dial_number[1], 3);
						temp[3] = '.';
						p_strncpy(&temp[4], &g_dial_number[4], 4);
						temp[8] = 0;
						freq_rx = atof((char *)temp);
						next = rx_freq_to_chan(freq_rx);
					}

					config_pll_paras(interphone_mode, 0, next);
					sprintf((char *)temp, "F=%3.4f/%03d", freq_rx, next);
					tip = temp;
				}
			}
		}
		else if ((g_dial_number[0] == THE_STAR_INPUT_CHAR) && (g_dial_number[1] == THE_STAR_INPUT_CHAR) && (g_dial_number_index == 8))	// 20221107-�人����ϵͳ���񱨱�(**123456#)
		{
			send_service_report_msg(&g_dial_number[2]);
		}
		else
		{
			g_dial_number[g_dial_number_index] = 0;
			if (g_dial_number_index && (interphone_mode != INTERPHONE_MODE_MPT_CONV))
			{
				rcode = dial_number_to_id(g_dial_number);
				if ((g_dial_number[0] == '*') && (g_dial_number[1] == '0'))
				{
					send_status_msg_auto(rcode, *(g_dial_number + 2) & 0x00ff);
					tip = 0;
				}
				else
				{
					tip = background_id_exist_in_the_book(rcode);
					if (tip == 0)
					{
						tip = (uint8_t *)0xffffffff;
						call_ptt_async(CALL_PTT_ASYNC_CALLING, rcode, 0, 0, g_dial_number);
						set_sync_call_stack_type(SYNC_CALL_STACK_PTT_PRESSED);
						set_sync_call_stack_type(SYNC_CALL_STACK_PTT_RELEASED);
					}
				}
			}
		}
		delete_dial_number(0, 1);
		if (tip == (uint8_t *)0xffffffff)
		{
			if (rcode)
				ptt_pressed_tip(rcode, temp, 1000000);
		}
		else if (tip)
		{
			GL_LCD_TipDraw(0, tip, 0, 1000);
		}
	}
}

void standby_screen_poll(uint16_t index, uint32_t code)
{
#ifndef P1_DEL_UNNECESSARY_KEY_PROC

	uint32_t rcode;
	uint16_t next;
	uint8_t temp[30], *tip = 0, knob_adjust_volume, lr_key_switch_watch;
	USER_BOOK *book;


	if (code != KEY_CODE_KNOB_ENTER)
	{
		if ((code == KEY_CODE_KNOB_CLOCK) || (code == KEY_CODE_KNOB_ANTICLOCK))
		{
			knob_adjust_volume = (key_knob_adjust_volume() && (index != 0xfff1) && (index != 0xfff0)) ? 1 : 0;
			if (knob_adjust_volume)
				switch_watch_channel_delay_with_confirm();
		}
		else if ((code == KEY_CODE_LEFT) || (code == KEY_CODE_RIGHT))
		{
			lr_key_switch_watch = (maintain_setup_parameter(PARA_OPERATE_READ, KEY_FUNC_REMAP_VOLUME_IND, 0) == 2) ? 1 : 0;
			if (lr_key_switch_watch == 0)
				switch_watch_channel_delay_with_confirm();
		}
		else
		{
			switch_watch_channel_delay_with_confirm();
		}
	}

	if ((code != KEY_CODE_MIDDLE) && (code != KEY_CODE_KNOB_CLOCK) && (code != KEY_CODE_KNOB_ANTICLOCK))
		reset_key_knob_mode();

	if ((code >= KEY_CODE_0) && (code <= KEY_CODE_2) && IS_A1_ZZWPRO_MODE())	// KEY A/B/C
	{
		shortcut_input_process(code - 1);
		return;
	}

	if ((code >= KEY_CODE_0) && (code <= KEY_CODE_9))	// 0 - 9
	{
		insert_one_dial_number(code);
	}
	else if (code == KEY_CODE_STAR)	// *
	{
		insert_one_dial_number((uint32_t)THE_STAR_INPUT_CHAR + KEY_CODE_0 - '0');
	}
	else if (code == KEY_CODE_VIRT_RETURN)

#else	// P1_DEL_UNNECESSARY_KEY_PROC

	if (code == KEY_CODE_VIRT_RETURN)

#endif	// P1_DEL_UNNECESSARY_KEY_PROC

	{
//		if (g_static_ptr->misc_static_config.tactics_radio && is_bt_insert())	// 20220506: 1. ���������ȹ��ܲ�Ҫ�ˣ�����һ�����˰�����̨���С��ұ����Ǹ��汾�����ؼ���
//		{
//			GL_LCD_TipDraw(0, (uint8_t *)speaker_position_tip[maintain_setup_parameter(PARA_OPERATE_READ, LANGUAGE_PARAS_POS, 0) ? 1 : 0][set_speaker_chan(0xff)], 0, 1000);
//		}
//		else
		{
			clr_last_call_id(0, 1);
			delete_dial_number(1, 1);
		}
	}

#ifndef P1_DEL_UNNECESSARY_KEY_PROC

	else if ((code == KEY_CODE_KNOB_CLOCK) || (code == KEY_CODE_KNOB_ANTICLOCK))
	{
		if (knob_adjust_volume)
		{
			free_adjust_volume((code == KEY_CODE_KNOB_CLOCK) ? KEY_CODE_RIGHT : KEY_CODE_LEFT);
		}
		else
		{
			rcode = code;
standby_screen_switch_group:
			delete_dial_number(0, 1);
			clr_last_call_id(0, 1);

			next = search_next_watching_group(watch_index, rcode);	// search group, but no backgound
//			printf("index=%x, next=%x, watch_index=%d", index, next, watch_index);
			if ((next != 0xffff) && (next != watch_index))
			{
				if (next != 0xfffe)
				{
					if (switch_delay_timer_id == 0xff)				// ��һ���л��������ʼ�غ�����������ȷ���л�ʱ�����������أ�
					{
						watch_index_bak = watch_index;
						a1_watch_index_bak = a1_watch_index;
					}
					a1_watch_index = 0xffff;						// ��A1������Ч
					watch_index = next;
					maintain_stack_parameter(PARA_OPERATE_WRITE, STACK_PARA_WATCH_ID, &watch_index);
				}
				if (index != 0xfff0)								// switch at standby (self-defined iop)
				{
					book = get_watch_at_user_book(1, 0xffff, 0);
					SetLabel(page_standby, WATCH_NAME_LABEL_ID, book ? book->name : "");
					RefreshPageControl(page_standby, WATCH_NAME_LABEL_ID);
					if (IS_SWITCH_GRP_WITHOUT_CONFIRM() == 0)
						DrawControlObjReverse(page_standby->PageControls[WATCH_NAME_LABEL_ID]);
					if (interphone_mode != INTERPHONE_MODE_MPT_CONV)
					{
						gen_user_number_info(book->id, temp);
						SetLabel(page_standby, WATCH_NUMBER_LABEL_ID, temp);
						RefreshPageControl(page_standby, WATCH_NUMBER_LABEL_ID);
					}
					else
					{
						print_mpt_freq_info(book->bind_ch);
					}
				}

				reverse_standby_watch_name(key_knob_mode & KEY_KNOB_SWITCH_VOL_GRP_MASK);
			}

			if (IS_SWITCH_GRP_WITHOUT_CONFIRM() == 0)
				delay_to_set_stack_watch_with_confirm();
			else
				set_sync_call_stack_type(SYNC_CALL_STACK_SWITCH_WATCH);

			if (IS_SWITCH_GRP_FIXED_CONFIRM() == 0)
				set_switch_group_without_confirm(0, 0);
		}
	}
	else if ((code == KEY_CODE_LEFT) || (code == KEY_CODE_RIGHT))
	{
		if (lr_key_switch_watch)
		{
			rcode = (code == KEY_CODE_RIGHT) ? KEY_CODE_KNOB_CLOCK : KEY_CODE_KNOB_ANTICLOCK;
			goto standby_screen_switch_group;
		}
		else
		{
			free_adjust_volume(code);
		}
	}
	else if (code == KEY_CODE_KNOB_ENTER)
	{
		if (switch_delay_timer_id != 0xff)
		{
			switch_delay_timer_id = timer_destroy(switch_delay_timer_id);
//			DrawControlObjReverse(page_standby->PageControls[WATCH_NAME_LABEL_ID]);
//			if (g_runtime_inst.runtime_paras.misc_runtime_config.local_id_at_standby)
//			{
//				get_standby_watch_number_info(temp);
//				SetLabel(page_standby, WATCH_NUMBER_LABEL_ID, temp);
//				RefreshPageControl(page_standby, WATCH_NUMBER_LABEL_ID);
//			}
			refresh_standby_screen_watch_group();
			set_sync_call_stack_type(SYNC_CALL_STACK_SWITCH_WATCH);
			printf("Delay to set watch with confirm");
		}
		else
		{
			leave_the_standby_screen();
			delete_dial_number(0, 0);
			SwitchPage(show_main_screen);
		}
	}
	else if (code == KEY_CODE_MIDDLE)
	{
		reserve_key_knob_mode();
		reverse_standby_watch_name(1);
	}

#endif	// P1_DEL_UNNECESSARY_KEY_PROC

	else if (code == KEY_CODE_VIRT_SWITCH_PAGE)
	{
		leave_the_standby_screen();
	}

#ifndef P1_DEL_UNNECESSARY_KEY_PROC

	else if ((code == KEY_CODE_PTT) || (code == KEY_CODE_USER1) || (code == KEY_CODE_3PTT))
	{
		if ((interphone_mode == INTERPHONE_MODE_ZZW_ADHOC) && (code == KEY_CODE_USER1))
		{
			delete_dial_number(0, 0);
			maintain_stack_parameter(PARA_OPERATE_READ, STACK_ZZW_2PTT_CALL_ID, &rcode);
			call_ptt_async(CALL_PTT_ASYNC_CALLING, rcode, 0, 0, g_dial_number);
		}
		else
		{
			if (g_dial_number_index && (interphone_mode != INTERPHONE_MODE_MPT_CONV))
			{
				last_call_id_save = 0;	// ��������
				rcode = dial_number_to_id(g_dial_number);
				tip = background_id_exist_in_the_book(rcode);
				call_ptt_async(CALL_PTT_ASYNC_CALLING, tip ? 0 : rcode, 0, 0, g_dial_number);
				delete_dial_number(0, 1);
			}
			else
			{
				rcode = (get_books_item_content(watch_index, 1))->id;
			}
		}

		if (tip == 0)
		{
			if (interphone_mode < INTERPHONE_MODE_ZZW_ADHOC)
				ptt_pressed_tip(rcode, temp, 1000000);
		}
		else
		{
			GL_LCD_TipDraw(0, tip, 0, 1000);
		}
	}
	else if (code == KEY_CODE_WELL)
	{
		standby_screen_process_well();
	}
	else
	{
		if (code & 0xffff0000)	// repeat
		{
			rcode = code & 0x0000ffff;
			if (rcode == KEY_CODE_VIRT_RETURN)
			{
				delete_dial_number(0, 1);
			}
			else if ((rcode >= KEY_CODE_0) && (rcode <= KEY_CODE_9))	// 0 - 9
			{
//				insert_one_dial_number(rcode);
				if ((code >> 16) == 2)
					shortcut_input_process((uint16_t)rcode - 1);
			}
		}
	}

#endif	// P1_DEL_UNNECESSARY_KEY_PROC
}

void standby_screen_active_switch(uint16_t refresh_flag, uint32_t air_num)
{
	uint16_t count = get_books_item_count();

	if ((air_num == DYNAMIC_CONFIG_CANCEL) || (air_num == DYNAMIC_CONFIG_RECONF))
	{
		if (air_num == DYNAMIC_CONFIG_CANCEL)
			goto active_switch_need_not_switch;
	}
	else
	{
		if (air_num & DYNAMIC_CONFIG_DEL_GRP)		// del
		{
			if (watch_index > count + (uint8_t)(air_num >> 8))	// the watch is in the back of the deleted, so it should decrease 2 for searching
				watch_index--;
active_switch_need_not_switch:
			if (--watch_index >= count + (uint8_t)air_num)		// decrease 1 to search the original
				watch_index = count + (uint8_t)air_num - 1;		// point to the last(should be search at 0)
		}
		else
		{
			if (air_num & 0x00008000)							// the add is background
				goto active_switch_need_not_switch;
			else
				watch_index = count + (uint8_t)air_num - 2;		// point to the last second(because it will add 1(KEY_CODE_KNOB_CLOCK)
		}
	}

	standby_screen_poll(refresh_flag, KEY_CODE_KNOB_CLOCK);
}

void add_slot_detail_to_screen(uint8_t flag)	// 0-refresh(del & add again)
{
	uint8_t i, temp[2];
	GL_PageControls_TypeDef* label;

	if (slot_detail_total)
	{
		CLR_SLOT_DETAIL_TO_SCREEN();
		if (slot_detail_total == 2)
		{
			slot_occupy_mask[0] = (1 << 0) | (1 << 2) | (1 << 4);
			slot_occupy_mask[1] = (1 << 1) | (1 << 3) | (1 << 5);
			slot_occupy_mask[2] = 0;
		}
		else if (slot_detail_total == 3)
		{
			slot_occupy_mask[0] = (1 << 0) | (1 << 3);
			slot_occupy_mask[1] = (1 << 1) | (1 << 4);
			slot_occupy_mask[2] = (1 << 2) | (1 << 5);
		}
		else
		{
			memset(slot_occupy_mask, 0, 3);
		}

		for (i = 0, temp[1] = 0; i < slot_detail_total; i++)
		{
			temp[0] = meeting_label[i];
			label = NewLabel(temp, SLOT_DETAIL_LABEL_FONT_STYEL, BUTTON_ALIGN_MIDDLE, DRAW_BOX_SOLID_LINE/*DRAW_BOX_NO_LINE*/, LCD_COLOR_WHITE);
			AddPageControlObj(ZZW_SLOT_DETAIL_OBJ_ID + i, SLOT_DETAIL_LABEL_GET_X(i), SLOT_DETAIL_LABEL_GET_Y(), SLOT_DETAIL_LABEL_WIDTH, SLOT_DETAIL_LABEL_HEIGHT, label, page_standby);
		}

		if (flag == 0)
		{
			for (i = 0; i < slot_detail_total; i++)
				RefreshPageControl(page_standby, ZZW_SLOT_DETAIL_OBJ_ID + i);
		}

		SET_SLOT_DETAIL_TO_SCREEN();
	}
	else
	{
		CLR_SLOT_DETAIL_TO_SCREEN();
	}
}

uint8_t DestroyPageControl ( GL_Page_TypeDef* pPage, uint16_t obj_index );
void del_slot_detail_fr_screen(void)
{
	uint8_t i, index;

	CLR_SLOT_DETAIL_TO_SCREEN();
	for (i = 0, index = ZZW_SLOT_DETAIL_OBJ_ID + slot_detail_total - 1; i < slot_detail_total; i++, index--)
		DestroyPageControl(page_standby, index);
}

void set_slot_detail_total(uint8_t jump)
{
	uint8_t real_jump;

	real_jump = ((jump >= 1) && (jump <= 3)) ? (ZZWPRO_TR_STATUS_MAX_SLOT / jump) : ZZWPRO_TR_STATUS_MAX_SLOT;	// jump: 1-VO,2-V2,3-V3
	if (IS_SHOW_SLOT_DETAIL_TO_SCREEN())
	{
		if (real_jump != slot_detail_total)
		{
			set_slot_detail_widget_visible(0);
			del_slot_detail_fr_screen();
			slot_detail_total = real_jump;
			add_slot_detail_to_screen(0);
		}
	}
	else
	{
		slot_detail_total = real_jump;
	}
}

void draw_slot_detail_frame(uint8_t index, uint8_t solid)
{
//	if (IS_SHOW_SLOT_DETAIL_TO_SCREEN())
	{
		GL_LCD_DrawRect(SLOT_DETAIL_LABEL_GET_X(index) - SLOT_DETAIL_LABEL_FRAME_W_GAP / 2, SLOT_DETAIL_LABEL_GET_Y() - SLOT_DETAIL_LABEL_FRAME_H_GAP / 2,
			SLOT_DETAIL_LABEL_WIDTH + SLOT_DETAIL_LABEL_FRAME_W_GAP, SLOT_DETAIL_LABEL_HEIGHT + SLOT_DETAIL_LABEL_FRAME_H_GAP, solid ? LCD_COLOR_YELLOW : back_ground_color);
	}
}

void draw_slot_detail_reverse(uint8_t index)
{
//	if (IS_SHOW_SLOT_DETAIL_TO_SCREEN())
		DrawControlObjReverse(page_standby->PageControls[ZZW_SLOT_DETAIL_OBJ_ID + index]);
}

void set_slot_detail_widget_visible(uint8_t en)
{
	uint8_t i;

	if (en)
	{
		for (i = 0; i < slot_detail_total; i++)
		{
			SetControlObjVisible(page_standby->PageControls[ZZW_SLOT_DETAIL_OBJ_ID + i], 1);
			RefreshPageControl(page_standby, ZZW_SLOT_DETAIL_OBJ_ID + i);
		}
	}
	else
	{
		for (i = 0; i < slot_detail_total; i++)
		{
			SetControlObjVisible(page_standby->PageControls[ZZW_SLOT_DETAIL_OBJ_ID + i], 0);
			DrawControlObjInvisible(page_standby->PageControls[ZZW_SLOT_DETAIL_OBJ_ID + i]);
		}
	}
}

void test_print_rssi_dqi(void);
void show_slot_detail_at_bottom_screen(void)
{
	uint8_t i, slot_occupy;
#ifndef SHOW_SOLT_DETAIL_WITH_VOICE
	ZZWPRO_STATUS_STRUCT slot_status_new[ZZWPRO_TR_STATUS_MAX_SLOT];
#endif

	test_print_rssi_dqi();

	if (IS_SHOW_SLOT_DETAIL_TO_SCREEN() && (g_dial_number_index == 0))
	{
		set_slot_detail_total((uint8_t)(zzwpro_xcs & 0x07));

#ifndef SHOW_SOLT_DETAIL_WITH_VOICE
		memcpy(slot_status_new, get_channel_status_fr_can_frame(), sizeof(ZZWPRO_STATUS_STRUCT) * ZZWPRO_TR_STATUS_MAX_SLOT);
//		printf("%d %d %d %d %d %d(%02X)", slot_status_new[0].sta.rx, slot_status_new[1].sta.rx, slot_status_new[2].sta.rx,
//			slot_status_new[3].sta.rx, slot_status_new[4].sta.rx, slot_status_new[5].sta.rx, show_slot_detail_pre_status);
#endif

		if (slot_detail_total == ZZWPRO_TR_STATUS_MAX_SLOT)
		{
			for (i = 0; i < ZZWPRO_TR_STATUS_MAX_SLOT; i++)
			{
#ifndef SHOW_SOLT_DETAIL_WITH_VOICE
				if ((slot_status_new[i].sta.rx == 1) && ((show_slot_detail_pre_status & (1 << i)) == 0))
#else
				if ((receive_voice_counter[i]) && ((show_slot_detail_pre_status & (1 << i)) == 0))
#endif
				{
					show_slot_detail_pre_status |= 1 << i;
					draw_slot_detail_reverse(i);
				}
#ifndef SHOW_SOLT_DETAIL_WITH_VOICE
				else if ((slot_status_new[i].sta.rx == 0) && (show_slot_detail_pre_status & (1 << i)))
#else
				else if ((receive_voice_counter[i] == 0) && (show_slot_detail_pre_status & (1 << i)))
#endif
				{
					show_slot_detail_pre_status &= ~(1 << i);
					draw_slot_detail_reverse(i);
				}
			}

			if (own_playing_voice_now() || own_talking_now())
			{
				i = get_dsp_demod_zzw_slot();
				if (i < slot_detail_total)
					draw_slot_detail_reverse(i);
			}
		}
		else
		{
			for (i = 0, slot_occupy = 0; i < ZZWPRO_TR_STATUS_MAX_SLOT; i++)
			{
#ifndef SHOW_SOLT_DETAIL_WITH_VOICE
				if ((slot_status_new[i].sta.rx == 1) || (slot_status_new[i].sta.tx == 1))	// this slot has been occupied
#else
				if (receive_voice_counter[i])												// this slot has been occupied
#endif
					slot_occupy |= 1 << i;
			}

			for (i = 0; i < slot_detail_total; i++)
			{
				if ((slot_occupy & slot_occupy_mask[i]) && ((show_slot_detail_pre_status & (1 << i)) == 0))		// slot i has been occupied
				{
					show_slot_detail_pre_status |= 1 << i;
					draw_slot_detail_reverse(i);
				}
				else if (((slot_occupy & slot_occupy_mask[i]) == 0) && (show_slot_detail_pre_status & (1 << i)))
				{
					show_slot_detail_pre_status &= ~(1 << i);
					draw_slot_detail_reverse(i);
				}
			}
		}
	}
}

void show_standby_screen(void)
{
	GL_PageControls_TypeDef* label;
	uint32_t ctrl = 0;
	USER_BOOK *book;
	uint8_t temp[30];
	uint16_t watch_name_y = WATCH_NAME_LABEL_Y, watch_number_y = WATCH_NUMBER_LABEL_Y, resp_line_x, resp_line_y;
	GL_PageControls_TypeDef* label1 = 0;
	GL_PageControls_TypeDef* label2 = 0;
//	GL_PageControls_TypeDef* label3 = 0;


	/* ************** GRAPHIC LIBRARY - STANDBY SCREEN ************** */
	page_standby = malloc(sizeof(GL_Page_TypeDef));
	CreatePageObj(page_standby, standby_screen_poll);

	if (interphone_mode >= INTERPHONE_MODE_ZZWPRO_N)
	{
		watch_name_y -= 4;
		watch_number_y -= 10;
	}
//	if (get_real_esn_second_sector() != MOBILE_MODE_PDT_ZZW_811)
		lr_key_remap_ctrl(0);

	// dial string
	label = NewLabel(g_dial_number, DIAL_LABEL_LARGE_FONTS_STYLE, BUTTON_ALIGN_MIDDLE, DRAW_BOX_NO_LINE, front_ground_color);	// default: 24x12

	g_dial_number_index = 0;
	g_dial_number[g_dial_number_index] = 0;

	SetControlObjVisible(label, g_dial_number_index);
	AddPageControlObj(DIAL_LABEL_ID, 0, DIAL_LABEL_Y, LCD_PIXEL_WIDTH, DIAL_LABEL_Y_HEIGHT, label, page_standby);
	label = NewLabel(g_dial_number + DIAL_NUMBER_FIRST_LENGTH, DEFAULT_FONT_STYLE, BUTTON_ALIGN_MIDDLE, DRAW_BOX_NO_LINE, front_ground_color);
	SetControlObjVisible(label, (g_dial_number_index > DIAL_NUMBER_FIRST_LENGTH) ? 1 : 0);
	AddPageControlObj(DIAL_LABEL_ID2, DIAL_LABEL_ID2_X, DIAL_LABEL_ID2_Y, DIAL_LABEL_ID2_WIDTH, LABEL_DEFAULT_HEIGHT, label, page_standby);

	if (is_enter_instruction_to_conv())
	{
		ctrl = 1;
		maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_WATCH_ID, &watch_index);
		book = set_instruction_to_conv_exec_flag((uint8_t)DYNAMIC_CONFIG_RECONF);
	}
	else
	{
		book = get_watch_at_user_book(1, 0xffff, &watch_index);
		if (book && id_can_watch(interphone_mode, book->id))
			ctrl = 1;
	}

	if (interphone_mode == INTERPHONE_MODE_MPT_CONV)
	{
		if (book)
		{
			label1 = NewLabel(book->name, DEFAULT_FONT_STYLE, BUTTON_ALIGN_MIDDLE, DRAW_BOX_NO_LINE, front_ground_color);
#ifdef MPT_CONV_SHOW_FREQ
			AddPageControlObj(WATCH_NAME_LABEL_ID, 0, watch_name_y, LCD_PIXEL_WIDTH, LABEL_DEFAULT_HEIGHT, label1, page_standby);
#else
			AddPageControlObj(WATCH_NAME_LABEL_ID, 0, watch_name_y + DEFAULT_FONT_HEIGHT , LCD_PIXEL_WIDTH, LABEL_DEFAULT_HEIGHT, label1, page_standby);
#endif
			print_mpt_freq_info(book->bind_ch);
		}
		else
		{
			label1 = NewLabel((uint8_t*)get_user_name_address(), DEFAULT_FONT_STYLE, BUTTON_ALIGN_MIDDLE, DRAW_BOX_NO_LINE, front_ground_color);
			AddPageControlObj(WATCH_NAME_LABEL_ID, 0, watch_name_y, LCD_PIXEL_WIDTH, LABEL_DEFAULT_HEIGHT, label1, page_standby);
			DRAW_MPT_FREQ_INFO1("[no contacts]");
		}
	}
	else if (ctrl)
	{
		label1 = NewLabel(book->name, DEFAULT_FONT_STYLE, BUTTON_ALIGN_MIDDLE, DRAW_BOX_NO_LINE, front_ground_color);
		AddPageControlObj(WATCH_NAME_LABEL_ID, 0, watch_name_y, LCD_PIXEL_WIDTH, LABEL_DEFAULT_HEIGHT, label1, page_standby);

		if (last_call_id_save == 0)
		{
//			ctrl = get_standby_watch_number_info(0);
			if (g_runtime_inst.runtime_paras.misc_runtime_config.local_id_at_standby == 0)
				ctrl = book->id;
			else
				maintain_stack_parameter(PARA_OPERATE_READ, STACK_PARA_ID, &ctrl);
			gen_user_number_info(ctrl, temp);
		}
		else
		{
			get_be_called_name(last_call_id_save, temp);
			if (LAST_CALL_IS_GROUP())
			{
				if (last_call_timer_id == 0xff)
					last_call_timer_id = timer_initial(0, 10000, last_call_timer_handle);
			}
		}
		label2 = NewLabel(temp, DEFAULT_FONT_STYLE, BUTTON_ALIGN_MIDDLE, DRAW_BOX_NO_LINE, front_ground_color);
		AddPageControlObj(WATCH_NUMBER_LABEL_ID, 0, watch_number_y, LCD_PIXEL_WIDTH, LABEL_DEFAULT_HEIGHT, label2, page_standby);
		if (last_call_timer_id != 0xff)
		{
			ctrl = DEFAULT_FONT_WIDTH;
			resp_line_x = ERROR_TIP_X_OFFSET(temp, ctrl);
			resp_line_y = watch_number_y + LABEL_DEFAULT_HEIGHT - 1;
			ctrl *= p_strlen(temp);

			GL_LCDDrawLine(resp_line_x, resp_line_y, ctrl, front_ground_color, LCD_DIR_HORIZONTAL);
			GL_LCDDrawLine(resp_line_x, resp_line_y - 1, ctrl, front_ground_color, LCD_DIR_HORIZONTAL);
		}
	}
	else
	{
		label1 = NewLabel((uint8_t*)get_user_name_address(), DEFAULT_FONT_STYLE, BUTTON_ALIGN_MIDDLE, DRAW_BOX_NO_LINE, front_ground_color);
		AddPageControlObj(WATCH_NAME_LABEL_ID, 0, watch_name_y, LCD_PIXEL_WIDTH, LABEL_DEFAULT_HEIGHT, label1, page_standby);
		label2 = NewLabel("[no watching]", DEFAULT_FONT_STYLE, BUTTON_ALIGN_MIDDLE, DRAW_BOX_NO_LINE, front_ground_color);
		AddPageControlObj(WATCH_NUMBER_LABEL_ID, 0, watch_number_y, LCD_PIXEL_WIDTH, LABEL_DEFAULT_HEIGHT, label2, page_standby);
	}
	SetControlObjVisible(label1, !g_dial_number_index);
	SetControlObjVisible(label2, !g_dial_number_index);

	if (interphone_mode >= INTERPHONE_MODE_ZZWPRO_N)
		add_slot_detail_to_screen(1);

	page_standby->ShowPage( page_standby, GL_TRUE );

	update_standby_timer_id = timer_initial(0, 400, show_slot_detail_at_bottom_screen);
//	draw_slot_detail_frame(0, 1);
//	draw_slot_detail_frame(3, 1);
//	draw_slot_detail_frame(5, 1);
//	draw_slot_detail_reverse(0);
//	draw_slot_detail_reverse(2);
//	draw_slot_detail_reverse(5);
}

void shourtcut_switch_watch_from_book(uint16_t which_to_watch)
{
	USER_BOOK *book;
/*
	// use user group
	book = get_books_item_content(which_to_watch, 0);
	if (book && id_can_watch(interphone_mode, book->id))
	{
		watch_index = which_to_watch;
		maintain_stack_parameter(PARA_OPERATE_WRITE, STACK_PARA_WATCH_ID, &watch_index);
		stack_set_watch();

		delete_dial_number(0, 1);

		refresh_standby_screen_name_and_number(book, 1);
	}
*/
	book = get_user_book_item(which_to_watch);			// use user_book
	if (book && id_can_watch(interphone_mode, book->id))
	{
		if (switch_delay_timer_id == 0xff)				// ��һ���л��������ʼ�غ�����������ȷ���л�ʱ�����������أ�
		{
			watch_index_bak = watch_index;
			a1_watch_index_bak = a1_watch_index;
		}
		a1_watch_index = which_to_watch;				// ����ABC���ܼ�����A1������Ч���ɲ��޸�watch_index��watch_index_bak��

		if (IS_SWITCH_GRP_WITHOUT_CONFIRM())
		{
			refresh_standby_screen_name_and_number(book, 0);
		}
		else
		{
			refresh_standby_screen_name_and_number(book, 1);
			delay_to_set_stack_watch_with_confirm();
		}
	}
}

#if 0
uint16_t lab_value[6] = {350, 117, 11, 12, 13, 14};
const uint8_t lab_unit[6][4] = {
	"MHz", "dB", "W", "C", "G", "CM"};
const uint8_t lab_pos_x[6] = {0, 0, 0, 52, 0, 112};
const uint8_t lab_pos_y[6] = {12, 24, 52, 52, 0, 36};
const uint8_t lab_pos_xsize[6] = {128, 128, 64, 52, 24, 16};
const uint8_t lab_pos_ysize[6] = {12, 12, 12, 12, 12, 12};
void fresh_standby_test(void)
{
	uint8_t i, tmp[20];

	for (i = 0; i < 6; i++)
	{
		if (i == 2)
			i = 3;
		else if (i == 3)
			i = 2;
		lab_value[i]++;
		sprintf((char *)tmp, "%02d%s", lab_value[i], lab_unit[i]);
		SetLabel(page_standby, i, tmp);
		RefreshPageControl(page_standby, i);
		if (i == 3)
			i = 2;
		else if (i == 2)
			i = 3;
	}
}

void show_standby_screen3(void)
{
	GL_PageControls_TypeDef* label;
	uint8_t i, tmp[20];


	/* ************** GRAPHIC LIBRARY - STANDBY SCREEN ************** */
	page_standby = malloc(sizeof(GL_Page_TypeDef));
	CreatePageObj(page_standby, standby_screen_poll);

	// dial string
	for (i = 0; i < 6; i++)
	{
		sprintf((char *)tmp, "%02d%s", lab_value[i], lab_unit[i]);
		label = NewLabel(tmp, 2, BUTTON_ALIGN_MIDDLE, DRAW_BOX_SOLID_LINE, front_ground_color);
		AddPageControlObj(i, lab_pos_x[i], lab_pos_y[i], lab_pos_xsize[i], lab_pos_ysize[i], label, page_standby);
	}

	page_standby->ShowPage( page_standby, GL_TRUE );

	update_standby_timer_id = timer_initial(0, 1000, fresh_standby_test);
}

void show_standby_screen(void)
{
	GL_PageControls_TypeDef *label;

	uint8_t text[20];
    /* ************** GRAPHIC LIBRARY - STANDBY SCREEN ************** */
	page_standby = malloc(sizeof(GL_Page_TypeDef));
	CreatePageObj(page_standby, standby_screen_poll);

	//----------------------------------------------------------------------
	sprintf((char *)text, "%02d%s", lab_value[0], lab_unit[0]);
	label = NewLabel(text, DEFAULT_FONT_STYLE, BUTTON_ALIGN_MIDDLE, 0, front_ground_color);
	AddPageControlObj(0, 0, 15, LCD_PIXEL_WIDTH, LABEL_DEFAULT_HEIGHT, label, page_standby);
	//----------------------------------------------------------------------

	sprintf((char *)text, "%02d%s", lab_value[1], lab_unit[1]);
  label = NewLabel(text, DEFAULT_FONT_STYLE, BUTTON_ALIGN_LEFT, 0, front_ground_color);
	AddPageControlObj(1, 0, 33, LCD_PIXEL_WIDTH, LABEL_DEFAULT_HEIGHT, label, page_standby);

	//----------------------------------------------------------------------
	sprintf((char *)text, "%02d%s", lab_value[2], lab_unit[2]);
	label = NewLabel(text, 2, BUTTON_ALIGN_LEFT, 0, front_ground_color);
	AddPageControlObj(2, 0, 54, 50 , 12, label, page_standby);

	sprintf((char *)text, "%02d%s", lab_value[3], lab_unit[3]);
	label = NewLabel(text, 2, BUTTON_ALIGN_MIDDLE, 0, front_ground_color);
	AddPageControlObj(3, 52, 52, 24 , 12, label, page_standby);

  label = NewLabel((uint8_t *)"GATE", 2, BUTTON_ALIGN_MIDDLE, 0, front_ground_color);
	AddPageControlObj(4, 0, 0, 24, 12, label, page_standby);

	label = NewLabel((uint8_t *)"PTT", 2, BUTTON_ALIGN_MIDDLE, 0, front_ground_color);
	AddPageControlObj(5, 29, 0, 20, 12, label, page_standby);

  label = NewLabel((uint8_t *)"TALK", 2, BUTTON_ALIGN_MIDDLE, 0, front_ground_color);
  AddPageControlObj(6, 52, 0, 24, 12, label, page_standby);


  label = NewLabel((uint8_t *)"1", 2, BUTTON_ALIGN_MIDDLE, 0, front_ground_color);
  AddPageControlObj(7, 78, 0, 16, 12, label, page_standby);

  label = NewLabel((uint8_t *)"2", 2, BUTTON_ALIGN_MIDDLE, 0, front_ground_color);
  AddPageControlObj(8, 94, 0, 8, 12, label, page_standby);


	label = NewLabel((uint8_t *)"G", 2, BUTTON_ALIGN_MIDDLE, 0, front_ground_color);
	AddPageControlObj(9, 81, 52, 8, 12, label, page_standby);

  label = NewLabel((uint8_t *)"IP", 2, BUTTON_ALIGN_MIDDLE, 0, front_ground_color);
	AddPageControlObj(10, 93, 52, 16, 12, label, page_standby);


  label = NewLabel((uint8_t *)"LM", 2, BUTTON_ALIGN_MIDDLE, 0, front_ground_color);
  AddPageControlObj(11, 110, 52, 16, 12, label, page_standby);

  page_standby->ShowPage(page_standby, GL_TRUE);

  DrawControlObjInvisible(page_standby->PageControls[10]);

  update_standby_timer_id = timer_initial(0, 80, fresh_standby_test);   //�� 80ms �Ľ������
}
#endif

