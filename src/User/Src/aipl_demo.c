/**
 * @file aipl_demo.c
 * <AUTHOR> (<EMAIL>)
 * @brief
 * deamon servie for air interface physical layer
 * @version V1.001
 * @date 2024-08-19
 *
 * @copyright victel (c) 2024
 *
 * @par modifies history:
 * Date            Version   Author      Description
 * 2024-08-19      V1.001    liming
 */

#define _XOPEN_SOURCE 700
#include <stdio.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <sys/un.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include "aipl.h"
#include <sys/stat.h>
#include <errno.h>
#include "time_utils.h"
#include "victel_digital_spi_arm.h"
#include "victel_digital_hal.h"
#include "vlog.h"

static OS_THREAD_TYPE hThreadAipld;

#define THREAD_PRIORITY_AIPL_CLIENT   88

static void *gQueueAiplMessage;
tim_instant_t ins_aipl;

static uint8_t aipl_msg[1024];
extern MCU_INF_TYPEDEF *g_mcu_inf_struct;
int AcceptMessage(void *msg, int size)
{
//	tim_duration_t elapsed;

    static uint32_t counter  = 0 ;
    // uint8_t *pDataSrc;
    // uint8_t *pDataDest;
    // int i = 0;
    counter++;
    // vlog_v("aipl","entry %s %d::%u msg=%p spi=%p size=%d sizeof(MCU_FRAME_TYPEDEF)=%d",__FUNCTION__,__LINE__,counter,msg,&g_mcu_inf_struct->spi_frame,size,sizeof(MCU_FRAME_TYPEDEF));
    g_mcu_inf_struct->spi_frame.data[0] = 0xff;
    // memcpy((void *)(&g_mcu_inf_struct->spi_frame), (void *)aipl_msg, sizeof(MCU_FRAME_TYPEDEF));
	// memcpy((void *)(&g_mcu_inf_struct->spi_frame), (void *)msg, sizeof(MCU_FRAME_TYPEDEF));
    memcpy((void *)(&g_mcu_inf_struct->spi_frame), (void *)msg, size);

    // pDataSrc = (uint8_t*)msg;
    // pDataDest = (uint8_t*)(&g_mcu_inf_struct->spi_frame);
    // vlog_hexdump("aipl",16,msg,size);

    // for(i = 0;i < size;i++)
    // {
    //     pDataDest[i] = pDataSrc[i];
    // }

	OS_PostQ(gQueueAiplMessage, (void *)(&g_mcu_inf_struct->spi_frame), OS_AL_WAIT);
//	tim_instant_elapsed(&ins_aipl, &elapsed);
//	tim_instant_now(&ins_aipl);
//	printf("send msg to process thread(%llu)", tim_duration_to_usecs(&elapsed));
    // vlog_v("aipl","exit %s %d::%u",__FUNCTION__,__LINE__,counter);
    return 0;
}

void *ThreadAiplMessage(void *args)
{
    tsAiplMsg *pMsg;
//	static uint8_t slot_num = 0;

    while(1)
    {
		pMsg = OS_WaitQ(gQueueAiplMessage, OS_AL_WAIT);
        if (pMsg)
        {
//			dsp_slot_edge_interrupt((slot_num++) & 0x01);
			mcu_inf_rcv_frame();
        }
		else
		{
			printf("wait aipl msg=null?");
		}
    }
}

int aipl_pa_ctrl(uint8_t type, uint8_t flag)	// type:0-speaker,1-mic; flag:0-close,1-open
{
	tsSpeakerCtrlReqType scrt;

	scrt.cmd = flag;
	aipl_MsgSendToAipld(type ? AIPL_MIC_CTRL_REQ : AIPL_SPEAKER_CTRL_REQ, &scrt, sizeof(tsSpeakerCtrlReqType));
//	printf("\tsend aipl(%d)=%02d", AIPL_SPEAKER_CTRL_REQ, scrt.cmd);
	return 0;
}

int aipl_main(void)
{
	tim_instant_now(&ins_aipl);
	gQueueAiplMessage = OS_CreateQ(16);
	if(gQueueAiplMessage)
    {
        if(0 != OS_Create(&hThreadAipld, ThreadAiplMessage, NULL, THREAD_PRIORITY_AIPL_CLIENT))
        {
            perror("created client msg");
            exit(0);
        }
        vlog_i("WMI","aipl client msg recevied thread created!");
    }
    aipl_WMIInit(AcceptMessage);
    vlog_i("WMI","%s",aipl_VersionGet());

    return 0;
}


/*end of the file:aipl_demo.c */
