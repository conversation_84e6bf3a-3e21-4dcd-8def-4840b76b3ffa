/**
  ******************************************************************************
  *                Copyright (c) 2013, Victel tech Co., Ltd
  *                          All Rights Reserved
  *
  * <AUTHOR>
  * @version V1.0.0
  * @date    30-September-2013
  * @brief   This file provides
  *            - display the GPS information
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "global_define.h"
#include "stack_config.h"



#define SLOT_DETAIL_FONT_STYLE		1
#define SLOT_DETAIL_FONT_HEIGHT		DEFAULT_FONT_HEIGHT
#define SLOT_DETAIL_FONT_WIDTH		DEFAULT_FONT_WIDTH
#define SLOT_DETAIL_LINE_HEIGHT		(DEFAULT_FONT_HEIGHT + 4)


#define GEN_6SOLT_TEXT(text)											sprintf((char *)text, " TX RX TS RS RSSI DQI")
#define GEN_6SOLT_STATE_TEXT(text,solt,tx,rx,tx_solt,rx_solt,rssi,dqi)	sprintf((char *)text, "%d %c %c  %-2d %-2d %-4d %d", solt, tx, rx, tx_solt, rx_solt, rssi - 130, dqi)


uint8_t update_slot_detail_timer_id = 0xff;

static ZZWPRO_STATUS_STRUCT slot_status[ZZWPRO_TR_STATUS_MAX_SLOT];
extern POWER_CHECK_TYPEDEF rp_check[ZZWPRO_TR_STATUS_MAX_SLOT], fp_check[ZZWPRO_TR_STATUS_MAX_SLOT];

#ifndef P1_DEL_UNNECESSARY_KEY_PROC

GL_Page_TypeDef *page_slot_detail;


void slot_detail_show_poll(uint16_t index, uint32_t code)
{
	if (IS_RETURN_KEYCODE(code))
	{
		update_slot_detail_timer_id = timer_destroy(update_slot_detail_timer_id);
		if (is_project_entry_at_shortcut())
			SwitchPage(show_project_screen);
		else
			SwitchPage(show_setup_screen);
	}
	else if (code == KEY_CODE_VIRT_SWITCH_PAGE)
	{
		update_slot_detail_timer_id = timer_destroy(update_slot_detail_timer_id);
	}
}

#endif	// P1_DEL_UNNECESSARY_KEY_PROC

uint8_t update_tx_rx_state_icon(uint8_t value)
{
	if(value == 1)
		return '*';
	else if(value == 0)
		return ' ';
	else
		return 0xff;
}

uint8_t is_output_slot_detail(void)
{
	return (update_slot_detail_timer_id == 0xff) ? 0 : 1;
}

void show_slot_detail(void)
{
	uint8_t i, tx_state, tx_slot, rx_state, rx_slot, rx_rssi, rx_dqi, slot_detail[100];
	uint16_t rp_watt, fp_watt;
	ZZWPRO_STATUS_STRUCT slot_status_new[ZZWPRO_TR_STATUS_MAX_SLOT];

	if (toggle_print_slot_detail(0xff))
	{
		GEN_6SOLT_TEXT(slot_detail);
		printf("\r\n\t%s", slot_detail);
	}

	memcpy(slot_status_new, get_channel_status_fr_can_frame(), sizeof(ZZWPRO_STATUS_STRUCT) * ZZWPRO_TR_STATUS_MAX_SLOT);
	for (i = 0; i < ZZWPRO_TR_STATUS_MAX_SLOT; i++)
	{
		tx_state = update_tx_rx_state_icon(slot_status_new[i].sta.tx);
		if (tx_state == 0xff)	// do NOT update tx related
		{
			tx_state = update_tx_rx_state_icon(slot_status[i].sta.tx);
			tx_slot = slot_status[i].sta.tx_slot;
		}
		else
		{
			slot_status[i].sta.tx = slot_status_new[i].sta.tx;
			tx_slot = slot_status_new[i].sta.tx_slot;
			slot_status[i].sta.tx_slot = tx_slot;
		}

		rx_state = update_tx_rx_state_icon(slot_status_new[i].sta.rx);
		if (rx_state == 0xff)	// do NOT update rx related
		{
			rx_state = update_tx_rx_state_icon(slot_status[i].sta.rx);
			rx_slot = slot_status[i].sta.rx_slot;
			rx_rssi = slot_status[i].rssi;
			rx_dqi = slot_status[i].dqi;
		}
		else
		{
			slot_status[i].sta.rx = slot_status_new[i].sta.rx;
			rx_slot = slot_status_new[i].sta.rx_slot;
			rx_rssi = slot_status_new[i].rssi;
			rx_dqi = slot_status_new[i].dqi;
			slot_status[i].sta.rx_slot = rx_slot;
			slot_status[i].rssi = rx_rssi;
			slot_status[i].dqi = rx_dqi;
		}

		GEN_6SOLT_STATE_TEXT(slot_detail, i + 1, tx_state, rx_state, tx_slot, rx_slot, rx_rssi, rx_dqi);
		if (toggle_print_slot_detail(0xff) == 0)
		{
			GL_LCDPrintString(0, SLOT_DETAIL_LINE_HEIGHT * (i + 1), slot_detail, front_ground_color, back_ground_color, SLOT_DETAIL_FONT_STYLE, FONT_NONTRANSPARENT);
		}
		else
		{
			rp_watt = convert_rp_watt(cal_801_rp(rp_check[i].pwr_avg_value));
			fp_watt = cal_801_fp(fp_check[i].pwr_avg_value);
			printf("\t%s  %d.%d %d", slot_detail, rp_watt >> 4, rp_watt & 0x0f, fp_watt);
		}
	}
}

#ifndef P1_DEL_UNNECESSARY_KEY_PROC

void show_slot_detail_screen(void)
{
	uint8_t slot_detail[40];

	/* ************** GRAPHIC LIBRARY - GPS INFORMATION SHOW DETAIL SCREEN ************** */
	page_slot_detail = malloc(sizeof(GL_Page_TypeDef));
	CreatePageObj(page_slot_detail, slot_detail_show_poll);


	page_slot_detail->ShowPage( page_slot_detail, GL_TRUE );

	toggle_print_slot_detail(0);
	update_slot_detail_timer_id = timer_destroy(update_slot_detail_timer_id);

	memset(slot_status, 0, sizeof(ZZWPRO_STATUS_STRUCT) * ZZWPRO_TR_STATUS_MAX_SLOT);
	GEN_6SOLT_TEXT(slot_detail);
	GL_LCDPrintString(0, 0, slot_detail, front_ground_color, back_ground_color, SLOT_DETAIL_FONT_STYLE, FONT_NONTRANSPARENT);

	show_slot_detail();

	update_slot_detail_timer_id = timer_initial(0, 800, show_slot_detail);
	if (update_slot_detail_timer_id == 0xff)
		slot_detail_show_poll(0, KEY_CODE_VIRT_RETURN);
}

#endif	// P1_DEL_UNNECESSARY_KEY_PROC

void output_slot_detail_to_uart(void)
{
	if (is_output_slot_detail())
	{
		if (toggle_print_slot_detail(0xff) == 0)
		{
			printf("Please exit the detail screen first");
		}
		else
		{
			update_slot_detail_timer_id = timer_destroy(update_slot_detail_timer_id);
			toggle_print_slot_detail(0);
		}
	}
	else
	{
		toggle_print_slot_detail(1);
		update_slot_detail_timer_id = timer_initial(0, 800, show_slot_detail);
	}
}


