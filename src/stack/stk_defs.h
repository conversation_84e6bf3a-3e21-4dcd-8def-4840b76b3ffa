/**
 * @file stk_defs.h
 * <AUTHOR> (<EMAIL>)
 * @brief 基于 《自组网VX协议栈使用手册20220723》 的头文件
 * @version 0.2
 * @date 2024-08-09
 *
 * Copyright (c) 1997-2024 Victel Technology Ltd.
 *
 */
#if !defined(__STK_DEFS_H__)
#define __STK_DEFS_H__
#include <stdint.h>

/*
 * Arm Compiler 4/5
 */
#if   defined ( __CC_ARM )
    #define STK_PACKED    __attribute__((packed))
/*
 * Arm Compiler 6 (armclang)
 */
#elif defined (__ARMCC_VERSION) && (__ARMCC_VERSION >= 6010050)
    #define STK_PACKED    __attribute__((packed, aligned(1)))
/*
 * GNU Compiler
 */
#elif defined ( __GNUC__ )
#define STK_PACKED    __attribute__((packed, aligned(1)))
/*
 * IAR Compiler
 */
#elif defined ( __ICCARM__ )
#if __ICCARM_V8
    #define STK_PACKED __attribute__((packed, aligned(1)))
#else
    /* Needs IAR language extensions */
    #define STK_PACKED __packed
#endif
/*
 * TI Arm Compiler
 */
#elif defined ( __TI_ARM__ )
#define STK_PACKED    __attribute__((packed))
/*
 * TASKING Compiler
 */
#elif defined ( __TASKING__ )
#define STK_PACKED  __packed__
/*
 * COSMIC Compiler
 */
#elif defined ( __CSMC__ )
#define STK_PACKED  @packed
#else
  #error Unknown compiler.
#endif

// 扩展呼叫
#define STACK_EX_CALL_RADIO_CHECK 0x00
#define STACK_EX_CALL_RADIO_DISABLE 0x7F
#define STACK_EX_CALL_RADIO_ENABLE 0x7E
#define STACK_EX_CALL_ALERT 0x1F
#define STACK_EX_CALL_REMOTE_MONITOR 0x1D
#define STACK_EX_CALL_EMERGENCY_ALARM 0x27
// 模式控制:01：1跳链路，02：2跳链路。03：3跳链路，04：6跳链路
#define STACK_LINK_HOP_1 1
#define STACK_LINK_HOP_2 2
#define STACK_LINK_HOP_3 3
#define STACK_LINK_HOP_6 4

typedef enum
{
    STACK_CALL_TYPE_READ_VERSION = 0,
    STACK_CALL_TYPE_INITIAL = 1,
    STACK_CALL_TYPE_SET_PARAS = 2,
    STACK_CALL_TYPE_SET_NETWORK = 3,
    STACK_CALL_TYPE_SET_RESPONSE = 4,
    STACK_CALL_TYPE_RESET = 8,
    STACK_CALL_TYPE_SLOT_SYNC = 9,
    STACK_CALL_TYPE_AIR_SIGNAL = 10,
    STACK_CALL_TYPE_MIC_VOICE = 11,
    STACK_CALL_TYPE_INQUERY = 12,
    STACK_CALL_TYPE_GPS_UPDATE = 13,
    STACK_CALL_TYPE_POWERDOWN = 14,
    STACK_CALL_TYPE_PTT_ACTIVE = 15,
    STACK_CALL_TYPE_PTT2_ACTIVE = 16,
    STACK_CALL_TYPE_CANCEL_ACTIVE = 17,
    STACK_CALL_TYPE_SWITCH_CTRL = 18,
    STACK_CALL_TYPE_SWITCH_WATCH = 19,
    STACK_CALL_TYPE_AUTH_ACK = 20,
    STACK_CALL_TYPE_SEND_SIGNAL = 21
} stk_call_type_t;

typedef enum
{
    STACK_RET_TYPE_NULL = 0,
    STACK_RET_TYPE_VERSION = 128,
    STACK_RET_TYPE_RADIO_ST = 129,
    STACK_RET_TYPE_AIR_SIGNAL = 130,
    STACK_RET_TYPE_SPK_VOICE = 131,
    STACK_RET_TYPE_CALL_PROC = 132,
    STACK_RET_TYPE_CCHN_UPD = 134,
    STACK_RET_TYPE_PWRDWN_PERM = 135,
    STACK_RET_TYPE_AUTH_RQS = 136,
    STACK_RET_TYPE_DECODED_SIG = 137,
    STACK_RET_TYPE_SIG_MON = 192,
} stk_ret_type_t;

typedef enum
{
    STACK_GRP_TYPE_X = 0,      ///< 0， X跳频组
    STACK_GRP_TYPE_NV_O = 1,   ///< 1，V/N O组
    STACK_GRP_TYPE_NV_2H = 2,  ///< 2，V/N 2跳组
    STACK_GRP_TYPE_NV_3H = 3,  ///< 3，V/N 3跳组
    STACK_GRP_TYPE_NV_6H = 4,  ///< 4，V/N 6跳组
    STACK_GRP_TYPE_NV_Q6H = 5, ///< 5，V/N 快速6跳组
} stk_grp_type_t;

#pragma pack(push, 1)
/** @defgroup 协议栈接口参数
 * @{
 */
/**
 * @brief 电台工作模式
 * @ref stk_param_base_t
 */
typedef union
{
    struct
    {
        uint8_t priority :2;   ///< 优先级
        uint8_t pdt_dmr :1;    ///< PDT/DMR(1:PDT; 0:DMR)
        uint8_t stun :1;       ///< 遥晕
        uint8_t kill :1;       ///< 遥毙
        uint8_t gps :1;        ///< GPS上拉
        uint8_t ptt :1;        ///< PTT授权
        uint8_t patcs :1;      ///< PATCS
        uint8_t slot2_tx :1;   ///< 发送时隙2音频
        uint8_t slot2_rx :1;   ///< 接收时隙2音频
        uint8_t a_vgrp :1;     ///< 允许组呼
        uint8_t a_vdiv :1;     ///< 允许单呼
        uint8_t a_smsg :1;     ///< 允许短数据呼叫
        uint8_t a_stc :1;      ///< 允许状态呼叫
        uint8_t a_allc :1;     ///< 允许全系统呼叫
        uint8_t a_pstn :1;     ///< 允许市话呼叫
        uint8_t a_emerg :1;    ///< 允许紧急呼叫
        uint8_t rev_auth :1;   ///< 反向鉴权
        uint8_t cps :1;        ///< CPS信道方案
        uint8_t grp_attach :1; ///< 组附着
        uint8_t sig_mon :1;    ///< 信令监测
        uint8_t moto :1;       ///< MOTO
        uint8_t hl_auth :1;    ///< 高级鉴权
        uint8_t monitoring :1; ///< 监听
        uint8_t handover :1;   ///< 越区切换
        uint8_t prevent :2;    ///< 00：全回避，01：色码回避，10：不回避
        uint8_t auto_ch :1;    ///< 自动频点
        uint8_t conv_login :1; ///< 常规登录
        uint8_t alarm_ack :1;  ///< 报警应答
        uint8_t direct :1;     ///< 直通中继
        uint8_t analog :1;     ///< 模拟电台
    } b;
    uint32_t v;
} stk_mode_t;
/**
 * @brief 电台工作模式(扩展)
 * @ref stk_param_base_t
 */
typedef union
{
    struct
    {
        uint8_t adhoc :1;      ///< 自组网
        uint8_t station :1;    ///< 基站
        uint8_t hi_freq :1;    ///< 高频
        uint8_t e_link :1;     ///< E 链路
        uint8_t a_link :1;     ///< A 链路
        uint8_t p_link :1;     ///< P 链路
        uint8_t g_link :1;     ///< G链路
        uint8_t vplus :1;      ///< V+
        uint8_t n :1;          ///< N
        uint8_t i_dis_mgmt :1; ///< 跨区管理
        uint8_t q :1;          ///< Q
        uint8_t periphery :1;  ///< 外围
        uint8_t centre :1;     ///< 中心
        uint8_t vq :1;         ///< VQ
        uint8_t monitoring :1; ///< 监听
        uint8_t mixing :1;     ///< 混音
        uint8_t t1 :1;         ///< T1：允许时隙1转发
        uint8_t t2 :1;         ///< T2：允许时隙2转发
        uint8_t t3 :1;         ///< T3：允许时隙3转发
        uint8_t t4 :1;         ///< T4：允许时隙4转发
        uint8_t t5 :1;         ///< T5：允许时隙5转发
        uint8_t t6 :1;         ///< T6：允许时隙6转发
        uint8_t mst_sync :1;   ///< 同步主站
        uint8_t n2v :1;        ///< N2V
        uint8_t v25k :1;       ///< V25K
        uint8_t fec :1;        ///< FEC
        uint8_t v2v :1;        ///< V2V
        uint8_t vmod :1;       ///< VMOD
        uint8_t p_trunking :1; ///< 伪集群
        uint8_t dyn_slot :1;   ///< 动态时隙
        uint8_t cid :1;        ///< CID
        uint8_t rapid_link :1; ///< 快速链路
    } b;
    uint32_t v;
} stk_xmode_t;
/**
 * @brief 电台工作常数
 * @ref stk_param_base_t
 */
typedef struct
{
    uint8_t t_rand;     ///< 随机访问超时时间（2～60s，缺省10s）
    uint8_t t_nosig;    ///< 控制信道回落时间（1～15s，缺省5s）
    uint8_t t_pc;       ///< 话音呼叫超时时间（4~60s，缺省10s）
    uint8_t t_npc;      ///< 数据呼叫超时时间（2~20s，缺省5s）
    uint8_t t_pending;  ///< 被叫超时时间（2~60s，缺省5s）
    uint8_t t_alert;    ///< 振铃超时时间（2~30s，缺省10s）
    uint8_t t_dereg;    ///< 去登记超时时间（1～3s，缺省2s）
    uint8_t t_ptt;      ///< 单次PTT最大时间（10~60s，缺省60s）
    uint8_t t_inactive; ///< 话音信道回落时间（0~20s，缺省3s）
    uint8_t n_rqs;      ///< 呼叫重发次数（1~10，缺省6）
    uint8_t n_rqe;      ///< 紧急呼叫重发次数（1~20，缺省10）
    uint8_t n_maint;    ///< 撤线信令数（1~5，缺省4）
    uint8_t n_serr;     ///< 系统码容错（1~3，缺省2）
    uint8_t r_upper;    ///< 优先登录信号阈值上限（RSSI）
    uint8_t r_lower;    ///< 优先登录信号阈值下限（RSSI）
    uint8_t r_squelch;  ///< 静噪门阈值（RSSI）
    uint8_t t_csbk;     ///< 控制信令发送超时时间（1~10s，缺省3s）
    uint8_t n_csbk;     ///< 控制信令发送重发次数（1~10，缺省3）
    uint8_t n_ackw;     ///< 等待应答时隙数（4~15，缺省6）
    uint8_t n_lc;       ///< LC信令发送次数（1~4，缺省1）
    uint8_t r_home;     ///< 归属基站守候阈值（RSSI）
    uint8_t r_roar;     ///< 漫游基站守候阈值（RSSI）
    uint8_t b0;         ///< 保留
    uint8_t b1;         ///< 保留
} stk_conts_t;
/**
 * @brief RCODE
 * @ref stk_param_base_t
 */
typedef union
{
    struct
    {
        uint8_t cps_s :4;
        uint8_t cps_m :4;
        uint8_t cps_l :4;
        uint8_t _reserved :3;
        uint8_t is_cps :1;
    } cps;
    struct
    {
        uint16_t grp_sp :13;
        uint8_t _reserved :2;
        uint8_t is_cps :1;
    } c1343;
    uint16_t v;
} stk_rcode_t;
/**
 * @brief 组信息
 *
 */
typedef union
{
    struct
    {
        uint32_t id :24;
        uint8_t type :8;
    } u;
    struct
    {
        uint32_t id :24;
        uint8_t is_conv :1;
        uint8_t direct :1;
        uint8_t slot :2;
        uint8_t pq_grp :1;
        uint8_t _reserved :2;
        uint8_t global :1;
    } pdt_c; // PDT 常规
    struct
    {
        uint32_t id :24;
        uint8_t is_conv :1;
        uint8_t direct :1;
        uint8_t n_att :1;   // =0，归属组，=1，参与组
        uint8_t dyn_grp :1; // =1，动态组
        uint8_t pq_grp :1;
        uint8_t _reserved :2;
        uint8_t global :1;
    } pdt_t; // PDT 集群
    struct
    {
        uint32_t id :24;
        uint8_t gt :3;    ///< @ref stk_grp_type_t
        uint8_t slot :3;  ///< =1~6，绑定时隙1~6，=0，不绑定时隙
        uint8_t relay :1; ///< =1，中继组，=0，直通组
        uint8_t _reserved :1;
    } xvn;
    uint32_t v;
} stk_grp_t;
typedef union
{
    struct
    {
        uint8_t pdu :1;   ///< CM.0=1，PDU码，=0，信道FEC编码
        uint8_t last :1;  ///< CM.1=1，连续信令的最后一帧
        uint8_t slot2 :1; ///< CM.2=0，时隙1信令，=1，时隙2信令
    } b;
    uint8_t v;
} stk_cm_t;
typedef enum
{
    STACK_ST_PRIV = 0,              ///< 0：私有信令
    STACK_ST_LC_HEADER = 1,         ///< 1：语音头LC
    STACK_ST_LC_TEMINATOR = 2,      ///< 2：语音尾LC
    STACK_ST_CTRL = 3,              ///< 3：控制信令
    STACK_ST_MULTI_CTRL = 4,        ///< 4：多帧控制信令
    STACK_ST_MULTI_CTRL_SUBSEQ = 5, ///< 5：多帧信令后续帧
    STACK_ST_MSG_HEADER = 6,        ///< 6：短数据头
    STACK_ST_MSG_12 = 7,            ///< 7：1/2编码短数据
    STACK_ST_MSG_34 = 8,            ///< 8：3/4编码短数据
    STACK_ST_IDLE = 9,              ///< 9：空闲信令
    STACK_ST_VOICE_HEADER = 14,     ///< 14：音频头帧（同步码）
    STACK_ST_VOICE = 15,            ///< 15：音频帧（嵌入LC信令）
    STACK_ST_RC = 17,               ///< 17：单嵌入（RC）信令
} stk_st_t;
typedef union
{
    struct
    {
        uint8_t _reserved :5; ///< 预留
        uint8_t tc :1;        ///< b5：tc=0，时隙1，=1，时隙2
        uint8_t at :1;        ///< b6: at=1,表示另一个时隙上行忙；
    } b;
    uint8_t v;
} stk_tact_t;
typedef struct
{
    uint8_t n_deg;  // NDEG：1B，维度，0～90
    uint8_t n_min;  // NMIN：1B，维分整数，0～60
    uint8_t n_minf; // NMINF：2B，维分小数，低14位有效
    uint8_t e_deg;  // EDEG：1B，经度，0～180
    uint8_t e_min;  // EMIN：1B，经分整数，0～60
    uint8_t e_minf; // EMINF：2B，经分小数，低14位有效
    uint8_t ewns;   // EWNS：1B，b0：GPS信号质量标志；b1：=0，西经，=1，东经；b2：=0，南纬，=1，北纬；b3：数据加密标志
    uint8_t speed;  // SPEED：1B，0～126节（Knot）
} stk_nema_s_t;
typedef struct
{
    uint8_t n_deg;  // NDEG：1B，维度，0～90
    uint8_t n_min;  // NMIN：1B，维分整数，0～60
    uint8_t n_minf; // NMINF：2B，维分小数，低14位有效
    uint8_t e_deg;  // EDEG：1B，经度，0～180
    uint8_t e_min;  // EMIN：1B，经分整数，0～60
    uint8_t e_minf; // EMINF：2B，经分小数，低14位有效
    uint8_t ewns;   // EWNS：1B，b0：GPS信号质量标志；b1：=0，西经，=1，东经；b2：=0，南纬，=1，北纬；b3：数据加密标志
    uint8_t speed;  // SPEED：1B，0～126节（Knot）
    uint8_t utc_h;  // UTCH：1B，UTC时
    uint8_t utc_m;  // UTCM：1B，UTC分
    uint8_t utc_s;  // UTCS：1B，UTC秒
} stk_nema_t;
typedef struct
{
    uint8_t _reserved :1;
    uint8_t priority :2;   ///< 呼叫优先级
    uint8_t ovcm :1;       ///< ovcm 呼叫
    uint8_t broadcast :1;  ///< 广播组呼
    uint8_t _reserved2 :1; ///< 预留
    uint8_t secure :1;     ///< 端到端加密语音呼叫
    uint8_t emergency :1;  ///< 紧急呼叫
} stk_ctype_pcall_t;
typedef union
{
    uint16_t v;
    struct
    {
        uint8_t type;
        union
        {
            uint8_t v;
            stk_ctype_pcall_t voice;
            stk_ctype_pcall_t data;
            struct
            {
                uint8_t _reserved :6;
                uint8_t secure :1; ///< 端到端加密短数据呼叫
            } short_msg;
            struct
            {
                uint8_t _reserved :7;
                uint8_t grp :1; ///< b7=1，状态组呼，=0，状态单呼
            } state;
        } u;
    } s;
} stk_ctype_t;
typedef enum
{
    STACK_SMT_BINARY = 0,    ///< 0：二进制
    STACK_SMT_ID = 1,        ///< 1：电台ID
    STACK_SMT_BCD = 2,       ///< 2：BCD
    STACK_SMT_ISO7 = 3,      ///< 3：ISO7
    STACK_SMT_ISO8 = 4,      ///< 4：ISO8
    STACK_SMT_NEMA = 5,      ///< 5：NEMA
    STACK_SMT_IPADDR = 6,    ///< 6：IP地址
    STACK_SMT_UNICODE = 7,   ///< 7：UNICODE
    STACK_SMT_CSM_NACK = 12, ///< 12：常规无确认短数据
    STACK_SMT_CSM_ACK = 13,  ///< 13：常规确认短数据
    STACK_SMT_TSM_NACK = 14, ///< 14：集群无确认短数据
    STACK_SMT_TSM_ACK = 15,  ///< 15：集群有确认短数据
} stk_short_msg_t;
typedef union
{
    uint8_t v;
    struct
    {
        uint8_t type :4;      ///< @ref stk_short_msg_t
        uint8_t _reserved :2; ///< reserved
        uint8_t sdm_l_h2 :2;  ///< SDM数据字节数的高2b
    } short_msg;
    struct
    {
        uint8_t code :7; ///< 7b状态码，电台应根据状态码显示为一段预置的中文
    } state;
} stk_code_t;
typedef union
{
    struct
    {
        uint16_t syscode; ///< PDT模式：当前守候基站系统码
    } pdt;
    struct
    {
        uint8_t slot_num :4;    ///< 当前时隙号
        uint8_t _reserved :4;   ///< 暂无定义
        uint8_t talk_slot :4;   ///< 电台当前通话时隙/发射音频时隙
        uint8_t choose_slot :4; ///< 判选接收音频时隙
    } xvn;
} stk_lai_t;
typedef enum
{
    STACK_ECT_AUTH = 0x24,      ///< 0x24：鉴权指令
    STACK_ECT_STUN_KILL = 0x27, ///< 0x27：遥晕遥毙
    STACK_ECT_SN_SYNC = 0x29,   ///< 0x29：序列号同步
    STACK_ECT_GET_VSSF = 0x00,  ///< 0x00：获取音频加密同步帧
    STACK_ECT_SET_VSSF = 0x01,  ///< 0x01：设置音频加密同步帧
} stk_ect_t;
typedef enum
{
    STACK_ECX_SUCCESS = 0,        ///< 0：成功
    STACK_ECX_FAIL = 1,           ///< 1：失败
    STACK_ECX_SN_OUT_OF_SYNC = 2, ///< 2：序列号不同步
} stk_ecx_t;
/**
 * @brief ECS
 * @attention 遥晕遥毙时有效
 */
typedef enum
{
    STACK_ECS_STUN = 0,   ///< 0：遥晕
    STACK_ECS_REVIVE = 1, ///< 1：复活
    STACK_ECS_KILL = 2,   ///< 2：遥毙
} stk_ecs_t;
typedef enum
{
    STACK_VTYPE_B0_NULL = 0,     ///< =0，NULL
    STACK_VTYPE_B0_BG_SCAN = 1,  ///< =1，背景扫描
    STACK_VTYPE_B0_TXP_CTRL = 2, ///< =2，发射功率控制
    STACK_VTYPE_B0_MSG_ACK = 3,  ///< =3，短数据应答
} stk_vtype_b0_t;
typedef struct
{
    uint8_t b0; ///< @ref stk_vtype_b0_t
    uint8_t b1; ///< =0，无空闲时隙，=1，时隙1空闲，=2，时隙2空闲
    uint8_t b2; ///< 保留
    uint8_t b3; ///< 保留
} stk_vtype_t;
typedef union
{
    struct
    {
        uint16_t ch;  ///< 信道号
        uint8_t rssi; ///< RSSI
        uint8_t zero; ///< 保留
    } bg_scan;
    struct
    {
        uint8_t val; ///< =1，增加（3dB），=2，降低（3dB），=3，最大，=4，最小
    } txp_ctrl;
    struct
    {
        uint8_t subseq_num; ///< 后续短数据帧数
    } msg_ack;
    uint32_t v;
} stk_cr0_t;
/**
 * @brief 通话状态
 */
typedef union
{
    struct
    {
        uint8_t busy :1;        ///< 通话中
        uint8_t conv :1;        ///< 常规通话
        uint8_t grp :1;         ///< 组通话
        uint8_t all_ms :1;      ///< 全系统呼叫
        uint8_t emergency :1;   ///< 紧急呼叫
        uint8_t broadcast :1;   ///< 广播呼叫
        uint8_t dual_slot :1;   ///< 双时隙信道模式
        uint8_t using_slot2 :1; ///< 当前通话在时隙2
        uint8_t ofs_mode :1;    ///< 偏移信道模式
        uint8_t ovcm :1;        ///< OVCM
        uint8_t secure :1;      ///< 端到端加密
        uint8_t rejoin :1;      ///< 组呼再入
        uint8_t env_mon :1;     ///< 环境监听
        uint8_t _reserved :2;   ///< 预留
        uint8_t calling :1;     ///< 本机主叫
        uint8_t talking :1;     ///< 本机讲话
        uint8_t playback :1;    ///< 回放音频
        uint8_t ptt_en :1;      ///< PTT授权
        uint8_t ptt_dis :1;     ///< PTT禁止
        uint8_t count_up :1;    ///< =1，通话时间正计数，=0，通话时间减计数
    } b;
    uint32_t v;
} stk_vs_t;
/**
 * @brief 待机状态
 */
typedef union
{
    struct
    {
        uint8_t pdt :1;         ///< =1，PDT，=0，DMR
        uint8_t conv :1;        ///< 常规模式
        uint8_t direct :1;      ///< 直通模式
        uint8_t stun :1;        ///< 遥晕
        uint8_t kill :1;        ///< 遥毙
        uint8_t s_scan :1;      ///< 短扫描中
        uint8_t l_scan :1;      ///< 长扫描中
        uint8_t logged_in :1;   ///< 已登录基站
        uint8_t slot_sync :1;   ///< 时隙同步
        uint8_t scode_ok :1;    ///< 系统码匹配
        uint8_t rand_access :1; ///< 允许随机访问
        uint8_t login_req :1;   ///< 入网要求登录
        uint8_t hl_auth :1;     ///< 高级鉴权
        uint8_t monitoring :1;  ///< 监听模式
        uint8_t a_bs :1;        ///< 归属基站
        uint8_t bg_scan :1;     ///< 背景扫描
        uint8_t sig_scan :1;    ///< 指令扫描
        uint8_t pwr_off :1;     ///< 关机中
        uint8_t ccode_ok :1;    ///< 色码匹配
        uint8_t slot :1;        ///< 时隙标志，=0，时隙1；=1，时隙2
        uint8_t link_bs :1;     ///< 联网基站
        uint8_t _reserved :3;   ///< 预留
        uint8_t cslot_1 :1;     ///< 常规绑定时隙1
        uint8_t cslot_2 :1;     ///< 常规绑定时隙2
        uint8_t _reserved2 :1;  ///< 预留
        uint8_t gps :1;         ///< 主动上传GPS
        uint8_t _reserved3 :2;  ///< 预留
        uint8_t adhoc :1;       ///< 自组网
        uint8_t analog :1;      ///< 模拟
    } b;
    uint32_t v;
} stk_cs_t;
/**
 * @brief 扩展待机状态
 */
typedef union
{
    uint32_t v;
    struct
    {
        uint8_t mode :3;       ///< =0，X，=1，V/N O，=2，V/N 2，=3，V/N 3，=4，V/N 6，=5，V/N S6
        uint8_t gps_sync :1;   ///< GPS同步
        uint8_t sta_sync :1;   ///< 基站同步
        uint8_t radio_sync :1; ///< 电台同步
        uint8_t hbeat_sync :1; ///< 心跳同步
        uint8_t slot_err :1;   ///< 时隙异常
        uint8_t n_mode :1;     ///< N 模式
        uint8_t q_mode :1;     ///< Q 模式
        uint8_t v25k_mode :1;  ///< V25K模式
        uint32_t _reserved :11; ///< 暂无定义
        uint8_t nvq_mode :1;   ///< 22b,NVQ模式（发Q收N/V）
    } b;
} stk_xcs_t;
typedef union
{
    struct
    {
        uint8_t incoming :1; ///< 呼入进行中
        uint8_t outgoing :1; ///< 呼出进行中
        uint8_t queued :1;   ///< 呼叫已排队
        uint8_t ringing :1;  ///< 振铃
        uint8_t success :1;  ///< 呼叫成功
        uint8_t fail :1;     ///< 呼叫失败（失败原因包括后面的重传次数完、超时、主叫取消、基站或被叫应答拒绝）
        uint8_t rt_done :1;  ///< 重传次数完毕
        uint8_t timeout :1;  ///< 超时
        uint8_t canceled :1; ///< 主叫取消
        uint8_t acked :1;    ///< 基站或被叫应答，原因见ACKW
    } b;
    uint32_t v;
} stk_step_t;
typedef union
{
    struct
    {
        uint8_t slot_sync :1;  ///< =1，当前时隙对齐GPS时间
        uint8_t gpsw_start :1; ///< =1，GPS窗口起始
    } b;
    uint8_t v;
} stk_pps_t;
typedef union
{
    /*
     主控软件转发协议栈信令到CAN总线时，将b6~0填充到CAN总线帧CMDX；
     接收到CAN总线数据帧时，将CMDX拷贝到PDN，并置b7=1，再转发到协议栈。
     接收到Q信令时，如果数据帧源模块CAN节点地址是奇数，则置PDN.0=1，表示是Q频点2信令。
     */
    struct
    {
        uint8_t slot :3;       ///< NV：0~5；
                               ///< Q：=0~3，Q频点1~4信令，主控软件跳频到指定频点发送信令；
                               ///< PDT：0~1。
        uint8_t pdt_sig :1;    ///< =1，PDT数字音频/信令，=0，FEC音频/信令；
        uint8_t v_sig :1;      ///< =1，V信令
        uint8_t n_sig :1;      ///< =1，N信令
        uint8_t q_sig :1;      ///< =1，Q信令
        uint8_t can_sig :1;    ///< =1，CAN信令；=0，空口信令；
        uint8_t slot2_sig :1;  ///< =0，接收1信令；=1，接收2信令
        uint32_t _reserved :22; ///< 预留
        uint8_t gps :1;        ///< =1，跳频发送GPS，主控软件跳频到专用数传频点，发送信令，然后返回当前频点。
    } b;
    uint32_t v;
} stk_pdn_t;
typedef union
{
    struct
    {
        uint8_t can_op :1; ///< b0=0，无线基带输出，=1，有线CAN输出
    } b;
    uint8_t v;
} stk_cws_t;
typedef union
{
    struct
    {
        uint8_t slot :3;    ///< b2~b0：时隙序号
        uint8_t from_bs :1; ///< =1，基站信号，=0，电台信号
        uint8_t type :4;    ///< b7~b4：信令类型
        // 1：语音头，2：语音尾，3：控制信令，6：短数据头帧，7：短数据后续帧，14：音频头帧，15：音频后续帧，9：PDT信令包
    } b;
    uint8_t v;
} stk_chs_t;
typedef enum
{
    STACK_SD_TYPE_V_IND = 0,  ///< 0：语音单呼
    STACK_SD_TYPE_V_GRP = 1,  ///< 1：语音组呼
    STACK_SD_TYPE_SM_IND = 4, ///< 4：短数据单呼
    STACK_SD_TYPE_SM_GRP = 5, ///< 5：短数据组呼
    STACK_SD_TYPE_GPS = 17,   ///< 17：GPS上传
    STACK_SD_TYPE_SYNC = 18,  ///< 18：同步
    STACK_SD_TYPE_INTR = 19,  ///< 19：强插
} stk_sd_type_t;
typedef union
{
    uint8_t v;
    struct
    {
        uint8_t gps_valid :1;  ///< b0：=1，NEMAS为有效数据
        uint8_t priority :2;   ///< b2b1：呼叫优先级
        uint8_t _reserved :1;  ///< reserved
        uint8_t broadcast :1;  ///< b4：广播组呼
        uint8_t _reserved2 :2; ///< reserved
        uint8_t emergency :1;  ///< b7：紧急呼叫
    } voice;
    struct
    {
        uint8_t subseq_num; ///< // 短数据呼叫后续帧数：0~3
    } short_msg;
} stk_sd_bit_t;
typedef union
{
    struct
    {
        uint8_t type;        ///< @ref stk_sd_type_t
        stk_sd_bit_t bit;      ///< @ref stk_sd_bit_t
        uint8_t id1[3];      ///< 被叫电台ID
        uint8_t id2[3];      ///< 主叫电台ID
        stk_nema_s_t gpsd; ///< 不包含UTC的GPS数据
    } voice;
    struct
    {
        uint8_t type;      ///< @ref stk_sd_type_t
        stk_sd_bit_t bit;    ///< @ref stk_sd_bit_t
        uint8_t id1[3];    ///< 被叫电台ID
        uint8_t id2[3];    ///< 主叫电台ID
        uint8_t data0[46]; ///< X：10B，V：46B，N：16B；V25：16B；Q：0
    } short_msg;
    struct
    {
        uint8_t data[46]; ///< X：10B，V：46B，N：16B；V25：16B；Q：0
    } short_msg_subseq;
} stk_sig_data_t;
typedef struct
{
    uint8_t ptt_on :1;    // BS.0：1，按下，0，松开 for PPT
    uint8_t ptt2_on :1;   // BS.1：1，按下，0，松开 for PPT2
    uint8_t _reserved :4; ///< 保留
    uint8_t q :1;         ///< BS.6：1，Q
    uint8_t nv :1;        ///< BS.7：1，N，0，V
} stk_bs_t;

/**
 * @}
 */

/** @defgroup 协议栈接口缓冲数据结构
 * @{
 */
/**
 * @brief 基本参数数据结构体
 * @ref STACK_CALL_TYPE_SET_PARAS
 */
typedef struct
{
    stk_conts_t conts; ///< 电台工作常数
    uint8_t esn[16];  ///< 电台电子码，有效位为79b，详见PDT协议
    uint32_t id;      ///< 电台空口号码, 实际使用低3B，最高字节需为0
    stk_mode_t mode;   ///< 电台工作模式
    uint16_t ctime;   ///< 内置通话时间 范围30～240s，缺省180s
    stk_rcode_t rcode; ///< rcode
    stk_xmode_t xmode; ///< 电台工作模式
    uint16_t gpsw;    ///< GPS窗口, 设置电台上传GPS的V时隙（180ms），=0，取消GPS自动上传功能
    uint8_t gpst;     ///< GPS时隙, 设置电台上传GPS的时隙，0~5
    uint8_t gpsp;     ///< GPS周期, 设置电台随机上传GPS的周期，对基站有效。=0，取消随机上传功能，上传周期时间=GPSP x 9s
} stk_param_base_t;
/**
 * @brief 控制信道参数结构体
 * @ref STACK_CALL_TYPE_SET_NETWORK
 */
typedef struct
{
    uint16_t nid;    ///< 网络ID, 格式同系统码，用于背景扫描控制信道时匹配基站系统码
    uint16_t nidm;   ///< 网络ID掩码, NID的掩码，如只比较NID的高8b，NIDM=0xFF00
    uint16_t chl;    ///< 起始信道, 长扫描控制信道起始信道号
    uint16_t chh;    ///< 结束信道, 长扫描控制信道结束信道号（若CHL=CHH=0，表示只搜索下面信道表中CHx，不进行长扫描）
    uint32_t ch[64]; ///< 基站控制信道, 信道频点（不用应设置为0），其中b12~b15为信道色码
} stk_param_cchn_t;
/**
 * @brief 响应组参数结构体
 * @ref STACK_CALL_TYPE_SET_RESPONSE
 */
typedef struct
{
    stk_grp_t grp[32];
} stk_grps_t;
/**
 * @brief 时隙同步数据结构体
 * @ref STACK_CALL_TYPE_SLOT_SYNC
 */
typedef struct
{
    stk_vtype_t vtype;
    stk_cr0_t cr0;
    stk_xcs_t xcs;
    uint32_t slots;
} stk_slot_sync_t;
/**
 * @brief NULL返回数据结构体
 * @ref STACK_RET_TYPE_NULL
 */
typedef struct
{
    stk_vtype_t vtype;
    stk_cr0_t cr0;
} stk_null_ret_t;
/**
 * @brief 版本信息数据结构体
 * @ref STACK_RET_TYPE_VERSION
 */
typedef struct
{
    uint8_t func[2]; ///< BCD编码，协议栈软件功能编码
    uint8_t ver[2];  ///< BCD编码，协议栈软件版本
    char date[12];   ///< 字符串“mmm dd yyyy”
    char time[9];    ///< 字符串“hh:mm:ss”
} stk_version_t;
/**
 * @brief 电台状态数据结构体
 * @ref STACK_RET_TYPE_RADIO_ST
 */
typedef struct
{
    stk_lai_t lai;    ///< 基站ID
    uint16_t chan;   ///< 当前信道, 241～640
    uint32_t id1;    ///< 被叫电台ID
    uint32_t id2;    ///< 主叫电台ID
    uint32_t id3;    ///< 当前讲话电台ID
    uint32_t ctimer; ///< 通话时间, 单位为s，减计数表示剩余通话时间，加计数表示通话已经进行的时间
    uint16_t tel;    ///< 分组数据呼叫临时分配被叫电台短号
    uint16_t sel;    ///< 分组数据呼叫临时分配主叫电台短号
    uint32_t tfreq;  ///< 发射频率, 指令常规后发射频率值（CHAN=0x0fff时有效）
    uint32_t rfreq;  ///< 接收频率, 指令常规后接收频率值（CHAN=0x0fff时有效）
    uint16_t gtime;  ///< GPS周期, 主动上传GPS时间周期（s），为0表示不启用
    uint16_t gdist;  ///< GPS距离, 主动上传GPS距离（m），为0表示不启用
    stk_xcs_t xcs;    ///< 扩展待机状态
    uint32_t slots;  ///< 时隙状态
    uint16_t qch[3]; ///< ????
} stk_radio_stat_t;
/**
 * @brief 空信令数据结构体
 * @ref STACK_CALL_TYPE_AIR_SIGNAL
 * @ref STACK_RET_TYPE_AIR_SIGNAL
 */
typedef union
{
    struct
    {
        uint8_t c[3];
        uint8_t v[33];
    } fec;
    struct
    {
        uint8_t zero;
        uint8_t cc; // 色码, 0～15
        uint8_t tact;
        uint8_t st;
        uint8_t p[18];
    } pdu;
    struct
    {
        uint8_t chs;
        uint8_t emb;
        uint8_t emb1;
        uint8_t emb2;
        uint8_t v[27];
    } n;
    struct
    {
        uint8_t chs;
        uint8_t emb;
        uint8_t emb1;
        uint8_t rssi;
        uint8_t v[27];
    } v;
    struct
    {
        uint8_t chs;
        uint8_t emb;
        uint8_t emb1;
        uint8_t rssi;
        uint8_t v[9];
    } q;
} stk_air_sig_t;
/**
 * @brief 音频数据结构体
 * @ref STACK_CALL_TYPE_MIC_VOICE
 * @ref STACK_RET_TYPE_SPK_VOICE
 */
typedef union
{
    struct
    {
        uint8_t zero[3];
        uint8_t v[33];
    } pdt;
    struct
    {
        uint8_t chs;
        uint8_t emb;
        uint8_t emb1;
        uint8_t emb2;
        uint8_t v[27];
    } n;
    struct
    {
        uint8_t chs;
        uint8_t emb;
        uint8_t emb1;
        uint8_t rssi;
        uint8_t v[27];
    } v;
    struct
    {
        uint8_t chs;
        uint8_t emb;
        uint8_t emb1;
        uint8_t rssi;
        uint8_t v[9];
    } q;
} stk_voice_t;
/**
 * @brief 呼叫进程数据结构体
 * @ref STACK_RET_TYPE_CALL_PROC
 */
typedef struct
{
    stk_ctype_t ctype;
    uint8_t code;
    uint8_t bytes;     ///< 短数据数据长度,SDM数据字节数/PSTN号码字节数的低8b，其它为0
    uint32_t id1;      ///< 被叫电台ID
    uint32_t id2;      ///< 主叫电台ID
    uint8_t sdm[1024]; ///< 短数据内容,短数据内容/PSTN拨号BCD码, 加密呼叫时，前12B为加密参数：0x04  0x08
                       ///< KEYI（8B加密参数） 0  0
} stk_call_proc_t;
/**
 * @brief 更新控制信道数据结构体
 * @ref STACK_RET_TYPE_CCHN_UPD
 */
typedef struct
{
    uint16_t ch[64];
} stk_cchn_update_t;
/**
 * @brief 鉴权请求数据结构体
 * @ref STACK_RET_TYPE_AUTH_RQS
 */
typedef struct
{
    uint8_t ect;        ///< @ref stk_ect_t
    uint8_t ecs;        ///< @ref stk_ecs_t
    uint8_t revw;       ///< 保留
    uint8_t edata[256]; ///< 高字节在前，数据内容、格式和长度同SD加密卡通信帧中DATA
} stk_auth_rqs_t;
/**
 * @brief 鉴权应答数据结构体
 * @ref STACK_CALL_TYPE_AUTH_ACK
 */
typedef struct
{
    uint8_t ect;        ///< @ref stk_ect_t
    uint8_t ecx;        ///< @ref stk_ecx_t
    uint8_t revw;       ///< 保留
    uint8_t edata[256]; ///< 高字节在前，数据内容、格式和长度同SD加密卡通信帧中DATA
} stk_auth_ack_t;
/**
 * @brief 信令数据结构体
 * @ref STACK_CALL_TYPE_SEND_SIGNAL
 * @ref STACK_RET_TYPE_DECODED_SIG
 */
typedef struct
{
    uint8_t zero[3];
    uint8_t st;    ///< 信令类型, @ref stk_st_t
    uint8_t p[18]; ///< P0 P1……P11/P17
} stk_raw_sig_t;
typedef struct
{
    uint8_t stype;   ///< 信令类型
    uint8_t csbko;   ///< 控制信令码
    uint8_t rssi;    ///< 信号场强
    uint8_t tact;    ///< 时隙标志, @ref stk_tact_t
    uint32_t slc;    ///< 公共信道信令
    uint8_t pdu[36]; ///< 信令PDU
} stk_sig_mon_t;
/**
 * @}
 */
/**
 * @brief 协议栈接口调用参数 R1 和 R2
 *
 */
typedef union
{
    uint32_t reg[2];
    struct
    {
        stk_pps_t pps; ///< @ref stk_pps_t
    } slot_sync;
    struct
    {
        uint32_t rssi;
        stk_pdn_t pdn; ///< @ref stk_pdn_t
    } air_signal;
    struct
    {
        stk_cws_t cws; ///< @ref stk_cws_t
    } inquery;
    struct
    {
        stk_bs_t bs;
    } ptt;
    struct
    {
        uint32_t chn;
    } sw_ctrl;
    struct
    {
        stk_grp_t grp0;
        uint32_t ch0;
    } sw_watch;
} stk_call_aux_t;
/**
 * @brief 协议栈接口调用返回 R1 和 R2
 *
 */
typedef union
{
    uint32_t reg[2];
    struct
    {
        stk_mode_t mode;
        stk_grp_t grp0;
    } null;
    struct
    {
        stk_cs_t cs;
        stk_vs_t vs;
    } radio_st;
    struct
    {
        stk_cm_t cm;
        uint8_t _padding[3];
        stk_pdn_t pdn;
    } air_signal;
    struct
    {
        stk_step_t step;
        uint32_t ackw;
    } call_proc;
    struct
    {
        uint32_t pd; ///< 写入FLASH，若PD=1,更新完控制信道参数后断电
    } cchn_upd;
    struct
    {
        uint32_t pd; ///< PD=1,允许断电
    } pwr_dwn;
    struct
    {
        stk_cs_t cs;
        uint32_t ch0;
    } sig_mon;
} stk_ret_aux_t;

#pragma pack(pop)

#endif // __STK_DEFS_H__
