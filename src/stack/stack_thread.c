/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <linux/rpmsg.h>
#include <sys/time.h>

//============================> Libraries Headers <============================
#include <ti_rpmsg_char.h>
#include "vlog.h"

//============================> Project Headers <============================
#include "ipc_stk_voc.h"
#include "time_utils.h"
#include "stack_thread.h"
#include <pthread.h>
#include <stack_config.h>
#include <global_define.h>
#include <DVSI.h>
#ifdef TEST_VOCODER_FUNCTION
#include <test_pcm16.h>
#endif



/******************************************************************************

                                Defines

 ******************************************************************************/

#define STACK_THREAD_SLEEP_TIME            30 // 协议栈线程休眠时间

#define STACK_LOG_TAG                      "Stack"
#define STACK_LOG_ENABLED                  1

#if (STACK_LOG_ENABLED == 1)
    #define STACK_LOG_D(fmt, ...)              vlog_d(STACK_LOG_TAG, fmt, ##__VA_ARGS__)
    #define STACK_LOG_I(fmt, ...)              vlog_i(STACK_LOG_TAG, fmt, ##__VA_ARGS__)
    #define STACK_LOG_W(fmt, ...)              vlog_w(STACK_LOG_TAG, fmt, ##__VA_ARGS__)
    #define STACK_LOG_E(fmt, ...)              vlog_e(STACK_LOG_TAG, fmt, ##__VA_ARGS__)
#else
    #define STACK_LOG_D(fmt, ...)
    #define STACK_LOG_I(fmt, ...)
    #define STACK_LOG_W(fmt, ...)
    #define STACK_LOG_E(fmt, ...)
#endif

/******************************************************************************

                                Prototypes

 ******************************************************************************/

/******************************************************************************

                                Variables

 ******************************************************************************/

static pthread_t vocoder_thread_id;
static pthread_mutex_t ipc_lock;

#ifdef TEST_VOCODER_FUNCTION
    FILE *f_enc_dec;
	uint8_t file_end_flag = 0;
#endif

/******************************************************************************

                                Static Functions

 ******************************************************************************/

//============================> Helper Functions <============================

//============================> Thread Function <============================
void wait_ipc_idle(uint8_t *pre_str, uint16_t ext_wait_us)
{
#ifdef XFER_MEASURE_TIME
	tim_instant_t ins;
	tim_duration_t elapsed;
	uint64_t wait1, wait2;

	tim_instant_now(&ins);
#endif

	pthread_mutex_lock(&ipc_lock);

#ifdef XFER_MEASURE_TIME
	tim_instant_elapsed(&ins, &elapsed);
	wait1 = tim_duration_to_usecs(&elapsed);
	tim_instant_now(&ins);
#endif

	if (ext_wait_us)
	{
		usleep(ext_wait_us);
#ifdef XFER_MEASURE_TIME
		tim_instant_elapsed(&ins, &elapsed);
		wait2 = tim_duration_to_usecs(&elapsed);
#endif
	}
#ifdef XFER_MEASURE_TIME
	else
	{
		wait2 = 0;
	}
//	vlog_v("stack","%s mwait_ipc%c=%llu/,delay=%llu", (char *)pre_str, wait1 > 1000 ? 'B' : 'G', wait1, wait2);
	if (wait1 > 1000)
		vlog_v("stack","%s mwait_ipc/%c=%llu,delay=%llu", 'B', (char *)pre_str, wait1, wait2);
#endif
}

void set_ipc_idle(uint16_t ext_wait_us)
{
	if (ext_wait_us)
		usleep(ext_wait_us);
	pthread_mutex_unlock(&ipc_lock);
}

#ifdef XFER_MEASURE_TIME
	tim_instant_t ins_enc, ins_dec;
#endif
uint8_t ipc_ambe_encode(short *voice)	// 10ms
{
	wait_ipc_idle("enc_wait", 200);
//#ifdef XFER_MEASURE_TIME
//	vlog_v("stack","\r\nambe_enc");
//#endif

#ifdef XFER_MEASURE_TIME
	tim_instant_now(&ins_enc);
#endif
	ipc_sv_ambe_encode(0, voice, 0);

#ifdef XFER_MEASURE_TIME
	vlog_v("stack","c_ambe_enc/%c=%llu", ipc_sv_last_xfer_time() > 100 ? 'B' : 'G', ipc_sv_last_xfer_time());
//	if (ipc_sv_last_xfer_time() > 100)
//		vlog_v("stack","c_ambe_enc/%c=%llu", 'B', ipc_sv_last_xfer_time());
#endif
	set_ipc_idle(0);
	return 1;
}
uint8_t ipc_ambe_decode(uint8_t *codeword)	// 10ms
{
	short i, channel_codeword_dec[SELP_DATA_SIZE_FEC], *c = channel_codeword_dec;
	uint8_t *ptr = codeword;

	for (i = 0; i < SELP_DATA_SIZE_FEC; i += 8, ptr++)
	{
		*c++ = (*ptr & 0x80) ? 1 : 0;
		*c++ = (*ptr & 0x40) ? 1 : 0;
		*c++ = (*ptr & 0x20) ? 1 : 0;
		*c++ = (*ptr & 0x10) ? 1 : 0;
		*c++ = (*ptr & 0x08) ? 1 : 0;
		*c++ = (*ptr & 0x04) ? 1 : 0;
		*c++ = (*ptr & 0x02) ? 1 : 0;
		*c++ = (*ptr & 0x01) ? 1 : 0;
	}

	wait_ipc_idle("dec_wait", 200);
//#ifdef XFER_MEASURE_TIME
//	vlog_v("stack","\r\nambe_dec");
//#endif

#ifdef XFER_MEASURE_TIME
	tim_instant_now(&ins_dec);
#endif
	ipc_sv_ambe_decode(0, channel_codeword_dec, 0);

#ifdef XFER_MEASURE_TIME
	vlog_v("stack","c_ambe_dec/%c=%llu", ipc_sv_last_xfer_time() > 100 ? 'B' : 'G', ipc_sv_last_xfer_time());
//	if (ipc_sv_last_xfer_time() > 100)
//		vlog_v("stack","c_ambe_dec/%c=%llu", 'B', ipc_sv_last_xfer_time());
#endif
	set_ipc_idle(0);
	return 1;
}


#ifdef TEST_VOCODER_FUNCTION
static uint8_t save_ipc_enc_flag = 0, save_ipc_dec_flag = 0;
#endif
extern uint8_t encode_dat[];
extern int16_t pcm_voice_buffer[];
void save_ipc_enc_dec_callback(void *arg, uint8_t chn, bool encode, const void *p, uint32_t l)
{
	short *channel_codeword_enc = (short *)p;
	uint8_t i, *ptr = encode_dat;
#ifdef XFER_MEASURE_TIME
	tim_duration_t elapsed;
	uint64_t enc_dec_times;
#endif

	if (encode)		// 返回编码结果
	{
#ifdef XFER_MEASURE_TIME
		tim_instant_elapsed(&ins_enc, &elapsed);
		enc_dec_times = tim_duration_to_usecs(&elapsed);
		vlog_v("stack","\tt_ambe_enc/%c=%llu,l=%lu", enc_dec_times > 5500 ? 'B' : 'G', enc_dec_times, l);
#endif
		for (i = 0; i < SELP_DATA_SIZE_FEC; i += 8)
		{
			*ptr++ = (channel_codeword_enc[i] & 0x01) << 7 | (channel_codeword_enc[i + 1] & 0x01) << 6 |
				(channel_codeword_enc[i + 2] & 0x01) << 5 | (channel_codeword_enc[i + 3] & 0x01) << 4 |
				(channel_codeword_enc[i + 4] & 0x01) << 3 | (channel_codeword_enc[i + 5] & 0x01) << 2 |
				(channel_codeword_enc[i + 6] & 0x01) << 1 | (channel_codeword_enc[i + 7] & 0x01) << 0;
		}
#ifdef TEST_VOCODER_FUNCTION
		save_ipc_enc_flag++;
#else
		save_mic_encode_data(encode_dat);
#endif
	}
	else
	{
#ifdef XFER_MEASURE_TIME
		tim_instant_elapsed(&ins_dec, &elapsed);
		enc_dec_times = tim_duration_to_usecs(&elapsed);
		vlog_v("stack","\tt_ambe_dec/%c=%llu,l=%lu,%04x %04x %04x %04x", enc_dec_times > 3500 ? 'B' : 'G', enc_dec_times, l,
			((uint16_t *)p)[0], ((uint16_t *)p)[1], ((uint16_t *)p)[2], ((uint16_t *)p)[3]);
#endif
		memcpy((void *)pcm_voice_buffer, p, SELP_FRAME_SIZE * sizeof(short));
#ifdef TEST_VOCODER_FUNCTION
  		save_ipc_dec_flag++;
#else
		packet_spk_voice_to_mcu_inf_frame(SET_SPEAKER_VOICE_VOLUME, pcm_voice_buffer);
#endif
	}
}



#ifdef TEST_VOCODER_FUNCTION

void test_ipc_ambe_vocoder(void)
{
	static short *pcm16 = (short *)test_pcm16, ping_pang = 0;
	static uint16_t encode_pos = 0;
	short voice[SELP_FRAME_SIZE];
	static uint8_t codeword[SELP_BYTE_SIZE_FEC];


	if ((save_ipc_enc_flag == 0) && (save_ipc_dec_flag == 0))
	{
		save_ipc_enc_flag = 1;
		ipc_ambe_encode(pcm16);
		if (encode_pos + SELP_FRAME_SIZE * sizeof(short) > TEST_PCM16_LENGTH)
		{
			pcm16 = test_pcm16;
			encode_pos = 0;
			file_end_flag = 1;
//			fclose(f_enc_dec);
		}
		else
		{
			pcm16 += SELP_FRAME_SIZE;
			encode_pos += SELP_FRAME_SIZE * sizeof(short);
		}
	}
	else if (save_ipc_enc_flag == 2)
	{
		save_ipc_enc_flag = 3;
#if 0//def TEST_VOCODER_FUNCTION
		vlog_v("stack","\tENC_cb:%02x %02x %02x %02x %02x %02x %02x %02x %02x",
			encode_dat[0], encode_dat[1], encode_dat[2], encode_dat[3], encode_dat[4],
			encode_dat[5], encode_dat[6], encode_dat[7], encode_dat[8]);
#endif
		usleep(5000);
		save_ipc_dec_flag = 1;
		ipc_ambe_decode(encode_dat);
	}
	else if (save_ipc_dec_flag == 2)
	{
		save_ipc_dec_flag = 3;
#if 0//def TEST_VOCODER_FUNCTION
//		vlog_v("stack","\tDEC_cb:%02x %02x %02x %02x %02x %02x %02x %02x",
//			pcm_voice_buffer[0], pcm_voice_buffer[1], pcm_voice_buffer[2], pcm_voice_buffer[3],
//			pcm_voice_buffer[SELP_FRAME_SIZE - 4], pcm_voice_buffer[SELP_FRAME_SIZE - 3],
//			pcm_voice_buffer[SELP_FRAME_SIZE - 2], pcm_voice_buffer[SELP_FRAME_SIZE - 1]);
		vlog_v("stack","\tDEC_cb:%04x %04x %04x %04x %04x %04x %04x %04x",
			(uint16_t *)pcm_voice_buffer[0], (uint16_t *)pcm_voice_buffer[1], (uint16_t *)pcm_voice_buffer[2], (uint16_t *)pcm_voice_buffer[3],
			(uint16_t *)pcm_voice_buffer[SELP_FRAME_SIZE - 4], (uint16_t *)pcm_voice_buffer[SELP_FRAME_SIZE - 3],
			(uint16_t *)pcm_voice_buffer[SELP_FRAME_SIZE - 2], (uint16_t *)pcm_voice_buffer[SELP_FRAME_SIZE - 1]);
#endif
		usleep(7000);
	}
	else
	{
		if ((save_ipc_enc_flag == 3) && (save_ipc_dec_flag == 3))
		{
			save_ipc_enc_flag = 0;
			save_ipc_dec_flag = 0;
		}
		usleep(200);
	}
}

#endif


void vocoder_init(void)
{
    ipc_ret_t r_b;

	wait_ipc_idle("voc_init_wait", 1000);

    // 复位编码器
    r_b = ipc_sv_ambe_init_enc(0);
    vlog_v("stack","[%d,%llu]ipc_sv_ambe_init_enc", r_b, ipc_sv_last_xfer_time());
	usleep(1000);

    // 复位解码器
    r_b = ipc_sv_ambe_init_dec(0);
    vlog_v("stack","[%d,%llu]ipc_sv_ambe_init_dec", r_b, ipc_sv_last_xfer_time());
	// usleep(1000);

	ipc_sv_set_voc_callback(save_ipc_enc_dec_callback, 0);
    vlog_v("vocoder","ipc_sv_set_voc_callback ok");
	set_module_init_done_flag(MODULE_INIT_DONE_STACK);
    vlog_v("vocoder","set_module_init_done_flag ok");
	set_ipc_idle(1000);/*600*/

}

void *vocoder_thread_func(void *arg)
{
	vocoder_init();

#ifdef TEST_VOCODER_FUNCTION
//	f_enc_dec = fopen("enc_dec.pcm", "wb");
#endif

	while (1)
    {
#ifdef TEST_VOCODER_FUNCTION
		test_ipc_ambe_vocoder();
#else
  		vocoder_working_process();
#endif
		usleep(200);
    }
}

/******************************************************************************

                                    Main

 ******************************************************************************/

uint32_t stack_call_interface(uint32_t type, uint32_t *aux1, uint32_t *aux2, uint32_t buff, uint16_t buff_len)
{
	uint32_t ret;
	ipc_stk_args_t *args;

	wait_ipc_idle("stk_wait", 0);
//#ifdef XFER_MEASURE_TIME
	// vlog_v("stack","call_stack");
//#endif
//	usleep(1000);

	args = ipc_sv_call_stk(type, *aux1, *aux2, (void *)buff, buff_len);
	if (args)
	{
		ret = args->cmd;
		*aux1 = args->aux1;
		*aux2 = args->aux2;
        // vlog_v("stack","buff=%08x");
		memcpy((void *)buff, args->buf, args->buflen);
	}
	else
	{
		ret = STACK_RETURN_TYPE_TIMEOUT;
	}

//#ifdef XFER_MEASURE_TIME
	// vlog_v("stack","call_stack end");
//#endif
	set_ipc_idle(0);
	return ret;
}

int stack_thread_init(void)
{
    ipc_ret_t r_b;
    tim_instant_t ins;
    tim_duration_t elapsed;
    ipc_sv_mcu_ver_t v = {0};

    // 初始化
	pthread_mutex_init(&ipc_lock, NULL);
	ipc_sv_init();

    // 获取MCU端软件版本
    r_b = ipc_sv_read_mcu_ver(&v);
    STACK_LOG_I("[%d,%llu]ipc_sv_read_mcu_ver:%d.%d.%d_%x", r_b, ipc_sv_last_xfer_time(), v.major_ver, v.minor_ver, v.patch_ver, v.func);
//	usleep(500);

	tim_instant_now(&ins);
	// 加载协议栈
	r_b = ipc_sv_update_stk("/victel/app/Raido_V309AM6231_format.bin");
	tim_instant_elapsed(&ins, &elapsed);
	STACK_LOG_I("[%d,%llu]ipc_sv_update_stk %s", r_b, tim_duration_to_usecs(&elapsed), (r_b == IPC_R_SUCCESS) ? "OK" : "fail");
//	usleep(500);

    return 0;
}

int vocoder_thread_start(void)
{
    if (pthread_create(&vocoder_thread_id, NULL, vocoder_thread_func, NULL) != 0)
    {
        return -1;
    }
}


extern uint32_t timestamp_1s, timestamp_2s, timestamp_kb_scan, timestamp_10ms;
extern uint8_t peroidic_refresh_flag;
static tim_instant_t ins_10ms = {0};
static uint32_t g_check_40ms_timestamp = 0, g_check_1s_timestamp = 0;
void set_10ms_timer(void)
{
	tim_duration_t elapsed;

	tim_instant_elapsed(&ins_10ms, &elapsed);
	if (tim_duration_to_usecs(&elapsed) >= 10 * 1000)
	{
		tim_instant_now(&ins_10ms);
		timestamp_10ms++;

		if (timestamp_10ms >= g_check_40ms_timestamp + 4)
		{
			g_check_40ms_timestamp = timestamp_10ms;
			timestamp_kb_scan++;
		}

		if (timestamp_10ms >= g_check_1s_timestamp + 100)
		{
			g_check_1s_timestamp = timestamp_10ms;
			timestamp_1s++;
//			peroidic_refresh_flag = 1;
		}
	}
	else
	{
		usleep(200);
	}
}


