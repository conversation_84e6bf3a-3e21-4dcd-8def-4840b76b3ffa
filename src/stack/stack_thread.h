#ifndef STACK_THREAD_H
#define STACK_THREAD_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdint.h>
#include <time.h>
#include <stdbool.h>

//============================> Libraries Headers <============================

//============================> Project Headers <============================

/******************************************************************************

                                Defines

 ******************************************************************************/

/******************************************************************************

                                Enums

 ******************************************************************************/

/******************************************************************************

                                Structs

 ******************************************************************************/

/******************************************************************************

                                Functions

 ******************************************************************************/

/**
 * @brief 初始化协议栈线程
 *
 * @return int 0: 成功
 */
void vocoder_init(void);
void *vocoder_thread_func(void *arg);
int stack_thread_init(void);

/**
 * @brief 启动协议栈线程
 *
 * @return int 0: 成功
 */
int vocoder_thread_start(void);
void set_10ms_timer(void);

#ifdef __cplusplus
}
#endif

#endif
