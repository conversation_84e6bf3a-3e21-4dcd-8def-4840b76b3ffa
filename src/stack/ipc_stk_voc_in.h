#if !defined(__IPC_STK_VOC_IN_H__)
#define __IPC_STK_VOC_IN_H__

#include "ipc_stk_voc.h"

#pragma pack(push, 1)

#define IPC_MSG_SYNC 0xA55A

typedef enum
{
    IPC_MSG_CMD_NULL,
    IPC_MSG_CMD_STK,
    IPC_MSG_CMD_CTRL,
    IPC_MSG_CMD_VOC,
} ipc_msg_cmd_t;

typedef enum
{
    IPC_CTRL_CMD_ECHO,
    IPC_CTRL_CMD_READ_VER,
    IPC_CTRL_CMD_SEND_STK,
    IPC_CTRL_CMD_SET_STK,
    IPC_CTRL_CMD_SEND_VOC,
    IPC_CTRL_CMD_SET_VOC,
} ipc_ctrl_cmd_t;


typedef enum
{
    IPC_VOC_CMD_ENCODE,
    IPC_VOC_CMD_DECODE,
    IPC_VOC_CMD_RST_ENC,
    IPC_VOC_CMD_RST_DEC,
    IPC_VOC_CMD_RST_BOTH,
} ipc_voc_cmd_t;

typedef struct
{
    uint16_t sync;
    uint16_t len;
    uint8_t cmd;
    uint8_t scmd; // ipc_ctrl_cmd_t, ipc_voc_cmd_t ...
    uint8_t chn;
    uint8_t rcode;
    union
    {
        union
        {
            uint32_t args[64];
            uint8_t args_b[64 * 4];
        } ctrl;
        struct
        {
            short data[160]; // pcm: 160, ebits: 72
        } voc;
        ipc_stk_args_t stk;
    } u;
} ipc_message_t;

#define IPC_MSG_FIELD_OFS(member) ((uintptr_t) & ((ipc_message_t *)0)->member)
#define IPC_MSG_CTRL_LEN(_l_) (IPC_MSG_FIELD_OFS(u) + (_l_))
#define IPC_MSG_VOC_LEN(_data_l_) (IPC_MSG_FIELD_OFS(u.voc.data) + ((_data_l_) * (sizeof(short))))
#define IPC_MSG_STK_LEN(_buf_l_) (IPC_MSG_FIELD_OFS(u.stk.buf) + (_buf_l_))

#pragma pack(pop)

#endif // __IPC_STK_VOC_IN_H__
