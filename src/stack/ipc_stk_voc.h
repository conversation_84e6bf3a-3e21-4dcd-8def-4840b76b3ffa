#if !defined(__IPC_STK_VOC_H__)
#define __IPC_STK_VOC_H__
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include "stk_defs.h"

#define STK_BUFF_MAX 512

typedef enum
{
    IPC_R_SUCCESS = 0,
    IPC_R_ERROR,
    IPC_R_EIO,
    IPC_R_EFILE,
    IPC_R_ECHKSUM,
    IPC_R_EINVAL,
    IPC_R_EUNKNOWN,
    IPC_R_ETIMEOUT,
} ipc_ret_t;

typedef enum
{
    IPC_STK_TYPE_XV,
    IPC_STK_TYPE_PDT,
} ipc_stk_type_t;

#pragma pack(push, 1)
typedef struct
{
    uint32_t cmd;
    uint32_t aux1;
    uint32_t aux2;
    uint32_t buflen;
    uint8_t buf[STK_BUFF_MAX];
} ipc_stk_args_t;

#pragma pack(pop)

typedef struct
{
    uint8_t major_ver;
    uint8_t minor_ver;
    uint8_t patch_ver;
    uint32_t func;
} ipc_sv_mcu_ver_t;

typedef void (*ipc_voc_callback_t)(void *arg, uint8_t chn, bool encode, const void *p, uint32_t l);

/**
 * @brief 初始化函数，在调用其他函数前必须先进行初始化
 *
 * @return true
 * @return false
 */
bool ipc_sv_init(void);

/**
 * @brief 释放函数，不再使用时释放所有资源
 *
 */
void ipc_sv_deinit(void);

void ipc_sv_set_voc_callback(ipc_voc_callback_t cb, void *arg);

/**
 * @brief 获取MCU版本
 *
 * @param ver 返回IPC_R_SUCCESS时会被修改，允许NULL输入
 * @return ipc_ret_t
 */
ipc_ret_t ipc_sv_read_mcu_ver(ipc_sv_mcu_ver_t *ver);

/**
 * @brief 通过文件更新协议栈
 *
 * @param filename 文件名称
 * @return ipc_ret_t
 */
ipc_ret_t ipc_sv_update_stk(const char *filename);

/**
 * @brief 调用协议栈函数
 * @attention 返回指针使用了内部缓存，禁止在该数据使用完毕前调用其他参数
 *
 * @param cmd R0输入
 * @param aux1 R1输入
 * @param aux2 R2输入
 * @param p 输入数据指针
 * @param l 输入数据长度
 * @return ipc_stk_args_t* 成功时返回协议栈返回内容，失败时返回NULL
 */
ipc_stk_args_t *ipc_sv_call_stk(uint32_t cmd, uint32_t aux1, uint32_t aux2, const void *p, uint32_t l);

/**
 * @brief 初始化/复位ambe语音编码器
 *
 * @param chn 通道，目前支持0-1
 * @return ipc_ret_t
 */
ipc_ret_t ipc_sv_ambe_init_enc(uint8_t chn);

/**
 * @brief 初始化/复位ambe语音解码器
 *
 * @param chn 通道，目前支持0-1
 */
ipc_ret_t ipc_sv_ambe_init_dec(uint8_t chn);

/**
 * @brief ambe语音编码
 *
 * @param chn 通道，目前支持0-1
 * @param p 160个16位PCM数据
 * @param obuf 非NULL时，进行阻塞编码，返回true的时候存储编码输出数据指针
 * @return true
 * @return false
 */
bool ipc_sv_ambe_encode(uint8_t chn, const void *p, short **obuf);

/**
 * @brief ambe语音解码
 *
 * @param chn 通道，目前支持0-1
 * @param p 72个16位PCM数据
 * @param obuf 非NULL时，进行阻塞编码，返回true的时候存储编码输出数据指针
 * @return true
 * @return false
 */
bool ipc_sv_ambe_decode(uint8_t chn, const void *p, short **obuf);
/**
 * @brief 获取最后一次阻塞传输花费的时间,C文件上XFER_MEASURE_TIME非0才有用
 *
 * @return uint64_t
 */
uint64_t ipc_sv_last_xfer_time(void);

#endif // __IPC_STK_VOC_H__
