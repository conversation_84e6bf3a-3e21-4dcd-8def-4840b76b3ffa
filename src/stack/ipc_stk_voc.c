#define _GNU_SOURCE

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <linux/rpmsg.h>
#include <ti_rpmsg_char.h>
#include <sys/time.h>

#include <pthread.h>
#include <semaphore.h>

#include "global_define.h"
#include "ipc_stk_voc_in.h"
#include "time_utils.h"
#include "vlog.h"

// 计算传输时间
#define XFER_TX_DEBUG 0
#define XFER_RX_DEBUG 0
#define REMOTE_ENDPT 14

typedef struct
{
    rpmsg_char_dev_t *dev;
    ipc_message_t rxm;
#ifdef XFER_MEASURE_TIME
    tim_instant_t last_tx_ins;
    uint64_t last_elapsed_us;
#endif
    sem_t xfer_sem;
    pthread_t rx_thr;
    volatile uint8_t terminate;
    volatile uint8_t wait_flag;
    volatile uint8_t wait_cmd;
    volatile uint8_t wait_scmd;
} ipc_xfer_t;

static ipc_xfer_t g_xfer;
static volatile uint8_t g_wait_cmd = IPC_MSG_CMD_NULL;
static volatile uint8_t g_wait_scmd = 0;
static ipc_voc_callback_t g_voc_cb = NULL;
static void *g_voc_arg = NULL;

static bool s_xfer_init(ipc_xfer_t *xfer, uint16_t r_endpt);
static void s_xfer_deinit(ipc_xfer_t *xfer);
static bool s_xfer_exec(ipc_xfer_t *xfer, ipc_message_t *txm, size_t wait_ms);
static void *s_xfer_rx_entry(void *arg);

static bool s_sem_wait_ms(sem_t *s, size_t msecs);
static void s_print_msg(const char *prefix, const ipc_message_t *msg);
static uint32_t s_hash_calc(const void *v, uint32_t len, uint32_t hval);

bool ipc_sv_init(void)
{
    rpmsg_char_init(NULL);
    return s_xfer_init(&g_xfer, REMOTE_ENDPT);
}

void ipc_sv_deinit(void)
{
    s_xfer_deinit(&g_xfer);
    rpmsg_char_exit();
}

void ipc_sv_set_voc_callback(ipc_voc_callback_t cb, void *arg)
{
    g_voc_cb = cb;
    g_voc_arg = arg;
}

ipc_ret_t ipc_sv_read_mcu_ver(ipc_sv_mcu_ver_t *ver)
{
    ipc_message_t txm;

    txm.len = IPC_MSG_CTRL_LEN(0);
    txm.cmd = IPC_MSG_CMD_CTRL;
    txm.scmd = IPC_CTRL_CMD_READ_VER;
    txm.chn = 0;
    txm.rcode = 0;
    if (s_xfer_exec(&g_xfer, &txm, 20))
    {
        ipc_ret_t rcode = (ipc_ret_t)g_xfer.rxm.rcode;
        if (rcode == IPC_R_SUCCESS && ver)
        {
            ver->major_ver = g_xfer.rxm.u.ctrl.args_b[0];
            ver->minor_ver = g_xfer.rxm.u.ctrl.args_b[1];
            ver->patch_ver = g_xfer.rxm.u.ctrl.args_b[2];
            ver->func = g_xfer.rxm.u.ctrl.args[1];
        }
        return (ipc_ret_t)g_xfer.rxm.rcode;
    }
    return IPC_R_ETIMEOUT;
}

ipc_ret_t ipc_sv_update_stk(const char *filename)
{
    ipc_ret_t r;
    FILE *f_stk = fopen(filename, "rb");
    if (f_stk != NULL)
    {
        uint8_t *ptr;
        ssize_t rl;
        uint32_t ofs = 0;
        uint32_t hval = 5381;
        ipc_message_t txm;

        txm.cmd = IPC_MSG_CMD_CTRL;
        txm.scmd = IPC_CTRL_CMD_SEND_STK;
        txm.chn = 0;
        txm.rcode = 0;
        ptr = (uint8_t *)&txm.u.ctrl.args[1];
        while ((rl = fread(ptr, 1, 256, f_stk)) > 0)
        {
            txm.len = IPC_MSG_CTRL_LEN(4 + rl);
            txm.u.ctrl.args[0] = ofs;
            hval = s_hash_calc(ptr, rl, hval);
            if (s_xfer_exec(&g_xfer, &txm, 120))
            {
                r = (ipc_ret_t)g_xfer.rxm.rcode;
                if (r != IPC_R_SUCCESS)
                {
                    goto _upd_stk_end;
                }
            }
            else
            {
                r = IPC_R_ETIMEOUT;
                goto _upd_stk_end;
            }
            ofs += rl;
        }
        txm.len = IPC_MSG_CTRL_LEN(8);
        txm.scmd = IPC_CTRL_CMD_SET_STK;
        txm.u.ctrl.args[0] = ofs;
        txm.u.ctrl.args[1] = hval;
        if (s_xfer_exec(&g_xfer, &txm, 1000))
        {
            r = (ipc_ret_t)g_xfer.rxm.rcode;
        }
        else
        {
            r = IPC_R_ETIMEOUT;
        }
    _upd_stk_end:
        fclose(f_stk);
        return r;
    }
    return IPC_R_EFILE;
}

ipc_stk_args_t *ipc_sv_call_stk(uint32_t cmd, uint32_t aux1, uint32_t aux2, const void *p, uint32_t l)
{
    ipc_message_t txm;

    if (l > sizeof(txm.u.stk.buf))
    {
        l = sizeof(txm.u.stk.buf);
    }
    if (p != NULL && (uintptr_t)p != (uintptr_t)txm.u.stk.buf)
    {
        memcpy(txm.u.stk.buf, p, l);
    }
    txm.u.stk.buflen = l;

    txm.len = IPC_MSG_STK_LEN(txm.u.stk.buflen);
    txm.cmd = IPC_MSG_CMD_STK;
    txm.scmd = 0;
    txm.chn = 0;
    txm.rcode = 0;
    txm.u.stk.cmd = cmd;
    txm.u.stk.aux1 = aux1;
    txm.u.stk.aux2 = aux2;
    if (s_xfer_exec(&g_xfer, &txm, CALL_STACK_WAITING_MS))
    {
        if (g_xfer.rxm.rcode == 0)
        {
#ifdef XFER_MEASURE_TIME
			printf("\tcall/%c:%04d=%04d(l=%d,t=%llu)", g_xfer.last_elapsed_us > 1500 ? 'B' : 'G',
				cmd, g_xfer.rxm.u.stk.cmd, txm.u.stk.buflen, g_xfer.last_elapsed_us);
#endif
            return &g_xfer.rxm.u.stk;
        }
    }
#ifdef XFER_MEASURE_TIME
	printf("\tcall %04d fail(rcode=%d,l=%d,t=%d)", cmd, g_xfer.rxm.rcode, g_xfer.rxm.u.stk.cmd, txm.u.stk.buflen, g_xfer.last_elapsed_us);
#endif
    return NULL;
}

ipc_ret_t ipc_sv_ambe_init_enc(uint8_t chn)
{
    ipc_message_t txm;

    txm.len = IPC_MSG_VOC_LEN(0);
    txm.cmd = IPC_MSG_CMD_VOC;
    txm.scmd = IPC_VOC_CMD_RST_ENC;
    txm.chn = chn;
    txm.rcode = 0;
    if (s_xfer_exec(&g_xfer, &txm, 20))
    {
        return (ipc_ret_t)g_xfer.rxm.rcode;
    }
    return IPC_R_ETIMEOUT;
}

ipc_ret_t ipc_sv_ambe_init_dec(uint8_t chn)
{
    ipc_message_t txm;

    txm.len = IPC_MSG_VOC_LEN(0);
    txm.cmd = IPC_MSG_CMD_VOC;
    txm.scmd = IPC_VOC_CMD_RST_DEC;
    txm.chn = chn;
    txm.rcode = 0;
    if (s_xfer_exec(&g_xfer, &txm, 20))
    {
        return (ipc_ret_t)g_xfer.rxm.rcode;
    }
    return IPC_R_ETIMEOUT;
}

bool ipc_sv_ambe_encode(uint8_t chn, const void *p, short **obuf)
{
    ipc_message_t txm;

    txm.len = IPC_MSG_VOC_LEN(160);
    txm.cmd = IPC_MSG_CMD_VOC;
    txm.scmd = IPC_VOC_CMD_ENCODE;
    txm.chn = chn;
    txm.rcode = 0;
    memcpy(txm.u.voc.data, p, sizeof(short) * 160);
    if (obuf != NULL)
    {
        bool r = s_xfer_exec(&g_xfer, &txm, AMBE_ENCODE_WAITING_MS);
        if (r)
        {
            *obuf = g_xfer.rxm.u.voc.data;
        }
		else
		{
			*obuf = 0;
		}
        return r;
    }
    else
    {
        return s_xfer_exec(&g_xfer, &txm, 0);
    }
}

bool ipc_sv_ambe_decode(uint8_t chn, const void *p, short **obuf)
{
    ipc_message_t txm;

    txm.len = IPC_MSG_VOC_LEN(72);
    txm.cmd = IPC_MSG_CMD_VOC;
    txm.scmd = IPC_VOC_CMD_DECODE;
    txm.chn = chn;
    txm.rcode = 0;
    memcpy(txm.u.voc.data, p, sizeof(short) * 72);
    if (obuf != NULL)
    {
        bool r = s_xfer_exec(&g_xfer, &txm, AMBE_DECODE_WAITING_MS);
        if (r)
        {
            *obuf = g_xfer.rxm.u.voc.data;
        }
		else
		{
			*obuf = 0;
		}
        return r;
    }
    else
    {
        return s_xfer_exec(&g_xfer, &txm, 0);
    }
}

uint64_t ipc_sv_last_xfer_time(void)
{
#ifdef XFER_MEASURE_TIME
    return g_xfer.last_elapsed_us;
#else
    return 0;
#endif
}

static bool s_xfer_init(ipc_xfer_t *xfer, uint16_t r_endpt)
{
    int r;
    memset(xfer, 0, sizeof(*xfer));
    xfer->dev = rpmsg_char_open(M4F_MCU0_0, NULL, RPMSG_ADDR_ANY, r_endpt, "rpmsg-stk-voc", 0);

    // 初始化信号量
    if (sem_init(&xfer->xfer_sem, 0, 0) != 0) {
        printf("Failed to initialize semaphore: %d\n", errno);
        if (xfer->dev != NULL) {
            rpmsg_char_close(xfer->dev);
            xfer->dev = NULL;
        }
        return false;
    }

    r = pthread_create(&xfer->rx_thr, NULL, s_xfer_rx_entry, xfer);
    if (r != 0) {
        printf("Failed to create rx thread: %d\n", r);
        sem_destroy(&xfer->xfer_sem);
        if (xfer->dev != NULL) {
            rpmsg_char_close(xfer->dev);
            xfer->dev = NULL;
        }
        return false;
    }

    return xfer->dev != NULL;
}

static void s_xfer_deinit(ipc_xfer_t *xfer)
{
    if (xfer != NULL)
    {
        xfer->terminate = 1;
        if (xfer->dev != NULL)
        {
            rpmsg_char_close(xfer->dev);
        }
        pthread_join(xfer->rx_thr, NULL);
        sem_destroy(&xfer->xfer_sem);
        xfer->dev = NULL;
    }
}

static bool s_xfer_exec(ipc_xfer_t *xfer, ipc_message_t *txm, size_t wait_ms)
{
#ifdef XFER_MEASURE_TIME
	tim_duration_t elapsed;
#endif

#if __GLIBC__ >= 2 && __GLIBC_MINOR__ >= 30
#define SEM_WAIT(s, ts_ex) sem_clockwait(s, CLOCK_MONOTONIC, ts_ex)
#else
#define SEM_WAIT(s, ts_ex) sem_timedwait(s, ts_ex)
#endif
    if (xfer->dev)
    {
        txm->sync = IPC_MSG_SYNC;
#if XFER_TX_DEBUG
        s_print_msg("TX", &xfer->txm);
#endif
#ifdef XFER_MEASURE_TIME
        tim_instant_now(&xfer->last_tx_ins);
		vlog_v("ipc","wrdev=%08x,fd=%08x,ep=%08x(%d %d)", (uint32_t)xfer->dev, (uint32_t)xfer->dev->fd, (uint32_t)xfer->dev->endpt, txm->cmd, txm->len);
#endif
        if (write(xfer->dev->fd, txm, txm->len) == txm->len)
        {
            if (wait_ms != 0)
            {
                struct timespec expire;
#if __GLIBC__ >= 2 && __GLIBC_MINOR__ >= 30
                clock_gettime(CLOCK_MONOTONIC, &expire);
#else
                struct timeval tv;
                gettimeofday(&tv, NULL);
                expire.tv_sec = tv.tv_sec;
                expire.tv_nsec = tv.tv_usec * 1000;
#endif
                xfer->wait_cmd = txm->cmd;
                xfer->wait_scmd = txm->scmd;
                xfer->wait_flag = 1;

                expire.tv_sec += wait_ms / 1000;
                expire.tv_nsec += (wait_ms % 1000) * 1000000;
                if (expire.tv_nsec > 1000000000)
                {
                    expire.tv_nsec -= 1000000000;
                    expire.tv_sec += 1;
                }

                // 添加超时计数和重试逻辑
                int retry_count = 0;
                const int max_retries = 3;

                do
                {
                    int r = SEM_WAIT(&xfer->xfer_sem, &expire);
                    if (xfer->wait_flag == 2 && xfer->wait_cmd == xfer->rxm.cmd && xfer->wait_scmd == xfer->rxm.scmd)
                    {
                        xfer->wait_flag = 0;
#ifdef XFER_MEASURE_TIME
                        tim_instant_elapsed(&xfer->last_tx_ins, &elapsed);
                        xfer->last_elapsed_us = tim_duration_to_usecs(&elapsed);
#endif
                        // vlog_v("ipc","accepted from m4 Ok:cmd=%d, scmd=%d",xfer->rxm.cmd, xfer->rxm.scmd);
                        return true;
                    }

                    if (r != 0)
                    {
                        if (errno == EINTR)
                        {
                            // 被信号中断，继续等待
                            continue;
                        }
                        else if (errno == ETIMEDOUT && retry_count < max_retries)
                        {
                            // 超时但未达到最大重试次数，重新发送
                            retry_count++;
                            vlog_w("ipc","Timeout, retrying %d/%d: cmd=%d, scmd=%d, stk_cmd=%d",
                                   retry_count, max_retries, txm->cmd, txm->scmd, txm->u.stk.cmd);

                            // 重新计算超时时间
#if __GLIBC__ >= 2 && __GLIBC_MINOR__ >= 30
                            clock_gettime(CLOCK_MONOTONIC, &expire);
#else
                            gettimeofday(&tv, NULL);
                            expire.tv_sec = tv.tv_sec;
                            expire.tv_nsec = tv.tv_usec * 1000;
#endif
                            expire.tv_sec += wait_ms / 1000;
                            expire.tv_nsec += (wait_ms % 1000) * 1000000;
                            if (expire.tv_nsec > 1000000000)
                            {
                                expire.tv_nsec -= 1000000000;
                                expire.tv_sec += 1;
                            }

                            // 重新发送消息
                            if (write(xfer->dev->fd, txm, txm->len) != txm->len)
                            {
                                vlog_w("ipc","Retry write failed: cmd=%d, scmd=%d, stk_cmd=%d",
                                       txm->cmd, txm->scmd, txm->u.stk.cmd);
                                break;
                            }
                            continue;
                        }
                        else
                        {
#ifdef XFER_MEASURE_TIME
                            tim_instant_elapsed(&xfer->last_tx_ins, &elapsed);
                            xfer->last_elapsed_us = tim_duration_to_usecs(&elapsed);
#endif
						vlog_v("ipc","r=%d,errno=%d,cmd=%d,scmd=%d,stk_cmd=%d", r, errno, txm->cmd, txm->scmd, txm->u.stk.cmd);
                        break;
                        }
                    }
                } while (1);
                xfer->wait_flag = 0;
                return false;
            }
			else
			{
#ifdef XFER_MEASURE_TIME
				tim_instant_elapsed(&xfer->last_tx_ins, &elapsed);
				xfer->last_elapsed_us = tim_duration_to_usecs(&elapsed);
#endif
			}
            return true;
        }
		else
		{
			printf("write error,cmd=%d,scmd=%d,stk_cmd=%d", txm->cmd, txm->scmd, txm->u.stk.cmd);
		}
    }
    return false;
}

static void *s_xfer_rx_entry(void *arg)
{
    ipc_xfer_t *xfer = arg;
    int fd;
    ssize_t rlen;
    ipc_message_t rmsg;
    if (xfer->dev == NULL)
    {
        return NULL;
    }
    fd = xfer->dev->fd;

    while (xfer->terminate == 0)
    {
        rlen = read(fd, &rmsg, sizeof(rmsg));
		// vlog_v("ipc","rddev=%08x,fd=%08x,ep=%08x(%d %d %d)",
		// 	(uint32_t)xfer->dev, (uint32_t)xfer->dev->fd, (uint32_t)xfer->dev->endpt, rmsg.cmd, rmsg.len, IPC_MSG_VOC_LEN(0));
        if (rlen < 0)
        {
			printf("read ipc=-1");
            break;
        }
        if (rlen < 8 || rmsg.sync != IPC_MSG_SYNC || rlen != rmsg.len)
        {
            continue;
        }
#if XFER_RX_DEBUG
        s_print_msg("RX", &rmsg);
#endif
//		if (g_voc_cb != NULL && rmsg.cmd == IPC_MSG_CMD_VOC)
        if ((rmsg.cmd == IPC_MSG_CMD_VOC) && ((rmsg.scmd == IPC_VOC_CMD_ENCODE) || (rmsg.scmd == IPC_VOC_CMD_DECODE)))
		{
			if (g_voc_cb != NULL)
				g_voc_cb(g_voc_arg, rmsg.chn, (rmsg.scmd == IPC_VOC_CMD_ENCODE) ? 1 : 0, rmsg.u.voc.data, rmsg.len - IPC_MSG_VOC_LEN(0));
        }
		else
		{
			if (xfer->wait_flag == 1)
			{
				if (xfer->wait_cmd == rmsg.cmd && xfer->wait_scmd == rmsg.scmd)
				{
					memcpy(&xfer->rxm, &rmsg, rmsg.len);
					xfer->wait_flag = 2;
                    // vlog_v("ipc","accepted from m4:cmd=%d, scmd=%d",rmsg.cmd, rmsg.scmd);
					sem_post(&xfer->xfer_sem);
				}
			}
		}
    }
    return NULL;
}

static bool s_sem_wait_ms(sem_t *s, size_t msecs)
{
#if __GLIBC__ >= 2 && __GLIBC_MINOR__ >= 30
#define SEM_WAIT(s, ts_ex) sem_clockwait(s, CLOCK_MONOTONIC, ts_ex)
#else
#define SEM_WAIT(s, ts_ex) sem_timedwait(s, ts_ex)
#endif
    struct timespec expire =
        {
            .tv_sec = msecs / 1000,
            .tv_nsec = ((long long)msecs % 1000) * 1000000,
        };
    while (1)
    {
        if (SEM_WAIT(s, &expire) == 0)
        {
            return true;
        }
        if (errno != EINTR)
        {
            return false;
        }
    }
}

static void s_print_msg(const char *prefix, const ipc_message_t *msg)
{
    char txbuf[1024];
    int txl;
    uint16_t blen;
    const uint8_t *bptr;
    txl = snprintf(txbuf, sizeof(txbuf), "[%s][%x,%d_%x,%x,%d,%d]", prefix, msg->sync, msg->len, msg->cmd, msg->scmd, msg->chn, msg->rcode);
    blen = msg->len - 8;
    bptr = (const uint8_t *)&msg->u;
    while (blen >= 8)
    {
        txl += snprintf(&txbuf[txl], sizeof(txbuf) - txl, " %02x %02x %02x %02x %02x %02x %02x %02x",
                        bptr[0], bptr[1], bptr[2], bptr[3], bptr[4], bptr[5], bptr[6], bptr[7]);
        bptr += 8;
        blen -= 8;
    }
    while (blen > 0)
    {
        txl += snprintf(&txbuf[txl], sizeof(txbuf) - txl, " %02x", *bptr);
        bptr += 1;
        blen -= 1;
    }

    printf("%.*s\n", txl, txbuf);
}

static uint32_t s_hash_calc(const void *v, uint32_t len, uint32_t hval)
{
    unsigned char *bp = (unsigned char *)v;
    unsigned char *be = bp + len;
    const uint32_t seed = 131; // 31 131 1313 13131 131313 etc..
    while (bp < be)
    {
        hval = hval * seed + (uint32_t)*bp++;
    }
    return hval;
}
