#!/bin/bash

# 打印信息
function echo_info()
{
    echo -e "\e[32m[INFO]\e[0m $1"
}

# 打印错误信息
function echo_error()
{
    echo -e "\e[31m[ERROR]\e[0m $1" >&2
}

# 显示使用说明
function show_usage()
{
    echo "Usage: $0 [arm|x86]"
    echo "Example:"
    echo "  $0 arm    # Compile for ARM aarch64"
    echo "  $0 x86    # Compile for x86_64"
    exit 1
}

# 清理构建目录
function clean_build_dir()
{
    local BUILD_DIR="$1"
    echo_info "清理构建目录：$BUILD_DIR"

    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        echo_info "已删除旧的构建目录。"
    fi

    mkdir -p "$BUILD_DIR"
}

# 编译
function ninja_build()
{
    local BUILD_DIR="$1"

    cd "$BUILD_DIR"

    ninja

    echo_info "编译完成。"
}

function verify_build()
{
    local LIB_PATH="$1"
    local ARCH="$2"
    local LIB_NAME="$3"

    echo_info "验证 $LIB_NAME 编译结果..."

    if [ ! -f "$LIB_PATH" ]; then
        echo_error "静态库文件未找到：$LIB_PATH"
        exit 1
    fi

    echo_info "检查 $LIB_NAME 的系统架构："

    # 使用 file 命令检查一个对象文件的架构
    OBJECT_FILE=$(ar -t "$LIB_PATH" | grep '\.o$' | head -n 1)
    if [ -z "$OBJECT_FILE" ]; then
        echo_error "无法从 $LIB_PATH 中提取对象文件。"
        exit 1
    fi

    ar -x "$LIB_PATH" "$OBJECT_FILE" >/dev/null 2>&1
    FILE_OUTPUT=$(file "$OBJECT_FILE")
    rm "$OBJECT_FILE"

    echo_info "$OBJECT_FILE: $FILE_OUTPUT"

    # 根据预期架构判断
    case "$ARCH" in
        arm)
            EXPECTED_PATTERN="aarch64"
            ;;
        x86)
            EXPECTED_PATTERN="x86-64"
            ;;
        *)
            echo_error "未知的架构类型：$ARCH"
            exit 1
            ;;
    esac

    if echo "$FILE_OUTPUT" | grep -q "$EXPECTED_PATTERN"; then
        echo_info "$LIB_NAME 已正确为 $ARCH 架构编译。"
    else
        echo_error "$LIB_NAME 的架构不正确。预期 $EXPECTED_PATTERN，但得到：$FILE_OUTPUT"
        exit 1
    fi
}

