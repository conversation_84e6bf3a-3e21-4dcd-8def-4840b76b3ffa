#!/bin/bash

# ==============================================================================
# 配置部分
# ==============================================================================

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 设置项目根目录（假设脚本位于 scripts/ 目录下）
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# ==============================================================================
# 导入公共函数
# ==============================================================================
source "$SCRIPT_DIR/utils.sh"

# ==============================================================================
# 函数定义
# ==============================================================================

# 配置 CMake
function configure_cmake() {
    local BUILD_DIR="$1"
    local TOOLCHAIN_FILE="$2"

    echo_info "配置 CMake..."

    cd "$BUILD_DIR"

    if [ "$TOOLCHAIN_FILE" != "" ]; then
        cmake -DCMAKE_TOOLCHAIN_FILE="$TOOLCHAIN_FILE" \
              -DCMAKE_EXPORT_COMPILE_COMMANDS=1 \
              -G "Ninja" \
              ..
    else
        cmake -G "Ninja" \
              -DCMAKE_EXPORT_COMPILE_COMMANDS=1 \
              ..
    fi

    echo_info "CMake 配置完成"
}

# ==============================================================================
# 执行流程
# ==============================================================================

# 检查参数
if [ $# -ne 1 ]; then
    show_usage
fi

TARGET_ARCH="$1"

# 根据目标架构设置工具链文件和构建目录
if [ "$TARGET_ARCH" == "arm" ]; then

    #TOOLCHAIN_FILE="$PROJECT_ROOT/arm-toolchain.cmake"
    TOOLCHAIN_FILE="$PROJECT_ROOT/toolchaina53.cmake"
    BUILD_DIR="$PROJECT_ROOT/build"
elif [ "$TARGET_ARCH" == "x86" ]; then
    TOOLCHAIN_FILE=""
    BUILD_DIR="$PROJECT_ROOT/build"
else
    echo_error "未知的目标架构：$TARGET_ARCH"
    show_usage
fi

echo_info "目标架构：$TARGET_ARCH"

clean_build_dir "$BUILD_DIR"
configure_cmake "$BUILD_DIR" "$TOOLCHAIN_FILE"
ninja_build "$BUILD_DIR"

echo_info "构建完成"
