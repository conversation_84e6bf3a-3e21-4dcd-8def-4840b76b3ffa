# GPS自动波特率检测功能Linux移植指南

## 概述

本文档详细说明了将 `src/Board/Victel_digital_board/victel_digital_usart.c` 文件中的GPS自动波特率检测功能完整移植到嵌入式Linux环境的过程。

## 移植范围

### 主要移植函数
1. `autoset_gps_module_baudrate()` - GPS自动波特率检测主函数
2. `autoset_gps_module_baudrate_handle()` - GPS波特率检测处理函数
3. `switch_gps_uart_baudrate()` - GPS串口波特率切换函数
4. `check_gps_is_alive()` - GPS模块存活检测函数
5. `is_gps_alive()` - GPS存活状态查询函数

### 相关全局变量和常量
- `autoset_gps_baud_stage` - GPS波特率检测阶段状态
- `autoset_gps_baud_counter` - GPS波特率检测计数器
- `paras_num_of_gps_sentence` - GPS语句接收计数
- `timer_autoset_gps_baud` - 定时器状态标志
- `bd_table[]` - 支持的波特率表 (9600, 38400, 115200)

## Linux适配实现

### 1. 头文件适配
```c
// 添加Linux相关头文件
#include <termios.h>
#include <fcntl.h>
#include <sys/time.h>
#include <errno.h>
#include "gps.h"
#include "serial.h"
```

### 2. 全局变量适配
```c
// Linux适配：GPS自动波特率检测相关变量
static pthread_t gps_autoset_thread = 0;
static int gps_autoset_thread_running = 0;
static int linux_gps_fd = -1;  // Linux GPS串口文件描述符
```

### 3. 定时器机制适配
原有的嵌入式定时器机制被替换为pthread线程：
```c
// Linux适配：定时器线程函数
static void* gps_autoset_timer_thread(void* arg)
{
    while (gps_autoset_thread_running) {
        // 处理GPS数据读取
        linux_process_gps_data();
        
        usleep(400000);  // 400ms延时
        if (gps_autoset_thread_running) {
            autoset_gps_module_baudrate_handle();
        }
    }
    return NULL;
}
```

### 4. 串口操作适配
使用Linux标准的termios接口替换原有的嵌入式串口操作：

#### 波特率设置
```c
#ifdef __linux__
    // Linux适配：使用termios设置GPS串口波特率
    if (linux_gps_fd >= 0) {
        if (set_baud(linux_gps_fd, bd_table[GET_CURE_RATE_INDEX()]) < 0) {
            vlog_i("usart", "Failed to set GPS baudrate to %d", bd_table[GET_CURE_RATE_INDEX()]);
        }
    }
#else
    usart_set_baudrate(UART_GPS_INDEX, bd_table[GET_CURE_RATE_INDEX()]);
#endif
```

#### 数据发送
```c
#ifdef __linux__
    // Linux适配：发送CASIC模块波特率设置命令
    linux_put_gps_data((uint8_t *)set_gps_baudrate_casic[GET_CURE_RATE_INDEX()], 
                       p_strlen(set_gps_baudrate_casic[GET_CURE_RATE_INDEX()]));
    usleep(2000);  // 等待数据发送完成
#else
    put_data(UART_GPS_INDEX, (uint8_t *)set_gps_baudrate_casic[GET_CURE_RATE_INDEX()], 
             p_strlen(set_gps_baudrate_casic[GET_CURE_RATE_INDEX()]));
    sys_delay(2);  // wait the last char send complete
#endif
```

### 5. GPS数据读取适配
```c
// Linux适配：GPS数据读取和处理函数
static void linux_process_gps_data(void)
{
    static uint8_t gps_buffer[512];
    static uint16_t gps_data_index = 0;
    uint8_t temp_buffer[256];
    int bytes_read;
    int i;
    
    if (linux_gps_fd < 0) return;
    
    // 读取GPS数据
    bytes_read = read_gps_data(linux_gps_fd, temp_buffer, sizeof(temp_buffer) - 1);
    if (bytes_read <= 0) return;
    
    // 处理接收到的数据，按行解析NMEA语句
    for (i = 0; i < bytes_read; i++) {
        if (gps_data_index < sizeof(gps_buffer) - 1) {
            gps_buffer[gps_data_index] = temp_buffer[i];
            
            if (gps_buffer[gps_data_index] == '\n') {  // 一行数据完成
                if (gps_data_index > 0 && gps_buffer[gps_data_index - 1] == '\r') {
                    gps_buffer[gps_data_index - 1] = 0;  // 去掉\r
                } else {
                    gps_buffer[gps_data_index] = 0;
                }
                
                // 解析GPS数据
                if (gps_data_index > 5) {  // 至少要有基本的NMEA格式
                    parse_gps_data(gps_buffer, gps_data_index);
                }
                gps_data_index = 0;
            } else {
                gps_data_index++;
            }
        } else {
            // 缓冲区溢出，重置
            gps_data_index = 0;
        }
    }
}
```

## 功能保持

### 1. 自动波特率检测算法
保持原有的三种波特率循环检测逻辑：
- 9600 bps
- 38400 bps  
- 115200 bps

### 2. GPS模块类型支持
继续支持以下GPS模块：
- UM220 (Unicore)
- CASIC (中科微)
- Techtotop (泰斗)
- GMTK (联发科)

### 3. CASIC模块特殊功能
保持CASIC模块的波特率设置和参数保存命令：
- `$PCAS01,1*1D` (9600)
- `$PCAS01,3*1F` (38400)  
- `$PCAS01,5*19` (115200)
- `$PCAS00*01` (保存参数)

## 使用方法

### 1. 编译
```bash
make -f Makefile.gps_test
```

### 2. 运行测试
```bash
# 使用USB转串口设备
./test_gps_autoset /dev/ttyUSB0 1

# 使用板载串口
./test_gps_autoset /dev/ttyS3 2
```

### 3. 集成到应用程序
```c
#include "victel_digital_usart.h"

// 启动GPS自动波特率检测
autoset_gps_module_baudrate(SET_GPS_DEFAULT_BAUDRATE_IDX);

// 在主循环中检查GPS状态
check_gps_is_alive();

// 查询GPS存活状态
if (is_gps_alive()) {
    printf("GPS模块正常工作\n");
}

// 程序退出时清理资源
#ifdef __linux__
gps_autoset_cleanup();
#endif
```

## 注意事项

1. **设备权限**：确保应用程序有访问GPS串口设备的权限
2. **设备路径**：根据实际硬件配置修改GPS_PATH定义
3. **线程安全**：GPS自动检测使用独立线程，注意线程同步
4. **资源清理**：程序退出时调用清理函数释放资源
5. **错误处理**：检查串口打开和配置的返回值

## 测试验证

移植完成后，可以通过以下方式验证功能：

1. **波特率自动检测**：连接不同波特率的GPS模块，验证自动检测功能
2. **模块兼容性**：测试不同厂商的GPS模块
3. **长时间运行**：验证系统稳定性和资源泄漏
4. **异常处理**：测试设备断开重连等异常情况

## 总结

本次移植成功将原有的GPS自动波特率检测功能从嵌入式环境适配到Linux环境，保持了核心算法和功能特性，同时充分利用了Linux系统的标准接口和多线程能力。移植后的代码具有良好的可维护性和扩展性。
