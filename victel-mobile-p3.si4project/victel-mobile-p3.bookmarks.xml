<?xml version="1.0" encoding="utf-8"?>
<SourceInsightBookmarks
	AppVer="4.00.0121"
	AppVerMinReader="4.00.0009"
	>
	<Bookmarks>
		<Mark
			Name="0275808231732001205202"
			Comment=""
			File="src\User\Src\stack_config.c"
			Line="3234"
			Column="9"
			Symbol="stack_call_paras_len"
			SymbolInstance="0"
			SymbolType="Variable"
			LineOffset="0"
			/>
		<Mark
			Name="2054699441337001205202"
			Comment=""
			File="src\stack\stack_thread.c"
			Line="356"
			Column="21"
			Symbol="stack_call_interface"
			SymbolInstance="0"
			SymbolType="Function"
			LineOffset="0"
			/>
		<Mark
			Name="3491557403253061205202"
			Comment=""
			File="src\User\Src\stack_config.c"
			Line="3268"
			Column="9"
			Symbol="CALL_STACK_PROCESS"
			SymbolInstance="0"
			SymbolType="Function"
			LineOffset="0"
			/>
		<Mark
			Name="3490607634253061205202"
			Comment=""
			File="src\User\Src\stack_config.c"
			Line="5332"
			Column="5"
			Symbol="stack_return_process"
			SymbolInstance="0"
			SymbolType="Function"
			LineOffset="0"
			/>
		<Mark
			Name="2603519531858081205202"
			Comment=""
			File="src\stack\ipc_stk_voc.c"
			Line="468"
			Column="21"
			Symbol="s_xfer_rx_entry"
			SymbolInstance="1"
			SymbolType="Function"
			LineOffset="0"
			/>
		<Mark
			Name="2605618663858081205202"
			Comment=""
			File="src\stack\ipc_stk_voc.c"
			Line="336"
			Column="12"
			Symbol="s_xfer_exec"
			SymbolInstance="1"
			SymbolType="Function"
			LineOffset="0"
			/>
		<Mark
			Name="2815555372339081205202"
			Comment=""
			File="src\stack\ipc_stk_voc.c"
			Line="153"
			Column="16"
			Symbol="ipc_sv_call_stk"
			SymbolInstance="0"
			SymbolType="Function"
			LineOffset="0"
			/>
		<Mark
			Name="7279419600056002205202"
			Comment=""
			File="src\User\Src\dsp_config.c"
			Line="4996"
			Column="5"
			Symbol="dsp_slot_middle_interrupt"
			SymbolInstance="0"
			SymbolType="Function"
			LineOffset="0"
			/>
		<Mark
			Name="7284196144056002205202"
			Comment=""
			File="src\User\Src\dsp_config.c"
			Line="3858"
			Column="5"
			Symbol="dsp_slot_edge_interrupt"
			SymbolInstance="0"
			SymbolType="Function"
			LineOffset="0"
			/>
	</Bookmarks>
</SourceInsightBookmarks>
