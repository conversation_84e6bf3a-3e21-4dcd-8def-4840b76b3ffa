cmake_minimum_required(VERSION 3.10)
project(lvgl C CXX)
set(CMAKE_VERBOSE_MAKEFILE ON)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
# ---------------------------------------------------------------------------------------
# 项目选项定义
# ---------------------------------------------------------------------------------------

# 设备型号配置
option(MODEL_P1 "使用 P1 作为设备型号" OFF)
option(MODEL_P3_AM6231 "使用 P3 AM6231 作为设备型号" ON)

# 根据用户选择的设备型号，生成对应的宏
if(MODEL_P1)
    message(STATUS "Device model: P1")
    set(DEVICE_MODEL_DEFINE "MODEL_P1=1")
elseif(MODEL_P3_AM6231)
    message(STATUS "Device model: P3 AM6231")
    set(DEVICE_MODEL_DEFINE "MODEL_P3_AM6231=1")
else()
    message(STATUS "No device model selected")
    set(DEVICE_MODEL_DEFINE "")
endif()

# ---------------------------------------------------------------------------------------
# 编译器设置
# ---------------------------------------------------------------------------------------
# 将指定目录添加到编译器的头文件搜索路径中
include_directories(${PROJECT_SOURCE_DIR}/config)

# 设置 C 和 C++ 的标准
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置可执行文件的输出路径和工作目录
set(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/bin)
set(WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})

# ---------------------------------------------------------------------------------------
# 添加依赖库
# ---------------------------------------------------------------------------------------

# ---------------------------------------------------------------------------------------
# 添加子目录
# ---------------------------------------------------------------------------------------

# 添加 EasyLogger 子目录
# add_subdirectory(libs/EasyLogger)
# target_compile_definitions(easylogger PRIVATE DELOG_CONF_INCLUDE_SIMPLE)

# 添加 p3 子目录
add_subdirectory(libs/applications)

# ---------------------------------------------------------------------------------------
# 创建可执行文件
# ---------------------------------------------------------------------------------------

file(GLOB_RECURSE SRC_FILES
    ${CMAKE_CURRENT_SOURCE_DIR}/src/*.c
    ${CMAKE_CURRENT_SOURCE_DIR}/src/*.h
)

add_executable(vapp
    ${SRC_FILES}
    ${PROJECT_SOURCE_DIR}/src/main.c
)
# target_link_options(vapp PUBLIC "-rdynamic")
target_compile_options(vapp PUBLIC
#        -pedantic-errors
     -Wall
#   -g
 )

target_compile_definitions(vapp PRIVATE VICTEL_MOBILE_NO_LCD __CC_ARM USE_2D_OBJECTS LINUX_VERSION) # 添加设备型号的宏定义

# 链接各种库到可执行文件
target_link_libraries(vapp
    # easylogger
    ${PROJECT_SOURCE_DIR}/libs//vlog/libvlog.a
    m
    pthread
    p3
    ${PROJECT_SOURCE_DIR}/libs/ti_rpmsg_char/lib/libti_rpmsg_char.a
    ${PROJECT_SOURCE_DIR}/libs/aipl/libaipl.a
    ${PROJECT_SOURCE_DIR}/libs/libvui.so
)

target_include_directories(vapp
    PUBLIC
    ${PROJECT_SOURCE_DIR}/src/utils
    ${PROJECT_SOURCE_DIR}/src/stack
    ${PROJECT_SOURCE_DIR}/libs/ti_rpmsg_char/include
    ${PROJECT_SOURCE_DIR}/libs/applications/release/include
    ${PROJECT_SOURCE_DIR}/src/Board/Victel_digital_board
    ${PROJECT_SOURCE_DIR}/src/Libraries/GUI
    ${PROJECT_SOURCE_DIR}/src/User/Inc
    ${PROJECT_SOURCE_DIR}/libs/aipl/include
    ${PROJECT_SOURCE_DIR}/libs/vlog
)

# 创建一个名为 run 的自定义目标，当执行 ninja run 时，会自动编译并运行 main 可执行文件
add_custom_target(run COMMAND ${EXECUTABLE_OUTPUT_PATH}/vapp DEPENDS vapp)


# ---------------------------------------------------------------------------------------
# 调试选项
# ---------------------------------------------------------------------------------------
# Apply additional compile options if the build type is Debug
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    message(STATUS "Debug mode enabled")

    target_compile_options(lvgl PRIVATE
        -pedantic-errors
        -Wall
#        -Wclobbered
#        -Wdeprecated
#        -Wdouble-promotion
#        -Wempty-body
#        -Wextra
#        -Wformat-security
#        -Wmaybe-uninitialized
        # -Wmissing-prototypes
#        -Wpointer-arith
#        -Wmultichar
#        -Wno-pedantic # ignored for now, we convert functions to pointers for properties table.
#        -Wreturn-type
#        -Wshadow
#        -Wshift-negative-value
#        -Wsizeof-pointer-memaccess
#        -Wtype-limits
#        -Wundef
#        -Wuninitialized
#        -Wunreachable-code
#        -Wfloat-conversion
#        -Wstrict-aliasing
    )

if (ASAN)
    message(STATUS "AddressSanitizer enabled")

    # Add AddressSanitizer flags
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address -fno-omit-frame-pointer")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=address -fno-omit-frame-pointer")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=address")
    set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -fsanitize=address")
else()
    message(STATUS "AddressSanitizer disabled")
endif()
endif()
