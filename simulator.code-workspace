{
	// https://code.visualstudio.com/docs/editor/workspaces
	// https://code.visualstudio.com/docs/editor/multi-root-workspaces
	// https://code.visualstudio.com/docs/editor/variables-reference

	"folders": [
		{
			"path": "."
		},
	],
	// extensions.json section
	"extensions": {
		"recommendations": [
			"ms-vscode.cpptools",              // common C/C++ support
			"ms-vscode.cpptools-themes",       // general C/C++ theme
			"ms-vscode.cmake-tools"            // cmake support
		],
		"unwantedRecommendations": [
		]
	},
	// settings.json section
	"settings": {
		"files.trimTrailingWhitespace": true,
		"files.insertFinalNewline": true,
		"files.trimFinalNewlines": true,
		"cmake.configureOnOpen": true,
		"files.associations": {
			"glob.h": "c",
			"lvgl.h": "c",
			"cstdint": "c",
			"array": "c",
			"compare": "c",
			"functional": "c",
			"tuple": "c",
			"type_traits": "c",
			"utility": "c",
			"victel_digital_board.h": "c",
			"victel_digital_usart.h": "c",
			"stddef.h": "c",
			"math.h": "c",
			"victel_digital_xvmode.h": "c",
			"global_define.h": "c",
			"stack_config.h": "c",
			"stdlib.h": "c",
			"victel_digital_spi_arm.h": "c",
			"victel_digital_lcd.h": "c",
			"vlog.h": "c",
			"stdarg.h": "c",
			"pthread.h": "c",
			"victel_digital_flash.h": "c",
			"string": "c",
			"string_view": "c",
			"cstdlib": "c",
			"time_utils.h": "c"
		},
	},
	// tasks.json section
	"tasks": {
		"version": "2.0.0",
		"tasks": [
			{
				"label": "Build",
				"command": "cmake",
				"args": [
					"--build", "${command:cmake.buildDirectory}"
				],
				"group": {
					"kind": "build",
					"isDefault": true
				},
				"problemMatcher": {
					"owner": "cpp",
					"fileLocation": ["relative", "${workspaceFolder}"],
					"pattern": {
						"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
						"file": 1,
						"line": 2,
						"column": 3,
						"severity": 4,
						"message": 5
					}
				}
			},
			{
				"label": "Build and Run",
				"type": "shell",
				"command": "${workspaceFolder}/bin/main",
				"group": {
					"kind": "test",
					"isDefault": true
				},
				"dependsOn": "Build"
			}
		],
	},
	// launch.json section
	"launch": {
		"version": "0.2.0",
		"configurations": [
			{
				"name": "Debug LVGL demo with gdb",
				"type": "cppdbg",
				"request": "launch",
		        "program": "${workspaceFolder}/bin/main",
				"args": [],
				"cwd": "${workspaceFolder}",
				"preLaunchTask": "Build",
				"stopAtEntry": false,
				"linux": {
					"MIMode": "gdb",
					"miDebuggerPath": "/usr/bin/gdb"
				  },
				  "osx": {
					"MIMode": "lldb"
				  },
				  "windows": {
					"MIMode": "gdb",
					"miDebuggerPath": "C:\\MinGw\\bin\\gdb.exe"
				  }
			},
			{
				"name": "Debug LVGL demo with LLVM",
				"type": "cppdbg",
				"request": "launch",
		        "program": "${workspaceFolder}/bin/main",
				"args": [],
				"cwd": "${workspaceFolder}",
				"preLaunchTask": "Build",
				"stopAtEntry": false,
				"MIMode": "lldb"
			},
		],
	},
}
