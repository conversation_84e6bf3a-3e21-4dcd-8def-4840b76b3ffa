{"name": "Simulator project for LVGL embedded GUI Library", "maintainer": "LVGL", "hostOperatingsystem": ["Linux", "Windows", "MacOS"], "environment": ["VSCode", "SDL"], "description": "LVGL is written mainly for microcontrollers and embedded systems, however you can run the library on your PC as well without any embedded hardware. The code written on PC can be simply copied when you are using an embedded system. The project can use SDL but it can be easily replaced by any other built-in LVGL drivers.", "shortDescription": "VSCode-based project to run LVGL on PC.", "urlToClone": "https://github.com/lvgl/lv_port_pc_vscode", "logos": ["https://raw.githubusercontent.com/lvgl/project-creator/master/meta/images/vscode/logo.svg"], "branches": ["release/v9.2"], "settings": [{"type": "dropdown", "label": "Color Depth", "options": [{"name": "16 (RGB565)", "value": "16"}, {"name": "24 (RGB565)", "value": "24"}, {"name": "32 (RGB565)", "value": "32"}], "actions": [{"toReplace": "#define LV_COLOR_DEPTH \\d+", "newContent": "#define LV_COLOR_DEPTH {value}", "filePath": "lv_conf.h"}]}, {"type": "dropdown", "label": "Show performance monitor", "options": [{"name": "Yes", "value": "1", "default": "true"}, {"name": "No", "value": "0"}], "actions": [{"toReplace": "#define LV_USE_PERF_MONITOR .*", "newContent": "#define LV_USE_PERF_MONITOR {value}", "filePath": "lv_conf.h"}]}]}