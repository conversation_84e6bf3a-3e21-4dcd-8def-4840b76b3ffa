/**
 * GPS自动波特率检测功能测试程序
 * 
 * 编译命令：
 * gcc -o test_gps_autoset test_gps_autoset.c \
 *     src/Board/Victel_digital_board/victel_digital_usart.c \
 *     libs/applications/src/gps.c \
 *     libs/applications/src/serial.c \
 *     -I src/Board/Victel_digital_board \
 *     -I src/User/Inc \
 *     -I libs/applications/release/include \
 *     -pthread -DLINUX_TEST_MODE
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>

// 模拟必要的全局变量和函数
uint32_t timestamp_kb_scan = 0;
uint8_t paras_num_of_gps_sentence = 0;

// 模拟的vlog_i函数
void vlog_i(const char* tag, const char* format, ...)
{
    va_list args;
    va_start(args, format);
    printf("[%s] ", tag);
    vprintf(format, args);
    printf("\n");
    va_end(args);
}

// 模拟的p_strlen函数
size_t p_strlen(const char* str)
{
    return strlen(str);
}

// 模拟的parse_gps_data函数
void parse_gps_data(uint8_t *data, uint16_t num)
{
    printf("Parsing GPS data: %s (length: %d)\n", data, num);
    // 模拟接收到GPS数据，增加计数器
    paras_num_of_gps_sentence++;
}

// 包含GPS自动波特率检测相关的函数声明
extern void autoset_gps_module_baudrate(uint8_t flag);
extern void check_gps_is_alive(void);
extern uint8_t is_gps_alive(void);
extern void gps_autoset_cleanup(void);

static int running = 1;

void signal_handler(int sig)
{
    printf("\nReceived signal %d, cleaning up...\n", sig);
    running = 0;
}

int main(int argc, char *argv[])
{
    printf("GPS自动波特率检测功能测试程序\n");
    printf("================================\n");
    
    // 注册信号处理函数
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 检查命令行参数
    if (argc < 2) {
        printf("用法: %s <GPS设备路径> [波特率索引]\n", argv[0]);
        printf("波特率索引: 0=9600, 1=38400, 2=115200\n");
        printf("示例: %s /dev/ttyUSB0 1\n", argv[0]);
        return 1;
    }
    
    // 设置GPS设备路径（这里需要修改GPS_PATH的定义）
    printf("GPS设备路径: %s\n", argv[1]);
    
    // 获取波特率索引
    uint8_t baud_index = 1;  // 默认38400
    if (argc >= 3) {
        baud_index = atoi(argv[2]);
        if (baud_index > 2) {
            printf("警告: 波特率索引超出范围，使用默认值1 (38400)\n");
            baud_index = 1;
        }
    }
    
    printf("初始波特率索引: %d\n", baud_index);
    printf("开始GPS自动波特率检测...\n");
    printf("按Ctrl+C停止测试\n\n");
    
    // 启动GPS自动波特率检测
    autoset_gps_module_baudrate(baud_index);
    
    // 主循环
    int loop_count = 0;
    while (running) {
        // 模拟主循环处理
        usleep(100000);  // 100ms
        loop_count++;
        
        // 每秒检查一次GPS状态
        if (loop_count % 10 == 0) {
            timestamp_kb_scan = loop_count / 10;
            check_gps_is_alive();
            
            printf("时间: %ds, GPS存活状态: %s, 接收语句数: %d\n", 
                   timestamp_kb_scan, 
                   is_gps_alive() ? "是" : "否",
                   paras_num_of_gps_sentence);
        }
        
        // 测试运行60秒后自动退出
        if (loop_count >= 600) {
            printf("测试时间到，自动退出\n");
            break;
        }
    }
    
    printf("\n正在清理资源...\n");
    gps_autoset_cleanup();
    
    printf("测试完成\n");
    return 0;
}
