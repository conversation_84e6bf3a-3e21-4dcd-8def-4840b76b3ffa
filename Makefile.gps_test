# GPS自动波特率检测功能测试Makefile

CC = gcc
CFLAGS = -Wall -Wextra -g -O2 -pthread
DEFINES = -D__linux__ -DLINUX_TEST_MODE

# 包含路径
INCLUDES = -I src/Board/Victel_digital_board \
           -I src/User/Inc \
           -I libs/applications/release/include \
           -I src/stack

# 源文件
SOURCES = test_gps_autoset.c \
          libs/applications/src/gps.c \
          libs/applications/src/serial.c

# 需要从victel_digital_usart.c中提取的GPS相关函数
GPS_FUNCTIONS = src/Board/Victel_digital_board/victel_digital_usart.c

# 目标文件
TARGET = test_gps_autoset

# 默认目标
all: $(TARGET)

# 编译目标
$(TARGET): $(SOURCES) $(GPS_FUNCTIONS)
	@echo "编译GPS自动波特率检测测试程序..."
	$(CC) $(CFLAGS) $(DEFINES) $(INCLUDES) -o $(TARGET) $(SOURCES) $(GPS_FUNCTIONS)
	@echo "编译完成: $(TARGET)"

# 清理
clean:
	rm -f $(TARGET)
	@echo "清理完成"

# 运行测试（需要指定GPS设备）
test: $(TARGET)
	@echo "运行GPS自动波特率检测测试..."
	@echo "请确保GPS设备已连接，然后运行："
	@echo "./$(TARGET) /dev/ttyUSB0"
	@echo "或者："
	@echo "./$(TARGET) /dev/ttyS3"

# 帮助信息
help:
	@echo "GPS自动波特率检测功能测试Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  all     - 编译测试程序"
	@echo "  clean   - 清理编译文件"
	@echo "  test    - 显示测试运行说明"
	@echo "  help    - 显示此帮助信息"
	@echo ""
	@echo "使用示例:"
	@echo "  make -f Makefile.gps_test"
	@echo "  ./test_gps_autoset /dev/ttyUSB0 1"

.PHONY: all clean test help
