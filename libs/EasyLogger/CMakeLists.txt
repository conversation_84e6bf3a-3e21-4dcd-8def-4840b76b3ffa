# ---------------------------------------------------------------------------------------
# 项目名称
# ---------------------------------------------------------------------------------------
project(easylogger)

# ---------------------------------------------------------------------------------------
# 编译器
# ---------------------------------------------------------------------------------------
set(CMAKE_C_STANDARD 11)                   # 设置 C 标准
set(CMAKE_INCLUDE_CURRENT_DIR ON)          # 包含当前目录

# ---------------------------------------------------------------------------------------
# 添加可执行文件/可执行库
# ---------------------------------------------------------------------------------------

file(GLOB_RECURSE SRC_FILES
    ${CMAKE_CURRENT_SOURCE_DIR}/easylogger/src/*.c
    ${CMAKE_CURRENT_SOURCE_DIR}/easylogger/port/*.c
)
add_library(${PROJECT_NAME} STATIC ${SRC_FILES})

target_include_directories(easylogger PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/easylogger/inc
)
