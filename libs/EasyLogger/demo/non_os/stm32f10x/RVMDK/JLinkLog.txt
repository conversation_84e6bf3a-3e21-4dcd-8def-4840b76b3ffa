
T0F5C 000:772 SEGGER J-Link V4.15n (beta) Log File (0001ms, 0770ms total)
T0F5C 000:772 DLL Compiled: Jun 18 2010 19:55:09 (0001ms, 0770ms total)
T0F5C 000:772 Logging started @ 2015-07-31 13:25 (0001ms, 0770ms total)
T0F5C 000:773 JLINK_SetWarnOutHandler(...) (0000ms, 0771ms total)
T0F5C 000:773 JLINK_OpenEx(...)
Firmware: J-Link ARM V8 compiled Jul 17 2014 12:31:18
Hardware: V8.00
S/N: 17935099
Feature(s): RDI,FlashDL,FlashBP,JFlash  returns O.K. (0143ms, 0771ms total)
T0F5C 000:916 JLINK_SetErrorOutHandler(...) (0000ms, 0914ms total)
T0F5C 000:916 JLINK_ExecCommand("ProjectFile = "D:\Program\STM32\EasyLogger\demo\non_os\stm32f10x\RVMDK\JLinkSettings.ini"", ...)  returns 0x00 (0001ms, 0914ms total)
T0F5C 000:917 JLINK_ExecCommand("DisableConnectionTimeout", ...)  returns 0x00 (0000ms, 0915ms total)
T0F5C 000:919 JLINK_TIF_Select(JLINKARM_TIF_SWD)  returns 0x00 (0001ms, 0915ms total)
T0F5C 000:920 JLINK_SetSpeed(2000) (0000ms, 0916ms total)
T0F5C 000:920 JLINK_GetHardwareVersion()  returns 0x13880 (0000ms, 0916ms total)
T0F5C 000:920 JLINK_GetDLLVersion()  returns 41514 (0000ms, 0916ms total)
T0F5C 000:920 JLINK_GetFirmwareString(...) (0000ms, 0916ms total)
T0F5C 000:921 JLINK_GetDLLVersion()  returns 41514 (0000ms, 0916ms total)
T0F5C 000:921 JLINK_GetCompileDateTime() (0000ms, 0916ms total)
T0F5C 000:921 JLINK_GetFirmwareString(...) (0000ms, 0916ms total)
T0F5C 000:921 JLINK_GetHardwareVersion()  returns 0x13880 (0000ms, 0916ms total)
T0F5C 000:921 JLINK_Reset() >0x108 TIF>Found SWD-DP with ID 0x1BA01477 >0x33 TIF> >0x33 TIF> >0x35 TIF> >0x33 TIF> >0x35 TIF> >0x33 TIF> >0x33 TIF> >0x9B TIF> >0x9B TIF> >0x1D7 TIF> >0x13A TIF> >0x13A TIF>TPIU fitted. >0x13A TIF>ETM fitted. >0x13A TIF>  FPUnit: 6 code (BP) slots and 2 literal slots >0x13A TIF> >0x13A TIF> >0x1D7 TIF> >0x1D7 TIF> >0x20C TIF> >0x13A TIF> >0x13A TIF> >0x1D7 TIF> >0x1D7 TIF> >0x13A TIF> >0x13A TIF> >0x11E4 TIF> >0x1D7 TIF> >0x13A TIF> >0x11E4 TIF> (0044ms, 0916ms total)
T0F5C 000:965 JLINK_JTAG_StoreRaw(..., 0x108 Bits)  returns 0x00 (0000ms, 0960ms total)
T0F5C 000:965 JLINK_JTAG_GetU32(BitPos = 227) >0x108 TIF>  returns 0x1BA01477 (0001ms, 0960ms total)
T0F5C 000:966 JLINK_GetDebugInfo(0x100) -- Value=0xE00FF003  returns 0x00 (0000ms, 0961ms total)
T0F5C 000:966 JLINK_ReadMem (0xE00FF000, 0x0018 Bytes, ...) -- CPU_ReadMem(24 bytes @ 0xE00FF000) -- Data:  03 F0 F0 FF 03 20 F0 FF 03 30 F0 FF 03 10 F0 FF ...  returns 0x00 (0001ms, 0961ms total)
T0F5C 000:967 JLINK_ReadMemU32(0xE000ED00, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED00) -- Data:  31 C2 1F 41  returns 0x01 (0001ms, 0962ms total)
T0F5C 000:969 JLINK_Halt()  returns 0x00 (0000ms, 0963ms total)
T0F5C 000:969 JLINK_IsHalted()  returns TRUE (0000ms, 0963ms total)
T0F5C 000:969 JLINK_ReadMemU32(0xE000EDF0, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000EDF0) -- Data:  03 00 03 00  returns 0x01 (0001ms, 0963ms total)
T0F5C 000:970 JLINK_WriteU32(0xE000EDF0, 0xA05F0003) -- CPU_WriteMem(4 bytes @ 0xE000EDF0)  returns 0x00 (0001ms, 0964ms total)
T0F5C 000:971 JLINK_WriteU32(0xE000EDFC, 0x01000000) -- CPU_WriteMem(4 bytes @ 0xE000EDFC)  returns 0x00 (0001ms, 0965ms total)
T0F5C 000:972 JLINK_ReadMemU32(0xE0002000, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0002000) -- Data:  60 02 00 00  returns 0x01 (0000ms, 0966ms total)
T0F5C 000:972 JLINK_ReadMemU32(0xE0001000, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001000) -- Data:  01 00 00 40  returns 0x01 (0001ms, 0966ms total)
T0F5C 000:974 JLINK_ExecCommand("Device = STM32F103RE", ...)
                JLINK_ExecCommand("map ram 0x20000000 - 0x2000FFFF", ...)  returns 0x00 (0000ms, 0000ms total)
                JLINK_AddMirrorAreaEx(Addr = 0x00000000, Size = 0x00000000) (0000ms, 0000ms total)
                returns 0x00 (0000ms, 0967ms total)
T0F5C 000:974 JLINK_GetHWStatus(...)  returns 0x00 (0001ms, 0967ms total)
T0F5C 000:975 JLINK_GetNumBPUnits(Type = 0xFFFFFF00) >0x108 TIF>Found SWD-DP with ID 0x1BA01477 >0x33 TIF> >0x33 TIF> >0x35 TIF> >0x33 TIF> >0x35 TIF> >0x33 TIF> >0x33 TIF> >0x9B TIF> >0x9B TIF> >0x1D7 TIF> >0x13A TIF> >0x13A TIF>TPIU fitted. >0x13A TIF>ETM fitted. >0x13A TIF>  FPUnit: 6 code (BP) slots and 2 literal slots >0x13A TIF> >0x13A TIF>  returns 0x06 (0015ms, 0968ms total)
T0F5C 000:990 JLINK_GetNumBPUnits(Type = 0xF0)  returns 0x800 (0000ms, 0983ms total)
T0F5C 000:990 JLINK_GetNumWPUnits()  returns 0x04 (0000ms, 0983ms total)
T0F5C 000:990 JLINK_GetSpeed()  returns 0x7D0 (0000ms, 0983ms total)
T0F5C 000:990 JLINK_ReadMemU32(0xE000E004, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000E004) -- Data:  01 00 00 00  returns 0x01 (0001ms, 0983ms total)
T0F5C 000:991 JLINK_WriteMem(0xE0001000, 0x001C Bytes, ...) -- Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ... -- CPU_WriteMem(28 bytes @ 0xE0001000)  returns 0x1C (0002ms, 0984ms total)
T0F5C 000:993 JLINK_ReadMem (0xE0001000, 0x001C Bytes, ...) -- CPU_ReadMem(28 bytes @ 0xE0001000) -- Data:  01 00 00 40 00 00 00 00 00 00 00 00 00 00 00 00 ...  returns 0x00 (0001ms, 0986ms total)
T0F5C 000:994 JLINK_ReadReg(R15)  returns 0x08000144 (0000ms, 0987ms total)
T0F5C 000:994 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 0987ms total)
T0F5C 001:541 JLINK_SetResetType(JLINKARM_RESET_TYPE_NORMAL)  returns JLINKARM_RESET_TYPE_NORMAL (0000ms, 0987ms total)
T0F5C 001:541 JLINK_Reset() >0x108 TIF>Found SWD-DP with ID 0x1BA01477 >0x33 TIF> >0x33 TIF> >0x35 TIF> >0x33 TIF> >0x35 TIF> >0x33 TIF> >0x33 TIF> >0x9B TIF> >0x9B TIF> >0x1D7 TIF> >0x13A TIF> >0x13A TIF>TPIU fitted. >0x13A TIF>ETM fitted. >0x13A TIF>  FPUnit: 6 code (BP) slots and 2 literal slots >0x13A TIF> >0x13A TIF> >0x1D7 TIF> >0x1D7 TIF> >0x20C TIF> >0x13A TIF> >0x13A TIF> >0x1D7 TIF> >0x1D7 TIF> >0x13A TIF> >0x13A TIF> >0x1D7 TIF> >0x13A TIF> >0x11E4 TIF> (0038ms, 0987ms total)
T0F5C 001:579 JLINK_ReadReg(R15)  returns 0x08000144 (0001ms, 1025ms total)
T0F5C 001:580 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R0)  returns 0x007E4E98 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R1)  returns 0x00000020 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R2)  returns 0x0000005B (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R3)  returns 0x08002900 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R14)  returns 0xFFFFFFFF (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(R15)  returns 0x08000144 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1026ms total)
T0F5C 001:580 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1026ms total)
T0F5C 001:581 JLINK_ReadMem (0x08000144, 0x003C Bytes, ...) -- CPU_ReadMem(64 bytes @ 0x08000140) -- Updating C cache (64 bytes @ 0x08000140) -- Read from C cache (60 bytes @ 0x08000144) -- Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...  returns 0x00 (0002ms, 1026ms total)
T0F5C 002:010 JLINK_ReadMem (0x00000000, 0x00C3 Bytes, ...)
                JLINK_ReadMemHW(0x00000000, 0x0004 Bytes, ...) -- CPU_ReadMem(4 bytes @ 0x00000000) -- Data:  80 05 00 20  returns 0x00 (0001ms, 0000ms total)
                JLINK_ReadMemHW(0x08000000, 0x0004 Bytes, ...) -- CPU_ReadMem(4 bytes @ 0x08000000) -- Data:  80 05 00 20  returns 0x00 (0001ms, 0001ms total)
                JLINK_WriteMemHW(0x00000000, 0x0004 Bytes, ...) -- Data:  01 00 01 00 -- CPU_WriteMem(4 bytes @ 0x00000000)  returns 0x04 (0001ms, 0002ms total)
                JLINK_ReadMemHW(0x00000000, 0x0004 Bytes, ...) -- CPU_ReadMem(4 bytes @ 0x00000000) -- Data:  80 05 00 20  returns 0x00 (0000ms, 0003ms total)
               -- MA0 is in flash -- Unmirror addr 0x00000000 -- CPU_ReadMem(256 bytes @ 0x08000000) -- Updating C cache (256 bytes @ 0x08000000) -- Read from C cache (195 bytes @ 0x08000000) -- Data:  80 05 00 20 45 01 00 08 2D 0B 00 08 4F 01 00 08 ...  returns 0x00 (0007ms, 1028ms total)
T16D8 002:424 JLINK_SetBPEx(Addr = 0x080027C4, Type = 0xFFFFFFF2)  returns 0x00000001 (0000ms, 1035ms total)
T16D8 002:424 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002000) -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) -- Invalidate MA0 status (0007ms, 1035ms total)
T16D8 002:532 JLINK_IsHalted()  returns TRUE (0005ms, 1042ms total)
T16D8 002:537 JLINK_Halt()  returns 0x00 (0000ms, 1042ms total)
T16D8 002:537 JLINK_IsHalted()  returns TRUE (0000ms, 1042ms total)
T16D8 002:537 JLINK_IsHalted()  returns TRUE (0000ms, 1042ms total)
T16D8 002:537 JLINK_IsHalted()  returns TRUE (0000ms, 1042ms total)
T16D8 002:537 JLINK_ReadReg(R15)  returns 0x080027C4 (0000ms, 1042ms total)
T16D8 002:537 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1042ms total)
T16D8 002:537 JLINK_ClrBPEx(BPHandle = 0x00000001)  returns 0x00 (0000ms, 1042ms total)
T16D8 002:537 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0001ms, 1042ms total)
T16D8 002:538 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1043ms total)
T16D8 002:539 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0000ms, 1044ms total)
T16D8 002:539 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1044ms total)
T16D8 002:540 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1045ms total)
T16D8 002:541 JLINK_ReadReg(R0)  returns 0x080027C5 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R1)  returns 0x20000580 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R2)  returns 0x00000000 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R3)  returns 0x080014C9 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R14)  returns 0x080005ED (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(R15)  returns 0x080027C4 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1046ms total)
T16D8 002:541 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1046ms total)
T16D8 004:641 JLINK_ReadMem (0x080027C4, 0x003C Bytes, ...) -- CPU_ReadMem(64 bytes @ 0x080027C0) -- Updating C cache (64 bytes @ 0x080027C0) -- Read from C cache (60 bytes @ 0x080027C4) -- Data:  FD F7 A8 FF FF F7 09 FC FF 21 00 20 FF F7 5E FE ...  returns 0x00 (0002ms, 1046ms total)
T16D8 004:643 JLINK_SetBPEx(Addr = 0x080027C8, Type = 0xFFFFFFF2)  returns 0x00000002 (0000ms, 1048ms total)
T16D8 004:643 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0008ms, 1048ms total)
T16D8 004:751 JLINK_IsHalted()  returns TRUE (0004ms, 1056ms total)
T16D8 004:755 JLINK_Halt()  returns 0x00 (0000ms, 1056ms total)
T16D8 004:755 JLINK_IsHalted()  returns TRUE (0000ms, 1056ms total)
T16D8 004:755 JLINK_IsHalted()  returns TRUE (0001ms, 1056ms total)
T16D8 004:756 JLINK_IsHalted()  returns TRUE (0000ms, 1056ms total)
T16D8 004:756 JLINK_ReadReg(R15)  returns 0x080027C8 (0000ms, 1056ms total)
T16D8 004:756 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1056ms total)
T16D8 004:756 JLINK_ClrBPEx(BPHandle = 0x00000002)  returns 0x00 (0000ms, 1056ms total)
T16D8 004:756 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0001ms, 1056ms total)
T16D8 004:757 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1057ms total)
T16D8 004:758 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0000ms, 1058ms total)
T16D8 004:759 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0000ms, 1058ms total)
T16D8 004:759 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1058ms total)
T16D8 004:760 JLINK_ReadReg(R0)  returns 0x00000400 (0000ms, 1059ms total)
T16D8 004:760 JLINK_ReadReg(R1)  returns 0x00000001 (0000ms, 1059ms total)
T16D8 004:760 JLINK_ReadReg(R2)  returns 0x000C0000 (0000ms, 1059ms total)
T16D8 004:760 JLINK_ReadReg(R3)  returns 0x04030400 (0000ms, 1059ms total)
T16D8 004:760 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1059ms total)
T16D8 004:760 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1059ms total)
T16D8 004:760 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1059ms total)
T16D8 004:760 JLINK_ReadReg(R7)  returns 0x00000000 (0001ms, 1059ms total)
T16D8 004:761 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadReg(R14)  returns 0x080007CF (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadReg(R15)  returns 0x080027C8 (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1060ms total)
T16D8 004:761 JLINK_ReadMem (0x20000584, 0x0004 Bytes, ...) -- CPU_ReadMem(64 bytes @ 0x20000580) -- Updating C cache (64 bytes @ 0x20000580) -- Read from C cache (4 bytes @ 0x20000584) -- Data:  D0 E9 00 23  returns 0x00 (0001ms, 1060ms total)
T0F5C 004:847 JLINK_ReadMem (0x080027C8, 0x003C Bytes, ...) -- CPU_ReadMem(128 bytes @ 0x080027C0) -- Updating C cache (128 bytes @ 0x080027C0) -- Read from C cache (60 bytes @ 0x080027C8) -- Data:  FF F7 09 FC FF 21 00 20 FF F7 5E FE 07 21 01 20 ...  returns 0x00 (0002ms, 1061ms total)
T16D8 004:867 JLINK_SetBPEx(Addr = 0x080027CC, Type = 0xFFFFFFF2)  returns 0x00000003 (0000ms, 1063ms total)
T16D8 004:867 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0007ms, 1063ms total)
T16D8 004:975 JLINK_IsHalted()  returns TRUE (0005ms, 1070ms total)
T16D8 004:980 JLINK_Halt()  returns 0x00 (0000ms, 1070ms total)
T16D8 004:980 JLINK_IsHalted()  returns TRUE (0000ms, 1070ms total)
T16D8 004:980 JLINK_IsHalted()  returns TRUE (0000ms, 1070ms total)
T16D8 004:980 JLINK_IsHalted()  returns TRUE (0000ms, 1070ms total)
T16D8 004:980 JLINK_ReadReg(R15)  returns 0x080027CC (0000ms, 1070ms total)
T16D8 004:980 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1070ms total)
T16D8 004:980 JLINK_ClrBPEx(BPHandle = 0x00000003)  returns 0x00 (0000ms, 1070ms total)
T16D8 004:980 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0001ms, 1070ms total)
T16D8 004:981 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1071ms total)
T16D8 004:982 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1072ms total)
T16D8 004:983 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1073ms total)
T16D8 004:984 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1074ms total)
T16D8 004:985 JLINK_ReadReg(R0)  returns 0x00000000 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R1)  returns 0x00000001 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R2)  returns 0x000C0000 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R3)  returns 0x04030400 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R14)  returns 0x08001FEF (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(R15)  returns 0x080027CC (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1075ms total)
T16D8 004:985 JLINK_ReadMem (0x20000584, 0x0004 Bytes, ...) -- CPU_ReadMem(64 bytes @ 0x20000580) -- Updating C cache (64 bytes @ 0x20000580) -- Read from C cache (4 bytes @ 0x20000584) -- Data:  D0 E9 00 23  returns 0x00 (0002ms, 1075ms total)
T0F5C 005:075 JLINK_ReadMem (0x080027CC, 0x003C Bytes, ...) -- CPU_ReadMem(128 bytes @ 0x080027C0) -- Updating C cache (128 bytes @ 0x080027C0) -- Read from C cache (60 bytes @ 0x080027CC) -- Data:  FF 21 00 20 FF F7 5E FE 07 21 01 20 FF F7 5A FE ...  returns 0x00 (0003ms, 1077ms total)
T16D8 005:108 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027CC) -- Simulated  returns 0x00 (0000ms, 1080ms total)
T16D8 005:108 JLINK_ReadReg(R15)  returns 0x080027CE (0000ms, 1080ms total)
T16D8 005:108 JLINK_ReadReg(XPSR)  returns 0x21000000 (0000ms, 1080ms total)
T16D8 005:108 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027CE) -- Simulated  returns 0x00 (0000ms, 1080ms total)
T16D8 005:108 JLINK_ReadReg(R15)  returns 0x080027D0 (0000ms, 1080ms total)
T16D8 005:108 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1080ms total)
T16D8 005:108 JLINK_SetBPEx(Addr = 0x080027D4, Type = 0xFFFFFFF2)  returns 0x00000004 (0000ms, 1080ms total)
T16D8 005:108 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0007ms, 1080ms total)
T16D8 005:216 JLINK_IsHalted()  returns TRUE (0005ms, 1087ms total)
T16D8 005:221 JLINK_Halt()  returns 0x00 (0000ms, 1087ms total)
T16D8 005:221 JLINK_IsHalted()  returns TRUE (0000ms, 1087ms total)
T16D8 005:221 JLINK_IsHalted()  returns TRUE (0000ms, 1087ms total)
T16D8 005:221 JLINK_IsHalted()  returns TRUE (0000ms, 1087ms total)
T16D8 005:221 JLINK_ReadReg(R15)  returns 0x080027D4 (0000ms, 1087ms total)
T16D8 005:221 JLINK_ReadReg(XPSR)  returns 0x81000000 (0000ms, 1087ms total)
T16D8 005:221 JLINK_ClrBPEx(BPHandle = 0x00000004)  returns 0x00 (0000ms, 1087ms total)
T16D8 005:221 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0001ms, 1087ms total)
T16D8 005:222 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1088ms total)
T16D8 005:223 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1089ms total)
T16D8 005:224 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1090ms total)
T16D8 005:225 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1091ms total)
T16D8 005:226 JLINK_ReadReg(R0)  returns 0x20000160 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R1)  returns 0x000000FF (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R2)  returns 0x000C0000 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R3)  returns 0x04030400 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R14)  returns 0x080027D5 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(R15)  returns 0x080027D4 (0000ms, 1092ms total)
T16D8 005:226 JLINK_ReadReg(XPSR)  returns 0x81000000 (0001ms, 1092ms total)
T16D8 005:227 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1093ms total)
T16D8 005:227 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1093ms total)
T16D8 005:227 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1093ms total)
T0F5C 005:292 JLINK_ReadMem (0x080027D4, 0x003C Bytes, ...) -- CPU_ReadMem(128 bytes @ 0x080027C0) -- Updating C cache (128 bytes @ 0x080027C0) -- Read from C cache (60 bytes @ 0x080027D4) -- Data:  07 21 01 20 FF F7 5A FE 07 21 02 20 FF F7 56 FE ...  returns 0x00 (0002ms, 1093ms total)
T16D8 005:312 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027D4) -- Simulated  returns 0x00 (0000ms, 1095ms total)
T16D8 005:312 JLINK_ReadReg(R15)  returns 0x080027D6 (0000ms, 1095ms total)
T16D8 005:312 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1095ms total)
T16D8 005:312 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027D6) -- Simulated  returns 0x00 (0000ms, 1095ms total)
T16D8 005:312 JLINK_ReadReg(R15)  returns 0x080027D8 (0000ms, 1095ms total)
T16D8 005:312 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1095ms total)
T16D8 005:312 JLINK_SetBPEx(Addr = 0x080027DC, Type = 0xFFFFFFF2)  returns 0x00000005 (0000ms, 1095ms total)
T16D8 005:312 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0008ms, 1095ms total)
T16D8 005:421 JLINK_IsHalted()  returns TRUE (0004ms, 1103ms total)
T16D8 005:425 JLINK_Halt()  returns 0x00 (0000ms, 1103ms total)
T16D8 005:425 JLINK_IsHalted()  returns TRUE (0001ms, 1103ms total)
T16D8 005:426 JLINK_IsHalted()  returns TRUE (0000ms, 1103ms total)
T16D8 005:426 JLINK_IsHalted()  returns TRUE (0000ms, 1103ms total)
T16D8 005:426 JLINK_ReadReg(R15)  returns 0x080027DC (0000ms, 1103ms total)
T16D8 005:426 JLINK_ReadReg(XPSR)  returns 0x81000000 (0000ms, 1103ms total)
T16D8 005:426 JLINK_ClrBPEx(BPHandle = 0x00000005)  returns 0x00 (0000ms, 1103ms total)
T16D8 005:426 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0001ms, 1103ms total)
T16D8 005:427 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1104ms total)
T16D8 005:428 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1105ms total)
T16D8 005:429 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1106ms total)
T16D8 005:430 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R0)  returns 0x20000160 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R1)  returns 0x00000007 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R2)  returns 0x000C0000 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R3)  returns 0x04030400 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R14)  returns 0x080027DD (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(R15)  returns 0x080027DC (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(XPSR)  returns 0x81000000 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1107ms total)
T16D8 005:431 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1107ms total)
T0F5C 005:498 JLINK_ReadMem (0x080027DC, 0x003C Bytes, ...) -- CPU_ReadMem(128 bytes @ 0x080027C0) -- Updating C cache (128 bytes @ 0x080027C0) -- Read from C cache (60 bytes @ 0x080027DC) -- Data:  07 21 02 20 FF F7 56 FE 07 21 03 20 FF F7 52 FE ...  returns 0x00 (0002ms, 1107ms total)
T16D8 005:520 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027DC) -- Simulated  returns 0x00 (0000ms, 1109ms total)
T16D8 005:520 JLINK_ReadReg(R15)  returns 0x080027DE (0000ms, 1109ms total)
T16D8 005:520 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1109ms total)
T16D8 005:520 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027DE) -- Simulated  returns 0x00 (0000ms, 1109ms total)
T16D8 005:520 JLINK_ReadReg(R15)  returns 0x080027E0 (0000ms, 1109ms total)
T16D8 005:520 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1109ms total)
T16D8 005:520 JLINK_SetBPEx(Addr = 0x080027E4, Type = 0xFFFFFFF2)  returns 0x00000006 (0000ms, 1109ms total)
T16D8 005:520 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0008ms, 1109ms total)
T16D8 005:629 JLINK_IsHalted()  returns TRUE (0004ms, 1117ms total)
T16D8 005:633 JLINK_Halt()  returns 0x00 (0000ms, 1117ms total)
T16D8 005:633 JLINK_IsHalted()  returns TRUE (0000ms, 1117ms total)
T16D8 005:633 JLINK_IsHalted()  returns TRUE (0000ms, 1117ms total)
T16D8 005:633 JLINK_IsHalted()  returns TRUE (0000ms, 1117ms total)
T16D8 005:633 JLINK_ReadReg(R15)  returns 0x080027E4 (0000ms, 1117ms total)
T16D8 005:633 JLINK_ReadReg(XPSR)  returns 0x81000000 (0000ms, 1117ms total)
T16D8 005:634 JLINK_ClrBPEx(BPHandle = 0x00000006)  returns 0x00 (0000ms, 1118ms total)
T16D8 005:634 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0000ms, 1118ms total)
T16D8 005:634 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1118ms total)
T16D8 005:635 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1119ms total)
T16D8 005:636 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1120ms total)
T16D8 005:637 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1121ms total)
T16D8 005:638 JLINK_ReadReg(R0)  returns 0x20000160 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R1)  returns 0x00000007 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R2)  returns 0x000C0000 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R3)  returns 0x04030400 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R14)  returns 0x080027E5 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(R15)  returns 0x080027E4 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(XPSR)  returns 0x81000000 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1122ms total)
T16D8 005:638 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1122ms total)
T0F5C 005:688 JLINK_ReadMem (0x080027E4, 0x003C Bytes, ...) -- CPU_ReadMem(128 bytes @ 0x080027C0) -- Updating C cache (128 bytes @ 0x080027C0) -- Read from C cache (60 bytes @ 0x080027E4) -- Data:  07 21 03 20 FF F7 52 FE A7 21 04 20 FF F7 4E FE ...  returns 0x00 (0003ms, 1122ms total)
T16D8 005:709 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027E4) -- Simulated  returns 0x00 (0000ms, 1125ms total)
T16D8 005:709 JLINK_ReadReg(R15)  returns 0x080027E6 (0000ms, 1125ms total)
T16D8 005:709 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1125ms total)
T16D8 005:709 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027E6) -- Simulated  returns 0x00 (0000ms, 1125ms total)
T16D8 005:709 JLINK_ReadReg(R15)  returns 0x080027E8 (0000ms, 1125ms total)
T16D8 005:709 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1125ms total)
T16D8 005:709 JLINK_SetBPEx(Addr = 0x080027EC, Type = 0xFFFFFFF2)  returns 0x00000007 (0000ms, 1125ms total)
T16D8 005:709 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0007ms, 1125ms total)
T16D8 005:817 JLINK_IsHalted()  returns TRUE (0004ms, 1132ms total)
T16D8 005:821 JLINK_Halt()  returns 0x00 (0000ms, 1132ms total)
T16D8 005:821 JLINK_IsHalted()  returns TRUE (0000ms, 1132ms total)
T16D8 005:821 JLINK_IsHalted()  returns TRUE (0000ms, 1132ms total)
T16D8 005:821 JLINK_IsHalted()  returns TRUE (0000ms, 1132ms total)
T16D8 005:821 JLINK_ReadReg(R15)  returns 0x080027EC (0000ms, 1132ms total)
T16D8 005:821 JLINK_ReadReg(XPSR)  returns 0x81000000 (0000ms, 1132ms total)
T16D8 005:821 JLINK_ClrBPEx(BPHandle = 0x00000007)  returns 0x00 (0000ms, 1132ms total)
T16D8 005:821 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0001ms, 1132ms total)
T16D8 005:822 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1133ms total)
T16D8 005:823 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1134ms total)
T16D8 005:824 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1135ms total)
T16D8 005:825 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1136ms total)
T16D8 005:826 JLINK_ReadReg(R0)  returns 0x20000160 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R1)  returns 0x00000007 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R2)  returns 0x000C0000 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R3)  returns 0x04030400 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R14)  returns 0x080027ED (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(R15)  returns 0x080027EC (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(XPSR)  returns 0x81000000 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1137ms total)
T16D8 005:826 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1137ms total)
T0F5C 005:870 JLINK_ReadMem (0x080027EC, 0x003C Bytes, ...) -- CPU_ReadMem(128 bytes @ 0x080027C0) -- Updating C cache (128 bytes @ 0x080027C0) -- Read from C cache (60 bytes @ 0x080027EC) -- Data:  A7 21 04 20 FF F7 4E FE A7 21 05 20 FF F7 4A FE ...  returns 0x00 (0002ms, 1137ms total)
T16D8 005:984 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027EC) -- Simulated  returns 0x00 (0000ms, 1139ms total)
T16D8 005:984 JLINK_ReadReg(R15)  returns 0x080027EE (0000ms, 1139ms total)
T16D8 005:984 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1139ms total)
T16D8 005:984 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027EE) -- Simulated  returns 0x00 (0000ms, 1139ms total)
T16D8 005:984 JLINK_ReadReg(R15)  returns 0x080027F0 (0000ms, 1139ms total)
T16D8 005:984 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1139ms total)
T16D8 005:984 JLINK_SetBPEx(Addr = 0x080027F4, Type = 0xFFFFFFF2)  returns 0x00000008 (0000ms, 1139ms total)
T16D8 005:984 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0009ms, 1139ms total)
T16D8 006:095 JLINK_IsHalted()  returns TRUE (0004ms, 1148ms total)
T16D8 006:099 JLINK_Halt()  returns 0x00 (0000ms, 1148ms total)
T16D8 006:099 JLINK_IsHalted()  returns TRUE (0000ms, 1148ms total)
T16D8 006:099 JLINK_IsHalted()  returns TRUE (0000ms, 1148ms total)
T16D8 006:099 JLINK_IsHalted()  returns TRUE (0000ms, 1148ms total)
T16D8 006:099 JLINK_ReadReg(R15)  returns 0x080027F4 (0000ms, 1148ms total)
T16D8 006:099 JLINK_ReadReg(XPSR)  returns 0x81000000 (0000ms, 1148ms total)
T16D8 006:099 JLINK_ClrBPEx(BPHandle = 0x00000008)  returns 0x00 (0000ms, 1148ms total)
T16D8 006:099 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0001ms, 1148ms total)
T16D8 006:100 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1149ms total)
T16D8 006:101 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1150ms total)
T16D8 006:102 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1151ms total)
T16D8 006:103 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1152ms total)
T16D8 006:104 JLINK_ReadReg(R0)  returns 0x20000160 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R1)  returns 0x000000A7 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R2)  returns 0x000C0000 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R3)  returns 0x04030400 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R14)  returns 0x080027F5 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(R15)  returns 0x080027F4 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(XPSR)  returns 0x81000000 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1153ms total)
T16D8 006:104 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1153ms total)
T0F5C 006:164 JLINK_ReadMem (0x080027F4, 0x003C Bytes, ...) -- CPU_ReadMem(128 bytes @ 0x080027C0) -- Updating C cache (128 bytes @ 0x080027C0) -- Read from C cache (60 bytes @ 0x080027F4) -- Data:  A7 21 05 20 FF F7 4A FE 05 20 FF F7 E7 FD FF F7 ...  returns 0x00 (0003ms, 1153ms total)
T16D8 006:185 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027F4) -- Simulated  returns 0x00 (0000ms, 1156ms total)
T16D8 006:185 JLINK_ReadReg(R15)  returns 0x080027F6 (0000ms, 1156ms total)
T16D8 006:185 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1156ms total)
T16D8 006:185 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027F6) -- Simulated  returns 0x00 (0000ms, 1156ms total)
T16D8 006:185 JLINK_ReadReg(R15)  returns 0x080027F8 (0000ms, 1156ms total)
T16D8 006:185 JLINK_ReadReg(XPSR)  returns 0x01000000 (0000ms, 1156ms total)
T16D8 006:185 JLINK_SetBPEx(Addr = 0x080027FC, Type = 0xFFFFFFF2)  returns 0x00000009 (0000ms, 1156ms total)
T16D8 006:185 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0008ms, 1156ms total)
T16D8 006:294 JLINK_IsHalted()  returns TRUE (0005ms, 1164ms total)
T16D8 006:299 JLINK_Halt()  returns 0x00 (0000ms, 1164ms total)
T16D8 006:299 JLINK_IsHalted()  returns TRUE (0000ms, 1164ms total)
T16D8 006:299 JLINK_IsHalted()  returns TRUE (0000ms, 1164ms total)
T16D8 006:299 JLINK_IsHalted()  returns TRUE (0000ms, 1164ms total)
T16D8 006:299 JLINK_ReadReg(R15)  returns 0x080027FC (0000ms, 1164ms total)
T16D8 006:299 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1164ms total)
T16D8 006:299 JLINK_ClrBPEx(BPHandle = 0x00000009)  returns 0x00 (0000ms, 1164ms total)
T16D8 006:299 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0001ms, 1164ms total)
T16D8 006:300 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1165ms total)
T16D8 006:301 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1166ms total)
T16D8 006:302 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0000ms, 1167ms total)
T16D8 006:302 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1167ms total)
T16D8 006:303 JLINK_ReadReg(R0)  returns 0x20000160 (0000ms, 1168ms total)
T16D8 006:303 JLINK_ReadReg(R1)  returns 0x000000A7 (0000ms, 1168ms total)
T16D8 006:303 JLINK_ReadReg(R2)  returns 0x000C0000 (0000ms, 1168ms total)
T16D8 006:303 JLINK_ReadReg(R3)  returns 0x04030400 (0001ms, 1168ms total)
T16D8 006:304 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(R14)  returns 0x080027FD (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(R15)  returns 0x080027FC (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1169ms total)
T16D8 006:304 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1169ms total)
T0F5C 006:369 JLINK_ReadMem (0x080027FC, 0x003C Bytes, ...) -- CPU_ReadMem(128 bytes @ 0x080027C0) -- Updating C cache (128 bytes @ 0x080027C0) -- Read from C cache (60 bytes @ 0x080027FC) -- Data:  05 20 FF F7 E7 FD FF F7 0F FF 0F E0 00 F0 3C F8 ...  returns 0x00 (0002ms, 1169ms total)
T16D8 006:396 JLINK_Step() -- Read from C cache (2 bytes @ 0x080027FC) -- Simulated  returns 0x00 (0000ms, 1171ms total)
T16D8 006:396 JLINK_ReadReg(R15)  returns 0x080027FE (0000ms, 1171ms total)
T16D8 006:396 JLINK_ReadReg(XPSR)  returns 0x21000000 (0000ms, 1171ms total)
T16D8 006:396 JLINK_SetBPEx(Addr = 0x08002802, Type = 0xFFFFFFF2)  returns 0x0000000A (0000ms, 1171ms total)
T16D8 006:396 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0007ms, 1171ms total)
T16D8 006:504 JLINK_IsHalted()  returns TRUE (0004ms, 1178ms total)
T16D8 006:508 JLINK_Halt()  returns 0x00 (0001ms, 1178ms total)
T16D8 006:509 JLINK_IsHalted()  returns TRUE (0000ms, 1179ms total)
T16D8 006:509 JLINK_IsHalted()  returns TRUE (0000ms, 1179ms total)
T16D8 006:509 JLINK_IsHalted()  returns TRUE (0000ms, 1179ms total)
T16D8 006:509 JLINK_ReadReg(R15)  returns 0x08002802 (0000ms, 1179ms total)
T16D8 006:509 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1179ms total)
T16D8 006:509 JLINK_ClrBPEx(BPHandle = 0x0000000A)  returns 0x00 (0000ms, 1179ms total)
T16D8 006:509 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0001ms, 1179ms total)
T16D8 006:510 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1180ms total)
T16D8 006:511 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1181ms total)
T16D8 006:512 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1182ms total)
T16D8 006:513 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1183ms total)
T16D8 006:514 JLINK_ReadReg(R0)  returns 0x2000013C (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R1)  returns 0x000000A7 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R2)  returns 0x000C0000 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R3)  returns 0x04030400 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R14)  returns 0x08002803 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(R15)  returns 0x08002802 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1184ms total)
T16D8 006:514 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1184ms total)
T16D8 006:515 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1184ms total)
T16D8 006:629 JLINK_ReadMem (0x08002802, 0x0002 Bytes, ...) -- CPU_ReadMem(64 bytes @ 0x08002800) -- Updating C cache (64 bytes @ 0x08002800) -- Read from C cache (2 bytes @ 0x08002802) -- Data:  FF F7  returns 0x00 (0001ms, 1184ms total)
T16D8 006:630 JLINK_ReadMem (0x08002804, 0x003C Bytes, ...) -- Read from C cache (60 bytes @ 0x08002804) -- Data:  0F FF 0F E0 00 F0 3C F8 20 21 07 48 FE F7 32 F9 ...  returns 0x00 (0000ms, 1185ms total)
T16D8 006:630 JLINK_SetBPEx(Addr = 0x08002806, Type = 0xFFFFFFF2)  returns 0x0000000B (0000ms, 1185ms total)
T16D8 006:630 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0007ms, 1185ms total)
T16D8 006:738 JLINK_IsHalted()  returns TRUE (0005ms, 1192ms total)
T16D8 006:743 JLINK_Halt()  returns 0x00 (0000ms, 1192ms total)
T16D8 006:743 JLINK_IsHalted()  returns TRUE (0000ms, 1192ms total)
T16D8 006:743 JLINK_IsHalted()  returns TRUE (0000ms, 1192ms total)
T16D8 006:743 JLINK_IsHalted()  returns TRUE (0000ms, 1192ms total)
T16D8 006:743 JLINK_ReadReg(R15)  returns 0x08002806 (0000ms, 1192ms total)
T16D8 006:743 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1192ms total)
T16D8 006:743 JLINK_ClrBPEx(BPHandle = 0x0000000B)  returns 0x00 (0000ms, 1192ms total)
T16D8 006:743 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0001ms, 1192ms total)
T16D8 006:744 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0002ms, 1193ms total)
T16D8 006:746 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1195ms total)
T16D8 006:747 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1196ms total)
T16D8 006:748 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1197ms total)
T16D8 006:749 JLINK_ReadReg(R0)  returns 0x00000000 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R1)  returns 0x00000055 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R2)  returns 0x08002650 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R3)  returns 0x08002648 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R14)  returns 0x08002869 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(R15)  returns 0x08002806 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1198ms total)
T16D8 006:749 JLINK_ReadMem (0x20000584, 0x0004 Bytes, ...) -- CPU_ReadMem(64 bytes @ 0x20000580) -- Updating C cache (64 bytes @ 0x20000580) -- Read from C cache (4 bytes @ 0x20000584) -- Data:  D0 E9 00 23  returns 0x00 (0002ms, 1198ms total)
T0F5C 006:832 JLINK_ReadMem (0x08002806, 0x0002 Bytes, ...) -- CPU_ReadMem(64 bytes @ 0x08002800) -- Updating C cache (64 bytes @ 0x08002800) -- Read from C cache (2 bytes @ 0x08002806) -- Data:  0F E0  returns 0x00 (0002ms, 1200ms total)
T0F5C 006:834 JLINK_ReadMem (0x08002808, 0x003C Bytes, ...) -- CPU_ReadMem(64 bytes @ 0x08002840) -- Updating C cache (64 bytes @ 0x08002840) -- Read from C cache (60 bytes @ 0x08002808) -- Data:  00 F0 3C F8 20 21 07 48 FE F7 32 F9 06 48 FF F7 ...  returns 0x00 (0001ms, 1202ms total)
T16D8 006:858 JLINK_Step() -- Read from C cache (2 bytes @ 0x08002806) -- Not simulated -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C)  returns 0x00 (0011ms, 1203ms total)
T16D8 006:869 JLINK_ReadReg(R15)  returns 0x08002828 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R0)  returns 0x00000000 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R1)  returns 0x00000055 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R2)  returns 0x08002650 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R3)  returns 0x08002648 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R14)  returns 0x08002869 (0000ms, 1214ms total)
T16D8 006:869 JLINK_ReadReg(R15)  returns 0x08002828 (0000ms, 1214ms total)
T16D8 006:870 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1215ms total)
T16D8 006:870 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1215ms total)
T16D8 006:870 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1215ms total)
T16D8 006:870 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1215ms total)
T16D8 006:870 JLINK_ReadMem (0x20000584, 0x0004 Bytes, ...) -- CPU_ReadMem(64 bytes @ 0x20000580) -- Updating C cache (64 bytes @ 0x20000580) -- Read from C cache (4 bytes @ 0x20000584) -- Data:  D0 E9 00 23  returns 0x00 (0001ms, 1215ms total)
T16D8 007:548 JLINK_ReadMem (0x08002828, 0x003C Bytes, ...) -- CPU_ReadMem(128 bytes @ 0x08002800) -- Updating C cache (128 bytes @ 0x08002800) -- Read from C cache (60 bytes @ 0x08002828) -- Data:  EE E7 00 00 00 0C 01 40 80 96 98 00 10 B5 06 48 ...  returns 0x00 (0003ms, 1216ms total)
T16D8 007:551 JLINK_Step() -- Read from C cache (2 bytes @ 0x08002828) -- Not simulated -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C)  returns 0x00 (0012ms, 1219ms total)
T16D8 007:563 JLINK_ReadReg(R15)  returns 0x08002808 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R0)  returns 0x00000000 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R1)  returns 0x00000055 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R2)  returns 0x08002650 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R3)  returns 0x08002648 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R14)  returns 0x08002869 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(R15)  returns 0x08002808 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1231ms total)
T16D8 007:563 JLINK_ReadMem (0x20000584, 0x0004 Bytes, ...) -- CPU_ReadMem(64 bytes @ 0x20000580) -- Updating C cache (64 bytes @ 0x20000580) -- Read from C cache (4 bytes @ 0x20000584) -- Data:  D0 E9 00 23  returns 0x00 (0001ms, 1231ms total)
T0F5C 007:609 JLINK_ReadMem (0x08002808, 0x003C Bytes, ...) -- CPU_ReadMem(128 bytes @ 0x08002800) -- Updating C cache (128 bytes @ 0x08002800) -- Read from C cache (60 bytes @ 0x08002808) -- Data:  00 F0 3C F8 20 21 07 48 FE F7 32 F9 06 48 FF F7 ...  returns 0x00 (0002ms, 1232ms total)
T16D8 008:309 JLINK_SetBPEx(Addr = 0x0800280C, Type = 0xFFFFFFF2)  returns 0x0000000C (0000ms, 1234ms total)
T16D8 008:309 JLINK_Go() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0008ms, 1234ms total)
T16D8 008:418 JLINK_IsHalted()  returns TRUE (0005ms, 1242ms total)
T16D8 008:423 JLINK_Halt()  returns 0x00 (0000ms, 1242ms total)
T16D8 008:423 JLINK_IsHalted()  returns TRUE (0000ms, 1242ms total)
T16D8 008:423 JLINK_IsHalted()  returns TRUE (0000ms, 1242ms total)
T16D8 008:423 JLINK_IsHalted()  returns TRUE (0000ms, 1242ms total)
T16D8 008:423 JLINK_ReadReg(R15)  returns 0x0800280C (0000ms, 1242ms total)
T16D8 008:423 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1242ms total)
T16D8 008:423 JLINK_ClrBPEx(BPHandle = 0x0000000C)  returns 0x00 (0000ms, 1242ms total)
T16D8 008:423 JLINK_ReadMemU32(0xE000ED30, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE000ED30) -- Data:  03 00 00 00  returns 0x01 (0001ms, 1242ms total)
T16D8 008:424 JLINK_ReadMemU32(0xE0001028, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001028) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1243ms total)
T16D8 008:425 JLINK_ReadMemU32(0xE0001038, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001038) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1244ms total)
T16D8 008:426 JLINK_ReadMemU32(0xE0001048, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001048) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1245ms total)
T16D8 008:427 JLINK_ReadMemU32(0xE0001058, 0x0001 Items, ...) -- CPU_ReadMem(4 bytes @ 0xE0001058) -- Data:  01 00 00 40  returns 0x01 (0001ms, 1246ms total)
T16D8 008:429 JLINK_ReadReg(R0)  returns 0x00000000 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R1)  returns 0x2000001E (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R2)  returns 0x0000005B (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R3)  returns 0x08002900 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R4)  returns 0x08002A28 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R5)  returns 0x08002A28 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R6)  returns 0x00000000 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R7)  returns 0x00000000 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R8)  returns 0x00000000 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R9)  returns 0x20000160 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R10)  returns 0x00000000 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R11)  returns 0x00000000 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R12)  returns 0x00000000 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R13)  returns 0x20000580 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R14)  returns 0x08002869 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(R15)  returns 0x0800280C (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(XPSR)  returns 0x61000000 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(MSP)  returns 0x20000580 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(PSP)  returns 0x20000800 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadReg(CFBP)  returns 0x00000000 (0000ms, 1247ms total)
T16D8 008:429 JLINK_ReadMem (0x20000584, 0x0004 Bytes, ...) -- CPU_ReadMem(64 bytes @ 0x20000580) -- Updating C cache (64 bytes @ 0x20000580) -- Read from C cache (4 bytes @ 0x20000584) -- Data:  D0 E9 00 23  returns 0x00 (0002ms, 1247ms total)
T0F5C 008:497 JLINK_ReadMem (0x0800280C, 0x003C Bytes, ...) -- CPU_ReadMem(128 bytes @ 0x08002800) -- Updating C cache (128 bytes @ 0x08002800) -- Read from C cache (60 bytes @ 0x0800280C) -- Data:  20 21 07 48 FE F7 32 F9 06 48 FF F7 DD FB 20 21 ...  returns 0x00 (0003ms, 1249ms total)
T0F5C 044:432 JLINK_WriteU32(0xE000EDFC, 0x00000000) -- CPU_WriteMem(4 bytes @ 0xE000EDFC)  returns 0x00 (0002ms, 1252ms total)
T0F5C 044:434 JLINK_Close() -- CPU_WriteMem(4 bytes @ 0xE0002008) -- CPU_WriteMem(4 bytes @ 0xE000200C) -- CPU_WriteMem(4 bytes @ 0xE0002010) -- CPU_WriteMem(4 bytes @ 0xE0002014) -- CPU_WriteMem(4 bytes @ 0xE0002018) -- CPU_WriteMem(4 bytes @ 0xE000201C) (0031ms, 1254ms total)
