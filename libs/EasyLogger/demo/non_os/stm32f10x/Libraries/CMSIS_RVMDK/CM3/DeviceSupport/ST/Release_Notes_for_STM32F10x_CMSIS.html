<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40"><head>





<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="File-List" href="Library_files/filelist.xml">
<link rel="Edit-Time-Data" href="Library_files/editdata.mso"><!--[if !mso]> <style> v\:* {behavior:url(#default#VML);} o\:* {behavior:url(#default#VML);} w\:* {behavior:url(#default#VML);} .shape {behavior:url(#default#VML);} </style> <![endif]--><title>Release Notes for STM32F10x CMSIS</title><!--[if gte mso 9]><xml> <o:DocumentProperties> <o:Author>STMicroelectronics</o:Author> <o:LastAuthor>STMicroelectronics</o:LastAuthor> <o:Revision>37</o:Revision> <o:TotalTime>136</o:TotalTime> <o:Created>2009-02-27T19:26:00Z</o:Created> <o:LastSaved>2009-03-01T17:56:00Z</o:LastSaved> <o:Pages>1</o:Pages> <o:Words>522</o:Words> <o:Characters>2977</o:Characters> <o:Company>STMicroelectronics</o:Company> <o:Lines>24</o:Lines> <o:Paragraphs>6</o:Paragraphs> <o:CharactersWithSpaces>3493</o:CharactersWithSpaces> <o:Version>11.6568</o:Version> </o:DocumentProperties> </xml><![endif]--><!--[if gte mso 9]><xml> <w:WordDocument> <w:Zoom>110</w:Zoom> <w:ValidateAgainstSchemas/> <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid> <w:IgnoreMixedContent>false</w:IgnoreMixedContent> <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText> <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel> </w:WordDocument> </xml><![endif]--><!--[if gte mso 9]><xml> <w:LatentStyles DefLockedState="false" LatentStyleCount="156"> </w:LatentStyles> </xml><![endif]-->



<style>
<!--
/* Style Definitions */
p.MsoNormal, li.MsoNormal, div.MsoNormal
{mso-style-parent:"";
margin:0in;
margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
h2
{mso-style-next:Normal;
margin-top:12.0pt;
margin-right:0in;
margin-bottom:3.0pt;
margin-left:0in;
mso-pagination:widow-orphan;
page-break-after:avoid;
mso-outline-level:2;
font-size:14.0pt;
font-family:Arial;
font-weight:bold;
font-style:italic;}
a:link, span.MsoHyperlink
{color:blue;
text-decoration:underline;
text-underline:single;}
a:visited, span.MsoHyperlinkFollowed
{color:blue;
text-decoration:underline;
text-underline:single;}
p
{mso-margin-top-alt:auto;
margin-right:0in;
mso-margin-bottom-alt:auto;
margin-left:0in;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
@page Section1
{size:8.5in 11.0in;
margin:1.0in 1.25in 1.0in 1.25in;
mso-header-margin:.5in;
mso-footer-margin:.5in;
mso-paper-source:0;}
div.Section1
{page:Section1;}
-->
</style><!--[if gte mso 10]> <style> /* Style Definitions */ table.MsoNormalTable {mso-style-name:"Table Normal"; mso-tstyle-rowband-size:0; mso-tstyle-colband-size:0; mso-style-noshow:yes; mso-style-parent:""; mso-padding-alt:0in 5.4pt 0in 5.4pt; mso-para-margin:0in; mso-para-margin-bottom:.0001pt; mso-pagination:widow-orphan; font-size:10.0pt; font-family:"Times New Roman"; mso-ansi-language:#0400; mso-fareast-language:#0400; mso-bidi-language:#0400;} </style> <![endif]--><!--[if gte mso 9]><xml> <o:shapedefaults v:ext="edit" spidmax="5122"/> </xml><![endif]--><!--[if gte mso 9]><xml> <o:shapelayout v:ext="edit"> <o:idmap v:ext="edit" data="1"/> </o:shapelayout></xml><![endif]--></head><body lang="EN-US" link="blue" vlink="blue">
<div class="Section1">
<p class="MsoNormal"><span style="font-family: Arial;"><o:p><br>
</o:p></span></p>
<div align="center">
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
<tbody>
<tr style="">
<td style="padding: 0cm;" valign="top">
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
<tbody>
          <tr>
            <td style="vertical-align: top;"><span style="font-size: 8pt; font-family: Arial; color: blue;"><a href="../../../../../Release_Notes.html">Back to Release page</a></span></td>
          </tr>
<tr style="">
<td style="padding: 1.5pt;">
<h1 style="margin-bottom: 18pt; text-align: center;" align="center"><span style="font-size: 20pt; font-family: Verdana; color: rgb(51, 102, 255);">Release
Notes for STM32F10x CMSIS</span><span style="font-size: 20pt; font-family: Verdana;"><o:p></o:p></span></h1>
<p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;">Copyright 2010 STMicroelectronics</span><span style="color: black;"><u1:p></u1:p><o:p></o:p></span></p>
<p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;"><img alt="" id="_x0000_i1025" src="../../../../../_htmresc/logo.bmp" style="border: 0px solid ; width: 86px; height: 65px;"></span><span style="font-size: 10pt;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
<p class="MsoNormal"><span style="font-family: Arial; display: none;"><o:p>&nbsp;</o:p></span></p>
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" width="900">
<tbody>
<tr>
<td style="padding: 0cm;" valign="top">
<h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial;"><span style="font-size: 12pt; color: white;">Contents<o:p></o:p></span></h2>
<ol style="margin-top: 0cm;" start="1" type="1">
<li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><a href="#History">STM32F10x CMSIS
update History</a><o:p></o:p></span></li>
<li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><a href="#License">License</a><o:p></o:p></span></li>
</ol>
<span style="font-family: &quot;Times New Roman&quot;;"></span>
<h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial;"><a name="History"></a><span style="font-size: 12pt; color: white;">STM32F10x CMSIS
update History</span></h2>
            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 558.05pt;"><span style="font-size: 10pt; font-family: Arial; color: white;">3.4.0
- 10/15/2010</span></h3>

            <ol>
<li><b><i><span style="font-size: 10pt; font-family: Verdana;">General</span></i></b></li>
            </ol>

            <ul style="margin-top: 0in;" type="disc">
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add support
for&nbsp;<b>STM32F10x High-density Value line devices</b>.</span></li>
            </ul>
            <ol start="2">
              <li><b><i><span style="font-size: 10pt; font-family: Verdana;">STM32F10x CMSIS Device Peripheral Access Layer </span></i></b></li>
            </ol>


            
            <ul style="margin-top: 0in;" type="disc">
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">STM32F10x CMSIS Cortex-M3 Device Peripheral Access Layer Header File:</span> <span style="font-weight: bold; font-style: italic;">stm32f10x.h</span></span><br>
              </li><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Update to support High-density Value line devices</span><span style="font-size: 10pt; font-family: Verdana;"></span></li><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add new define <span style="font-style: italic;">STM32F10X_HD_VL</span></span></li>
                  <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">RCC, AFIO, FSMC bits definition updated</span></li>
</ul>
                <li class="MsoNormal" style="">

                  <span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">All
STM32 devices definitions are commented by default. User has to select the
appropriate device before starting else an error will be signaled on compile
time.</span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add new IRQs definitons inside the IRQn_Type enumeration for STM23 High-density Value line devices.</span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">"<span style="font-weight: bold;">bool</span>" type removed.</span><br>
                  <span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;"></span></li>
</ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">STM32F10x CMSIS Cortex-M3 Device Peripheral Access Layer System Files:</span> <span style="font-weight: bold; font-style: italic;">system_stm32f10x.h and system_stm32f10x.c</span></span><br>
                <span style="font-size: 10pt; font-family: Verdana;"></span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;">"system_stm32f10x.c" </span><span style="font-weight: bold;"></span>moved to to "<span style="font-weight: bold; font-style: italic;">STM32F10x_StdPeriph_Template</span>" directory. This file is also moved to each example directory under "<span style="font-weight: bold; font-style: italic;">STM32F10x_StdPeriph_Examples</span>".</span><br>
<span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;"></span></span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">SystemInit_ExtMemCtl() </span>function: update to support High-density Value line devices.</span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add "<span style="font-style: italic;">VECT_TAB_SRAM</span>" inside "</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;">system_stm32f10x.c</span></span><span style="font-size: 10pt; font-family: Verdana;">"
to select if the user want to place the Vector Table in internal SRAM.
An additional define is also to specify the Vector Table offset "<span style="font-style: italic;">VECT_TAB_OFFSET</span>".<br>
                  </span></li>

              </ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">STM32F10x CMSIS startup files:</span></span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;">startup_stm32f10x_xx.s</span></span></li><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add three
startup files for STM32 High-density Value line devices:
                  <span style="font-weight: bold; font-style: italic;">startup_stm32f10x_hd_vl.s</span></span></li></ul>
            </ul>
            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 558.05pt;"><span style="font-size: 10pt; font-family: Arial; color: white;">3.3.0
- 04/16/2010</span></h3>

<ol><li><b><i><span style="font-size: 10pt; font-family: Verdana;">General</span></i></b></li></ol>
<ul style="margin-top: 0in;" type="disc"><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add support
for&nbsp;<b>STM32F10x XL-density devices</b>.</span></li><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add startup files for TrueSTUDIO toolchain<br></span></li></ul><ol start="2"><li><b><i><span style="font-size: 10pt; font-family: Verdana;">STM32F10x CMSIS Device Peripheral Access Layer </span></i></b></li></ol>

            <ul style="margin-top: 0in;" type="disc"><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">STM32F10x CMSIS Cortex-M3 Device Peripheral Access Layer Header File:</span> <span style="font-weight: bold; font-style: italic;">stm32f10x.h</span></span><br>
              </li><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Update to support XL-density devices</span><span style="font-size: 10pt; font-family: Verdana;"></span></li><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add new define <span style="font-style: italic;">STM32F10X_XL</span></span></li></ul><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add new IRQs for&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">TIM9..14</span></li><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Update FLASH_TypeDef structure</span></li><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add new IP instances TIM9..14</span></li><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">RCC, AFIO, DBGMCU bits definition updated</span></li></ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Correct IRQs definition for MD-, LD-, MD_VL- and LD_VL-density devices&nbsp;(remove&nbsp;comma "," at the end of enum list)<br></span></li></ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">STM32F10x CMSIS Cortex-M3 Device Peripheral Access Layer System Files:</span> <span style="font-weight: bold; font-style: italic;">system_stm32f10x.h and system_stm32f10x.c</span></span><br>
                <span style="font-size: 10pt; font-family: Verdana;"></span></li><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">SystemInit_ExtMemCtl() </span>function: update to support XL-density devices</span></li><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">SystemInit()</span> function: swap the order of SetSysClock() and SystemInit_ExtMemCtl() functions.&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><br>
                  </span></li></ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">STM32F10x CMSIS startup files:</span><span style="font-weight: bold; font-style: italic;"></span><span style="font-style: italic;"><span style="font-weight: bold;"></span></span></span></li><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">add three
startup files for STM32 XL-density&nbsp;devices:
                  <span style="font-weight: bold; font-style: italic;">startup_stm32f10x_xl.s</span></span></li><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;">startup_stm32f10x_md_vl.s</span> for RIDE7: add USART3 IRQ&nbsp;Handler (was missing in&nbsp;previous version)</span></li><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add startup files for TrueSTUDIO toolchain</span></li></ul></ul><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;"></span></span>
<h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 558.05pt;"><span style="font-size: 10pt; font-family: Arial; color: white;">3.2.0
- 03/01/2010</span></h3>
<ol style="margin-top: 0in;" start="1" type="1">
<li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">General</span></i></b><i><span style="font-size: 10pt; font-family: Verdana;"></span></i><i><span style="font-size: 10pt;"><o:p></o:p></span></i></li>
</ol>
<ul style="margin-top: 0in;" type="disc">

              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">STM32F10x CMSIS files updated to <span style="font-weight: bold;">CMSIS V1.30</span> release</span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Directory structure updated to be aligned with CMSIS V1.30<br>
                </span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add support
for&nbsp;<b>STM32 Low-density Value line (STM32F100x4/6) and
Medium-density Value line (STM32F100x8/B) devices</b>.&nbsp;</span><span style="font-size: 10pt;"><o:p></o:p></span></li>

</ul>
<ol style="margin-top: 0in;" start="2" type="1">
<li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">CMSIS Core Peripheral Access Layer</span></i></b></li></ol>
            <ul>
              <li><b><i><span style="font-size: 10pt; font-family: Verdana;"></span></i></b><span style="font-size: 10pt; font-family: Verdana;"> Refer to <a href="../../../CMSIS_changes.htm" target="_blank">CMSIS changes</a></span></li>
            </ul>
            <ol style="margin-top: 0in; list-style-type: decimal;" start="3">
              <li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">STM32F10x CMSIS Device Peripheral Access Layer </span></i></b><b><i><span style="font-size: 10pt;"><o:p></o:p></span></i></b></li>

            </ol>

            <ul style="margin-top: 0in;" type="disc">

              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">STM32F10x CMSIS Cortex-M3 Device Peripheral Access Layer Header File:</span> <span style="font-weight: bold; font-style: italic;">stm32f10x.h</span></span><br>
              </li>
              <ul>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Update
the stm32f10x.h file to support new Value line devices features: CEC
peripheral, new General purpose timers TIM15, TIM16 and TIM17.</span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Peripherals Bits definitions updated to be in line with Value line devices available features.<br>
                  </span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">HSE_Value,
HSI_Value and HSEStartup_TimeOut changed to upper case: HSE_VALUE,
HSI_VALUE and HSE_STARTUP_TIMEOUT. Old names are kept for legacy
purposes.<br>
                  </span></li>
              </ul>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">STM32F10x CMSIS Cortex-M3 Device Peripheral Access Layer System Files:</span> <span style="font-weight: bold; font-style: italic;">system_stm32f10x.h and system_stm32f10x.c</span></span><br>
                <span style="font-size: 10pt; font-family: Verdana;"></span></li>
              <ul>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">SystemFrequency variable name changed to SystemCoreClock</span><br>
                  <span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;"></span></span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Default
                  </span></span><span style="font-size: 10pt; font-family: Verdana;">SystemCoreClock</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;"> is changed to 24MHz when Value line devices are selected and to 72MHz on other devices.</span></span><span style="font-size: 10pt;"><o:p></o:p></span><span style="font-size: 10pt; font-family: Verdana;"> <br>
                  </span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">All while(1) loop were removed from all clock setting functions. User has to handle the HSE startup failure.<br>
                  </span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Additional function <span style="font-weight: bold; font-style: italic;">void SystemCoreClockUpdate (void)</span> is provided.<br>
                  </span></li>
              </ul>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="text-decoration: underline;">STM32F10x CMSIS Startup files:</span> <span style="font-weight: bold; font-style: italic;">startup_stm32f10x_xx.s</span></span></li>
              <ul>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add new
startup files for STM32 Low-density Value line devices:
                  <span style="font-weight: bold; font-style: italic;">startup_stm32f10x_ld_vl.s</span></span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add new startup
files for STM32 Medium-density Value line devices:
                  <span style="font-weight: bold; font-style: italic;">startup_stm32f10x_md_vl.s</span></span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">SystemInit() function is called from startup file (startup_stm32f10x_xx.s) before to branch to application main.<br>
To reconfigure the default setting of SystemInit() function, refer to system_stm32f10x.c file <br>
</span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">GNU startup file for Low density devices (startup_stm32f10x_ld.s) is updated to fix compilation errors.<br>
</span></li>
              </ul>

            </ul>

<ul style="margin-top: 0in;" type="disc">
</ul>
<h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial;"><a name="License"></a><span style="font-size: 12pt; color: white;">License<o:p></o:p></span></h2>
<p class="MsoNormal" style="margin: 4.5pt 0cm;"><span style="font-size: 10pt; font-family: Verdana; color: black;">The
enclosed firmware and all the related documentation are not covered by
a License Agreement, if you need such License you can contact your
local STMicroelectronics office.<u1:p></u1:p><o:p></o:p></span></p>
<p class="MsoNormal"><b style=""><span style="font-size: 10pt; font-family: Verdana; color: black;">THE
PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO
SAVE TIME. AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR
ANY DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY
CLAIMS ARISING FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY
CUSTOMERS OF THE CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH
THEIR PRODUCTS. <o:p></o:p></span></b></p>
<p class="MsoNormal"><span style="color: black;"><o:p>&nbsp;</o:p></span></p>
<div class="MsoNormal" style="text-align: center;" align="center"><span style="color: black;">
<hr align="center" size="2" width="100%"></span></div>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt; text-align: center;" align="center"><span style="font-size: 10pt; font-family: Verdana; color: black;">For
complete documentation on </span><span style="font-size: 10pt; font-family: Verdana;">STM32(<span style="color: black;">CORTEX M3) 32-Bit Microcontrollers
visit </span><u><span style="color: blue;"><a href="http://www.st.com/stm32" target="_blank">www.st.com/STM32</a></span></u></span><span style="color: black;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
<p class="MsoNormal"><span style="font-size: 10pt;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
</div>
<p class="MsoNormal"><o:p>&nbsp;</o:p></p>
</div>
</body></html>