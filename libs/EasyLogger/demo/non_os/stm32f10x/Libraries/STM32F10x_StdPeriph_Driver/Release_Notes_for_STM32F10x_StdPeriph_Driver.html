<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40"><head>




<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="File-List" href="Library_files/filelist.xml">
<link rel="Edit-Time-Data" href="Library_files/editdata.mso"><!--[if !mso]> <style> v\:* {behavior:url(#default#VML);} o\:* {behavior:url(#default#VML);} w\:* {behavior:url(#default#VML);} .shape {behavior:url(#default#VML);} </style> <![endif]--><title>Release Notes for STM32F10x Standard Peripherals Library Drivers</title><!--[if gte mso 9]><xml> <o:DocumentProperties> <o:Author>STMicroelectronics</o:Author> <o:LastAuthor>STMicroelectronics</o:LastAuthor> <o:Revision>37</o:Revision> <o:TotalTime>136</o:TotalTime> <o:Created>2009-02-27T19:26:00Z</o:Created> <o:LastSaved>2009-03-01T17:56:00Z</o:LastSaved> <o:Pages>1</o:Pages> <o:Words>522</o:Words> <o:Characters>2977</o:Characters> <o:Company>STMicroelectronics</o:Company> <o:Lines>24</o:Lines> <o:Paragraphs>6</o:Paragraphs> <o:CharactersWithSpaces>3493</o:CharactersWithSpaces> <o:Version>11.6568</o:Version> </o:DocumentProperties> </xml><![endif]--><!--[if gte mso 9]><xml> <w:WordDocument> <w:Zoom>110</w:Zoom> <w:ValidateAgainstSchemas/> <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid> <w:IgnoreMixedContent>false</w:IgnoreMixedContent> <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText> <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel> </w:WordDocument> </xml><![endif]--><!--[if gte mso 9]><xml> <w:LatentStyles DefLockedState="false" LatentStyleCount="156"> </w:LatentStyles> </xml><![endif]-->



<style>
<!--
/* Style Definitions */
p.MsoNormal, li.MsoNormal, div.MsoNormal
{mso-style-parent:"";
margin:0in;
margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
h2
{mso-style-next:Normal;
margin-top:12.0pt;
margin-right:0in;
margin-bottom:3.0pt;
margin-left:0in;
mso-pagination:widow-orphan;
page-break-after:avoid;
mso-outline-level:2;
font-size:14.0pt;
font-family:Arial;
font-weight:bold;
font-style:italic;}
a:link, span.MsoHyperlink
{color:blue;
text-decoration:underline;
text-underline:single;}
a:visited, span.MsoHyperlinkFollowed
{color:blue;
text-decoration:underline;
text-underline:single;}
p
{mso-margin-top-alt:auto;
margin-right:0in;
mso-margin-bottom-alt:auto;
margin-left:0in;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
@page Section1
{size:8.5in 11.0in;
margin:1.0in 1.25in 1.0in 1.25in;
mso-header-margin:.5in;
mso-footer-margin:.5in;
mso-paper-source:0;}
div.Section1
{page:Section1;}
-->
</style><!--[if gte mso 10]> <style> /* Style Definitions */ table.MsoNormalTable {mso-style-name:"Table Normal"; mso-tstyle-rowband-size:0; mso-tstyle-colband-size:0; mso-style-noshow:yes; mso-style-parent:""; mso-padding-alt:0in 5.4pt 0in 5.4pt; mso-para-margin:0in; mso-para-margin-bottom:.0001pt; mso-pagination:widow-orphan; font-size:10.0pt; font-family:"Times New Roman"; mso-ansi-language:#0400; mso-fareast-language:#0400; mso-bidi-language:#0400;} </style> <![endif]--><!--[if gte mso 9]><xml> <o:shapedefaults v:ext="edit" spidmax="5122"/> </xml><![endif]--><!--[if gte mso 9]><xml> <o:shapelayout v:ext="edit"> <o:idmap v:ext="edit" data="1"/> </o:shapelayout></xml><![endif]--></head><body lang="EN-US" link="blue" vlink="blue">
<div class="Section1">
<p class="MsoNormal"><span style="font-family: Arial;"><o:p><br>
</o:p></span></p>
<div align="center">
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
<tbody>
<tr style="">
<td style="padding: 0cm;" valign="top">
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
<tbody>
          <tr>
            <td style="vertical-align: top;"><span style="font-size: 8pt; font-family: Arial; color: blue;"><a href="../../Release_Notes.html">Back to Release page</a></span></td>
          </tr>
<tr style="">
<td style="padding: 1.5pt;">
<h1 style="margin-bottom: 18pt; text-align: center;" align="center"><span style="font-size: 20pt; font-family: Verdana; color: rgb(51, 102, 255);">Release
Notes for STM32F10x Standard Peripherals Library Drivers
(StdPeriph_Driver)</span><span style="font-size: 20pt; font-family: Verdana;"><o:p></o:p></span></h1>
<p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;">Copyright 2010 STMicroelectronics</span><span style="color: black;"><u1:p></u1:p><o:p></o:p></span></p>
<p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;"><img alt="" id="_x0000_i1025" src="../../_htmresc/logo.bmp" style="border: 0px solid ; width: 86px; height: 65px;"></span><span style="font-size: 10pt;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
<p class="MsoNormal"><span style="font-family: Arial; display: none;"><o:p>&nbsp;</o:p></span></p>
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" width="900">
<tbody>
<tr>
<td style="padding: 0cm;" valign="top">
<h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial;"><span style="font-size: 12pt; color: white;">Contents<o:p></o:p></span></h2>
<ol style="margin-top: 0cm;" start="1" type="1">
<li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><a href="#History">STM32F10x Standard Peripherals Library
Drivers update History</a><o:p></o:p></span></li>
<li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><a href="#License">License</a><o:p></o:p></span></li>
</ol>
<span style="font-family: &quot;Times New Roman&quot;;">
</span>
<h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial;"><a name="History"></a><span style="font-size: 12pt; color: white;">STM32F10x Standard
Peripherals Library Drivers&nbsp; update History</span></h2>
            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 558.05pt;"><span style="font-size: 10pt; font-family: Arial; color: white;">3.4.0
- 10/15/2010</span></h3>

            <ol style="margin-top: 0in;" start="1" type="1">
<li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">General</span></i></b><i><span style="font-size: 10pt; font-family: Verdana;"> </span></i><i><span style="font-size: 10pt;"><o:p></o:p></span></i></li>
            </ol>

            <ul style="margin-top: 0in;" type="disc">
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add support for <span style="font-weight: bold;">STM32F10x High-density value line </span>devices.</span></li>
            </ul>

            <ol style="margin-top: 0in;" start="2" type="1">
<li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">STM32F10x_StdPeriph_Driver</span></i></b><b><i><span style="font-size: 10pt;"><o:p></o:p></span></i></b></li>
            </ol>

            
            <ul style="margin-top: 0in;" type="disc">

              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_bkp.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Delete BKP registers definition from stm32f10x_bkp.c and use defines within stm32f10x.h file. </span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_can.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Delete CAN registers definition from stm32f10x_can.c and use defines within stm32f10x.h file.<br>
</span></span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Update the wording of some defines and Asserts macro. <br>
                  </span></span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">CAN_GetFlagStatus()
and CAN_ClearFlag() functions: updated to support new flags (were not
supported in previous version). These flags are:&nbsp; CAN_FLAG_RQCP0,
CAN_FLAG_RQCP1, CAN_FLAG_RQCP2, CAN_FLAG_FMP1, CAN_FLAG_FF1,
CAN_FLAG_FOV1, CAN_FLAG_FMP0, CAN_FLAG_FF0,&nbsp;&nbsp; CAN_FLAG_FOV0,
CAN_FLAG_WKU, CAN_FLAG_SLAK and CAN_FLAG_LEC. <br>
                  </span></span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">CAN_GetITStatus()
function: add a check of the interrupt enable bit before getting the
status of corresponding interrupt pending bit. <br>
                  </span></span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">CAN_ClearITPendingBit() function: correct the procedure to clear the interrupt pending bit. <br>
                  </span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_crc.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Delete CRC registers definition from stm32f10x_crc.c and use defines within stm32f10x.h file.</span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_dac.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Delete DAC registers definition from stm32f10x_dac.c and use defines within stm32f10x.h file. </span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_dbgmcu.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Delete DBGMCU registers definition from stm32f10x_dbgmcu.c and use defines within stm32f10x.h file. </span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_dma.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Delete DMA registers definition from stm32f10x_dma.c and use defines within stm32f10x.h file.</span></span></li>
                <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Add new function "void DMA_SetCurrDataCounter(DMA_Channel_TypeDef* DMAy_Channelx, uint16_t DataNumber);"<br>
                  </span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_flash.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">FLASH functions (Erase and Program) updated to always clear the "PG", "MER" and "PER" bits even in case of TimeOut Error.</span><span style="font-style: italic;"></span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_fsmc.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Add new member "FSMC_AsynchronousWait" in "FSMC_NORSRAMInitTypeDef" structure.</span><span style="font-style: italic;"></span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_gpio.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">GPIO_PinRemapConfig()</span> function: add new values for <span style="font-style: italic;">GPIO_Remap</span> parameter, to support new <span style="font-style: italic;">remap for TIM6, TIM7 and DAC DMA requests, TIM12 and DAC Triggers / DMA2_Channel5 Interrupt mapping.</span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_pwr.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Delete PWR registers definition from stm32f10x_pwr.c and use defines within stm32f10x.h and core_cm3.h files.</span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_rtc.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Delete RTC registers definition from stm32f10x_rtc.c and use defines within stm32f10x.h file.</span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_spi.h/.c</span></li>
              <ul>
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Add new definition for I2S Audio Clock frequencies "I2S_AudioFreq_192k".</span></span></li>
              </ul>
              <li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_tim.h/.c</span></li>
<ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">Add new definition for TIM Input Capture Polarity "TIM_ICPolarity_BothEdge".</span></span></li></ul>
            
            </ul>

            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 558.05pt;"><span style="font-size: 10pt; font-family: Arial; color: white;">3.3.0
- 04/16/2010</span></h3>

<ol style="margin-top: 0in;" start="1" type="1"><li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">General</span></i></b><i><span style="font-size: 10pt; font-family: Verdana;"> </span></i><i><span style="font-size: 10pt;"><o:p></o:p></span></i></li></ol>
<ul style="margin-top: 0in;" type="disc"><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add support for <span style="font-weight: bold;">STM32F10x XL-density </span>devices.</span></li><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">I2C driver: events description and management enhancement.</span></li></ul>
<ol style="margin-top: 0in;" start="2" type="1"><li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">STM32F10x_StdPeriph_Driver</span></i></b><b><i><span style="font-size: 10pt;"><o:p></o:p></span></i></b></li></ol>
<ul style="margin-top: 0in;" type="disc"><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_dbgmcu.h/.c</span></li><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">DBGMCU_Config()</span> function: add new values <span style="font-style: italic;">DBGMCU_TIMx_STOP</span> (x: 9..14) for <span style="font-style: italic;">DBGMCU_Periph</span> parameter.</span></li></ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_flash.h/.c:
updated to support Bank2 of XL-density devices (up to 1MByte of Flash
memory). For more details, refer to the description provided within
stm32f10x_flash.c file.</span></li><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_gpio.h/.c</span></li><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">GPIO_PinRemapConfig()</span> function: add new values for <span style="font-style: italic;">GPIO_Remap</span> parameter, to support new <span style="font-style: italic;">remap for FSMC_NADV pin and TIM9..11,13,14.</span></span></li></ul><li class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_i2c.h/.c: I2C events description and management enhancement. <br></span></li><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">I2C_CheckEvent()</span>
function: updated to check whether the last event contains the
I2C_EVENT&nbsp; (instead of check whether the last event is equal to
I2C_EVENT)<br></span></li></ul><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add
detailed description of I2C events and how to manage them using the
functions provided by this driver. For more information, refer to
stm32f10x_i2c.h and stm32f10x_i2c.c files.</span></li></ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_rcc.h/.c: updated to support TIM9..TIM14 APB clock and reset configuration</span></li><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_tim.h/.c: updated to support new Timers TIM9..TIM14.</span></li><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">stm32f10x_sdio.h:&nbsp;</span></li><ul><li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">SDIO_SetSDIOReadWaitMode() function: correct values of SDIO_ReadWaitMode parameter<br>change <br>&nbsp;
#define
SDIO_ReadWaitMode_CLK&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp; ((uint32_t)0x00000000)<br>&nbsp; #define
SDIO_ReadWaitMode_DATA2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
((uint32_t)0x00000001)<br>by<br>&nbsp; #define
SDIO_ReadWaitMode_CLK&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp; ((uint32_t)0x00000001)<br>&nbsp; #define
SDIO_ReadWaitMode_DATA2&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
((uint32_t)0x00000000)</span></li></ul></ul>
<h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 558.05pt;"><span style="font-size: 10pt; font-family: Arial; color: white;">3.2.0
- 03/01/2010</span></h3>
<ol style="margin-top: 0in;" start="1" type="1">
<li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">General</span></i></b><i><span style="font-size: 10pt; font-family: Verdana;"> </span></i><i><span style="font-size: 10pt;"><o:p></o:p></span></i></li>
</ol>
<ul style="margin-top: 0in;" type="disc">

<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add support
for&nbsp;<b>STM32 Low-density Value line (STM32F100x4/6) and
Medium-density Value line (STM32F100x8/B) devices</b>.</span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Almost
peripherals drivers were updated to support Value
line devices features</span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Drivers limitations fix and enhancements. </span><span style="font-size: 10pt;"><o:p></o:p></span></li>

</ul>
<ol style="margin-top: 0in;" start="2" type="1">
<li class="MsoNormal" style=""><b><i><span style="font-size: 10pt; font-family: Verdana;">STM32F10x_StdPeriph_Driver</span></i></b><b><i><span style="font-size: 10pt;"><o:p></o:p></span></i></b></li>
</ol>
<ul style="margin-top: 0in;" type="disc">
<li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Add new
firmware driver for CEC peripheral: stm32f10x_cec.h and stm32f10x_cec.c</span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">Timers drivers stm32f10x_tim.h/.c: add support for new General Purpose Timers: TIM15, TIM16 and TIM17.</span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">RCC driver: add support for new Value peripherals: HDMI-CEC, TIM15, TIM16 and TIM17.</span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">GPIO driver: add new remap parameters for TIM1, TIM15, TIM16, TIM17 and HDMI-CEC: </span><span style="font-size: 10pt; font-family: Verdana;">GPIO_Remap_TIM1_DMA, </span><span style="font-size: 10pt; font-family: Verdana;">GPIO_Remap_TIM15, GPIO_Remap_TIM16, GPIO_Remap_TIM17, GPIO_Remap_CEC.</span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">USART
driver: add support for Oversampling by 8 mode and onebit method. 2
functions has been added: USART_OverSampling8Cmd() and
USART_OneBitMethodCmd().<br>
                </span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">DAC
driver: add new functions handling the DAC under run feature:
DAC_ITConfig(), DAC_GetFlagStatus(), DAC_ClearFlag(), DAC_GetITStatus()
and DAC_ClearITPendingBit().</span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">DBGMCU driver: add new parameters for TIM15, TIM16 and TIM17: DBGMCU_TIM15_STOP, DBGMCU_TIM16_STOP, DBGMCU_TIM17_STOP.<br>
                </span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">FLASH
driver: the FLASH_EraseOptionBytes() function updated. This is now just
erasing the option bytes without modifying the RDP status either
enabled or disabled.</span></li>
              <li class="MsoNormal" style=""><span style="font-size: 10pt; font-family: Verdana;">PWR
driver: the PWR_EnterSTOPMode() function updated. When woken up from
STOP mode, this function resets again the SLEEPDEEP bit in the
Cortex-M3 System Control register to allow Sleep mode entering.</span></li>
              

</ul>
<h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial;"><a name="License"></a><span style="font-size: 12pt; color: white;">License<o:p></o:p></span></h2>
<p class="MsoNormal" style="margin: 4.5pt 0cm;"><span style="font-size: 10pt; font-family: Verdana; color: black;">The
enclosed firmware and all the related documentation are not covered by
a License Agreement, if you need such License you can contact your
local STMicroelectronics office.<u1:p></u1:p><o:p></o:p></span></p>
<p class="MsoNormal"><b style=""><span style="font-size: 10pt; font-family: Verdana; color: black;">THE
PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO
SAVE TIME. AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR
ANY DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY
CLAIMS ARISING FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY
CUSTOMERS OF THE CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH
THEIR PRODUCTS. <o:p></o:p></span></b></p>
<p class="MsoNormal"><span style="color: black;"><o:p>&nbsp;</o:p></span></p>
<div class="MsoNormal" style="text-align: center;" align="center"><span style="color: black;">
<hr align="center" size="2" width="100%"></span></div>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt; text-align: center;" align="center"><span style="font-size: 10pt; font-family: Verdana; color: black;">For
complete documentation on </span><span style="font-size: 10pt; font-family: Verdana;">STM32(<span style="color: black;">CORTEX M3) 32-Bit Microcontrollers
visit </span><u><span style="color: blue;"><a href="http://www.st.com/stm32" target="_blank">www.st.com/STM32</a></span></u></span><span style="color: black;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
<p class="MsoNormal"><span style="font-size: 10pt;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
</div>
<p class="MsoNormal"><o:p>&nbsp;</o:p></p>
</div>
</body></html>