import rtconfig
Import('RTT_ROOT')
from building import *

# get current directory
cwd = GetCurrentDir()

# The set of source files associated with this SConscript file.
src = Split("""
CMSIS/CM3/CoreSupport/core_cm3.c
CMSIS/CM3/DeviceSupport/ST/STM32F10x/system_stm32f10x.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_crc.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_rcc.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_wwdg.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_pwr.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_exti.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_bkp.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_i2c.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_adc.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_dac.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_rtc.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_fsmc.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_tim.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_iwdg.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_spi.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_flash.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_sdio.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_gpio.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_usart.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_dbgmcu.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_dma.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_can.c
STM32F10x_StdPeriph_Driver/src/stm32f10x_cec.c
STM32F10x_StdPeriph_Driver/src/misc.c
""")

# starupt scripts for each STM32 kind
startup_scripts = {}
startup_scripts['STM32F10X_CL']     = 'startup_stm32f10x_cl.s'
startup_scripts['STM32F10X_HD']     = 'startup_stm32f10x_hd.s'
startup_scripts['STM32F10X_HD_VL']  = 'startup_stm32f10x_hd_vl.s'
startup_scripts['STM32F10X_LD']     = 'startup_stm32f10x_ld.s'
startup_scripts['STM32F10X_LD_VL']  = 'startup_stm32f10x_ld_vl.s'
startup_scripts['STM32F10X_MD']     = 'startup_stm32f10x_md.s'
startup_scripts['STM32F10X_MD_VL']  = 'startup_stm32f10x_md_vl.s'
startup_scripts['STM32F10X_XL']	    = 'startup_stm32f10x_xl.s'

# add for startup script 
if rtconfig.CROSS_TOOL == 'gcc':
    src = src + ['CMSIS/CM3/DeviceSupport/ST/STM32F10x/startup/gcc_ride7/' + startup_scripts[rtconfig.STM32_TYPE]]
elif rtconfig.CROSS_TOOL == 'keil':
    src = src + ['CMSIS/CM3/DeviceSupport/ST/STM32F10x/startup/arm/' + startup_scripts[rtconfig.STM32_TYPE]]
elif rtconfig.CROSS_TOOL == 'iar':
    src = src + ['CMSIS/CM3/DeviceSupport/ST/STM32F10x/startup/iar/' + startup_scripts[rtconfig.STM32_TYPE]]

path = [cwd + '/STM32F10x_StdPeriph_Driver/inc', 
    cwd + '/CMSIS/CM3/CoreSupport',
    cwd + '/CMSIS/CM3/DeviceSupport/ST/STM32F10x']

CPPDEFINES = ['USE_STDPERIPH_DRIVER', rtconfig.STM32_TYPE]
group = DefineGroup('STM32_StdPeriph', src, depend = [''], CPPPATH = path, CPPDEFINES = CPPDEFINES)

Return('group')
