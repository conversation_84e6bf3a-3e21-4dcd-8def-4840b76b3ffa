<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <configuration>
    <name>stm32f103xE</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>1</debug>
    <settings>
      <name>General</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>24</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>ExePath</name>
          <state>stm32f103xE\Exe</state>
        </option>
        <option>
          <name>ObjPath</name>
          <state>stm32f103xE\Obj</state>
        </option>
        <option>
          <name>ListPath</name>
          <state>stm32f103xE\List</state>
        </option>
        <option>
          <name>GEndianMode</name>
          <state>0</state>
        </option>
        <option>
          <name>Input variant</name>
          <version>3</version>
          <state>0</state>
        </option>
        <option>
          <name>Input description</name>
          <state>Automatic choice of formatter.</state>
        </option>
        <option>
          <name>Output variant</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>Output description</name>
          <state>Automatic choice of formatter.</state>
        </option>
        <option>
          <name>GOutputBinary</name>
          <state>0</state>
        </option>
        <option>
          <name>OGCoreOrChip</name>
          <state>1</state>
        </option>
        <option>
          <name>GRuntimeLibSelect</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>GRuntimeLibSelectSlave</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>RTDescription</name>
          <state>Use the normal configuration of the C/C++ runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
        </option>
        <option>
          <name>OGProductVersion</name>
          <state>6.50.2.4581</state>
        </option>
        <option>
          <name>OGLastSavedByProductVersion</name>
          <state>7.60.1.11206</state>
        </option>
        <option>
          <name>GeneralEnableMisra</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraVerbose</name>
          <state>0</state>
        </option>
        <option>
          <name>OGChipSelectEditMenu</name>
          <state>STM32F103ZE	ST STM32F103ZE</state>
        </option>
        <option>
          <name>GenLowLevelInterface</name>
          <state>1</state>
        </option>
        <option>
          <name>GEndianModeBE</name>
          <state>1</state>
        </option>
        <option>
          <name>OGBufferedTerminalOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>GenStdoutInterface</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>GeneralMisraVer</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules04</name>
          <version>0</version>
          <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
        </option>
        <option>
          <name>RTConfigPath2</name>
          <state>$TOOLKIT_DIR$\INC\c\DLib_Config_Normal.h</state>
        </option>
        <option>
          <name>GBECoreSlave</name>
          <version>24</version>
          <state>38</state>
        </option>
        <option>
          <name>OGUseCmsis</name>
          <state>0</state>
        </option>
        <option>
          <name>OGUseCmsisDspLib</name>
          <state>0</state>
        </option>
        <option>
          <name>GRuntimeLibThreads</name>
          <state>0</state>
        </option>
        <option>
          <name>CoreVariant</name>
          <version>24</version>
          <state>38</state>
        </option>
        <option>
          <name>GFPUDeviceSlave</name>
          <state>STM32F103ZE	ST STM32F103ZE</state>
        </option>
        <option>
          <name>FPU2</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>NrRegs</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>NEON</name>
          <state>0</state>
        </option>
        <option>
          <name>GFPUCoreSlave2</name>
          <version>24</version>
          <state>38</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ICCARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>31</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCDefines</name>
          <state>USE_STDPERIPH_DRIVER</state>
          <state>STM32F10X_HD</state>
          <state>USE_FULL_ASSERT</state>
        </option>
        <option>
          <name>CCPreprocFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocComments</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMnemonics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagSuppress</name>
          <state>Pa050</state>
        </option>
        <option>
          <name>CCDiagRemark</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagWarning</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagError</name>
          <state></state>
        </option>
        <option>
          <name>CCObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>CCAllowList</name>
          <version>1</version>
          <state>00000000</state>
        </option>
        <option>
          <name>CCDebugInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>IEndianMode</name>
          <state>1</state>
        </option>
        <option>
          <name>IProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>CCLangConformance</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSignedPlainChar</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRequirePrototypes</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagWarnAreErr</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCompilerRuntimeInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>IFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>CCLibConfigHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state></state>
        </option>
        <option>
          <name>CompilerMisraOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIncludePath2</name>
          <state>$PROJ_DIR$\..\app\inc</state>
          <state>$PROJ_DIR$\..\components\rtt_uart</state>
          <state>$PROJ_DIR$\..\components\easyflash\inc</state>
          <state>$PROJ_DIR$\..\components\easylogger\inc</state>
          <state>$PROJ_DIR$\..\components\easylogger\plugins\flash</state>
          <state>$PROJ_DIR$\..\components\others</state>
          <state>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\inc</state>
          <state>$PROJ_DIR$\..\RT-Thread-1.2.2\include</state>
          <state>$PROJ_DIR$\..\RT-Thread-1.2.2\components\drivers\include</state>
          <state>$PROJ_DIR$\..\RT-Thread-1.2.2\components\drivers\include\drivers</state>
          <state>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh</state>
          <state>$PROJ_DIR$\..\Libraries\CMSIS_EWARM\Include</state>
          <state>$PROJ_DIR$\..\Libraries\CMSIS_EWARM\CM3\DeviceSupport\ST\STM32F10x</state>
          <state>$PROJ_DIR$\..\..\..\..\..\easylogger\inc</state>
          <state>$PROJ_DIR$\..\..\..\..\..\easylogger\plugins\flash</state>
        </option>
        <option>
          <name>CCStdIncCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCodeSection</name>
          <state>.text</state>
        </option>
        <option>
          <name>IInterwork2</name>
          <state>0</state>
        </option>
        <option>
          <name>IProcessorMode2</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevel</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategy</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCOptLevelSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CompilerMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>CompilerMisraRules04</name>
          <version>0</version>
          <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
        </option>
        <option>
          <name>CCPosIndRopi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndRwpi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndNoDynInit</name>
          <state>0</state>
        </option>
        <option>
          <name>IccLang</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccAllowVLA</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCppDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccExceptions</name>
          <state>1</state>
        </option>
        <option>
          <name>IccRTTI</name>
          <state>1</state>
        </option>
        <option>
          <name>IccStaticDestr</name>
          <state>1</state>
        </option>
        <option>
          <name>IccCppInlineSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IccFloatSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptimizationNoSizeConstraints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCNoLiteralPool</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategySlave</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCGuardCalls</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>AARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>AObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>AEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>ACaseSensitivity</name>
          <state>1</state>
        </option>
        <option>
          <name>MacroChars</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>AWarnEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnWhat</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnOne</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange1</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange2</name>
          <state></state>
        </option>
        <option>
          <name>ADebug</name>
          <state>1</state>
        </option>
        <option>
          <name>AltRegisterNames</name>
          <state>0</state>
        </option>
        <option>
          <name>ADefines</name>
          <state></state>
        </option>
        <option>
          <name>AList</name>
          <state>0</state>
        </option>
        <option>
          <name>AListHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>AListing</name>
          <state>1</state>
        </option>
        <option>
          <name>Includes</name>
          <state>0</state>
        </option>
        <option>
          <name>MacDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>MacExps</name>
          <state>1</state>
        </option>
        <option>
          <name>MacExec</name>
          <state>0</state>
        </option>
        <option>
          <name>OnlyAssed</name>
          <state>0</state>
        </option>
        <option>
          <name>MultiLine</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>TabSpacing</name>
          <state>8</state>
        </option>
        <option>
          <name>AXRef</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDefines</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefInternal</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDual</name>
          <state>0</state>
        </option>
        <option>
          <name>AProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AOutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>AMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsEdit</name>
          <state>100</state>
        </option>
        <option>
          <name>AIgnoreStdInclude</name>
          <state>0</state>
        </option>
        <option>
          <name>AUserIncludes</name>
          <state></state>
        </option>
        <option>
          <name>AExtraOptionsCheckV2</name>
          <state>0</state>
        </option>
        <option>
          <name>AExtraOptionsV2</name>
          <state></state>
        </option>
        <option>
          <name>AsmNoLiteralPool</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>OBJCOPY</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OOCOutputFormat</name>
          <version>3</version>
          <state>3</state>
        </option>
        <option>
          <name>OCOutputOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCOutputFile</name>
          <state>EasyFlash.bin</state>
        </option>
        <option>
          <name>OOCCommandLineProducer</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCObjCopyEnable</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CUSTOM</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <extensions></extensions>
        <cmdline></cmdline>
        <hasPrio>0</hasPrio>
      </data>
    </settings>
    <settings>
      <name>BICOMP</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
    <settings>
      <name>BUILDACTION</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <prebuild></prebuild>
        <postbuild></postbuild>
      </data>
    </settings>
    <settings>
      <name>ILINK</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>17</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>IlinkLibIOConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>XLinkMisraHandler</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkInputFileSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOutputFile</name>
          <state>EasyLogger.out</state>
        </option>
        <option>
          <name>IlinkDebugInfoEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkKeepSymbols</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinaryFile</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinarySymbol</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinarySegment</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinaryAlign</name>
          <state></state>
        </option>
        <option>
          <name>IlinkDefines</name>
          <state></state>
        </option>
        <option>
          <name>IlinkConfigDefines</name>
          <state></state>
        </option>
        <option>
          <name>IlinkMapFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogFile</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogInitialization</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogModule</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogSection</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogVeneer</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIcfOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkIcfFile</name>
          <state>$PROJ_DIR$\..\components\others\stm32f103xE.icf</state>
        </option>
        <option>
          <name>IlinkIcfFileSlave</name>
          <state></state>
        </option>
        <option>
          <name>IlinkEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkSuppressDiags</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsRem</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsWarn</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsErr</name>
          <state></state>
        </option>
        <option>
          <name>IlinkWarningsAreErrors</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkUseExtraOptions</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>IlinkLowLevelInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAutoLibEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAdditionalLibs</name>
          <state></state>
        </option>
        <option>
          <name>IlinkOverrideProgramEntryLabel</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabelSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabel</name>
          <state>__iar_program_start</state>
        </option>
        <option>
          <name>DoFill</name>
          <state>0</state>
        </option>
        <option>
          <name>FillerByte</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>FillerStart</name>
          <state>0x0</state>
        </option>
        <option>
          <name>FillerEnd</name>
          <state>0x0</state>
        </option>
        <option>
          <name>CrcSize</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcAlign</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcPoly</name>
          <state>0x11021</state>
        </option>
        <option>
          <name>CrcCompl</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcBitOrder</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcInitialValue</name>
          <state>0x0</state>
        </option>
        <option>
          <name>DoCrc</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkBufferedTerminalOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkStdoutInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcFullSize</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIElfToolPostProcess</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogAutoLibSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogRedirSymbols</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogUnusedFragments</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcReverseByteOrder</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcUseAsInput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptInline</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptExceptionsAllow</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptExceptionsForce</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptMergeDuplSections</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptUseVfe</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptForceVfe</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackAnalysisEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackControlFile</name>
          <state></state>
        </option>
        <option>
          <name>IlinkStackCallGraphFile</name>
          <state></state>
        </option>
        <option>
          <name>CrcAlgorithm</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcUnitSize</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IlinkThreadsSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogCallGraph</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IARCHIVE</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>IarchiveInputs</name>
          <state></state>
        </option>
        <option>
          <name>IarchiveOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>IarchiveOutput</name>
          <state>###Unitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>BILINK</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
  </configuration>
  <group>
    <name>app</name>
    <file>
      <name>$PROJ_DIR$\..\app\src\app.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\app\src\app_task.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\app\inc\rtconfig.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\app\src\stm32f10x_it.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\app\src\user_finsh_cmd.c</name>
    </file>
  </group>
  <group>
    <name>components</name>
    <group>
      <name>easyflash</name>
      <group>
        <name>port</name>
        <file>
          <name>$PROJ_DIR$\..\components\easyflash\port\ef_port.c</name>
        </file>
      </group>
      <group>
        <name>src</name>
        <file>
          <name>$PROJ_DIR$\..\components\easyflash\src\easyflash.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\components\easyflash\src\ef_env.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\components\easyflash\src\ef_env_wl.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\components\easyflash\src\ef_iap.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\components\easyflash\src\ef_log.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\components\easyflash\src\ef_utils.c</name>
        </file>
      </group>
    </group>
    <group>
      <name>easylogger</name>
      <group>
        <name>plugins</name>
        <group>
          <name>flash</name>
          <file>
            <name>$PROJ_DIR$\..\..\..\..\..\easylogger\plugins\flash\elog_flash.c</name>
          </file>
          <file>
            <name>$PROJ_DIR$\..\components\easylogger\plugins\flash\elog_flash_port.c</name>
          </file>
        </group>
      </group>
      <group>
        <name>port</name>
        <file>
          <name>$PROJ_DIR$\..\components\easylogger\port\elog_port.c</name>
        </file>
      </group>
      <group>
        <name>src</name>
        <file>
          <name>$PROJ_DIR$\..\..\..\..\..\easylogger\src\elog.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\..\..\..\..\easylogger\src\elog_async.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\..\..\..\..\easylogger\src\elog_buf.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$\..\..\..\..\..\easylogger\src\elog_utils.c</name>
        </file>
      </group>
    </group>
    <group>
      <name>others</name>
      <file>
        <name>$PROJ_DIR$\..\components\others\bsp.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\components\others\utils.c</name>
      </file>
    </group>
    <group>
      <name>rtt_uart</name>
      <file>
        <name>$PROJ_DIR$\..\components\rtt_uart\usart.c</name>
      </file>
    </group>
  </group>
  <group>
    <name>libs</name>
    <group>
      <name>cmsis</name>
      <file>
        <name>$PROJ_DIR$\..\Libraries\CMSIS_EWARM\CM3\DeviceSupport\ST\STM32F10x\startup\iar\startup_stm32f10x_hd.s</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\CMSIS_EWARM\CM3\DeviceSupport\ST\STM32F10x\system_stm32f10x.c</name>
      </file>
    </group>
    <group>
      <name>std_periph_driver</name>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\misc.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_adc.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_bkp.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_can.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_cec.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_crc.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_dac.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_dbgmcu.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_dma.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_exti.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_flash.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_i2c.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_iwdg.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_pwr.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_rtc.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_sdio.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_spi.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_usart.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_wwdg.c</name>
      </file>
    </group>
  </group>
  <group>
    <name>rt_thread_1.2.2</name>
    <group>
      <name>drivers</name>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\drivers\src\completion.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\drivers\src\dataqueue.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\drivers\src\pipe.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\drivers\src\portal.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\drivers\src\ringbuffer.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\drivers\serial\serial.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\drivers\src\wrokqueue.c</name>
      </file>
    </group>
    <group>
      <name>finsh</name>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\cmd.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\finsh_compiler.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\finsh_error.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\finsh_heap.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\finsh_init.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\finsh_node.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\finsh_ops.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\finsh_parser.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\finsh_token.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\finsh_var.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\finsh_vm.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\msh.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\msh_cmd.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\shell.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\components\finsh\symbol.c</name>
      </file>
    </group>
    <group>
      <name>kernel</name>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\clock.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\cpuusage.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\device.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\idle.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\ipc.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\irq.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\kservice.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\mem.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\memheap.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\mempool.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\module.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\module.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\object.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\scheduler.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\slab.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\thread.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\src\timer.c</name>
      </file>
    </group>
    <group>
      <name>libcpu</name>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\libcpu\arm\cortex-m3\context_iar.S</name>
      </file>
      <file>
        <name>$PROJ_DIR$\..\RT-Thread-1.2.2\libcpu\arm\cortex-m3\cpuport.c</name>
      </file>
    </group>
  </group>
</project>


