<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_proj.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>stm32f103xE</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F103RE</Device>
          <Vendor>STMicroelectronics</Vendor>
          <Cpu>IRAM(0x20000000-0x2000FFFF) IROM(0x8000000-0x807FFFF) CLOCK(8000000) CPUTYPE("Cortex-M3")</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile>"STARTUP\ST\STM32F10x.s" ("STM32 Startup Code")</StartupFile>
          <FlashDriverDll>UL2CM3(-O14 -S0 -C0 -N00("ARM Cortex-M3") -D00(1BA00477) -L00(4) -FO7 -********** -FC800 -FN1 -FF0STM32F10x_512 -********** -FL080000)</FlashDriverDll>
          <DeviceId>4229</DeviceId>
          <RegisterFile>stm32f10x_lib.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath>ST\STM32F10x\</RegisterFilePath>
          <DBRegisterFilePath>ST\STM32F10x\</DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Output\</OutputDirectory>
          <OutputName>EasyLogger</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Output\list\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll>DARMSTM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pSTM32F103RE</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TARMSTM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pSTM32F103RE</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>7</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>Segger\JL2CM3.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4099</DriverSelection>
          </Flash1>
          <Flash2>Segger\JL2CM3.dll</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M3"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x10000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>0</interw>
            <Optim>1</Optim>
            <oTime>1</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <VariousControls>
              <MiscControls>--C99</MiscControls>
              <Define>USE_STDPERIPH_DRIVER,STM32F10X_HD,USE_FULL_ASSERT</Define>
              <Undefine></Undefine>
              <IncludePath>..\app\inc;..\components\rtt_uart;..\components\others;..\components\easyflash\inc;..\components\easylogger\inc;..\components\easylogger\plugins\flash;..\Libraries\STM32F10x_StdPeriph_Driver\inc;..\Libraries\CMSIS_RVMDK\CM3\CoreSupport;..\Libraries\CMSIS_RVMDK\CM3\DeviceSupport\ST\STM32F10x;..\RT-Thread-1.2.2\include;..\RT-Thread-1.2.2\components\drivers\include;..\RT-Thread-1.2.2\components\drivers\include\drivers;..\RT-Thread-1.2.2\components\finsh;..\..\..\..\..\easylogger\inc;..\..\..\..\..\easylogger\plugins\flash</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--keep __fsym_*</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>app</GroupName>
          <Files>
            <File>
              <FileName>app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\APP\src\app.c</FilePath>
            </File>
            <File>
              <FileName>app_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\APP\src\app_task.c</FilePath>
            </File>
            <File>
              <FileName>user_finsh_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\app\src\user_finsh_cmd.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\APP\src\stm32f10x_it.c</FilePath>
            </File>
            <File>
              <FileName>delay_conf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\APP\inc\delay_conf.h</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_conf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\APP\inc\stm32f10x_conf.h</FilePath>
            </File>
            <File>
              <FileName>rtconfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\APP\inc\rtconfig.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>bsp</GroupName>
          <Files>
            <File>
              <FileName>bsp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\others\bsp.c</FilePath>
            </File>
            <File>
              <FileName>utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\others\utils.c</FilePath>
            </File>
            <File>
              <FileName>usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\rtt_uart\usart.c</FilePath>
            </File>
            <File>
              <FileName>cpuport.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\libcpu\arm\cortex-m3\cpuport.c</FilePath>
            </File>
            <File>
              <FileName>context_rvds.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\RT-Thread-1.2.2\libcpu\arm\cortex-m3\context_rvds.S</FilePath>
            </File>
            <File>
              <FileName>startup_stm32f10x_hd.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\Libraries\CMSIS_RVMDK\CM3\DeviceSupport\ST\STM32F10x\startup\arm\startup_stm32f10x_hd.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>easyflash</GroupName>
          <Files>
            <File>
              <FileName>easyflash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\easyflash\src\easyflash.c</FilePath>
            </File>
            <File>
              <FileName>ef_env.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\easyflash\src\ef_env.c</FilePath>
            </File>
            <File>
              <FileName>ef_env_wl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\easyflash\src\ef_env_wl.c</FilePath>
            </File>
            <File>
              <FileName>ef_iap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\easyflash\src\ef_iap.c</FilePath>
            </File>
            <File>
              <FileName>ef_log.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\easyflash\src\ef_log.c</FilePath>
            </File>
            <File>
              <FileName>ef_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\easyflash\src\ef_utils.c</FilePath>
            </File>
            <File>
              <FileName>ef_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\easyflash\port\ef_port.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>easylogger</GroupName>
          <Files>
            <File>
              <FileName>elog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\easylogger\src\elog.c</FilePath>
            </File>
            <File>
              <FileName>elog_async.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\easylogger\src\elog_async.c</FilePath>
            </File>
            <File>
              <FileName>elog_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\easylogger\src\elog_buf.c</FilePath>
            </File>
            <File>
              <FileName>elog_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\easylogger\src\elog_utils.c</FilePath>
            </File>
            <File>
              <FileName>elog_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\..\easylogger\plugins\flash\elog_flash.c</FilePath>
            </File>
            <File>
              <FileName>elog_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\easylogger\port\elog_port.c</FilePath>
            </File>
            <File>
              <FileName>elog_flash_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\components\easylogger\plugins\flash\elog_flash_port.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>std_periph_driver</GroupName>
          <Files>
            <File>
              <FileName>misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\misc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_bkp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_bkp.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_can.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_can.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_cec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_cec.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_crc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_dbgmcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_dbgmcu.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_fsmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_i2c.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_sdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_sdio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\STM32F10x_StdPeriph_Driver\src\stm32f10x_wwdg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>cmsis</GroupName>
          <Files>
            <File>
              <FileName>core_cm3.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\CMSIS_RVMDK\CM3\CoreSupport\core_cm3.c</FilePath>
            </File>
            <File>
              <FileName>system_stm32f10x.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\CMSIS_RVMDK\CM3\DeviceSupport\ST\STM32F10x\system_stm32f10x.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>rt_thread_kernel</GroupName>
          <Files>
            <File>
              <FileName>clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\clock.c</FilePath>
            </File>
            <File>
              <FileName>cpuusage.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\cpuusage.c</FilePath>
            </File>
            <File>
              <FileName>device.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\device.c</FilePath>
            </File>
            <File>
              <FileName>idle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\idle.c</FilePath>
            </File>
            <File>
              <FileName>ipc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\ipc.c</FilePath>
            </File>
            <File>
              <FileName>irq.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\irq.c</FilePath>
            </File>
            <File>
              <FileName>kservice.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\kservice.c</FilePath>
            </File>
            <File>
              <FileName>mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\mem.c</FilePath>
            </File>
            <File>
              <FileName>memheap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\memheap.c</FilePath>
            </File>
            <File>
              <FileName>mempool.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\mempool.c</FilePath>
            </File>
            <File>
              <FileName>module.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\module.c</FilePath>
            </File>
            <File>
              <FileName>object.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\object.c</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\scheduler.c</FilePath>
            </File>
            <File>
              <FileName>slab.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\slab.c</FilePath>
            </File>
            <File>
              <FileName>thread.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\thread.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\src\timer.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>rt_thread_drivers</GroupName>
          <Files>
            <File>
              <FileName>completion.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\drivers\src\completion.c</FilePath>
            </File>
            <File>
              <FileName>dataqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\drivers\src\dataqueue.c</FilePath>
            </File>
            <File>
              <FileName>pipe.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\drivers\src\pipe.c</FilePath>
            </File>
            <File>
              <FileName>portal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\drivers\src\portal.c</FilePath>
            </File>
            <File>
              <FileName>ringbuffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\drivers\src\ringbuffer.c</FilePath>
            </File>
            <File>
              <FileName>wrokqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\drivers\src\wrokqueue.c</FilePath>
            </File>
            <File>
              <FileName>serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\drivers\serial\serial.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>rt_thread_finsh</GroupName>
          <Files>
            <File>
              <FileName>cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\cmd.c</FilePath>
            </File>
            <File>
              <FileName>finsh_compiler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\finsh_compiler.c</FilePath>
            </File>
            <File>
              <FileName>finsh_error.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\finsh_error.c</FilePath>
            </File>
            <File>
              <FileName>finsh_heap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\finsh_heap.c</FilePath>
            </File>
            <File>
              <FileName>finsh_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\finsh_init.c</FilePath>
            </File>
            <File>
              <FileName>finsh_node.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\finsh_node.c</FilePath>
            </File>
            <File>
              <FileName>finsh_ops.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\finsh_ops.c</FilePath>
            </File>
            <File>
              <FileName>finsh_parser.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\finsh_parser.c</FilePath>
            </File>
            <File>
              <FileName>finsh_token.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\finsh_token.c</FilePath>
            </File>
            <File>
              <FileName>finsh_var.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\finsh_var.c</FilePath>
            </File>
            <File>
              <FileName>finsh_vm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\finsh_vm.c</FilePath>
            </File>
            <File>
              <FileName>msh.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\msh.c</FilePath>
            </File>
            <File>
              <FileName>msh_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\msh_cmd.c</FilePath>
            </File>
            <File>
              <FileName>shell.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\shell.c</FilePath>
            </File>
            <File>
              <FileName>symbol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RT-Thread-1.2.2\components\finsh\symbol.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
