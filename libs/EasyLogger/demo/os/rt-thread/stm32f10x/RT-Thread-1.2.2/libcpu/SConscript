Import('RTT_ROOT')
Import('rtconfig')
from building import *

comm = rtconfig.ARCH + '/common'
path = rtconfig.ARCH + '/' + rtconfig.CPU

# The set of source files associated with this SConscript file.
if rtconfig.PLATFORM == 'armcc':
	src = Glob(path + '/*.c') + Glob(path + '/*_rvds.S') + Glob(comm + '/*.c')

if rtconfig.PLATFORM == 'gcc':
	src = Glob(path + '/*.c') + Glob(path + '/*_gcc.S') + Glob(comm + '/*.c') + Glob(path + '/*_init.S')

if rtconfig.PLATFORM == 'iar':
	src = Glob(path + '/*.c') + Glob(path + '/*_iar.S') + Glob(comm + '/*.c')

if rtconfig.PLATFORM == 'cl':
	src = Glob(path + '/*.c')

if rtconfig.PLATFORM == 'mingw':
	src = Glob(path + '/*.c')

CPPPATH = [RTT_ROOT + '/libcpu/' + rtconfig.ARCH + '/' + rtconfig.CPU, RTT_ROOT + '/libcpu/' + rtconfig.ARCH + '/common']
group = DefineGroup(rtconfig.CPU.upper(), src, depend = [''], CPPPATH = CPPPATH)

Return('group')
