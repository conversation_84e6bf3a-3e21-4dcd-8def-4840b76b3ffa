Import('RTT_ROOT')
Import('rtconfig')
from building import *

src = Glob('*.c')

CPPPATH = [RTT_ROOT + '/include']
if rtconfig.CROSS_TOOL == 'keil' and GetDepend('RT_USING_MODULE') == True:
    LINKFLAGS = ' --keep __rtmsym_* '
else:
    LINKFLAGS = ''

if GetDepend('RT_USING_MODULE') == False:
    SrcRemove(src, ['module.c'])

if GetDepend('RT_USING_HEAP') == False or GetDepend('RT_USING_SMALL_MEM') == False:
    SrcRemove(src, ['mem.c'])

if GetDepend('RT_USING_HEAP') == False or GetDepend('RT_USING_SLAB') == False:
    SrcRemove(src, ['slab.c'])

if GetDepend('RT_USING_MEMPOOL') == False:
    SrcRemove(src, ['mempool.c'])

if GetDepend('RT_USING_MEMHEAP') == False:
    Src<PERSON><PERSON>ove(src, ['memheap.c'])
    if GetDepend('RT_USING_MEMHEAP_AS_HEAP'):
        SrcRemove(src, ['mem.c'])

if GetDepend('RT_USING_DEVICE') == False:
    SrcRemove(src, ['device.c'])

group = DefineGroup('Kernel', src, depend = [''], CPPPATH = CPPPATH, LINKFLAGS = LINKFLAGS)

Return('group')
