#MicroXplorer Configuration settings - do not modify
Dma.Request0=USART2_TX
Dma.RequestsNb=1
Dma.USART2_TX.0.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART2_TX.0.EventEnable=DISABLE
Dma.USART2_TX.0.Instance=DMA1_Channel1
Dma.USART2_TX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_TX.0.MemInc=DMA_MINC_ENABLE
Dma.USART2_TX.0.Mode=DMA_NORMAL
Dma.USART2_TX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_TX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_TX.0.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.USART2_TX.0.Priority=DMA_PRIORITY_LOW
Dma.USART2_TX.0.RequestNumber=1
Dma.USART2_TX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.USART2_TX.0.SignalID=NONE
Dma.USART2_TX.0.SyncEnable=DISABLE
Dma.USART2_TX.0.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.USART2_TX.0.SyncRequestNumber=1
Dma.USART2_TX.0.SyncSignalID=NONE
FREERTOS.BinarySemaphores01=elog_lock,Dynamic,NULL;elog_async,Dynamic,NULL;elog_dma_lock,Dynamic,NULL
FREERTOS.FootprintOK=true
FREERTOS.IPParameters=Tasks01,configUSE_NEWLIB_REENTRANT,BinarySemaphores01,FootprintOK
FREERTOS.Tasks01=defaultTask,24,256,StartDefaultTask,Default,NULL,Static,defaultTaskBuffer,defaultTaskControlBlock;elog,8,512,elog_entry,As external,NULL,Static,elogBuffer,elogControlBlock
FREERTOS.configUSE_NEWLIB_REENTRANT=1
File.Version=6
KeepUserPlacement=false
Mcu.CPN=STM32G070RBT6
Mcu.Family=STM32G0
Mcu.IP0=DMA
Mcu.IP1=FREERTOS
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=SYS
Mcu.IP5=USART2
Mcu.IPNb=6
Mcu.Name=STM32G070RBTx
Mcu.Package=LQFP64
Mcu.Pin0=PC13
Mcu.Pin1=PC14-OSC32_IN (PC14)
Mcu.Pin10=VP_SYS_VS_tim1
Mcu.Pin2=PC15-OSC32_OUT (PC15)
Mcu.Pin3=PF0-OSC_IN (PF0)
Mcu.Pin4=PA2
Mcu.Pin5=PA3
Mcu.Pin6=PA5
Mcu.Pin7=PA13
Mcu.Pin8=PA14-BOOT0
Mcu.Pin9=VP_FREERTOS_VS_CMSIS_V2
Mcu.PinsNb=11
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32G070RBTx
MxCube.Version=6.6.1
MxDb.Version=DB.6.0.60
NVIC.DMA1_Channel1_IRQn=true\:3\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.PendSV_IRQn=true\:3\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.SVC_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:true
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:3\:0\:false\:false\:false\:true\:false\:true\:false
NVIC.TIM1_BRK_UP_TRG_COM_IRQn=true\:3\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TimeBase=TIM1_BRK_UP_TRG_COM_IRQn
NVIC.TimeBaseIP=TIM1
NVIC.USART2_IRQn=true\:3\:0\:false\:false\:true\:true\:true\:true\:true
PA13.GPIOParameters=GPIO_Label
PA13.GPIO_Label=TMS
PA13.Locked=true
PA13.Mode=Serial_Wire
PA13.Signal=SYS_SWDIO
PA14-BOOT0.GPIOParameters=GPIO_Label
PA14-BOOT0.GPIO_Label=TCK
PA14-BOOT0.Locked=true
PA14-BOOT0.Mode=Serial_Wire
PA14-BOOT0.Signal=SYS_SWCLK
PA2.GPIOParameters=GPIO_PuPd
PA2.GPIO_PuPd=GPIO_PULLUP
PA2.Locked=true
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.GPIOParameters=GPIO_PuPd
PA3.GPIO_PuPd=GPIO_PULLUP
PA3.Locked=true
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA5.GPIOParameters=GPIO_Speed,GPIO_Label
PA5.GPIO_Label=LED_GREEN
PA5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA5.Locked=true
PA5.Signal=GPIO_Output
PC13.Locked=true
PC13.Mode=SYS_WakeUp1
PC13.Signal=SYS_WKUP2
PC14-OSC32_IN\ (PC14).Locked=true
PC14-OSC32_IN\ (PC14).Mode=LSE-External-Oscillator
PC14-OSC32_IN\ (PC14).Signal=RCC_OSC32_IN
PC15-OSC32_OUT\ (PC15).Locked=true
PC15-OSC32_OUT\ (PC15).Mode=LSE-External-Oscillator
PC15-OSC32_OUT\ (PC15).Signal=RCC_OSC32_OUT
PF0-OSC_IN\ (PF0).GPIOParameters=GPIO_Label
PF0-OSC_IN\ (PF0).GPIO_Label=MCO
PF0-OSC_IN\ (PF0).Locked=true
PF0-OSC_IN\ (PF0).Mode=HSE-External-Clock-Source
PF0-OSC_IN\ (PF0).Signal=RCC_OSC_IN
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32G070RBTx
ProjectManager.FirmwarePackage=STM32Cube FW_G0 V1.6.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=nucleo-g070rb.ioc
ProjectManager.ProjectName=nucleo-g070rb
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART2_UART_Init-USART2-false-HAL-true
RCC.ADCFreq_Value=64000000
RCC.AHBFreq_Value=64000000
RCC.APBFreq_Value=64000000
RCC.APBTimFreq_Value=64000000
RCC.CECFreq_Value=32786.88524590164
RCC.CortexFreq_Value=64000000
RCC.EXTERNAL_CLOCK_VALUE=12288000
RCC.FCLKCortexFreq_Value=64000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=64000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=64000000
RCC.I2S1Freq_Value=64000000
RCC.IPParameters=ADCFreq_Value,AHBFreq_Value,APBFreq_Value,APBTimFreq_Value,CECFreq_Value,CortexFreq_Value,EXTERNAL_CLOCK_VALUE,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2C1Freq_Value,I2S1Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSI_VALUE,MCO1PinFreq_Value,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PWRFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,TIM15Freq_Value,TIM1Freq_Value,USART1Freq_Value,USART2Freq_Value,VCOInputFreq_Value,VCOOutputFreq_Value
RCC.LPTIM1Freq_Value=16000000
RCC.LPTIM2Freq_Value=16000000
RCC.LPUART1Freq_Value=16000000
RCC.LSCOPinFreq_Value=32000
RCC.LSI_VALUE=32000
RCC.MCO1PinFreq_Value=64000000
RCC.PLLPoutputFreq_Value=64000000
RCC.PLLQoutputFreq_Value=64000000
RCC.PLLRCLKFreq_Value=64000000
RCC.PWRFreq_Value=64000000
RCC.SYSCLKFreq_VALUE=64000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TIM15Freq_Value=16000000
RCC.TIM1Freq_Value=16000000
RCC.USART1Freq_Value=64000000
RCC.USART2Freq_Value=64000000
RCC.VCOInputFreq_Value=16000000
RCC.VCOOutputFreq_Value=128000000
USART2.IPParameters=VirtualMode-Asynchronous,WordLength
USART2.VirtualMode-Asynchronous=VM_ASYNC
USART2.WordLength=WORDLENGTH_8B
VP_FREERTOS_VS_CMSIS_V2.Mode=CMSIS_V2
VP_FREERTOS_VS_CMSIS_V2.Signal=FREERTOS_VS_CMSIS_V2
VP_SYS_VS_tim1.Mode=TIM1
VP_SYS_VS_tim1.Signal=SYS_VS_tim1
board=NUCLEO-G070RB
boardIOC=true
