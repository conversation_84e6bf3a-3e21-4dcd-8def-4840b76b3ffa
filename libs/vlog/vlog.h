/**
 * @file vlog.h
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * 日志控制命令
 * @version V1.001
 * @date 2025-03-25 
 * 
 * @copyright victel (c) 2024
 * 
 * @par modifies history:
 * Date            Version   Author      Description
 * 2025-03-25      V1.001    liming      创建文件
 */

#ifndef __VLOG_H__
#define __VLOG_H__

#include <stdarg.h>
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>

int vlog_init(void);
void vlog_output(uint8_t level, const char *tag, const char *file, const char *func,const long line,const char *format, ...);
void vlog_hexdump(const char *name, uint8_t width, const void *buf, uint16_t size);

/* output log's level */
#define VLOG_LVL_ASSERT                     (0)
#define VOG_LVL_ERROR                       (1)
#define VOG_LVL_WARN                        (2)
#define VOG_LVL_INFO                        (3)
#define VOG_LVL_DEBUG                       (4)
#define VOG_LVL_VERBOSE                     (5)


#define vlog_a(tag, ...)     vlog_output(VLOG_LVL_ASSERT,tag,__FILE__,__FUNCTION__,__LINE__, __VA_ARGS__)
#define vlog_e(tag, ...)     vlog_output(VOG_LVL_ERROR,  tag,__FILE__,__FUNCTION__,__LINE__, __VA_ARGS__)
#define vlog_w(tag, ...)     vlog_output(VOG_LVL_WARN,   tag,__FILE__,__FUNCTION__,__LINE__, __VA_ARGS__)
#define vlog_i(tag, ...)     vlog_output(VOG_LVL_INFO,   tag,__FILE__,__FUNCTION__,__LINE__, __VA_ARGS__)
#define vlog_d(tag, ...)     vlog_output(VOG_LVL_DEBUG,  tag,__FILE__,__FUNCTION__,__LINE__, __VA_ARGS__)
#define vlog_v(tag, ...)     vlog_output(VOG_LVL_INFO,tag,__FILE__,__FUNCTION__,__LINE__, __VA_ARGS__)
#define vlog_h(tag, ...)     vlog_output(VOG_LVL_HEX,tag,__FILE__,__FUNCTION__,__LINE__, __VA_ARGS__)

// #define vlog_a(tag, ...)     vlog_output(VLOG_LVL_ASSERT,tag,NULL,__FUNCTION__,__LINE__, __VA_ARGS__)
// #define vlog_e(tag, ...)     vlog_output(VOG_LVL_ERROR,  tag,NULL,__FUNCTION__,__LINE__, __VA_ARGS__)
// #define vlog_w(tag, ...)     vlog_output(VOG_LVL_WARN,   tag,NULL,__FUNCTION__,__LINE__, __VA_ARGS__)
// #define vlog_i(tag, ...)     vlog_output(VOG_LVL_INFO,   tag,NULL,__FUNCTION__,__LINE__, __VA_ARGS__)
// #define vlog_d(tag, ...)     vlog_output(VOG_LVL_DEBUG,  tag,NULL,__FUNCTION__,__LINE__, __VA_ARGS__)
// #define vlog_v(tag, ...)     vlog_output(VOG_LVL_INFO,tag,NULL,__FUNCTION__,__LINE__, __VA_ARGS__)
// #define vlog_h(tag, ...)     vlog_output(VOG_LVL_HEX,tag,NULL,__FUNCTION__,__LINE__, __VA_ARGS__)

#endif /*#ifndef __VLOG_H__*/

/*end of the file:vlog.h*/
