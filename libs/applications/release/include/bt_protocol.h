#ifndef BT_PROTOCOL_H
#define BT_PROTOCOL_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <unistd.h>
#include <errno.h>
#include <fcntl.h>
#include <termios.h>
#include <semaphore.h>

//============================> Libraries Headers <============================
#include <linux/types.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

//============================> Project Headers <============================
#include "serial.h"
#include "pcm.h"
/******************************************************************************

                                Defines

 ******************************************************************************/
// =============================================================================
// 协议常量定义
// =============================================================================
#define FRAME_SIZE          96
#define DATA_SIZE           80
#define CHECKSUM_DATA_SIZE  88
#define MAX_DEVICE_NAME     20
#define MAX_DEVICES         16

// 帧标识符
#define FRAME_HEADER_1      0x17
#define FRAME_HEADER_2      0xEA
#define FRAME_HEADER_3      0x18
#define FRAME_HEADER_4      0xEB
#define FRAME_TAIL_1        0x19
#define FRAME_TAIL_2        0xEC

// 蓝牙命令定义
#define BT_CMD_BASE         0xFB00

// 串口文件描述符
#define BLUETOOTH_PATH      SERIAL_PATH(S1)
#define BLUETOOTH_ON_PATH   "/sys/class/leds/bt_on/brightness"

// 串口缓冲区
#define RX_BUFFER_SIZE      576

// 音频相关常量
#define AUDIO_FRAME_SIZE        80      // 80字节音频数据
#define AUDIO_SAMPLE_RATE       8000    // 8K采样率
#define AUDIO_FRAME_DURATION_MS 10      // 10ms音频帧
#define AUDIO_BUFFER_MIN_MS     180     // 最小180ms缓冲
#define AUDIO_BUFFER_FRAMES     (AUDIO_BUFFER_MIN_MS / AUDIO_FRAME_DURATION_MS)  // 18帧
#define MAX_CONTINUOUS_FRAMES   36      // 最大连续发送36帧

// 日志宏定义
#define BT_LOG(format, ...) \
    do { if (ctx->debug_mode) printf("[BT] " format "\n", ##__VA_ARGS__); } while(0)

#define BT_ERROR(format, ...) \
    printf("[BT ERROR] " format "\n", ##__VA_ARGS__)

// 操作
#define MIN(a, b) a < b ? a : b

/******************************************************************************

                            Function Pointer

 ******************************************************************************/
// 音频处理回调函数类型
typedef void (*audio_rx_callback_t)(const uint8_t *audio_data, uint16_t sequence, void *user_data);

/******************************************************************************

                                Enums

 ******************************************************************************/
// 蓝牙命令枚举
typedef enum {
    BT_ADDR_READ_MAC    = 0x00000000,
    BT_ADDR_CALL_CTRL   = 0x00000001,
    BT_ADDR_SCAN_RESULT = 0x00000002,
    BT_ADDR_CONNECT     = 0x00000003,
    BT_ADDR_RECONNECT   = 0x00000004,
    BT_ADDR_DISCONNECT  = 0x00000005,
    BT_ADDR_AUDIO       = 0x00000006,
    BT_ADDR_PTT         = 0x00000007,
    BT_ADDR_UPGRADE     = 0x00000008
} bt_addr_t;

// 通话控制索引
typedef enum {
    CALL_INDEX_END_OUT  = 0x0000,
    CALL_INDEX_OUTGOING = 0x0001,
    CALL_INDEX_END_IN   = 0x0002,
    CALL_INDEX_INCOMING = 0x0003
} call_index_t;

// 连接状态
typedef enum {
    CONN_STATE_DISCONNECTED = 0x00,
    CONN_STATE_CONNECTED    = 0x01,
    CONN_STATE_CONNECTING   = 0x02,
    CONN_STATE_TIMEOUT      = 0x03
} conn_state_t;

// PTT状态
typedef enum {
    PTT_RELEASED = 0,
    PTT_PRESSED  = 1
} ptt_state_t;


/******************************************************************************

                                Struct

 ******************************************************************************/
// 蓝牙版本信息结构体
typedef struct __attribute__((packed)) {
    uint32_t bt_major    : 4;   // 蓝牙版本主版本号
    uint32_t bt_minor    : 4;   // 蓝牙版本次版本号
    uint32_t bt_fm_major : 4;   // 固件版本主版本号
    uint32_t bt_fm_minor : 4;   // 固件版本次版本号
    uint32_t bt_fm_day   : 5;   // 固件日期-日
    uint32_t bt_fm_month : 4;   // 固件日期-月
    uint32_t bt_fm_year  : 7;   // 固件日期-年
} bt_version_t;

// 通信帧结构体
typedef struct __attribute__((packed)) {
    uint8_t  header[4];         // 帧头: 0x17 0xEA 0x18 0xEB
    uint16_t ctask;             // 操作命令 (Little Endian)
    uint16_t index;             // 命令序号 (Little Endian)
    uint32_t abs_addr;          // 操作地址 (Little Endian)
    uint8_t  data[DATA_SIZE];   // 数据域 (80字节)
    uint16_t checksum;          // 校验和 (Little Endian)
    uint8_t  tail[2];           // 帧尾: 0x19 0xEC
} comm_frame_t;

// 蓝牙设备信息结构体
typedef struct {
    uint8_t mac[6];             // MAC地址
    bt_version_t version;         // 版本信息
    char scan_device_name[MAX_DEVICES][MAX_DEVICE_NAME];       // 扫描设备名称
    char connect_device_name[MAX_DEVICE_NAME];                 // 连接设备名称
    conn_state_t conn_state;     // 连接状态
    ptt_state_t ptt_state;       // ptt状态
    call_index_t call_index;    // 通话控制索引
} bt_device_info_t;

// 音频帧缓冲区结构体
typedef struct {
    uint8_t frame[MAX_CONTINUOUS_FRAMES][AUDIO_FRAME_SIZE];
    uint16_t sequence[MAX_CONTINUOUS_FRAMES];
    int head;
    int tail;
    int count;
    pthread_mutex_t mutex;
} bt_audio_buffer_t;

// 音频帧处理结构体
typedef struct {
    bt_audio_buffer_t rx_buffer;
    bt_audio_buffer_t tx_buffer;
    uint16_t tx_sequence;
    uint16_t last_rx_sequence;
    audio_rx_callback_t rx_callback;
    void *callback_user_data;
    bool audio_active;
    pthread_t audio_thread;
}bt_audio_context_t;

// 蓝牙上下文结构体
typedef struct {
    int serial_fd;
    void *audio_context;
    bool debug_mode;
    bool continuous;
    bt_device_info_t device_info;
} bt_context_t;

// 线程
typedef struct {
    pthread_t receive_thread;
    bool thread_running;
    bt_context_t *ctx;
} bt_thread_t;

/******************************************************************************

                                Variables

 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/
// =============================================================================
// 底层帧操作函数
// =============================================================================

/**
 * @brief 初始化通信帧
 * @param frame 要初始化的帧
 */
static void bt_init_frame(comm_frame_t *frame);

/**
 * @brief 计算帧校验和
 * @param frame 要计算校验和的帧
 * @return 计算出的校验和
 */
static uint16_t bt_frame_calculate_checksum(const comm_frame_t *frame);

/**
 * @brief 设置帧校验和
 * @param frame 要设置校验和的帧
 */
static void bt_frame_set_checksum(comm_frame_t *frame);

/**
 * @brief 验证帧的完整性
 * @param frame 要验证的帧
 * @return true-验证成功，false-验证失败
 */
static bool bt_frame_validate(const comm_frame_t *frame);

// =============================================================================
// 串口通信函数
// =============================================================================

/**
 * @brief 发送帧到串口
 * @param ctx 蓝牙上下文结构体
 * @param frame 要发送的帧
 * @return 0-验证成功，-1-验证失败
 */
int bt_serial_send_frame(bt_context_t *ctx, const comm_frame_t *frame);

/**
 * @brief 接收原始数据
 * @param ctx 蓝牙上下文结构体
 * @param buffer 接收数据的缓冲区
 * @param size 接收数据的大小
 * @return 0-验证成功，-1-验证失败
 */
static int receive_raw_data(bt_context_t *ctx, uint8_t *buffer, size_t size);

/**
 * @brief 提取完整帧
 * @param ctx 蓝牙上下文结构体
 * @param frame 指向要提取完整帧的缓冲区
 * @return true-提取成功，false-提取失败
 */
static bool extract_complete_frame(bt_context_t *ctx, comm_frame_t *frame);

/**
 * @brief 接收线程函数
 * @param arg 接收线程参数
 */
static void* receive_thread_func(void *arg);

// =============================================================================
// 蓝牙收发音频函数
// =============================================================================

/**
 * @brief 初始化音频帧缓冲区
 * @param buffer 指向待初始化的音频帧缓冲区结构体指针
 * @return 0-初始化成功，-1-初始化失败
 */
static int bt_init_audio_buffer(bt_audio_buffer_t *buffer);


/**
 * @brief 销毁蓝牙音频缓冲区。
 * @param buffer 指向需要销毁的 bt_audio_buffer_t 缓冲区指针
 */
static void bt_destroy_audio_buffer(bt_audio_buffer_t *buffer);

/**
 * @brief 向蓝牙音频缓冲区发送音频数据
 * @param buffer 指向目标蓝牙音频缓冲区的指针
 * @param audio_data 指向要推送的音频数据的指针
 * @param sequence 音频数据的序列号，用于数据包排序或丢包检测
 * @return 成功返回0，失败返回-1
 */
static int bt_audio_buffer_push(bt_audio_buffer_t *buffer, const uint8_t *audio_data, uint16_t sequence);

/**
 * @brief 向蓝牙音频缓冲区取出音频数据
 * @param buffer 指向目标蓝牙音频缓冲区的指针
 * @param audio_data 指向要推送的音频数据的指针
 * @param sequence 音频数据的序列号，用于数据包排序或丢包检测
 * @return 成功返回0，失败返回-1
 */
static int bt_audio_buffer_pop(bt_audio_buffer_t *buffer, uint8_t *audio_data, uint16_t *sequence);
/******************************************************************************

                                Public Functions

 ******************************************************************************/
// =============================================================================
// 高级蓝牙操作函数
// =============================================================================

/**
 * @brief 读取蓝牙模块MAC地址及版本
 * @param ctx 蓝牙上下文结构体
 * @return 0-验证成功，-1-验证失败
 */
int bt_read_mac_version(bt_context_t *ctx);

/**
 * @brief 读取蓝牙扫描结果
 * @param ctx 蓝牙上下文结构体
 * @param index 要读取的设备数量索引
 * @return 0-验证成功，-1-验证失败
 */
int bt_read_scan_result(bt_context_t *ctx, uint16_t index);

/**
 * @brief 连接蓝牙设备
 * @param ctx 蓝牙上下文结构体
 * @return 0-验证成功，-1-验证失败
 */
int bt_connect_device(bt_context_t *ctx);

/**
 * @brief 回连蓝牙设备
 * @param ctx 蓝牙上下文结构体
 * @return 0-验证成功，-1-验证失败
 */
int bt_reconnect_device(bt_context_t *ctx);

/**
 * @brief 断开蓝牙连接
 * @param ctx 蓝牙上下文结构体
 * @return 0-验证成功，-1-验证失败
 */
int bt_disconnect_device(bt_context_t *ctx);

/**
 * @brief 通话控制
 * @param ctx 蓝牙上下文结构体
 * @param call_index 通话控制索引
 * @return 0-验证成功，-1-验证失败
 */
int bt_call_control(bt_context_t *ctx, call_index_t call_index);

/**
 * @brief 发送音频数据
 * @param ctx 蓝牙上下文结构体
 * @param alaw_data alaw格式音频帧
 * @param sequence 音频帧序列号
 * @return 0-验证成功，-1-验证失败
 */
int bt_send_audio(bt_context_t *ctx, const uint8_t *alaw_data, uint16_t sequence);

/**
 * @brief PTT事件控制
 * @param ctx 蓝牙上下文结构体
 * @param ptt_state PTT状态（0-释放，1-按下）
 * @return 0-验证成功，-1-验证失败
 */
int bt_ptt_event(bt_context_t *ctx, ptt_state_t ptt_state);

/**
 * @brief 蓝牙升级使能
 * @param ctx 蓝牙上下文结构体
 * @return 0-验证成功，-1-验证失败
 */
int bt_upgrade_enable(bt_context_t *ctx);

/**
 * @brief 发送全零帧
 * @param ctx 串口文件描述符
 * @return 0-验证成功，-1-验证失败
 */
int bt_serial_send_zero_frame(bt_context_t *ctx);

/**
 * @brief 处理并发送音频数据到蓝牙设备
 * @param ctx 蓝牙协议上下文指针，包含当前连接和传输的相关信息
 * @return int 返回操作结果，0表示成功，-1表示失败
 */
int bt_process_send_audio(bt_context_t *ctx);

/**
 * @brief 处理接收到的音频数据帧
 * @param ctx   蓝牙协议上下文指针，包含当前连接和状态信息
 * @param frame 指向接收到的通信帧的指针，包含音频数据及相关元数据
 * @return      处理结果，返回0表示成功，-1表示失败
 */
int bt_process_received_audio(bt_context_t *ctx, const comm_frame_t *frame);

// =============================================================================
// 工具函数
// =============================================================================

/**
 * @brief 打印帧的十六进制内容（调试用）
 * @param ctx 蓝牙上下文结构体
 * @param frame 要打印的帧
 */
void bt_frame_print_hex(bt_context_t *ctx, const comm_frame_t *frame);

// =============================================================================
// 蓝牙音频函数
// =============================================================================

/**
 * @brief 初始化音频功能
 * @param ctx 蓝牙上下文结构体
 * @param rx_callback 音频接收回调函数
 * @param user_data 用户自定义数据
 * @return 0-验证成功，-1-验证失败
 */
int bt_audio_send_pcm(bt_context_t *ctx, const short *pcm_data, size_t size);

/**
 * @brief 获取接收到的PCM音频数据
 * @param ctx 蓝牙上下文结构体
 * @param pcm_data 用于存储PCM音频数据的缓冲区
 * @return 返回读取的PCM数据大小，-1表示错误
 */
int bt_audio_get_pcm(bt_context_t *ctx, short *pcm_data);

/**
 * @brief 清理音频功能
 * @param ctx 蓝牙上下文结构体
 */
void bt_audio_cleanup(bt_context_t *ctx);

/**
 * @brief 初始化蓝牙音频功能
 * @param ctx 蓝牙上下文结构体
 * @param rx_callback 音频接收回调函数
 * @param user_data 用户自定义数据
 * @return 0-验证成功，-1-验证失败
 */
int bt_audio_init(bt_context_t *ctx, audio_rx_callback_t rx_callback, void *user_data);

/**
* @brief 开启蓝牙开关
* @param bool 蓝牙状态
* @return 0-验证成功，-1-验证失败
*/
int set_bt_state(bool state);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
