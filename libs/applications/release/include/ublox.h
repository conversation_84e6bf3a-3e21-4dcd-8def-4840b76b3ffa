#ifndef UBLOX_H
#define UBLOX_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdint.h>

//============================> Libraries Headers <============================

//============================> Project Headers <============================


/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/
#define UBX_SYNC1 0xF1
#define UBX_SYNC2 0xD9

/* 常用UBX指令*/
// 冷启动指令
#define CMD_COLD_START    (uint8_t[]){0xF1, 0xD9, 0x06, 0x40, 0x01, 0x00, 0x01, 0x48, 0x22}

// 温启动指令
#define CMD_WARM_START    (uint8_t[]){0xF1, 0xD9, 0x06, 0x40, 0x01, 0x00, 0x02, 0x49, 0x23}

// 热启动指令
#define CMD_HOT_START     (uint8_t[]){0xF1, 0xD9, 0x06, 0x40, 0x01, 0x00, 0x03, 0x4A, 0x24}

// 模块复位指令
#define CMD_RESET         (uint8_t[]){0xF1, 0xD9, 0x06, 0x40, 0x01, 0x00, 0x00, 0x47, 0x21}

// 恢复出厂设置指令
#define CMD_FACTORY_RESET (uint8_t[]){0xF1, 0xD9, 0x06, 0x09, 0x08, 0x00, 0x02, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x15, 0x01}

// 休眠低功耗模式指令
#define CMD_SLEEP_MODE    (uint8_t[]){0xF1, 0xD9, 0x06, 0x41, 0x05, 0x00, 0xFF, 0xFF, 0xFF, 0x7F, 0x03, 0xCB, 0x56}

// 查询固件/硬件版本信息指令
#define CMD_VERSION_INFO  (uint8_t[]){0xF1, 0xD9, 0x0A, 0x04, 0x00, 0x00, 0x0E, 0x34}

// 配置保存指令
#define CMD_SAVE_CONFIG   (uint8_t[]){0xF1, 0xD9, 0x06, 0x09, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2F, 0x00, 0x00, 0x00, 0x46, 0xB7}

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/
// UBX Protocol Class IDs
typedef enum{
    UBX_CLASSID_NAV = 0X01,
    UBX_CLASSID_RXM = 0X02,
    UBX_CLASSID_INF = 0X04,
    UBX_CLASSID_ACK = 0X05,
    UBX_CLASSID_CFG = 0X06,
    UBX_CLASSID_MON = 0X0A,
    UBX_CLASSID_AID = 0X0B,
    UBX_CLASSID_TIM = 0X0D,
    UBX_CLASSID_LOG = 0X21,
}ubx_class_id_t;

// ACK Message IDs (0x05)
typedef enum {
    UBX_ACK_NAK = 0x00,
    UBX_ACK_ACK = 0x01
} ubx_ack_id_t;

// AID Message IDs (0x0B)
typedef enum {
    UBX_AID_REQ = 0x00,
    UBX_AID_INI = 0x01,
    UBX_AID_HUI = 0x02,
    UBX_AID_DATA = 0x10,
    UBX_AID_EPH = 0x31,
    UBX_AID_ALP = 0x50,
    UBX_AID_AOP = 0x33,
    UBX_AID_ALPSRV = 0x32,
    UBX_AID_ALM = 0x30
} ubx_aid_id_t;

// CFG Message IDs (0x06)
typedef enum {
    UBX_CFG_PRT = 0x00,
    UBX_CFG_MSG = 0x01,
    UBX_CFG_INF = 0x02,
    UBX_CFG_RST = 0x04,
    UBX_CFG_DAT = 0x06,
    UBX_CFG_RATE = 0x08,
    UBX_CFG_CFG = 0x09,
    UBX_CFG_RXM = 0x11,
    UBX_CFG_ANT = 0x13,
    UBX_CFG_NMEA = 0x17,
    UBX_CFG_USB = 0x1B,
    UBX_CFG_NAVX5 = 0x23,
    UBX_CFG_NAV5 = 0x24,
    UBX_CFG_TP5 = 0x31,
    UBX_CFG_RINV = 0x34,
    UBX_CFG_ITFM = 0x39,
    UBX_CFG_PM2 = 0x3B,
    UBX_CFG_GNSS = 0x3E,
    UBX_CFG_LOGFILTER = 0x47,
    UBX_CFG_SBAS = 0x16
} ubx_cfg_id_t;

// INF Message IDs (0x04)
typedef enum {
    UBX_INF_ERROR = 0x00,
    UBX_INF_WARNING = 0x01,
    UBX_INF_NOTICE = 0x02,
    UBX_INF_TEST = 0x03,
    UBX_INF_DEBUG = 0x04
} ubx_inf_id_t;

// LOG Message IDs (0x21)
typedef enum {
    UBX_LOG_STRING = 0x04,
    UBX_LOG_CREATE = 0x07,
    UBX_LOG_INFO = 0x08,
    UBX_LOG_RETRIEVE = 0x09,
    UBX_LOG_RETRIEVEPOS = 0x0B,
    UBX_LOG_RETRIEVESTRING = 0x0D,
    UBX_LOG_FINDTIME = 0x0E,
    UBX_LOG_ERASE = 0x03
} ubx_log_id_t;

// MON Message IDs (0x0A)
typedef enum {
    UBX_MON_IO = 0x02,
    UBX_MON_VER = 0x04,
    UBX_MON_MSGPP = 0x06,
    UBX_MON_RXBUF = 0x07,
    UBX_MON_TXBUF = 0x08,
    UBX_MON_HW = 0x09,
    UBX_MON_HW2 = 0x0B,
    UBX_MON_RXR = 0x21
} ubx_mon_id_t;

// NAV Message IDs (0x01)
typedef enum {
    UBX_NAV_POSECEF = 0x01,
    UBX_NAV_POSLLH = 0x02,
    UBX_NAV_STATUS = 0x03,
    UBX_NAV_DOP = 0x04,
    UBX_NAV_SOL = 0x06,
    UBX_NAV_PVT = 0x07,
    UBX_NAV_VELECEF = 0x11,
    UBX_NAV_VELNED = 0x12,
    UBX_NAV_TIMEGPS = 0x20,
    UBX_NAV_TIMEUTC = 0x21,
    UBX_NAV_CLOCK = 0x22,
    UBX_NAV_SVINFO = 0x30,
    UBX_NAV_DGPS = 0x31,
    UBX_NAV_SBAS = 0x32,
    UBX_NAV_AOPSTATUS = 0x60
} ubx_nav_id_t;

// RXM Message IDs (0x02)
typedef enum {
    UBX_RXM_RAW = 0x10,
    UBX_RXM_SFRB = 0x11,
    UBX_RXM_SVSI = 0x20,
    UBX_RXM_ALM = 0x30,
    UBX_RXM_EPH = 0x31,
    UBX_RXM_PMREQ = 0x41
} ubx_rxm_id_t;

// TIM Message IDs (0x0D)
typedef enum {
    UBX_TIM_TP = 0x01,
    UBX_TIM_TM2 = 0x03,
    UBX_TIM_VRFY = 0x06
} ubx_tim_id_t;


/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/
#pragma pack(push, 1)  // 确保结构体按1字节对齐
typedef struct {
    uint8_t sync_char1; 
    uint8_t sync_char2;
    uint8_t class_id;
    uint8_t id;
    uint8_t length[2]; /* 小端模式 */
    uint8_t *payload; /* 小端模式 */
    uint8_t ck_a;
    uint8_t ck_b;
}UBXmessage;
#pragma pack(pop)
/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/



/******************************************************************************

                                Public Functions

 ******************************************************************************/
/**
 * @brief 计算NMEA协议的校验和
 * 
 * @param msg NMEA协议字符串, 以'$'开头且'*'结尾中间的字符串
 * @return 校验和结果
 */
int checksum(char * msg);

/**
 * @brief 计算UBX协议的校验和
 * 
 * @param message UBX协议的classid+id+length+playload
 * @param length 长度
 * @param ck_a 校验和A
 * @param ck_b 校验和B
 */
void calculate_checksum(uint8_t *message, int length, uint8_t *ck_a, uint8_t *ck_b);

/**
 * @brief 发送ubx协议消息
 * 
 * @param fd 串口文件描述符
 * @param msg ubx协议消息
 * @return 0:成功 -1:失败
 */
int send_ubx_message(int fd, UBXmessage *msg);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
