#ifndef SENSOR_H
#define SENSOR_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <linux/types.h>
#include "stdint.h"
//============================> Libraries Headers <============================

//============================> Project Headers <============================


/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

//============================> File Path <============================//
#define SENSOR_PATH(type)   "/dev/" #type
#define SC5883LP_PATH        SENSOR_PATH(sc5883lp)
#define ALS_PATH             SENSOR_PATH(als)
#define SA6100LP_PATH        SENSOR_PATH(sa6100lp)

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/
typedef struct s_sc5883lp_data
{
    int16_t x;
    int16_t y;
    int16_t z;
} sc5883lp_data_t;

typedef struct s_sa6100lp_data
{
    int16_t x;
    int16_t y;
    int16_t z;
} sa6100lp_data_t;

/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/



/******************************************************************************

                                Public Functions

 ******************************************************************************/
/**
 * @brief: 读取sc5883lp电子罗盘传感器数据
 * @param 传感器数据
 * @return -1: 失败, 0: 成功
 */
int read_sc5883lp_data(void *buf);

/**
 * @brief: 读取sa6100lp三轴加速度传感器数据
 * @param 传感器数据
 * @return -1: 失败, 0: 成功
 */
int read_sa6100lp_data(void *buf);

/**
 * @brief: 读取光照传感器数据
 * @param 传感器数据
 * @return -1: 失败, 0: 成功
 */
int read_als_data(unsigned short *data);


#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
