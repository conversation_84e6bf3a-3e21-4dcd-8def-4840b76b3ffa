#ifndef LED_H
#define LED_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>

//============================> Libraries Headers <============================

//============================> Project Headers <============================


/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

//============================> File Path <============================//
#define LED_PATH(type, color)   get_led_path(#type, color)
#define LED_BRIGHTNESS(color)   LED_PATH(brightness, color)
#define LED_TRIGGER(color)      LED_PATH(trigger, color)
#define LED_DELAY_ON(color)     LED_PATH(delay_on, color)
#define LED_DELAY_OFF(color)    LED_PATH(delay_off, color)
#define LED_KEYBOARD   "/sys/class/leds/key_light/brightness"

//============================> Trigger Mode <============================//
#define LED_TRIGGER_NONE        "none"
#define LED_TRIGGER_TIMER       "timer"
#define LED_TRIGGER_ONESHOT     "oneshot"
#define LED_TRIGGER_HEARTBEAT   "heartbeat"
#define LED_TRIGGER_DEFAULT_ON  "default-on"
#define LED_TRIGGER_TRANSIENT   "transient"
#define LED_TRIGGER_GPIO        "gpio"
#define LED_TRIGGER_CPU         "cpu"
#define LED_TRIGGER_CPU0        "cpu0"
#define LED_TRIGGER_PANIC       "panic"
#define LED_TRIGGER_ACTIVITY    "activity"
#define LED_TRIGGER_TTY         "tty"
#define LED_TRIGGER_MMC1        "mmc1"
#define LED_TRIGGER_MTD         "mtd"
#define LED_TRIGGER_NAND        "nand-disk"

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

static inline const char* get_led_path(const char *type, const char *color) {
    static char path[64];
    snprintf(path, sizeof(path), "/sys/class/leds/led-%s/%s", color, type);
    return path;
}

/******************************************************************************

                                Public Functions

 ******************************************************************************/

 /**
 * @brief 向指定的颜色LED灯设置亮灭
 * 
 * @param color LED灯颜色
 * @param brightness 1为亮，0为灭
 *
 */
void set_led_brightness(const char *color, int brightness);

/**
 * @brief 向指定的颜色LED灯设置触发模式
 * 
 * @param color LED灯颜色
 * @param trigger_mode 触发模式
 *
 */
void set_led_trigger(const char *color, const char *trigger_mode);

/**
 * @brief 当触发方式设置为"timer"时，向指定的颜色LED灯设置延时亮的时间
 * 
 * @param color LED灯颜色
 * @param delay_ms 延时亮时间(ms)
 *
 */
void set_led_delay_on(const char *color, int delay_ms);

/**
 * @brief 当触发方式设置为"timer"时，向指定的颜色LED灯设置延时灭的时间
 * 
 * @param color LED灯颜色
 * @param delay_ms 延时灭时间(ms)
 *
 */
void set_led_delay_off(const char *color, int delay_ms);

/**
 * @brief 设置键盘背光灯亮灭
 * 
 * @param state 1为亮，0为灭
 *
 */
void set_keyboard_led_brightness(bool state);


#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
