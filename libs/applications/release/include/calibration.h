#ifndef CALIBRATION_H
#define CALIBRATION_H

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <math.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>

// 常量定义
#define MAGBUFFSIZE 650                // 磁力计数据缓冲区大小
#define SC5883LP_UTPERCOUNT 0.1f       // 磁力计灵敏度 (uT/LSB),不同量程灵敏度不一致
#define DEFAULTB 50.0f                 // 默认地磁场强度 (uT)
#define X 0                            // 向量分量
#define Y 1
#define Z 2
#define ONETHIRD 0.33333333f           // 1/3
#define ONESIXTH 0.166666667f          // 1/6
#define MINMEASUREMENTS4CAL 40         // 4元素校准所需的最小测量数
#define MINMEASUREMENTS7CAL 100        // 7元素校准所需的最小测量数
#define MINMEASUREMENTS10CAL 150       // 10元素校准所需的最小测量数
#define MINBFITUT 22.0f                // 有效校准的最小地磁场强度 (uT)
#define MAXBFITUT 67.0f                // 有效校准的最大地磁场强度 (uT)
#define CORRUPTMATRIX 0.001F

// 点结构体
typedef struct {
    float x;
    float y;
    float z;
} Point_t;

// 磁力计校准结构体
typedef struct {
    float V[3];                  // current hard iron offset x, y, z, (uT)
    float invW[3][3];            // current inverse soft iron matrix
    float B;                     // current geomagnetic field magnitude (uT)
    float FourBsq;               // current 4*B*B (uT^2)
    float FitError;              // current fit error %
    float FitErrorAge;           // current fit error % (grows automatically with age)
    float trV[3];                // trial value of hard iron offset z, y, z (uT)
    float trinvW[3][3];          // trial inverse soft iron matrix size
    float trB;                   // trial value of geomagnetic field magnitude in uT
    float trFitErrorpc;          // trial value of fit error %
    float A[3][3];               // ellipsoid matrix A
    float invA[3][3];            // inverse of ellipsoid matrix A
    float matA[10][10];          // scratch 10x10 matrix used by calibration algorithms
    float matB[10][10];          // scratch 10x10 matrix used by calibration algorithms
    float vecA[10];              // scratch 10x1 vector used by calibration algorithms
    float vecB[4];               // scratch 4x1 vector used by calibration algorithms
    int8_t ValidMagCal;          // integer value 0, 4, 7, 10 denoting both valid calibration and solver used
    int16_t BpFast[3][MAGBUFFSIZE];   // uncalibrated magnetometer readings
    int8_t  valid[MAGBUFFSIZE];        // 1=has data, 0=empty slot
    int16_t MagBufferCount;           // number of magnetometer readings
} MagCalibration_t;

// 质量评估结构体
typedef struct {
    float gaps;                        // 覆盖率指标
    float variance;                    // 方差指标
    float wobble;                      // 摆动指标
    float fit_error;                   // 拟合误差指标
} Quality_t;

// 全局变量声明
extern MagCalibration_t magcal;
extern Quality_t quality;

// 函数声明
void calibration_init(void);
int calibration_add_data(int16_t x, int16_t y, int16_t z);
int calibration_run(void);
void calibration_apply(int16_t rawx, int16_t rawy, int16_t rawz, Point_t *out);
void calibration_calc_quality(void);
void calibration_print_results(void);
int calibration_read_data_from_device(const char *device_path);

// 质量评估函数
float quality_surface_gap_error(void);
float quality_magnitude_variance_error(void);
float quality_wobble_error(void);
float quality_fit_error(void);
void quality_reset(void);
void quality_update(const Point_t *point);

// 校准算法函数
void fUpdateCalibration4INV(MagCalibration_t *MagCal);
void fUpdateCalibration7EIG(MagCalibration_t *MagCal);
void fUpdateCalibration10EIG(MagCalibration_t *MagCal);

#endif // CALIBRATION_H