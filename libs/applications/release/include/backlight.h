#ifndef BACKLIGHT_H
#define BACKLIGHT_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================

//============================> Libraries Headers <============================

//============================> Project Headers <============================


/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

//============================> File Path <============================//
#define BACKLIGHT_PATH(type)   "/sys/class/backlight/backlight/" #type
#define BACKLIGHT_BRIGHTNESS   BACKLIGHT_PATH(brightness)

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/



/******************************************************************************

                                Public Functions

 ******************************************************************************/

/**
 * @brief 设置背光亮度
 * 
 * @param brightness 背光亮度值(0-100)
 *
 * @return 0:成功 -1:失败
 */
int set_backlight_brightness(int brightness);


#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
