/******************************************************************************

                                Includes

 ******************************************************************************/

#ifndef __UTILS_H__
#define __UTILS_H__

//============================> System Headers <============================
#include <stdio.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>

/******************************************************************************
  
                                Inline Functions
  
 ******************************************************************************/

/**
 * @brief 向指定路径的文件写入数据
 * 
 * @param path 文件路径
 * @param value 要写入的字符串值
 * @return int 成功返回0，失败返回-1
 */
static __inline__ int write_to_path(const char *path, const char *value) 
{
    int fd = open(path, O_WRONLY);
    if (fd == -1) {
        perror("Failed to open file");
        return -1;
    }

    if (write(fd, value, strlen(value)) == -1) {
        perror("Failed to write to file");
        close(fd);
        return -1;
    }

    close(fd);
    return 0;
}

#endif /* __UTILS_H__ */