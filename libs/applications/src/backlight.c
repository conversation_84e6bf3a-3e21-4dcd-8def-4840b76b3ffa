/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "backlight.h"
#include "utils.h"
/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/


/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/

int set_backlight_brightness(int brightness)
{
    if(brightness < 0 || brightness > 100)
    {
        printf("Invalid brightness value\n");
        return -1;
    }
    char buffer[16];
    snprintf(buffer, sizeof(buffer), "%d", brightness);
    write_to_path(BACKLIGHT_BRIGHTNESS, buffer);

    return 0;
}
