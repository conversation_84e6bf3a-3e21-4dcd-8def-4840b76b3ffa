/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <fcntl.h>
#include <time.h>
#include <unistd.h>
#include <termios.h>
#include <linux/rtc.h>
#include <sys/ioctl.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "rtctime.h"

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/


/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/
/******************************************************************************

                                Public Functions

 ******************************************************************************/
int read_rtc_time(int fd, void *buf)
{   
    int ret;
    struct rtc_time rtc_tm;
    rtc_time_t *time = (rtc_time_t *)buf;
    
    ret = ioctl(fd, RTC_RD_TIME, &rtc_tm);
    if (ret == -1) {
        perror("ioctl RTC_SET_TIME error");
        return -1;
    }

    time->hour = rtc_tm.tm_hour;
    time->minute = rtc_tm.tm_min;
    time->second = rtc_tm.tm_sec;

    return 0;
}

int set_rtc_time(int fd, void *buf)
{   
    int ret;
    struct rtc_time rtc_tm;
    rtc_time_t *time = (rtc_time_t *)buf;

    rtc_tm.tm_hour = time->hour;
    rtc_tm.tm_min = time->minute;
    rtc_tm.tm_sec = time->second;

    ret = ioctl(fd, RTC_SET_TIME, &rtc_tm);
    if (ret == -1) {
        perror("ioctl RTC_SET_TIME error");
        return -1;
    }

    return 0;
}