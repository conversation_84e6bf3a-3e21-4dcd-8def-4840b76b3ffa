/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdint.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "sensor.h"

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/


/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/
int read_sc5883lp_data(void *buf) 
{
    int ret; 
    sc5883lp_data_t *data = (sc5883lp_data_t *)buf;
    int fd = open(SC5883LP_PATH, O_RDONLY | O_NONBLOCK | O_NOCTTY);
    if (fd == -1) {
        perror("Failed to open file");
        return -1;
    }

    ret = read(fd, data, sizeof(sc5883lp_data_t));
    if(0 > ret) {
        perror("Failed to read from file");
        return -1;
    }

    close(fd);

    return 0;
}

int read_sa6100lp_data(void *buf) 
{
    int ret; 
    sa6100lp_data_t *data = (sa6100lp_data_t *)buf;
    int fd = open(SA6100LP_PATH, O_RDONLY | O_NONBLOCK | O_NOCTTY);
    if (fd == -1) {
        perror("Failed to open file");
        return -1;
    }

    ret = read(fd, data, sizeof(sa6100lp_data_t));
    if(0 > ret) {
        perror("Failed to read from file");
        return -1;
    }

    close(fd);

    return 0;
}

int read_als_data(unsigned short *data) 
{
    int ret; 
    int fd = open(ALS_PATH, O_RDONLY | O_NONBLOCK | O_NOCTTY);
    if (fd == -1) {
        perror("Failed to open file");
        return -1;
    }

    ret = read(fd, data, sizeof(data));
    if(0 > ret) {
        perror("Failed to read from file");
        return -1;
    }

    close(fd);

    return 0;
}