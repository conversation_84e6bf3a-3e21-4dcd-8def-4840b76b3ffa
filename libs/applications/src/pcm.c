/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdint.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "pcm.h"
/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/


/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/
static short search_table_bitscan(short pcm) {
    if (pcm < 0x0100) return 0;
    
    // 找到最高位1的位置
    short seg = 1;
    short temp = pcm >> 8;  // 右移8位
    
    while (temp >>= 1) {    // 继续右移直到为0
        seg++;
    }
    
    return (seg > 7) ? 7 : seg;  // 限制在0-7范围内
}

/******************************************************************************

                                Public Functions

 ******************************************************************************/
/*
 *
 *  [S][LLL][QQQQ]
 *  │  │    └─ 4位段内码
 *  │  └───── 3位段落码
 *  └──────── 1位符号位
 * 
 */

// PCM-Alaw转换函数
unsigned char pcm2pcma(short pcm) {
    unsigned char alaw = 0;
    short signFlag = pcm & 0x8000; 
    
    // 处理符号位
    if (signFlag != 0) {
        pcm = -pcm - 1;
    }
    
    // 16bit->13bit
    pcm &= 0x0fff;
    
    // 查找线段
    short seg = search_table_bitscan(pcm);
    
    // 量化
    if (seg == 0) {
        pcm >>= 1;
    } else {
        pcm >>= seg;
    }
    
    // 组装A-law
    alaw = (signFlag >> 8) | (seg << 4) | (pcm & 0x000f);
    
    // XOR防止长串0或1
    return (unsigned char)(alaw ^ 0xd5);
}

// Alaw-16bitPCM转换函数
short pcma2pcm(unsigned char alaw)
{
    char signFlag = alaw & 0x0080;  // 符号位
    alaw ^= 0x00d5;

    short seg = (alaw & 0x0070) >> 4;
    short pcm = (alaw & 0x000f) << 4 | 8;  //13bit->16bit
    if(seg > 0){
        pcm |= 0x0100;
        pcm <<= (seg - 1);
    }
    
    return (short)(signFlag == 0 ? pcm : -pcm);
}
