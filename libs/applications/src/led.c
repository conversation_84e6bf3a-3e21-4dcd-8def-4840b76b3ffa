/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "led.h"
#include "utils.h"
/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/


/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/

void set_led_brightness(const char *color, int brightness)
{
    char buffer[2];
    snprintf(buffer, sizeof(buffer), "%d", brightness);
    write_to_path(LED_BRIGHTNESS(color), buffer);
}

void set_led_trigger(const char *color, const char *trigger_mode)
{
    write_to_path(LED_TRIGGER(color), trigger_mode);
}

void set_led_delay_on(const char *color, int delay_ms)
{
    char buffer[16];
    snprintf(buffer, sizeof(buffer), "%d", delay_ms);
    write_to_path(LED_DELAY_ON(color), buffer);
}

void set_led_delay_off(const char *color, int delay_ms)
{
    char buffer[16];
    snprintf(buffer, sizeof(buffer), "%d", delay_ms);
    write_to_path(LED_DELAY_OFF(color), buffer);
}

void set_keyboard_led_brightness(bool state)
{
    char buffer[2];
    snprintf(buffer, sizeof(buffer), "%d", state ? 1 : 0);
    write_to_path(LED_KEYBOARD, buffer);
}
