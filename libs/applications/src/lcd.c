/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/fb.h>
//============================> Libraries Headers <============================

//============================> Project Headers <============================
#include "lcd.h"
/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/


/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/



/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/



/******************************************************************************
  
                                Variables    
  
 ******************************************************************************/

/******************************************************************************

                                Static Functions

 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/
int lcd_drv_set_rotation(lcd_drv_rotation_t rotation)
{
    int ret;

    if (rotation != LCD_DRV_ROTATE_0 && rotation != LCD_DRV_ROTATE_180)
    {
        return -1;
    }

    int fd = open(GC9307_PATH, O_RDWR);
    if (fd == -1) {
        perror("Failed to open file");
        return -1;
    }

    ret = ioctl(fd, FBIO_SETROTATION, &rotation);
    if (ret == -1) {
        perror("ioctl FBIO_SET_ROTATION error");
        close(fd);
        return -1;
    }

    close(fd);
    return 0;
}

void lcd_drv_toggle_sleep(bool state)
{
    int ret;

    int fd = open(FBDEV_PATH, O_WRONLY);
    if (fd == -1) {
        perror("Failed to open file");
        return;
    }

    ioctl(fd, FBIOBLANK, state ? FB_BLANK_POWERDOWN : FB_BLANK_NORMAL);
    if (ret == -1) {
        perror("ioctl FBIO_SET_ROTATION error");
        close(fd);
        return;
    }

    close(fd);
}

