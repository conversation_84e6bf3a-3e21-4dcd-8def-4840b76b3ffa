#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h>
#include "ublox.h"
#include "gps.h"

int checksum(char * msg) {
    char * p = msg;
    char sum = 0;

    while (*p && (*p != '*')) {
        sum ^= *p;
        p++;
    }

    return sum;
}

void calculate_checksum(uint8_t *message, int length, uint8_t *ck_a, uint8_t *ck_b) {
    *ck_a = 0;
    *ck_b = 0;
    for (int i = 0; i < length; i++) {
        *ck_a += message[i];
        *ck_b += *ck_a;
    }
}

int send_ubx_message(int fd, UBXmessage *msg) {
    uint8_t ck_a = 0, ck_b = 0;
    uint16_t length = (uint16_t)(msg->length[1] << 8 | msg->length[0]);

    // 使用栈分配代替动态分配，避免 malloc/free 的开销
    uint8_t message[length + 8];
    memset(message, 0, length + 8);

    // 构建完整消息
    message[0] = msg->sync_char1;
    message[1] = msg->sync_char2;
    message[2] = msg->class_id;
    message[3] = msg->id;
    message[4] = msg->length[0];
    message[5] = msg->length[1];
    memcpy(&message[6], msg->payload, length);

    // 计算校验和
    calculate_checksum(&message[2], length + 4, &ck_a, &ck_b);

    // 更新校验和到消息中
    message[length + 6] = ck_a;
    message[length + 7] = ck_b;

    // 更新到 UBXmessage 结构体
    msg->ck_a = ck_a;
    msg->ck_b = ck_b;

    // 发送数据
    ssize_t bytes_written = write_gps_data(fd, message, length + 8);
    if (bytes_written < 0) {
        perror("Failed to send UBX message");
        return -1;
    }

    return 0;
}

