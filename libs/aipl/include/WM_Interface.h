#ifndef __WM_INTERFACE_H__
#define __WM_INTERFACE_H__

#include <stdint.h>
#include "aipl.h"

#define WM_IPC_NULL     (0)
#define WM_IPC_MCU      (1)
#define WM_IPC_DSP      (2)
#define WM_IPC_PC       (30)

#define WM_IPC_HEAD     (0x5055)
#define WM_IPC_TAIL     (0xDCBA)

#define WM_Q_BUF_SIZE   (640)

typedef struct WM_IPC_TYPE
{
    unsigned short Head;
    unsigned short Length;
    unsigned char  SEQ;
    unsigned char  Distination;
    unsigned char  Source;
    unsigned char  Rsvd1;
    unsigned char  Data[WM_Q_BUF_SIZE];
    //unsigned char  Rsvd2;
    //unsigned char  Chksum;
    //unsigned short Tail;
}WM_IPC_TYPE;

typedef struct WM_Q_TYPE
{
    unsigned char  SAP0;
    unsigned char  SEQ0;
    unsigned char  SAP1;
    unsigned char  SEQ1;
    unsigned short Length;
    unsigned char  Data[WM_Q_BUF_SIZE];
}WM_Q_TYPE;

typedef enum WM_Q_EVENT_ENUM
{
    E_ID_NULL,
    E_ID_IPC,
    E_ID_PDU,
    E_ID_BER,
    E_ID_MIC,
}WM_Q_EVENT_ENUM;

//void WM_PostQ   (unsigned char SAP, unsigned char *s, unsigned short n);
void WM_PostQ   (unsigned int Event);
void WM_PostIntQ(unsigned int Event);
void WM_Indicate(unsigned char *s);
void WM_Request (unsigned char Id, unsigned char *s, unsigned short n);

void WM_IPC_Event(void);
void WM_PDU_Event(void);
void WM_BER_Event(void);
void WM_MIC_Event(void);
void WM_SCAN_Event(void);



/****AIPLD通信API*************************** */

int aipl_WMIInit(AiplProcessCallback MsgReceiveCallBack);

/*send message to aipld from wmi*/
int aipl_WMIMsgSendToAipld(unsigned char Id, unsigned char *s, unsigned short n);

/* send message to wmi from aipld*/
int aipl_WMIMsgSendToWMI(unsigned char Id, unsigned char *s, unsigned short n);


#endif /* __WM_INTERFACE_H__ */
