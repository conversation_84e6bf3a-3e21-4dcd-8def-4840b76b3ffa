/**
 * @file aipl.h
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * protocol bettween air interface physical layer(AI Layer1) and Data Link Layer(AI Layer 2)
 * @version V1.001
 * @date 2024-08-17
 * 
 * @copyright victel (c) 2024
 * 
 * @par modifies history:
 * Date            Version   Author      Description
 * 2024-08-17      V1.001    liming    
 * 2025-01-08      V1.002    liming      基于P2，调整消息id及内容，不分消息及语音通道 
 * 2025-03-03      V1.003    liming      1. 增加虚拟PTT，通过发送命令模拟PTT按下及释放  2.增加通用线程交互初始化及发送接口
 */
#ifndef _AIPL_H_ 
#define _AIPL_H_

#define MSG_FROM_AIPL    ('a')
#define MSG_FROM_WMI     ('w')
typedef enum _teAiplMsgType
{
    AIPL_ACK = 0,            /* normal ack*/
    AIPL_REGISTER,
    AIPL_VERSION,
    AIPL_RESERVED_2,
    AIPL_RESERVED_3,

    /* RF relevant */
    AIPL_RADIO_MOD_REQ,
    AIPL_SET_FREQ_REQ,
    AIPL_SET_POWER_REQ, 
    AIPL_TX_DATA_REQ,
    AIPL_RX_DATA_IND,

    AIPL_MONITOR_REQ,
    AIPL_MONITOR_RES,
    AIPL_TIMESLOT_IND,       /* report timeslot number*/
    AIPL_VIRTUAL_PTT,        /* 虚拟PTT*/
   
    /* audio path */
    AIPL_SPEAKER_CTRL_REQ,
    AIPL_MIC_CTRL_REQ,
    AIPL_AUDIO_SPK_VOL_SET_REQ,     /* MUTE PLAY */
    AIPL_AUDIO_PLAY_REQ,     /* Play voice after decode from vocoder*/
    AIPL_AUDIO_MIC_MUTE_REQ,
    AIPL_AUDIO_CAPTURE_IND,  /* capture from mic, send to Data link layer vocoder and encode*/

    /*  rf test */
    AIPL_RF_PARA_SET_REQ,

    AIPL_MSG_MAX
}teAiplMsgType;

#define AIPL_MSG_SIZE_MAX 1024
typedef struct _tsAiplMsg
{
    teAiplMsgType Type;
    int LenOfMsg;
    unsigned char Msg[4];
}tsAiplMsg;


/* AIPL_ACK */
typedef struct _tsAiplAck 
{
    unsigned int Status;
}tsAiplAck;

/*AIPL_REGISTER*/
typedef struct _tsRegisterType 
{
    unsigned int Channel;
}tsRegisterType;

/*AIPL_VERSION*/
typedef struct _tsAiplVersionType  
{
    int VerInfoLen;
    unsigned char Vers[4];//dummy for easing get ponitor address
}tsAiplVersionType;

/*AIPL_RADIO_MOD_REQ*/
typedef struct _tsRaidoModeReqType 
{
    unsigned int Mode;
}tsRaidoModeReqType;

/*AIPL_SET_FREQ_REQ*/
typedef struct _tsSetFreqReqType 
{
    unsigned int FreqType;  /* 1 -- 1st rf path ,2 - 2nd rf path(ONLY RX)*/
    unsigned int TxFreq;
    unsigned int RxFreq;
}tsSetFreqReqType;

/*AIPL_SET_POWER_REQ*/
typedef struct _tsSetPowerReq 
{
    unsigned int Power;
}tsSetPowerReq;

/*AIPL_TX_DATA_REQ*/
typedef struct _tsTxDataReq 
{
    unsigned int BurstDataType;
    unsigned char Symbols[36];//TBD:
}tsTxDataReq;


/*AIPL_RX_DATA_IND*/
typedef struct _tsRxDataIndType 
{
    unsigned int BurstDataType;
    unsigned char Symbols[36];//TBD:
}tsRxDataIndType;

/*AIPL_MONITOR_REQ*/
typedef struct _tsMonitorReqType 
{
    unsigned int RxPath;/* 1 - 1st RF Path ,2 - 2nd RF Path*/
    unsigned int RxFreq;
}tsMonitorReqType;

/*AIPL_MONITOR_RES*/
typedef struct _tsMonitorResType 
{
    unsigned int RxPath;  /* 1 -- 1st rf path ,2 - 2nd rf path(ONLY RX)*/
    unsigned int RxFreq;
    int Rssi;
}tsSetMonitorResType;

/*AIPL_TIMESLOT_IND*/
typedef struct _tsTimeSlotType 
{
    unsigned int Count;  
}tsTimeSlotType;

/*AIPL_VIRTUAL_PTT*/
typedef struct _tsVirturalPTTType 
{
    unsigned int State; /* 0 - release ptt, 1 - press ptt*/  
}tsVirturalPTTType ;

/*AIPL_SPEAKER_CTRL_REQ*/
typedef struct _tsSpeakerCtrlReqType
{
    int cmd; /*1 - On , 0 - Off*/
}tsSpeakerCtrlReqType;

/* AIPL_AUDIO_SPK_VOL_SET_REQ */
typedef struct _tsSpkVolSetReq
{
    unsigned int Volume; /* 0 - 100 %, 0 - indicate mute*/
}tsSpkVolSetReq;

/*AIPL_MIC_CTRL_REQ*/
typedef struct _tsMicCtrlReqType
{
    int cmd; /*1 - On , 0 - Off*/
    int MicPath; 
}tsMicCtrlReqType;

/*AIPL_AUDIO_PLAY_REQ*/
typedef struct _tsAudioPlayReqType 
{
    int VoiceType;/* 0 - voice , 1 - tone*/
    int ToneType;
    unsigned int size;
    char voice[960];  //60ms 960 =  60msx8*2
}tsAudioPlayReqType;

/*AIPL_RF_PARA_SET_REQ*/
typedef struct _tsRfParaSetReqType
{
    void *RfPara;
}tsRfParaSetReqType;

/*AIPL_AUDIO_CAPTURE_IND*/
typedef struct _tsAudioCaptureIndType 
{
    unsigned int size;
    char voice[960];  //20ms - > Max:60ms - 8Ksamples rate, 16bits. 30x8*2 *2= 960byte bytes
}tsAudioCaptureIndType;

/*send message to aipld*/
int aipl_MsgSendToAipld(teAiplMsgType type, void *msg, int size);

/*if need received message from aipld,please invoke aipl_Init,and register MsgReceiveCallBack*/
typedef int (*AiplProcessCallback)(void *msg, int size);
int aipl_Init(AiplProcessCallback MsgReceiveCallBack);


/*normal process communication*/
/*创建名称为name的服务端，接收其他线程信息，接收信息在MsgReceiveCallBack中处理
 *将创建线程接收消息
 */
int aipl_InitByName(const char *name,AiplProcessCallback MsgReceiveCallBack);

/*通过destname名称，任何进程及线程均可发送消息。*/
int aipl_MsgSendTo(const char *destname, void *msg, int size);

/*get aipl version*/
char *aipl_VersionGet(void);

/*********************pthread************************* */
typedef unsigned long int OS_THREAD_TYPE;

#define OS_NO_ERROR (0)
#define OS_AL_WAIT  (0)
#define OS_NO_WAIT  (1)
int   OS_Create(OS_THREAD_TYPE *Id, void *Routine, void *lpParam, int Priority);
void *OS_CreateQ(int n);
int   OS_PostQ(void *Handle, void *s, int Option);
void *OS_WaitQ(void *Handle, int Option);


/** utilities********************************************/
unsigned char  CRC8E(unsigned char Mask, unsigned char *s, unsigned int n);
unsigned short CRC16(unsigned short Mask, unsigned char *lpBuf, unsigned short Length);
unsigned int   CRC32(unsigned char *s, unsigned int Length);

void itoa(unsigned int n, char *s);
unsigned char htoa(unsigned char Byte);
unsigned char atoh(unsigned char Byte);
unsigned char itoh(unsigned char Byte);

void U16_U8(unsigned short Data, unsigned char *lpBuf);
void U32_U8(unsigned int Data, unsigned char *lpBuf);
unsigned short U8_U16(unsigned char *lpBuf);
unsigned int U8_U32(unsigned char *lpBuf);

unsigned int U8L_U32(unsigned char *lpBuf);
unsigned short U8L_U16(unsigned char *lpBuf);

void touppers(char *s);

void xmemcpy(unsigned char *lpOBJ, unsigned char *lpSRC, unsigned short n);
int atox(const char *pData);
unsigned char BCD(unsigned char c);

#endif /*end of #ifndef _AIPL_H_ */

/*end of the file : aipl.h*/
