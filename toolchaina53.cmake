set(G<PERSON><PERSON><PERSON>UX_PLATFORM ON)
set(CMAKE_SYSTEM_NAME "Linux")
set(TI_SDK_PATH "/home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-09.02.01.10")
SET(BUILD_PLATFROM_COMPLIER "aarch64" CACHE STRING "select build compliler")

# if (BUILD_PLATFROM_COMPLIER STREQUAL arm)
    message("arm compiler")
    set(CMAKE_SYSTEM_PROCESSOR "arm")
    set(SDK_PATH_TARGET "${TI_SDK_PATH}/linux-devkit/sysroots/x86_64-arago-linux")
    SET(CROSS_COMPILE "${TI_SDK_PATH}/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-linux-gnueabihf/bin/arm-none-linux-gnueabihf-")
# else ()
#     message("aarch64 compiler")
#     set(CMAKE_SYSTEM_PROCESSOR arm64)
#     set(SDK_PATH_TARGET "${TI_SDK_PATH}/linux-devkit/sysroots/aarch64-oe-linux")
#     SET(CROSS_COMPILE "${TI_SDK_PATH}/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-")
#     set(CMAKE_SYSROOT ${SDK_PATH_TARGET})
# endif ()


#specify the cross compiler
SET(CMAKE_C_COMPILER ${CROSS_COMPILE}gcc CACHE FILEPATH "Archiver")
SET(CMAKE_CXX_COMPILER ${CROSS_COMPILE}g++ CACHE FILEPATH "Archiver")
SET(CMAKE_AR ${CROSS_COMPILE}ar CACHE FILEPATH "Archiver")
SET(CMAKE_AS ${CROSS_COMPILE}as CACHE FILEPATH "Archiver")
SET(CMAKE_LD  ${CROSS_COMPILE}ld CACHE FILEPATH "Archiver")
SET(CMAKE_NM ${CROSS_COMPILE}nm CACHE FILEPATH "Archiver")
SET(CMAKE_STRIP  ${CROSS_COMPILE}strip CACHE FILEPATH "Archiver")
SET(CMAKE_RANLIB  ${CROSS_COMPILE}ranlib CACHE FILEPATH "Archiver")

# where is the target environment
SET(CMAKE_FIND_ROOT_PATH  ${SDK_PATH_TARGET})

# search for programs in the build host directories
SET(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)

# for libraries and headers in the target directories
SET(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
SET(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_VERBOSE_MAKEFILE ON)

set(ROOTFS_PATH "/home/<USER>/p3/rootfs/rootfs")
set(ALSA_PATH "/home/<USER>/alsa/install/include")
